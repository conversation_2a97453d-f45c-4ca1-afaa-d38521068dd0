/**
 * Modern Dashboard JavaScript - Clean Version
 */
(function($) {
    'use strict';

    function initCounterAnimations() {
        $('.counter-value').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.text().replace(/,/g, ''), 10);

            if (isNaN(countTo)) return;

            $this.text('0');

            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 1000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum).toLocaleString());
                },
                complete: function() {
                    $this.text(this.countNum.toLocaleString());
                }
            });
        });
    }

    function initCharts() {
        if (typeof Chart === 'undefined') {
            setTimeout(initCharts, 1000);
            return;
        }

        try {
            const chartTextColor = getComputedStyle(document.documentElement).getPropertyValue('--text-color') || '#333';
            const chartGridColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color') || '#e9ecef';
            const borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color') || '#e9ecef';
            const cardBg = getComputedStyle(document.documentElement).getPropertyValue('--card-bg') || '#fff';
            const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color') || '#4361ee';
            const infoColor = getComputedStyle(document.documentElement).getPropertyValue('--info-color') || '#3498db';

            Chart.defaults.color = chartTextColor;
            Chart.defaults.borderColor = borderColor;
            Chart.defaults.font.size = 14;
            Chart.defaults.font.weight = '500';

        const revenueCtx = document.getElementById('revenue-chart');
        if (revenueCtx && typeof adminDashboardData !== 'undefined' && adminDashboardData.revenueData) {

            window.revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: adminDashboardData.revenueData.labels || [],
                    datasets: [{
                        label: 'รายได้ (USDT)',
                        data: adminDashboardData.revenueData.values || [],
                        borderColor: primaryColor,
                        backgroundColor: primaryColor + '20',
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: primaryColor,
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6,
                        pointHoverRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            backgroundColor: cardBg,
                            titleColor: chartTextColor,
                            bodyColor: chartTextColor,
                            borderColor: borderColor,
                            borderWidth: 1,
                            padding: 12,
                            cornerRadius: 8,
                            titleFont: {
                                size: 16,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 14
                            },
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toFixed(2) + ' USDT';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: chartGridColor,
                                drawBorder: true,
                                drawOnChartArea: true,
                                drawTicks: true
                            },
                            ticks: {
                                color: chartTextColor,
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                padding: 8
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: chartGridColor,
                                drawBorder: true,
                                drawOnChartArea: true,
                                drawTicks: true
                            },
                            ticks: {
                                color: chartTextColor,
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                padding: 8,
                                callback: function(value) {
                                    return value.toFixed(2) + ' USDT';
                                }
                            }
                        }
                    }
                }
            });
        } else {
            console.warn('Revenue chart element not found or data not available');
        }

        const clicksCtx = document.getElementById('clicks-chart');
        if (clicksCtx && typeof adminDashboardData !== 'undefined' && adminDashboardData.clicksData) {

            window.clicksChart = new Chart(clicksCtx, {
                type: 'bar',
                data: {
                    labels: adminDashboardData.clicksData.labels || [],
                    datasets: [{
                        label: 'คลิก',
                        data: adminDashboardData.clicksData.values || [],
                        backgroundColor: infoColor,
                        borderRadius: 6,
                        borderWidth: 0,
                        hoverBackgroundColor: infoColor + 'CC'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: cardBg,
                            titleColor: chartTextColor,
                            bodyColor: chartTextColor,
                            borderColor: borderColor,
                            borderWidth: 1,
                            padding: 12,
                            cornerRadius: 8,
                            titleFont: {
                                size: 16,
                                weight: 'bold'
                            },
                            bodyFont: {
                                size: 14
                            },
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: chartGridColor,
                                drawBorder: true,
                                drawOnChartArea: true,
                                drawTicks: true
                            },
                            ticks: {
                                color: chartTextColor,
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                padding: 8
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: chartGridColor,
                                drawBorder: true,
                                drawOnChartArea: true,
                                drawTicks: true
                            },
                            ticks: {
                                color: chartTextColor,
                                font: {
                                    size: 12,
                                    weight: '500'
                                },
                                padding: 8,
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        } else {
            console.warn('Clicks chart element not found or data not available');
        }

        } catch (error) {
            console.error('Error initializing charts:', error);
        }
    }

    function initClearStatistics() {
        if (typeof adminDashboardData === 'undefined' || typeof Swal === 'undefined') {
            return;
        }

        $(document).off('click', '#clear-all-stats, #clear-all-statistics, .clear-position-stats');

        $(document).on('click', '#clear-all-stats, #clear-all-statistics', function(e) {
            e.preventDefault();

            Swal.fire({
                title: 'คุณแน่ใจหรือไม่?',
                text: 'การดำเนินการนี้จะล้างประวัติการซื้อขาย สถิติการคลิกทั้งหมด และข้อมูลสถิติต่างๆ แต่จะไม่ลบตำแหน่งโฆษณาและจำนวนลูกค้า',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#e74c3c',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ใช่, ล้างสถานะทั้งหมด',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_all_statistics',
                            security: adminDashboardData.clearStatsNonce
                        },
                        beforeSend: function() {
                            Swal.fire({
                                title: 'กำลังล้างสถานะ...',
                                text: 'กรุณารอสักครู่ ระบบกำลังล้างข้อมูลทั้งหมด',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'สำเร็จ!',
                                    text: response.data.message || 'ล้างสถานะทั้งหมดเรียบร้อยแล้ว',
                                    icon: 'success',
                                    confirmButtonColor: '#3085d6'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'เกิดข้อผิดพลาด!',
                                    text: response.data.message || 'ไม่สามารถล้างสถานะได้',
                                    icon: 'error',
                                    confirmButtonColor: '#3085d6'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด!',
                                text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                                icon: 'error',
                                confirmButtonColor: '#3085d6'
                            });
                        }
                    });
                }
            });
        });

        // รีเฟรชสถิติลูกค้า
        $(document).on('click', '#refresh-customer-stats', function() {
            const button = $(this);
            const originalText = button.html();
            const loadingContainer = $('#customer-stats-loading');
            const statsContainer = $('#customer-stats-container');

            // แสดง loading
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังโหลด...');
            loadingContainer.show();
            statsContainer.hide();

            // จำลองการโหลดข้อมูล
            setTimeout(() => {
                // รีเฟรชหน้า
                location.reload();
            }, 1500);
        });

        $(document).on('click', '.clear-position-stats', function(e) {
            e.preventDefault();

            const position = $(this).data('position');
            if (!position) return;

            Swal.fire({
                title: 'คุณแน่ใจหรือไม่?',
                text: `การดำเนินการนี้จะล้างข้อมูลสถิติของตำแหน่ง ${position} และไม่สามารถเรียกคืนได้`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#e74c3c',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ใช่, ล้างข้อมูล',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'clear_position_statistics',
                            security: adminDashboardData.clearStatsNonce,
                            position: position
                        },
                        beforeSend: function() {
                            Swal.fire({
                                title: 'กำลังดำเนินการ...',
                                text: 'กรุณารอสักครู่',
                                allowOutsideClick: false,
                                didOpen: () => {
                                    Swal.showLoading();
                                }
                            });
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'สำเร็จ!',
                                    text: `ล้างข้อมูลสถิติของตำแหน่ง ${position} เรียบร้อยแล้ว`,
                                    icon: 'success',
                                    confirmButtonColor: '#3085d6'
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'เกิดข้อผิดพลาด!',
                                    text: response.data || 'ไม่สามารถล้างข้อมูลสถิติได้',
                                    icon: 'error',
                                    confirmButtonColor: '#3085d6'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด!',
                                text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                                icon: 'error',
                                confirmButtonColor: '#3085d6'
                            });
                        }
                    });
                }
            });
        });
    }

    function ActiveTimersManager() {
        let intervalId = null;
        const container = $('#active-timers-container');

        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        function renderTimers(timers) {
            container.empty();

            if (!timers || timers.length === 0) {
                container.html(`
                    <div class="empty-state">
                        <i class="fas fa-check-circle"></i>
                        <p>ไม่มีการจองที่ใช้งานอยู่ในขณะนี้</p>
                    </div>
                `);
                return;
            }
            
            const grid = $('<div class="timers-grid"></div>');
            
            timers.forEach(timer => {
                const connectionStatusClass = timer.is_connected ? 'connected' : 'disconnected';
                const connectionText = timer.is_connected ? 'เชื่อมต่ออยู่' : 'หลุดการเชื่อมต่อ';
                const card = $(`
                    <div class="timer-card ${connectionStatusClass}">
                        <div class="timer-card-header">
                            <i class="fas fa-user-circle"></i>
                            <span class="user-name">${timer.display_name} (${timer.user_login})</span>
                        </div>
                        <div class="timer-card-body">
                            <div class="timer-display">${formatTime(timer.remaining_seconds)}</div>
                            <div class="timer-positions">
                                <i class="fas fa-th-large"></i> ${timer.positions_text || 'N/A'}
                            </div>
                        </div>
                        <div class="timer-card-footer">
                            <div class="connection-status ${connectionStatusClass}">
                                <i class="fas fa-wifi"></i> ${connectionText}
                            </div>
                        </div>
                    </div>
                `);
                grid.append(card);
            });
            container.append(grid);
        }

        function fetchTimers() {
            if (!$('body').hasClass('ad-management-pro_page_amp-ad-management-dashboard')) {
                stop();
                return;
            }

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_all_active_timers'
                },
                success: function(response) {
                    if (response.success) {
                        renderTimers(response.data.timers);
                    } else {
                        container.html('<div class="empty-state error"><i class="fas fa-exclamation-triangle"></i><p>Could not load timers.</p></div>');
                    }
                },
                error: function() {
                     container.html('<div class="empty-state error"><i class="fas fa-exclamation-triangle"></i><p>Error connecting to server.</p></div>');
                }
            });
        }
        
        function start() {
            stop();
            console.log('Admin Timer Polling Started.');
            fetchTimers();
            intervalId = setInterval(fetchTimers, 5000);
        }

        function stop() {
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
                console.log('Admin Timer Polling Stopped.');
            }
        }
        
        return {
            start: start,
            stop: stop,
            refresh: fetchTimers
        };
    }

    $(document).ready(function() {
        const timersManager = ActiveTimersManager();

        if ($('body').hasClass('ad-management-pro_page_amp-ad-management-dashboard')) {
            timersManager.start();

            $('#refresh-timers-btn').on('click', function() {
                $(this).find('i').addClass('fa-spin');
                timersManager.refresh();
                setTimeout(() => $(this).find('i').removeClass('fa-spin'), 1000);
            });
        }

        setTimeout(function() {
            initCharts();
            initCounterAnimations();
            initClearStatistics();

            $('.dashboard-card').addClass('animate-fade-in');
            $('.dashboard-section').addClass('animate-slide-up');

            setTimeout(function() {
                $('.animate-fade-in').addClass('visible');
                $('.animate-slide-up').addClass('visible');
            }, 100);
        }, 500);
    });

})(jQuery);
