<?php
require_once AMP_PLUGIN_DIR . 'includes/core/roles.php';

if (!defined('WPINC')) {
    die;
}

function add_admin_menu() {
    if (current_user_can('manage_options')) {
        add_menu_page(
            'Ads Management',
            'Ads Management',
            'manage_options',
            'ad-management-dashboard',
            'display_admin_dashboard_page', 
            'dashicons-admin-generic',
            5
        );

        add_submenu_page(
            'ad-management-dashboard',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'ad-management-dashboard',
            'display_admin_dashboard_page'
        );

        add_submenu_page(
            'ad-management-dashboard',
            'Ad Positions',
            'Ad Positions',
            'manage_options',
            'ad-management',
            'display_ad_management_page'
        );

        add_submenu_page(
            'ad-management-dashboard',
            'User Management',
            'User Management',
            'manage_options',
            'customer-management',
            'display_customer_management_page'
        );

        add_submenu_page(
            'ad-management-dashboard',
            'System Settings',
            'System Settings',
            'manage_options',
            'general-settings',
            'display_general_settings_page'
        );



        add_submenu_page(
            'ad-management-dashboard',
            'Export/Import Positions',
            'Export/Import',
            'manage_options',
            'export-import-positions',
            'display_export_import_positions_page'
        );



    }
}
add_action('admin_menu', 'add_admin_menu');




