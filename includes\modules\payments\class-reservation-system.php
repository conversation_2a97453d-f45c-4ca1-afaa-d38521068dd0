<?php

if (!defined('WPINC')) {
    die;
}

class AMP_Reservation_System {
    private static $instance = null;
    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    private function __construct() {
        $this->init_hooks();
    }
    private function init_hooks() {
        add_action('wp_ajax_create_reservation', [$this, 'handle_create_reservation']);
        add_action('wp_ajax_cancel_reservation', [$this, 'handle_cancel_reservation']);
        add_action('wp_ajax_get_reservation_status', [$this, 'handle_get_reservation_status']);
        add_action('wp_ajax_sync_reservation_timer', [$this, 'handle_sync_reservation_timer']);
        add_action('wp_ajax_get_all_active_timers', [$this, 'handle_get_all_active_timers']);
        add_action('wp_ajax_nopriv_get_all_active_timers', [$this, 'handle_get_all_active_timers']);
        add_action('wp_ajax_cancel_user_timer_admin', [$this, 'handle_cancel_user_timer_admin']);
        add_action('wp_ajax_force_cleanup_disconnected_timers', [$this, 'handle_force_cleanup_disconnected_timers']);
        add_action('wp_ajax_get_all_positions_status', [$this, 'handle_get_all_positions_status']);
        add_action('wp_ajax_nopriv_get_all_positions_status', [$this, 'handle_get_all_positions_status']);    
        add_filter('cron_schedules', [$this, 'add_cron_intervals']);
        add_action('amp_check_disconnected_timers_hook', [$this, 'cleanup_disconnected_timers']);

        if (!wp_next_scheduled('amp_check_disconnected_timers_hook')) {
            wp_schedule_event(time(), 'every_minute', 'amp_check_disconnected_timers_hook');
        }
    }

    public function add_cron_intervals($schedules) {
        $schedules['every_minute'] = array(
            'interval' => 60,
            'display'  => esc_html__('Every Minute'),
        );
        return $schedules;
    }

    public function cleanup_disconnected_timers($force = false) {
        global $wpdb;


        $users_with_timers = $wpdb->get_results($wpdb->prepare(
            "SELECT user_id, meta_value FROM {$wpdb->usermeta} WHERE meta_key = %s",
            'amp_countdown_timer'
        ));

        if (empty($users_with_timers)) {
            return 0;
        }

        $current_time = time();
        $cleaned_count = 0;
        $timeout_threshold = 70;

        foreach ($users_with_timers as $user_meta) {
            $timer_data = maybe_unserialize($user_meta->meta_value);
            if (!is_array($timer_data) || !isset($timer_data['status']) || $timer_data['status'] !== 'active') {
                continue;
            }

            $last_heartbeat = get_user_meta($user_meta->user_id, 'amp_timer_heartbeat', true);
            $heartbeat_age = $last_heartbeat ? ($current_time - intval($last_heartbeat)) : 999;
            
            if ($heartbeat_age > $timeout_threshold) {
                $this->cancel_user_timer($user_meta->user_id, 'cron_cleanup_disconnected');
                $cleaned_count++;
            }
        }
        return $cleaned_count;
    }

    public function handle_create_reservation() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Create reservation security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }

            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            $user_id = get_current_user_id();
            $cart_items = get_user_meta($user_id, 'amp_cart', true);   
            if (!is_array($cart_items) || empty($cart_items)) {

                wp_send_json_error(['message' => 'Cart is empty - cannot create timer']);
            }
            $settings = $this->get_reservation_settings();
            if (!$settings['enabled']) {
                wp_send_json_error(['message' => 'Reservation system is disabled']);
            }
            $timeout_minutes = isset($_POST['timeout_minutes']) ? intval($_POST['timeout_minutes']) : $settings['timeout_minutes'];
            if ($timeout_minutes <= 0) {
                $timeout_minutes = $settings['timeout_minutes'];
            }
            $this->cancel_user_timer($user_id);           
            $timer_result = $this->create_user_countdown_timer($user_id, $timeout_minutes, $cart_items);
            if ($timer_result['success']) {
                wp_send_json_success([
                    'timer_id' => $timer_result['timer_id'],
                    'duration_seconds' => $timer_result['duration_seconds'],
                    'remaining_time' => $timer_result['remaining_seconds'],
                    'timeout_minutes' => $timeout_minutes,
                    'server_timestamp' => $timer_result['server_timestamp'],
                    'message' => 'Server countdown timer created successfully'
                ]);
            } else {
                wp_send_json_error([
                    'message' => $timer_result['message']
                ]);
            }
        } catch (Exception $e) {
            error_log('AMP: Error creating countdown timer: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    public function handle_cancel_reservation() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Cancel reservation security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }

            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            $user_id = get_current_user_id();

            $result = $this->cancel_user_timer($user_id, 'manual_cancel');

            if ($result) {
                wp_send_json_success([
                    'message' => 'Countdown timer cancelled successfully'
                ]);
            } else {
                wp_send_json_error(['message' => 'Failed to cancel reservation']);
            }

        } catch (Exception $e) {
            error_log('AMP: Error cancelling reservation: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    public function handle_get_reservation_status() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Get reservation status security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }

            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            $user_id = get_current_user_id();
            
            $timer_data = get_user_meta($user_id, 'amp_countdown_timer', true);

            if (empty($timer_data) || !is_array($timer_data) || !isset($timer_data['status'])) {
                wp_send_json_success(['status' => 'inactive', 'remaining_time' => 0]);
                return;
            }

            $current_time = time();
            $elapsed_seconds = $current_time - $timer_data['started_at'];
            $remaining_seconds = max(0, $timer_data['duration_seconds'] - $elapsed_seconds);

            if ($timer_data['status'] === 'active' && $remaining_seconds <= 0) {
                 $this->cancel_user_timer($user_id, 'expired_on_get_status');
                 wp_send_json_success(['status' => 'expired', 'message' => 'Timer expired.']);
                 return;
            }

            if ($timer_data['status'] !== 'active') {
                wp_send_json_success(['status' => $timer_data['status'], 'message' => 'Timer is not active.']);
                return;
            }

            wp_send_json_success([
                'status' => 'active',
                'remaining_time' => $remaining_seconds,
                'duration_seconds' => $timer_data['duration_seconds'],
                'started_at' => $timer_data['started_at'],
                'server_timestamp' => $current_time,
                'timer_id' => $user_id . '_' . $timer_data['started_at']
            ]);
        } catch (Exception $e) {
            error_log('AMP: Error getting countdown timer status: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    public function handle_sync_reservation_timer() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Sync reservation timer security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }
            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            $user_id = get_current_user_id();          
            update_user_meta($user_id, 'amp_timer_heartbeat', time());

            $timer_data = get_user_meta($user_id, 'amp_countdown_timer', true);

            if (empty($timer_data) || !is_array($timer_data) || !isset($timer_data['status'])) {
                $last_purchase_successful = get_user_meta($user_id, 'amp_last_purchase_successful', true);
                $last_purchase_date = get_user_meta($user_id, 'amp_last_purchase_date', true);
                $last_payment_status = get_user_meta($user_id, 'amp_last_payment_status', true);
                $last_payment_date = get_user_meta($user_id, 'amp_last_payment_date', true);

                if ($last_purchase_successful && $last_purchase_date && (time() - strtotime($last_purchase_date) < 300)) {
                    delete_user_meta($user_id, 'amp_last_purchase_successful');
                    wp_send_json_success(['status' => 'completed', 'message' => 'Payment completed']);
                    return;
                }

                if ($last_payment_status && $last_payment_date && (time() - strtotime($last_payment_date) < 300)) {
                    $valid_failed_statuses = ['cancelled', 'expired', 'error', 'mismatch'];
                    if (in_array($last_payment_status, $valid_failed_statuses)) {
                        delete_user_meta($user_id, 'amp_last_payment_status');
                        delete_user_meta($user_id, 'amp_last_payment_date');
                        wp_send_json_success([
                            'status' => $last_payment_status,
                            'message' => 'Payment status: ' . $last_payment_status
                        ]);
                        return;
                    }
                }

                wp_send_json_success(['status' => 'inactive', 'message' => 'No active timer found.']);
                return;
            }

            $current_time = time();
            $heartbeat_age = $current_time - (get_user_meta($user_id, 'amp_timer_heartbeat', true) ?: $current_time);
            $elapsed_seconds = $current_time - $timer_data['started_at'];
            $remaining_seconds = max(0, $timer_data['duration_seconds'] - $elapsed_seconds);

            if ($timer_data['status'] === 'active') {
                if ($remaining_seconds <= 0) {
                    $this->cancel_user_timer($user_id, 'expired_on_sync');
                    wp_send_json_success(['status' => 'expired', 'message' => 'Timer has expired.']);
                    return;
                }
                if ($heartbeat_age > 60) {
                    $this->cancel_user_timer($user_id, 'heartbeat_lost_on_sync');
                    wp_send_json_success(['status' => 'expired', 'message' => 'Connection lost.']);
                    return;
                }
            }
            
            if ($timer_data['status'] !== 'active') {
                 $this->cancel_user_timer($user_id, 'final_cleanup_non_active');
                 wp_send_json_success(['status' => $timer_data['status'], 'message' => 'Timer is no longer active.']);
                 return;
            }
            
            wp_send_json_success([
                'status' => 'active',
                'remaining_time' => $remaining_seconds,
                'duration_seconds' => $timer_data['duration_seconds'],
                'started_at' => $timer_data['started_at'],
                'server_timestamp' => $current_time,
                'timer_id' => $user_id . '_' . $timer_data['started_at']
            ]);

        } catch (Exception $e) {
            error_log('AMP: Error syncing countdown timer: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    private function get_reservation_settings() {
        global $wpdb;
        $global_table = $wpdb->prefix . 'ad_price_global_settings';
        try {
            $use_timer_row = $wpdb->get_row($wpdb->prepare(
                "SELECT setting_value FROM {$global_table} WHERE setting_name = %s",
                'use_reservation_timer'
            ));
            $timeout_row = $wpdb->get_row($wpdb->prepare(
                "SELECT setting_value FROM {$global_table} WHERE setting_name = %s",
                'reservation_timeout'
            ));

            return [
                'enabled' => $use_timer_row ? (bool)intval($use_timer_row->setting_value) : false,
                'timeout_minutes' => $timeout_row ? intval($timeout_row->setting_value) : 3
            ];
        } catch (Exception $e) {
            error_log('AMP: Error getting reservation settings: ' . $e->getMessage());
            return [
                'enabled' => false,
                'timeout_minutes' => 3
            ];
        }
    }

    public function handle_get_all_active_timers() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        try {
            global $wpdb;
            $active_timers = $wpdb->get_results(
                "SELECT um.user_id, um.meta_value, u.user_login, u.display_name
                 FROM {$wpdb->usermeta} um
                 JOIN {$wpdb->users} u ON um.user_id = u.ID
                 WHERE um.meta_key = 'amp_countdown_timer'"
            );
            $current_time = time();
            $timer_list = [];
            $cleaned_count = 0;

            foreach ($active_timers as $timer_row) {
                $timer_data = maybe_unserialize($timer_row->meta_value);
                if (!is_array($timer_data) || !isset($timer_data['status']) || $timer_data['status'] !== 'active') {
                    continue;
                }

                $last_heartbeat = get_user_meta($timer_row->user_id, 'amp_timer_heartbeat', true);
                $heartbeat_age = $last_heartbeat ? ($current_time - intval($last_heartbeat)) : 999;
                
                $elapsed_seconds = $current_time - $timer_data['started_at'];
                $remaining_seconds = max(0, $timer_data['duration_seconds'] - $elapsed_seconds);

                if ($heartbeat_age > 60) {

                    delete_user_meta($timer_row->user_id, 'amp_countdown_timer');
                    delete_user_meta($timer_row->user_id, 'amp_timer_heartbeat');
                    $cleaned_count++;
                    continue; 
                }
                
                if ($remaining_seconds <= 0) {

                    delete_user_meta($timer_row->user_id, 'amp_countdown_timer');
                    delete_user_meta($timer_row->user_id, 'amp_timer_heartbeat');
                    $cleaned_count++;
                    continue;
                }
                
                $is_connected = true; 
                $position_names = [];
                if (isset($timer_data['cart_data']) && is_array($timer_data['cart_data'])) {
                    foreach ($timer_data['cart_data'] as $cart_item) {
                        if (isset($cart_item['position'])) {
                            $position_names[] = $cart_item['position'];
                        } elseif (isset($cart_item['position_name'])) {
                            $position_names[] = $cart_item['position_name'];
                        } elseif (isset($cart_item['ad_position'])) {
                            $position_names[] = $cart_item['ad_position'];
                        }
                    }
                }
                              
                $timer_list[] = [
                    'user_id' => $timer_row->user_id,
                    'user_login' => $timer_row->user_login,
                    'display_name' => $timer_row->display_name,
                    'status' => $timer_data['status'],
                    'remaining_seconds' => $remaining_seconds,
                    'duration_seconds' => $timer_data['duration_seconds'],
                    'started_at' => $timer_data['started_at'],
                    'is_connected' => $is_connected,
                    'heartbeat_age' => $heartbeat_age,
                    'cart_items' => count($timer_data['cart_data'] ?? []),
                    'position_names' => $position_names,
                    'positions_text' => implode(', ', $position_names),
                    'created_time' => date('Y-m-d H:i:s', $timer_data['started_at'])
                ];
            }

            wp_send_json_success(['timers' => $timer_list]);
        } catch (Exception $e) {
            error_log('AMP: Error getting active timers: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    public function handle_cancel_user_timer_admin() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
        try {
            $target_user_id = intval($_POST['user_id'] ?? 0);          
            if (!$target_user_id) {
                wp_send_json_error(['message' => 'Invalid user ID']);
            }
            delete_user_meta($target_user_id, 'amp_countdown_timer');
            delete_user_meta($target_user_id, 'amp_timer_heartbeat');

            wp_send_json_success(['message' => 'Timer cancelled and removed successfully']);
        } catch (Exception $e) {
            error_log('AMP: Error cancelling user timer: ' . $e->getMessage());
            wp_send_json_error(['message' => 'System error occurred']);
        }
    }

    public function handle_force_cleanup_disconnected_timers() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied.']);
            return;
        }
        
        $cleaned_count = $this->cleanup_disconnected_timers(true);
        
        wp_send_json_success([
            'message' => "Force cleanup completed. Cleaned up {$cleaned_count} disconnected timers.",
            'cleaned_count' => $cleaned_count
        ]);
    }

    private function create_user_countdown_timer($user_id, $timeout_minutes, $cart_items) {
        $current_timestamp = time();
        $duration_seconds = $timeout_minutes * 60;
        delete_user_meta($user_id, 'amp_countdown_timer');
        $timer_data = [
            'duration_seconds' => $duration_seconds,
            'started_at' => $current_timestamp,
            'status' => 'active',
            'cart_data' => $cart_items
        ];

        $result = update_user_meta($user_id, 'amp_countdown_timer', $timer_data);

        update_user_meta($user_id, 'amp_timer_heartbeat', $current_timestamp);

        $cache_manager = \AMP_Cache_Manager::instance();
        $cache_manager->clear_group('position_data');

            if ($result) {
            return [
                'success' => true,
                'timer_id' => $user_id . '_' . $current_timestamp,
                'duration_seconds' => $duration_seconds,
                'remaining_seconds' => $duration_seconds,
                'server_timestamp' => $current_timestamp
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to create countdown timer'
            ];
        }
    }
    
    private function get_user_countdown_timer($user_id) {
        if (empty($user_id)) {
            return false;
        }
        return get_user_meta($user_id, 'amp_countdown_timer', true);
    }
    
    public function cancel_user_timer($user_id, $reason = 'unknown') {
        $timer_data = get_user_meta($user_id, 'amp_countdown_timer', true);
        if ($timer_data && is_array($timer_data)) {
            delete_user_meta($user_id, 'amp_countdown_timer');
            delete_user_meta($user_id, 'amp_timer_heartbeat');


            $cache_manager = \AMP_Cache_Manager::instance();
            $cache_manager->clear_group('position_data');

            return true;
        }
        delete_user_meta($user_id, 'amp_countdown_timer');
        delete_user_meta($user_id, 'amp_timer_heartbeat');


        $cache_manager = \AMP_Cache_Manager::instance();
        $cache_manager->clear_group('position_data');

        return true;
    }

    public function handle_get_all_positions_status() {
        try {
            $cache_manager = \AMP_Cache_Manager::instance();
            $cache_manager->clear_group('position_data');

            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
            if (!$position_manager) {
                wp_send_json_error(['message' => 'Failed to initialize Position Manager.']);
                return;
            }

            $all_system_positions = $position_manager->get_positions(['limit' => 1000, 'status' => 'any']);
            if (empty($all_system_positions)) {
                wp_send_json_success([]);
                return;
            }

            $current_user_id = get_current_user_id();
            $reserved_positions_data = $this->get_all_reserved_positions();
            $statuses = [];

            foreach ($all_system_positions as $position) {
                $position_name = $position->name;
                $status_obj = [
                    'inactive' => false,
                    'reserved_by_user' => false,
                    'reserved_by_other' => false,
                    'owned_by_current_user' => false,
                    'is_expired' => false,
                    'owned_by_other' => false,
                    'can_add_to_cart' => false,
                    'expiring_soon' => false,
                    'days_remaining' => null,
                    'reason' => 'unknown'
                ];

                $ownership_state = $position_manager->get_position_ownership_state($position_name, $current_user_id);
                $visibility_state = $position_manager->get_position_visibility_state($position_name, $current_user_id);
                
                $is_owned = $ownership_state['is_owned'];
                $is_expired = $ownership_state['is_expired'];
                $is_owned_by_current_user = $ownership_state['is_owned_by_current_user'];

                $status_obj['reason'] = $visibility_state['reason'];
                $status_obj['can_add_to_cart'] = $visibility_state['is_purchasable'];
                $status_obj['days_remaining'] = $visibility_state['days_remaining'];
                
                if ($visibility_state['reason'] === 'expiring_soon') {
                    $status_obj['expiring_soon'] = true;
                }

                if ($position->status !== 'active') {
                    $status_obj['inactive'] = true;
                } elseif (array_key_exists($position_name, $reserved_positions_data)) {
                    if ($current_user_id > 0 && $reserved_positions_data[$position_name]['user_id'] == $current_user_id) {
                        $status_obj['reserved_by_user'] = true;
                    } else {
                        $status_obj['reserved_by_other'] = true;
                    }
                } elseif ($is_owned) {
                    if ($is_owned_by_current_user) {
                        $status_obj['owned_by_current_user'] = true;
                        $status_obj['is_expired'] = $is_expired;
                    } else {
                        $status_obj['owned_by_other'] = true;
                    }
                }

                $statuses[$position_name] = $status_obj;
            }

            wp_send_json_success($statuses);

        } catch (\Exception $e) {
            error_log('AMP: Error getting all positions status: ' . $e->getMessage());
            wp_send_json_error(['message' => 'A system error occurred while fetching position statuses.']);
        }
    }

    private function get_all_reserved_positions() {
        global $wpdb;
        $reserved_positions = [];
        $users_with_timers = $wpdb->get_results(
            "SELECT user_id, meta_value
             FROM {$wpdb->usermeta}
             WHERE meta_key = 'amp_countdown_timer'"
        );

        $current_time = time();
        foreach ($users_with_timers as $user_meta) {
            $timer_data = maybe_unserialize($user_meta->meta_value);

            if (!is_array($timer_data) || !isset($timer_data['status']) || $timer_data['status'] !== 'active') {
                continue;
            }

            $elapsed_seconds = $current_time - $timer_data['started_at'];

            if ($elapsed_seconds >= $timer_data['duration_seconds']) {
                continue;
            }

            if (isset($timer_data['cart_data']) && is_array($timer_data['cart_data'])) {
                foreach ($timer_data['cart_data'] as $cart_item) {
                    $position_name = null;
                    if (isset($cart_item['position'])) {
                        $position_name = $cart_item['position'];
                    } elseif (isset($cart_item['position_name'])) {
                        $position_name = $cart_item['position_name'];
                    } elseif (isset($cart_item['ad_position'])) {
                        $position_name = $cart_item['ad_position'];
                    }
                    if ($position_name) {
                        $reserved_positions[$position_name] = [
                            'user_id' => $user_meta->user_id,
                            'remaining_seconds' => $timer_data['duration_seconds'] - $elapsed_seconds
                        ];
                    }
                }
            }
        }
        return $reserved_positions;
    }
}
