<?php
namespace AdManagementPro\Modules\Shared;

use AdManagementPro\Core\Database;
use AdManagementPro\Core\Cache;

if (!defined('WPINC')) {
    die;
}

class UserManager {
    private $db;
    private $cache;
    private $security;
    private $context;
    public function __construct($context = 'public') {
        $this->context = $context;
        $this->db = \AdManagementPro\Core\Database::instance();
        $this->cache = \AdManagementPro\Core\Cache::instance();
        $this->security = null;

        $this->init_hooks();
    }
    public function set_security_manager($security_manager) {
        $this->security = $security_manager;
    }
    private function init_hooks() {
        if ($this->context === 'admin') {
            \add_action('show_user_profile', [$this, 'add_ad_positions_field']);
            \add_action('edit_user_profile', [$this, 'add_ad_positions_field']);
            \add_action('personal_options_update', [$this, 'save_ad_positions']);
            \add_action('edit_user_profile_update', [$this, 'save_ad_positions']);
        }
    }
    
    public function add_ad_positions_field($user) {
        if (!$this->can_manage_user_positions()) {
            return;
        }
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
        $positions = $position_manager->get_positions(['limit' => 1000]);
        $selected_positions = get_user_meta($user->ID, 'ad_positions', true);
        if (!is_array($selected_positions)) {
            $selected_positions = [];
        }
        
        echo '<h3>Ad Position Management</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th><label for="ad_positions">Assigned Ad Positions</label></th>';
        echo '<td>';
        
        if (!empty($positions)) {
            echo '<select id="ad_positions" name="ad_positions[]" multiple="multiple" style="width:350px;">';
            
            foreach ($positions as $position) {
                $selected = \in_array($position->name, $selected_positions) ? 'selected' : '';
                echo '<option value="' . \esc_attr($position->name) . '" ' . $selected . '>';
                echo \esc_html($position->name . ' (' . $position->width . 'x' . $position->height . ')');
                echo '</option>';
            }
            
            echo '</select>';
            echo '<p class="description">Select the ad positions this user can manage. Hold Ctrl/Cmd to select multiple positions.</p>';
        } else {
            echo '<p class="description">No ad positions available. Please create ad positions first.</p>';
        }
        
        echo '</td>';
        echo '</tr>';
        echo '</table>';
    }
    
    public function save_ad_positions($user_id) {
        if (!$this->can_manage_user_positions()) {
            return;
        }      
        if (!isset($_POST['_wpnonce']) || !\wp_verify_nonce($_POST['_wpnonce'], 'update-user_' . $user_id)) {
            return;
        }       
        $new_positions = [];
        if (isset($_POST['ad_positions']) && \is_array($_POST['ad_positions'])) {
            $new_positions = \array_map('sanitize_text_field', $_POST['ad_positions']);
        }
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
        $position_manager->assign_positions_to_user($user_id, $new_positions);
        

    }
        
    private function can_manage_user_positions() {
        return $this->context === 'admin' && \current_user_can('manage_options');
    }
    
    public function get_context_info() {
        return [
            'context' => $this->context,
            'can_manage_positions' => $this->can_manage_user_positions(),
            'current_user_id' => \get_current_user_id()
        ];
    }

    public function get_user_cart($user_id) {
        $cache_key = 'user_cart_' . $user_id;
        $cached_cart = $this->cache->get($cache_key, 'user_profiles');
        if (false !== $cached_cart) {
            return $cached_cart;
        }

        $cart = \get_user_meta($user_id, 'amp_cart', true);
        if (!\is_array($cart)) {
            $cart = [];
        }

        $this->cache->set($cache_key, $cart, 0, 'user_profiles');
        return $cart;
    }

    public function add_to_cart($user_id, $position_name, $duration) {
        $cart = $this->get_user_cart($user_id);
        $item_key = 'pos_' . $position_name;

        if (isset($cart[$item_key])) {
            return false;
        }

        $cart[$item_key] = [
            'position_name' => $position_name,
            'duration' => (int) $duration,
            'added_at' => \current_time('mysql')
        ];

        $result = \update_user_meta($user_id, 'amp_cart', $cart);
        if ($result) {
            $this->clear_user_cache($user_id);
        }
        return $result;
    }

    public function remove_from_cart($user_id, $position_name) {
        $cart = $this->get_user_cart($user_id);
        $item_key = 'pos_' . $position_name;

        if (isset($cart[$item_key])) {
            unset($cart[$item_key]);
            $result = \update_user_meta($user_id, 'amp_cart', $cart);
            if ($result) {
                $this->clear_user_cache($user_id);
            }
            return $result;
        }
        return true;
    }

    public function clear_cart($user_id) {
        $result = \update_user_meta($user_id, 'amp_cart', []);
        if ($result) {
            $this->clear_user_cache($user_id);
        }
        return $result;
    }

    public function get_cart_count($user_id) {
        $cart = $this->get_user_cart($user_id);
        return count($cart);
    }

    public function is_in_cart($user_id, $position_name) {
        $cart = $this->get_user_cart($user_id);
        $item_key = 'pos_' . $position_name;
        return isset($cart[$item_key]);
    }

    private function clear_user_cache($user_id) {
        if ($this->cache) {
            $this->cache->delete('user_cart_' . $user_id, 'user_profiles');
        }
    }
}