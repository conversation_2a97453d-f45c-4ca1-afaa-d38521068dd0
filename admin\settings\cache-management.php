<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';

function display_cache_management_content() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    if (!class_exists('\AMP_Cache_Manager')) {
        echo '<div class="notice notice-error"><p>❌ Cache class not found. Please check your installation.</p></div>';
        return;
    }

    try {
        $cache_manager = \AMP_Cache_Manager::instance();
        $cache_info = $cache_manager->get_cache_info();
        $cache_stats = $cache_manager->get_cache_stats();
        $cache_nonce = wp_create_nonce('amp_cache_management');

        if (!is_array($cache_info) || !isset($cache_info['enabled'])) {
            throw new Exception('Invalid cache information');
        }

    } catch (Exception $e) {
        echo '<div class="notice notice-error"><p>❌ Error initializing cache system: ' . esc_html($e->getMessage()) . '</p></div>';
        return;
    }
    ?>
    <div class="cache-management-container">
        <div class="cache-master-control">
            <div class="cache-status-header">
                <div class="status-info">
                    <h3>🚀 ระบบแคชข้อมูลหลัก</h3>
                    <p class="status-description">จัดการระบบแคชข้อมูลเพื่อเพิ่มประสิทธิภาพของเว็บไซต์</p>
                </div>
                <div class="master-toggle">
                    <label class="toggle-switch">
                        <input type="checkbox" id="master-cache-toggle" <?php echo $cache_info['enabled'] ? 'checked' : ''; ?>>
                        <span class="toggle-slider"></span>
                    </label>
                    <span class="toggle-label"><?php echo $cache_info['enabled'] ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?></span>
                </div>
            </div>
        </div>

        <div class="cache-backend-section">
             <div class="section-header">
                <h3>🔌 Cache Backend</h3>
                <p class="section-description">เทคโนโลยีที่ใช้ในการจัดเก็บแคชของ WordPress</p>
            </div>
            <div class="backend-status-card <?php echo $cache_info['dropin_present'] ? 'active' : 'inactive'; ?>">
                <div class="backend-icon">
                    <?php echo $cache_info['dropin_present'] ? '🚀' : '🐌'; ?>
                </div>
                <div class="backend-info">
                    <h4><?php echo esc_html($cache_info['active_backend']); ?></h4>
                    <p>
                        <?php if ($cache_info['dropin_present']): ?>
                            ✅ ตรวจพบไฟล์ <code>object-cache.php</code> - ระบบกำลังใช้ <strong>Persistent Cache</strong> (Redis/Memcached)
                        <?php elseif ($cache_info['disabled_dropin_present']): ?>
                            ⚠️ ตรวจพบ <code>object-cache.php.disabled</code> แต่ถูกปิดใช้งานอยู่ - สามารถเปิดใช้งานได้
                        <?php else: ?>
                            ℹ️ ไม่พบ <code>object-cache.php</code> - ระบบกำลังใช้ <strong>Database Cache</strong> (Non-persistent)
                        <?php endif; ?>
                    </p>
                </div>
                <div class="backend-toggle">
                    <button id="toggle-backend-btn"
                            class="button <?php echo $cache_info['dropin_present'] ? 'button-secondary' : 'button-primary'; ?>"
                            data-action="<?php echo $cache_info['dropin_present'] ? 'disable' : 'enable'; ?>">
                        <?php if ($cache_info['dropin_present']): ?>
                            🔴 ปิดใช้งาน Persistent Cache
                        <?php else: ?>
                            🚀 เปิดใช้งาน Persistent Cache
                        <?php endif; ?>
                    </button>
                </div>
            </div>
        </div>

        <div class="cache-backend-section">
             <div class="section-header">
                <h3>⚙️ ตั้งค่าแคชหลัก</h3>
                <p class="section-description">กำหนดระยะเวลาการเก็บแคชเริ่มต้นสำหรับทั้งระบบ</p>
            </div>
            <div class="backend-status-card" style="background-color: #f7f7f9; border-color: #e1e1e3;">
                <div class="backend-icon">⏱️</div>
                <div class="backend-info">
                    <h4 style="color: #333;">Default Cache Lifetime</h4>
                    <p style="color: #555;">ระยะเวลาที่แคชจะถูกเก็บไว้หากไม่มีการกำหนดค่าเฉพาะกลุ่ม (หน่วย: ชั่วโมง)</p>
                </div>
                <div class="expiration-controls">
                    <div class="input-group">
                        <input type="number" id="default-expiration-input"
                               value="<?php echo round($cache_info['default_expiration'] / 3600, 2); ?>"
                               min="0.1" step="0.1" placeholder="24">
                        <span class="input-unit">ชั่วโมง</span>
                    </div>
                    <button id="save-default-expiration-btn" class="button button-primary">
                        💾 บันทึกการตั้งค่า
                    </button>
                </div>
            </div>
        </div>

        <div class="cache-stats-section">
            <div class="section-header">
                <div class="header-content">
                    <div class="header-text">
                        <h3>📊 สถิติการใช้งาน</h3>
                        <p class="section-description">ข้อมูลการใช้งานแคชในระบบ (อัปเดตแบบเรียลไทม์)</p>
                    </div>
                    <button class="refresh-stats-btn" onclick="refreshCacheStats()" title="รีเฟรชสถิติ">
                        🔄 อัปเดต
                    </button>
                </div>
            </div>
        </div>

        <div class="cache-stats-grid" id="cache-stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <h4>Hit Rate</h4>
                    <?php
                    $total_operations = $cache_stats['hits'] + $cache_stats['misses'];
                    $hit_rate = $total_operations > 0 ? round(($cache_stats['hits'] / $total_operations) * 100, 2) : 0;
                    ?>
                    <span class="stat-value"><?php echo $hit_rate; ?>%</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-content">
                    <h4>Cache Hits</h4>
                    <span class="stat-value"><?php echo number_format($cache_stats['hits']); ?></span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">💾</div>
                <div class="stat-content">
                    <h4>Cache Sets</h4>
                    <span class="stat-value"><?php echo number_format($cache_stats['sets']); ?></span>
                </div>
            </div>
        </div>

        <div class="cache-groups-section">
            <div class="section-header">
                <h3>🗂️ กลุ่มแคชข้อมูล</h3>
                <p class="section-description">จัดการแคชแต่ละประเภทแยกกัน สามารถเปิด-ปิดได้อิสระ</p>
            </div>
            <div class="cache-groups-grid">
                <?php foreach ($cache_info['groups_with_status'] as $group => $group_data): ?>
                    <?php
                    $is_enabled = $group_data['enabled'] && $cache_info['enabled'];
                    $expiration = $group_data['expiration'];
                    ?>
                    <div class="cache-group-card <?php echo $is_enabled ? 'enabled' : 'disabled'; ?>">
                        <div class="group-header">
                            <div class="group-icon">
                                <?php
                                $icons = [
                                    'position_data' => '📍',
                                    'exchange_rates' => '💱',
                                    'user_dashboard' => '👤',
                                    'pricing_data' => '💰',
                                    'ga_analytics' => '📊',
                                    'cloudflare_zones' => '☁️',
                                    'user_profiles' => '👥',
                                    'payment_settings' => '💳'
                                ];
                                echo $icons[$group] ?? '📦';
                                ?>
                            </div>
                            <div class="group-info">
                                <h4><?php echo esc_html(ucwords(str_replace('_', ' ', $group))); ?></h4>
                                <span class="group-ttl"><?php echo round($expiration / 3600); ?> ชั่วโมง</span>
                                <span class="group-status <?php echo $is_enabled ? 'active' : 'inactive'; ?>">
                                    <?php echo $is_enabled ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="group-controls">
                            <label class="toggle-switch small">
                                <input type="checkbox" class="group-toggle" data-group="<?php echo esc_attr($group); ?>"
                                       <?php echo $is_enabled ? 'checked' : ''; ?>
                                       <?php echo !$cache_info['enabled'] ? 'disabled' : ''; ?>>
                                <span class="toggle-slider"></span>
                            </label>
                            <button class="clear-group-btn" data-group="<?php echo esc_attr($group); ?>" title="เคลียร์แคชกลุ่มนี้"
                                    <?php echo !$is_enabled ? 'disabled' : ''; ?>>
                                🗑️
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="realtime-data-section">
            <div class="section-header">
                <h3>⚡ ข้อมูล Real-time (ไม่ถูกแคช)</h3>
                <p class="section-description">กลุ่มข้อมูลเหล่านี้จะถูกดึงข้อมูลใหม่ทุกครั้ง ไม่มีการจัดเก็บในแคชเพื่อความสดใหม่สูงสุด</p>
            </div>
            <div class="realtime-tags-grid">
                <?php if (!empty($cache_info['realtime_data'])): ?>
                    <?php foreach ($cache_info['realtime_data'] as $item): ?>
                        <div class="realtime-tag">
                            <span class="tag-icon">
                                <?php
                                $realtime_icons = [
                                    'click_statistics' => '🖱️',
                                    'reservation_data' => '🗓️',
                                    'active_timers' => '⏳',
                                    'cart_data' => '🛒',
                                    'pending_payments' => '💳',
                                    'ga_analytics_realtime' => '📈'
                                ];
                                echo $realtime_icons[$item] ?? '📡';
                                ?>
                            </span>
                            <span class="tag-name"><?php echo esc_html(ucwords(str_replace('_', ' ', $item))); ?></span>
                            <span class="tag-status">LIVE</span>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>ไม่มีกลุ่มข้อมูล Real-time กำหนดไว้</p>
                <?php endif; ?>
            </div>
        </div>

        <div class="cache-actions-section">
            <div class="section-header">
                <h3>🛠️ การจัดการแคช</h3>
                <p class="section-description">เครื่องมือสำหรับทดสอบและจัดการระบบแคช</p>
            </div>
            <div class="action-buttons-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 28px; margin-top: 32px; width: 100%;">
                <button class="action-btn primary" onclick="runCacheTests()" style="display: flex; align-items: center; justify-content: flex-start; padding: 24px 28px; border-radius: 18px; font-size: 16px; font-weight: 600; cursor: pointer; border: none; position: relative; overflow: hidden; min-height: 80px; width: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
                    <div class="btn-content" style="display: flex; align-items: center; gap: 20px; width: 100%; z-index: 2; position: relative;">
                        <span class="btn-icon" style="font-size: 36px; flex-shrink: 0;">🧪</span>
                        <div class="btn-text-group" style="display: flex; flex-direction: column; align-items: flex-start; gap: 6px; flex: 1;">
                            <span class="btn-title" style="font-size: 18px; font-weight: 700; line-height: 1.2; margin: 0;">ทดสอบระบบ</span>
                            <span class="btn-subtitle" style="font-size: 14px; opacity: 0.85; line-height: 1.3; font-weight: 500; margin: 0;">ตรวจสอบการทำงาน</span>
                        </div>
                    </div>
                </button>
                <button class="action-btn danger" onclick="clearAllCache()" style="display: flex; align-items: center; justify-content: flex-start; padding: 24px 28px; border-radius: 18px; font-size: 16px; font-weight: 600; cursor: pointer; border: none; position: relative; overflow: hidden; min-height: 80px; width: 100%; background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; box-shadow: 0 10px 40px rgba(245, 101, 101, 0.3); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
                    <div class="btn-content" style="display: flex; align-items: center; gap: 20px; width: 100%; z-index: 2; position: relative;">
                        <span class="btn-icon" style="font-size: 36px; flex-shrink: 0;">🗑️</span>
                        <div class="btn-text-group" style="display: flex; flex-direction: column; align-items: flex-start; gap: 6px; flex: 1;">
                            <span class="btn-title" style="font-size: 18px; font-weight: 700; line-height: 1.2; margin: 0;">เคลียร์ทั้งหมด</span>
                            <span class="btn-subtitle" style="font-size: 14px; opacity: 0.85; line-height: 1.3; font-weight: 500; margin: 0;">ลบแคชทั้งระบบ</span>
                        </div>
                    </div>
                </button>
                <button class="action-btn secondary" onclick="refreshCacheInfo()" style="display: flex; align-items: center; justify-content: flex-start; padding: 24px 28px; border-radius: 18px; font-size: 16px; font-weight: 600; cursor: pointer; border: 2px solid #cbd5e0; position: relative; overflow: hidden; min-height: 80px; width: 100%; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); color: #475569; box-shadow: 0 10px 40px rgba(71, 85, 105, 0.15); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
                    <div class="btn-content" style="display: flex; align-items: center; gap: 20px; width: 100%; z-index: 2; position: relative;">
                        <span class="btn-icon" style="font-size: 36px; flex-shrink: 0;">🔄</span>
                        <div class="btn-text-group" style="display: flex; flex-direction: column; align-items: flex-start; gap: 6px; flex: 1;">
                            <span class="btn-title" style="font-size: 18px; font-weight: 700; line-height: 1.2; margin: 0; color: #475569;">รีเฟรชข้อมูล</span>
                            <span class="btn-subtitle" style="font-size: 14px; opacity: 0.85; line-height: 1.3; font-weight: 500; margin: 0; color: #475569;">โหลดข้อมูลใหม่</span>
                        </div>
                    </div>
                </button>
                <button class="action-btn info" onclick="showCacheInfo()" style="display: flex; align-items: center; justify-content: flex-start; padding: 24px 28px; border-radius: 18px; font-size: 16px; font-weight: 600; cursor: pointer; border: none; position: relative; overflow: hidden; min-height: 80px; width: 100%; background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%); color: white; box-shadow: 0 10px 40px rgba(66, 153, 225, 0.3); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);">
                    <div class="btn-content" style="display: flex; align-items: center; gap: 20px; width: 100%; z-index: 2; position: relative;">
                        <span class="btn-icon" style="font-size: 36px; flex-shrink: 0;">📊</span>
                        <div class="btn-text-group" style="display: flex; flex-direction: column; align-items: flex-start; gap: 6px; flex: 1;">
                            <span class="btn-title" style="font-size: 18px; font-weight: 700; line-height: 1.2; margin: 0;">ข้อมูลระบบ</span>
                            <span class="btn-subtitle" style="font-size: 14px; opacity: 0.85; line-height: 1.3; font-weight: 500; margin: 0;">ดูรายละเอียด</span>
                        </div>
                    </div>
                </button>
            </div>
        </div>

        <div id="test-results" class="test-results-section" style="display: none;">
            <div class="section-header">
                <h3>📋 ผลการทดสอบระบบแคช</h3>
            </div>
            <div id="test-summary" class="test-summary"></div>
            <div id="test-details" class="test-details"></div>
        </div>
    </div>

    <style>
    .cache-management-container {
        max-width: 100%;
        margin: 0;
    }

    .cache-master-control {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .cache-status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;
    }

    .status-info h3 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
    }

    .status-description {
        margin: 0;
        opacity: 0.9;
        font-size: 14px;
    }

    .master-toggle {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .toggle-label {
        font-weight: 500;
        font-size: 16px;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .toggle-switch.small {
        width: 44px;
        height: 24px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255,255,255,0.3);
        transition: .4s;
        border-radius: 34px;
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .toggle-switch.small .toggle-slider:before {
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
    }

    input:checked + .toggle-slider {
        background-color: #4CAF50;
    }

    input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }

    .toggle-switch.small input:checked + .toggle-slider:before {
        transform: translateX(20px);
    }

    .cache-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }

    .stat-icon {
        font-size: 32px;
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 12px;
    }

    .stat-content h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 700;
        color: #1f2937;
    }

    .section-header {
        margin-bottom: 20px;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
    }

    .header-text {
        flex: 1;
    }

    .refresh-stats-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        padding: 10px 18px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        white-space: nowrap;
        margin-top: 4px;
    }

    .refresh-stats-btn:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    }

    .refresh-stats-btn:active {
        transform: translateY(0);
    }

    .section-header h3 {
        margin: 0 0 8px 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-description {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
        line-height: 1.5;
        background: #f8fafc;
        padding: 8px 12px;
        border-radius: 6px;
        border-left: 3px solid #667eea;
    }

    .section-description {
        margin: 0;
        color: #6b7280;
        font-size: 14px;
    }

    .cache-groups-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
    }

    .cache-groups-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 16px;
    }

    .cache-group-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
        transition: all 0.2s ease;
    }

    .cache-group-card:hover {
        border-color: #cbd5e0;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    }

    .cache-group-card.disabled {
        opacity: 0.6;
        background: #f1f5f9;
    }

    .cache-group-card.disabled .group-icon {
        background: #e2e8f0;
        color: #64748b;
    }

    .group-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
    }

    .group-icon {
        font-size: 24px;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .group-info h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
    }

    .group-ttl {
        font-size: 12px;
        color: #6b7280;
        background: #e2e8f0;
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 8px;
    }

    .group-status {
        font-size: 11px;
        font-weight: 500;
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
    }

    .group-status.active {
        background: #dcfce7;
        color: #166534;
    }

    .group-status.inactive {
        background: #fee2e2;
        color: #dc2626;
    }

    .group-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
    }

    .clear-group-btn {
        background: #fee2e2;
        border: 1px solid #fecaca;
        color: #dc2626;
        padding: 6px 10px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .clear-group-btn:hover {
        background: #fecaca;
        border-color: #f87171;
    }

    .clear-group-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #f1f5f9;
        border-color: #e2e8f0;
        color: #94a3b8;
    }

    .toggle-switch input:disabled + .toggle-slider {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .realtime-data-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
    }

    .realtime-tags-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-top: 16px;
    }

    .realtime-tag {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        background: linear-gradient(135deg, #f0fdf4, #dcfce7);
        color: #15803d;
        padding: 12px 20px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        border: 1px solid #86efac;
        box-shadow: 0 2px 8px rgba(22, 163, 74, 0.1);
        transition: all 0.3s ease;
        cursor: default;
    }

    .realtime-tag:hover {
        transform: translateY(-2px) scale(1.03);
        box-shadow: 0 5px 15px rgba(22, 163, 74, 0.2);
    }

    .tag-icon {
        font-size: 20px;
    }

    .tag-name {
        line-height: 1;
    }

    .tag-status {
        background-color: #22c55e;
        color: white;
        font-size: 10px;
        font-weight: 700;
        padding: 3px 8px;
        border-radius: 10px;
        text-transform: uppercase;
        animation: pulse-live 2s infinite;
    }

    @keyframes pulse-live {
        0% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
        }
    }

    .cache-actions-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
    }

    .action-buttons-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
        gap: 28px !important;
        margin-top: 32px !important;
        width: 100% !important;
    }

    .action-btn {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        padding: 24px 28px !important;
        border-radius: 18px !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border: none !important;
        text-decoration: none !important;
        position: relative !important;
        overflow: hidden !important;
        box-shadow: 0 6px 24px rgba(0,0,0,0.12) !important;
        backdrop-filter: blur(10px) !important;
        transform: translateZ(0) !important;
        min-height: 80px !important;
        width: 100% !important;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .btn-content {
        display: flex !important;
        align-items: center !important;
        gap: 20px !important;
        width: 100% !important;
        z-index: 2 !important;
        position: relative !important;
    }

    .btn-text-group {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 6px !important;
        flex: 1 !important;
    }

    .btn-title {
        font-size: 18px !important;
        font-weight: 700 !important;
        line-height: 1.2 !important;
        letter-spacing: 0.5px !important;
        margin: 0 !important;
    }

    .btn-subtitle {
        font-size: 14px !important;
        opacity: 0.85 !important;
        line-height: 1.3 !important;
        font-weight: 500 !important;
        margin: 0 !important;
    }

    .btn-icon {
        font-size: 36px !important;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1)) !important;
        transition: transform 0.3s ease !important;
        flex-shrink: 0 !important;
    }

    .action-btn:hover .btn-icon {
        transform: scale(1.15) rotate(8deg) !important;
    }

    .action-btn.primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3) !important;
        border: 2px solid rgba(255,255,255,0.15) !important;
    }

    .action-btn.primary:hover {
        transform: translateY(-6px) scale(1.03) !important;
        box-shadow: 0 20px 60px rgba(102, 126, 234, 0.45) !important;
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
    }

    .action-btn.secondary {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
        color: #475569 !important;
        border: 2px solid #cbd5e0 !important;
        box-shadow: 0 10px 40px rgba(71, 85, 105, 0.15) !important;
    }

    .action-btn.secondary:hover {
        background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%) !important;
        border-color: #94a3b8 !important;
        transform: translateY(-5px) scale(1.03) !important;
        box-shadow: 0 16px 50px rgba(71, 85, 105, 0.2) !important;
    }

    .action-btn.danger {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%) !important;
        color: white !important;
        box-shadow: 0 10px 40px rgba(245, 101, 101, 0.3) !important;
        border: 2px solid rgba(255,255,255,0.15) !important;
    }

    .action-btn.danger:hover {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
        transform: translateY(-6px) scale(1.03) !important;
        box-shadow: 0 20px 60px rgba(245, 101, 101, 0.45) !important;
    }

    .action-btn.info {
        background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%) !important;
        color: white !important;
        box-shadow: 0 10px 40px rgba(66, 153, 225, 0.3) !important;
        border: 2px solid rgba(255,255,255,0.15) !important;
    }

    .action-btn.info:hover {
        background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%) !important;
        transform: translateY(-6px) scale(1.03) !important;
        box-shadow: 0 20px 60px rgba(66, 153, 225, 0.45) !important;
    }

    .action-btn:active {
        transform: translateY(-2px) scale(0.98) !important;
        transition: all 0.1s ease !important;
    }

    /* Override any conflicting styles */
    button.action-btn {
        display: flex !important;
        width: auto !important;
        min-width: 300px !important;
        height: auto !important;
        min-height: 80px !important;
        font-family: inherit !important;
        line-height: normal !important;
        text-align: left !important;
        vertical-align: top !important;
        box-sizing: border-box !important;
    }

    button.action-btn * {
        box-sizing: border-box !important;
    }

    /* Ensure proper spacing */
    .cache-actions-section .action-buttons-grid {
        margin: 32px 0 !important;
        padding: 0 !important;
    }

    /* Force visibility */
    .action-btn, .action-btn * {
        visibility: visible !important;
        opacity: 1 !important;
    }

    .test-results-section {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        border: 1px solid #e1e5e9;
    }

    .test-summary {
        background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
        border: 1px solid #81d4fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .test-summary h4 {
        margin: 0 0 12px 0;
        color: #0277bd;
        font-size: 18px;
    }

    .test-summary p {
        margin: 4px 0;
        color: #01579b;
    }

    .test-details {
        display: grid;
        gap: 8px;
    }

    .test-result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .test-result-item.pass {
        background: #dcfce7;
        border: 1px solid #bbf7d0;
        color: #166534;
    }

    .test-result-item.fail {
        background: #fee2e2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    .test-result-item:hover {
        transform: translateX(4px);
    }

    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .loading::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    @keyframes slideIn {
        from { transform: translateY(-10px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    .loading-state {
        opacity: 0.6;
        pointer-events: none;
        animation: pulse 1.5s infinite;
    }

    .success-state {
        animation: slideIn 0.3s ease-out;
    }

    .cache-group-card {
        animation: fadeIn 0.5s ease-out;
    }

    .cache-group-card:nth-child(1) { animation-delay: 0.1s; }
    .cache-group-card:nth-child(2) { animation-delay: 0.2s; }
    .cache-group-card:nth-child(3) { animation-delay: 0.3s; }
    .cache-group-card:nth-child(4) { animation-delay: 0.4s; }

    @media (max-width: 768px) {
        .cache-status-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .cache-stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .cache-groups-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons-grid {
            grid-template-columns: 1fr;
        }
    }

    .test-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 16px;
    }

    .test-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #e2e8f0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .test-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .test-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f1f5f9;
    }

    .test-card-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #334155;
    }

    .test-status {
        padding: 4px 10px;
        border-radius: 999px;
        font-size: 12px;
        font-weight: 700;
        text-transform: uppercase;
    }

    .test-status.pass {
        background-color: #dcfce7;
        color: #166534;
    }

    .test-status.fail {
        background-color: #fee2e2;
        color: #991b1b;
    }

    .test-description {
        font-size: 13px;
        color: #64748b;
        margin-bottom: 16px;
        line-height: 1.6;
    }

    .test-details-list {
        list-style: none;
        padding: 0;
        margin: 0;
        font-size: 13px;
    }

    .test-details-list li {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .test-details-list li:last-child {
        border-bottom: none;
    }

    .test-details-list .detail-label {
        color: #475569;
        font-weight: 500;
    }

    .test-details-list .detail-value {
        color: #1e293b;
        font-weight: 600;
        background: #f8fafc;
        padding: 2px 8px;
        border-radius: 6px;
    }

    .test-summary {
        display: flex;
        gap: 16px;
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 24px;
        border: 1px solid #e2e8f0;
        align-items: center;
    }

    .summary-chart {
        width: 80px;
        height: 80px;
        position: relative;
    }

    .summary-text {
        flex-grow: 1;
    }

    .summary-text h4 {
        margin: 0 0 8px 0;
        color: #1e293b;
    }

    .summary-stats {
        display: flex;
        gap: 24px;
        color: #475569;
    }
    
    .summary-stats span {
        font-size: 14px;
    }
    .summary-stats strong {
        color: #1e293b;
    }

    .cache-backend-section {
        margin-bottom: 24px;
    }

    .backend-status-card {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        border-radius: 12px;
        border: 1px solid;
    }

    .backend-status-card.active {
        background-color: #f0fdf4;
        border-color: #bbf7d0;
    }

    .backend-status-card.inactive {
        background-color: #fffbeb;
        border-color: #fde68a;
    }
    
    .backend-icon {
        font-size: 32px;
    }

    .backend-info h4 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
    }
    
    .backend-info h4 {
        color: #14532d;
    }
    .backend-status-card.inactive .backend-info h4 {
        color: #78350f;
    }

    .backend-info p {
        margin: 0;
        font-size: 14px;
    }
    .backend-status-card.active .backend-info p {
        color: #166534;
    }
    .backend-status-card.inactive .backend-info p {
        color: #854d0e;
    }

    .backend-status-indicator {
        margin-left: auto;
        padding: 6px 12px;
        border-radius: 99px;
        font-size: 12px;
        font-weight: 700;
        text-transform: uppercase;
    }
    
    .backend-status-card.active .backend-status-indicator {
        background-color: #22c55e;
        color: white;
    }
    
    .backend-status-card.inactive .backend-status-indicator {
        background-color: #f59e0b;
        color: white;
    }

    .backend-toggle {
        margin-left: auto;
    }

    .cache-master-control {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }

    .expiration-controls {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-left: auto;
    }

    .input-group {
        display: flex;
        align-items: center;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .input-group input {
        border: none !important;
        outline: none !important;
        padding: 10px 12px;
        width: 80px;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        box-shadow: none !important;
    }

    .input-group .input-unit {
        background: #f8f9fa;
        padding: 10px 12px;
        font-size: 12px;
        font-weight: 600;
        color: #6b7280;
        border-left: 1px solid #e5e7eb;
    }

    #save-default-expiration-btn {
        padding: 10px 16px !important;
        font-size: 13px !important;
        font-weight: 600 !important;
        border-radius: 8px !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
        transition: all 0.2s ease !important;
    }

    #save-default-expiration-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
    }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
    jQuery(document).ready(function($) {
        // Check if required WordPress variables exist
        if (typeof ajaxurl === 'undefined') {
            console.error('WordPress AJAX URL not found');
            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: 'ไม่พบ WordPress AJAX URL กรุณาโหลดหน้าใหม่',
                icon: 'error',
                confirmButtonColor: '#dc2626'
            });
            return;
        }
        $('#master-cache-toggle').on('change', function() {
            const isEnabled = $(this).is(':checked');
            toggleMasterCache(isEnabled);
        });

        $('.group-toggle').on('change', function() {
            const group = $(this).data('group');
            const isEnabled = $(this).is(':checked');
            toggleGroupCache(group, isEnabled);
        });

        $('.clear-group-btn').on('click', function() {
            const group = $(this).data('group');
            clearCacheGroup(group);
        });

        $('#save-default-expiration-btn').on('click', function() {
            const expirationHours = $('#default-expiration-input').val();
            saveDefaultExpiration(expirationHours);
        });

        // Auto-refresh disabled to save server resources
        // Users can manually refresh using the refresh button when needed
    });

    function toggleMasterCache(enable) {
        const toggleLabel = document.querySelector('.toggle-label');
        const masterToggle = document.getElementById('master-cache-toggle');
        const cacheSection = document.querySelector('.cache-management-container');

        // Add loading state
        if (cacheSection) {
            cacheSection.classList.add('loading-state');
        }

        // Disable toggle during operation
        if (masterToggle) {
            masterToggle.disabled = true;
        }

        Swal.fire({
            title: enable ? '🚀 เปิดใช้งานแคช' : '⏸️ ปิดใช้งานแคช',
            text: enable ? 'คุณต้องการเปิดใช้งานระบบแคชหรือไม่?' : 'คุณต้องการปิดใช้งานระบบแคชหรือไม่?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: enable ? '#667eea' : '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: enable ? 'เปิดใช้งาน' : 'ปิดใช้งาน',
            cancelButtonText: 'ยกเลิก',
            showLoaderOnConfirm: true,
            preConfirm: () => {
                return fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'amp_toggle_cache',
                        enable: enable ? '1' : '0',
                        security: '<?php echo $cache_nonce; ?>'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.data?.message || data.message || 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ');
                    }
                    return data;
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                const responseData = result.value.data || result.value;
                const enabled = responseData.enabled !== undefined ? responseData.enabled : enable;

                if (toggleLabel) {
                    toggleLabel.textContent = enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
                }

                Swal.fire({
                    title: 'สำเร็จ!',
                    text: responseData.message || (enabled ? 'เปิดใช้งานแคชแล้ว' : 'ปิดใช้งานแคชแล้ว'),
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    updateCacheStatus(enabled);
                }).finally(() => {
                    // Remove loading state and re-enable toggle
                    if (cacheSection) {
                        cacheSection.classList.remove('loading-state');
                        cacheSection.classList.add('success-state');
                        setTimeout(() => cacheSection.classList.remove('success-state'), 1000);
                    }
                    if (masterToggle) {
                        masterToggle.disabled = false;
                    }
                });
            } else if (result.isDismissed || result.dismiss) {
                // Revert toggle if cancelled
                const masterToggle = document.getElementById('master-cache-toggle');
                if (masterToggle) {
                    masterToggle.checked = !enable;
                    masterToggle.disabled = false;
                }
                // Remove loading state
                if (cacheSection) {
                    cacheSection.classList.remove('loading-state');
                }
            }
        }).catch((error) => {
            console.error('Master cache toggle error:', error);

            // Revert toggle on error
            const masterToggle = document.getElementById('master-cache-toggle');
            if (masterToggle) {
                masterToggle.checked = !enable;
                masterToggle.disabled = false;
            }

            // Remove loading state
            if (cacheSection) {
                cacheSection.classList.remove('loading-state');
            }

            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: error.message || 'ไม่สามารถเปลี่ยนสถานะแคชได้',
                icon: 'error',
                confirmButtonColor: '#dc2626'
            });
        });
    }

    function toggleGroupCache(group, enable) {
        const groupName = group.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        Swal.fire({
            title: enable ? '🚀 เปิดใช้งานแคชกลุ่ม' : '⏸️ ปิดใช้งานแคชกลุ่ม',
            text: `คุณต้องการ${enable ? 'เปิด' : 'ปิด'}ใช้งานแคช "${groupName}" หรือไม่?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: enable ? '#667eea' : '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: enable ? 'เปิดใช้งาน' : 'ปิดใช้งาน',
            cancelButtonText: 'ยกเลิก',
            showLoaderOnConfirm: true,
            preConfirm: () => {
                return fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'amp_toggle_cache_group',
                        group: group,
                        enable: enable ? '1' : '0',
                        security: '<?php echo $cache_nonce; ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.data.message || 'เกิดข้อผิดพลาด');
                    }
                    return data;
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed && result.value) {
                const responseData = result.value.data || result.value;

                Swal.fire({
                    title: 'สำเร็จ!',
                    text: responseData.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    // Update group card visual state
                    updateGroupCardStatus(group, responseData.enabled);
                });
            } else if (result.isDismissed || result.dismiss) {
                // Revert the toggle if cancelled
                const toggle = document.querySelector(`[data-group="${group}"]`);
                if (toggle) {
                    toggle.checked = !enable;
                }
            }
        }).catch((error) => {
            console.error('Group cache toggle error:', error);

            // Revert the toggle on error
            const toggle = document.querySelector(`[data-group="${group}"]`);
            if (toggle) {
                toggle.checked = !enable;
            }

            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: error.message || 'ไม่สามารถเปลี่ยนสถานะแคชกลุ่มได้',
                icon: 'error',
                confirmButtonColor: '#dc2626'
            });
        });
    }

    function clearCacheGroup(group) {
        const groupName = group.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

        Swal.fire({
            title: '🗑️ เคลียร์แคชกลุ่ม',
            text: `คุณต้องการเคลียร์แคชกลุ่ม "${groupName}" หรือไม่?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'เคลียร์แคช',
            cancelButtonText: 'ยกเลิก',
            showLoaderOnConfirm: true,
            preConfirm: () => {
                return fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'amp_clear_cache_group',
                        group: group,
                        security: '<?php echo $cache_nonce; ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.data.message || 'เกิดข้อผิดพลาด');
                    }
                    return data;
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: `เคลียร์แคชกลุ่ม "${groupName}" เรียบร้อยแล้ว`,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    refreshCacheInfo();
                });
            }
        }).catch((error) => {
            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: error.message,
                icon: 'error'
            });
        });
    }

    function clearAllCache() {
        Swal.fire({
            title: '🗑️ เคลียร์แคชทั้งหมด',
            text: 'คุณต้องการเคลียร์แคชทั้งหมดหรือไม่? การดำเนินการนี้จะลบข้อมูลแคชทั้งหมดในระบบ',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc2626',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'เคลียร์ทั้งหมด',
            cancelButtonText: 'ยกเลิก',
            showLoaderOnConfirm: true,
            preConfirm: () => {
                return fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'amp_clear_all_cache',
                        security: '<?php echo $cache_nonce; ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.data.message || 'เกิดข้อผิดพลาด');
                    }
                    return data;
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: 'เคลียร์แคชทั้งหมดเรียบร้อยแล้ว',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                }).then(() => {
                    location.reload();
                });
            }
        }).catch((error) => {
            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: error.message,
                icon: 'error'
            });
        });
    }

    function runCacheTests() {
        showLoader('กำลังทดสอบระบบแคช', 'กรุณารอสักครู่...');
        const security = '<?php echo esc_js($cache_nonce); ?>';
        
        fetch(ajaxurl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                action: 'amp_run_cache_tests',
                security: security
            })
        })
        .then(response => response.json())
        .then(data => {
            Swal.close();
            if (data.success) {
                displayTestResults(data.data);
                Swal.fire({
                    icon: 'success',
                    title: 'ทดสอบสำเร็จ',
                    text: `ผ่าน ${data.data.summary.passed} / ${data.data.summary.total} การทดสอบ`,
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                throw new Error(data.data.message || 'Unknown error occurred.');
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาด',
                text: error.message
            });
        });
    }

    function displayTestResults(data) {
        const resultsSection = document.getElementById('test-results');
        const summaryContainer = document.getElementById('test-summary');
        const detailsContainer = document.getElementById('test-details');

        // Display Summary
        summaryContainer.innerHTML = buildSummaryHtml(data.summary);
        renderSummaryChart(data.summary);

        // Display Details
        detailsContainer.innerHTML = buildDetailsHtml(data.results);
        
        resultsSection.style.display = 'block';
    }

    function buildSummaryHtml(summary) {
        return `
            <div class="summary-chart">
                <canvas id="summary-chart-canvas"></canvas>
            </div>
            <div class="summary-text">
                <h4>สรุปผลการทดสอบ</h4>
                <div class="summary-stats">
                    <span>การทดสอบทั้งหมด: <strong>${summary.total}</strong></span>
                    <span style="color: #16a34a;">ผ่าน: <strong>${summary.passed}</strong></span>
                    <span style="color: #dc2626;">ไม่ผ่าน: <strong>${summary.failed}</strong></span>
                    <span>อัตราความสำเร็จ: <strong>${summary.success_rate}%</strong></span>
                </div>
            </div>
        `;
    }

    function buildDetailsHtml(results) {
        let html = '<div class="test-details-grid">';
        results.forEach(result => {
            html += `
                <div class="test-card">
                    <div class="test-card-header">
                        <h5>${result.name}</h5>
                        <span class="test-status ${result.pass ? 'pass' : 'fail'}">
                            ${result.pass ? '✔ PASS' : '✖ FAIL'}
                        </span>
                    </div>
                    <p class="test-description">${result.description}</p>
                    <ul class="test-details-list">
                        ${Object.entries(result.details).map(([key, value]) => `
                            <li>
                                <span class="detail-label">${key}</span>
                                <span class="detail-value">${value}</span>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            `;
        });
        html += '</div>';
        return html;
    }

    function renderSummaryChart(summary) {
        const ctx = document.getElementById('summary-chart-canvas').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Passed', 'Failed'],
                datasets: [{
                    data: [summary.passed, summary.failed],
                    backgroundColor: ['#22c55e', '#ef4444'],
                    borderColor: ['#ffffff', '#ffffff'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '75%',
                plugins: {
                    legend: { display: false },
                    tooltip: { enabled: false }
                }
            }
        });
    }

    function updateCacheStatus(enabled) {
        const toggleLabel = document.querySelector('.toggle-label');
        if (toggleLabel) {
            toggleLabel.textContent = enabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
        }

        const masterToggle = document.getElementById('master-cache-toggle');
        if (masterToggle) {
            masterToggle.checked = enabled;
        }

        const groupToggles = document.querySelectorAll('.group-toggle');
        groupToggles.forEach(toggle => {
            toggle.disabled = !enabled;
            if (!enabled) {
                toggle.checked = false;
                // Update group card visual state
                const group = toggle.getAttribute('data-group');
                if (group) {
                    updateGroupCardStatus(group, false);
                }
            }
        });

        // Update group cards
        const groupCards = document.querySelectorAll('.cache-group-card');
        groupCards.forEach(card => {
            if (enabled) {
                card.classList.remove('disabled');
            } else {
                card.classList.add('disabled');
            }
        });

        console.log('Cache status updated:', enabled ? 'Enabled' : 'Disabled');
    }

    function updateGroupCardStatus(group, enabled) {
        const groupCard = document.querySelector(`[data-group="${group}"]`)?.closest('.cache-group-card');
        if (!groupCard) return;

        // Update card visual state
        if (enabled) {
            groupCard.classList.remove('disabled');
            groupCard.classList.add('enabled');
        } else {
            groupCard.classList.add('disabled');
            groupCard.classList.remove('enabled');
        }

        // Update status text
        const statusElement = groupCard.querySelector('.group-status');
        if (statusElement) {
            statusElement.textContent = enabled ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน';
            statusElement.className = `group-status ${enabled ? 'active' : 'inactive'}`;
        }

        // Update clear button
        const clearButton = groupCard.querySelector('.clear-group-btn');
        if (clearButton) {
            clearButton.disabled = !enabled;
        }

        console.log(`Group ${group} status updated:`, enabled ? 'Enabled' : 'Disabled');
    }

    function refreshCacheInfo() {
        Swal.fire({
            title: '🔄 รีเฟรชข้อมูล',
            text: 'กำลังโหลดข้อมูลใหม่...',
            icon: 'info',
            timer: 1000,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        }).then(() => {
            location.reload();
        });
    }

    function showCacheInfo() {
        const cacheInfo = <?php echo json_encode($cache_info); ?>;

        let infoHtml = `
            <div style="text-align: left; max-height: 450px; overflow-y: auto; padding-right: 15px;">
                <h4 style="margin-bottom: 16px; color: #1f2937;">📊 ข้อมูลระบบแคช</h4>

                <div style="margin-bottom: 16px;">
                    <strong>🔧 สถานะระบบ:</strong><br>
                    <span style="color: ${cacheInfo.enabled ? '#22c55e' : '#ef4444'};">
                        ${cacheInfo.enabled ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน'}
                    </span>
                </div>

                <div style="margin-bottom: 16px;">
                    <strong>⏱️ ระยะเวลาแคชเริ่มต้น:</strong><br>
                    <span style="color: #6366f1;">${cacheInfo.default_expiration} วินาที (${Math.round(cacheInfo.default_expiration / 3600)} ชั่วโมง)</span>
                </div>

                <div style="margin-bottom: 16px;">
                    <strong>💾 กลุ่มแคชที่จัดการ:</strong><br>`;

        if (cacheInfo.groups_with_status && typeof cacheInfo.groups_with_status === 'object') {
            Object.keys(cacheInfo.groups_with_status).forEach(group => {
                const groupData = cacheInfo.groups_with_status[group];
                const expiration = groupData.expiration;
                const isEnabled = groupData.enabled;
                const groupDisplayName = group.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                infoHtml += `
                    <div style="margin: 8px 0; padding: 8px; background: #f8fafc; border-radius: 6px; border-left: 3px solid ${isEnabled ? '#86efac' : '#fca5a5'};">
                        <strong>${groupDisplayName}:</strong> ${expiration}s
                        <span style="font-weight: 500; font-size: 12px; float: right; padding: 2px 8px; border-radius: 12px; background-color: ${isEnabled ? '#dcfce7' : '#fee2e2'}; color: ${isEnabled ? '#166534' : '#991b1b'};">
                            ${isEnabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน'}
                        </span>
                    </div>
                `;
            });
        }

        infoHtml += `</div><div style="margin-bottom: 16px;"><strong>⚡ ข้อมูล Real-time (ไม่แคช):</strong><br>`;

        if (cacheInfo.realtime_data && Array.isArray(cacheInfo.realtime_data)) {
            cacheInfo.realtime_data.forEach(item => {
                const itemDisplayName = item.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                infoHtml += `<span style="background: #fefce8; color: #854d0e; padding: 4px 10px; border-radius: 12px; margin: 4px 2px; display: inline-block; font-size: 12px; border: 1px solid #fde047;">${itemDisplayName}</span>`;
            });
        }

        infoHtml += `</div><div style="margin-bottom: 16px;">
                <strong>📈 สถิติการใช้งาน:</strong><br>
                <div style="background: #f0f9ff; padding: 12px; border-radius: 8px; margin-top: 8px; display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                    <div>🎯 Hits: <strong style="color: #22c55e;">${cacheInfo.stats.hits || 0}</strong></div>
                    <div>❌ Misses: <strong style="color: #ef4444;">${cacheInfo.stats.misses || 0}</strong></div>
                    <div>💾 Sets: <strong style="color: #3b82f6;">${cacheInfo.stats.sets || 0}</strong></div>
                    <div>🗑️ Deletes: <strong style="color: #f59e0b;">${cacheInfo.stats.deletes || 0}</strong></div>
                    <div style="grid-column: 1 / -1;">🧹 Clears: <strong style="color: #8b5cf6;">${cacheInfo.stats.clears || 0}</strong></div>
                </div>
            </div>
        </div>`;

        Swal.fire({
            title: 'ข้อมูลระบบแคช',
            html: infoHtml,
            icon: 'info',
            confirmButtonColor: '#667eea',
            confirmButtonText: 'ปิด',
            width: '600px'
        });
    }

    function showLoader(title, text) {
        Swal.fire({
            title: title,
            text: text,
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
    }

    function refreshCacheStats() {
        const refreshBtn = document.querySelector('.refresh-stats-btn');
        const statsGrid = document.getElementById('cache-stats-grid');

        // Add loading state
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '⏳ กำลังอัปเดต...';
        }

        if (statsGrid) {
            statsGrid.classList.add('loading-state');
        }

        if (typeof ajaxurl === 'undefined') {
            console.error('WordPress AJAX URL not found');
            resetRefreshButton();
            return;
        }

        fetch(ajaxurl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'amp_get_cache_stats',
                security: '<?php echo $cache_nonce; ?>'
            }),
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.data) {
                updateStatsDisplay(data.data);
                if (refreshBtn) {
                    refreshBtn.innerHTML = '✅ อัปเดตแล้ว';
                    setTimeout(() => {
                        refreshBtn.innerHTML = '🔄 อัปเดต';
                    }, 2000);
                }
            } else {
                const errorMsg = (data && data.data && data.data.message) || 'ไม่สามารถโหลดสถิติได้';
                throw new Error(errorMsg);
            }
        })
        .catch(error => {
            console.error('Stats refresh error:', error);

            let errorMessage = 'ไม่สามารถอัปเดตสถิติได้';
            if (error.message.includes('Failed to fetch')) {
                errorMessage = 'การเชื่อมต่อขัดข้อง กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ต';
            } else if (error.message.includes('HTTP')) {
                errorMessage = 'เซิร์ฟเวอร์ไม่ตอบสนอง: ' + error.message;
            } else {
                errorMessage += ': ' + error.message;
            }

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: errorMessage,
                    icon: 'error',
                    timer: 3000,
                    showConfirmButton: false
                });
            } else {
                alert(errorMessage);
            }
        })
        .finally(() => {
            resetRefreshButton();
        });
    }

    function resetRefreshButton() {
        const refreshBtn = document.querySelector('.refresh-stats-btn');
        const statsGrid = document.getElementById('cache-stats-grid');

        if (refreshBtn) {
            refreshBtn.disabled = false;
            if (refreshBtn.innerHTML.includes('⏳') || refreshBtn.innerHTML.includes('✅')) {
                refreshBtn.innerHTML = '🔄 อัปเดต';
            }
        }

        if (statsGrid) {
            statsGrid.classList.remove('loading-state');
        }
    }

    function updateStatsDisplay(stats) {
        // Update individual stat values with animation
        const elements = {
            'cache-hits': stats.hits,
            'cache-misses': stats.misses,
            'memory-items': stats.memory_cache_count,
            'hit-rate': stats.hit_rate + '%'
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                // Add update animation
                element.style.transform = 'scale(1.1)';
                element.style.color = '#059669';

                setTimeout(() => {
                    element.textContent = typeof value === 'number' ? value.toLocaleString() : value;
                    element.style.transform = 'scale(1)';
                    element.style.color = '';
                }, 150);
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        const toggleBtn = document.getElementById('toggle-backend-btn');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                const action = this.getAttribute('data-action');
                const confirmText = action === 'enable' 
                    ? 'คุณแน่ใจหรือไม่ว่าต้องการเปิดใช้งาน Persistent Cache? การดำเนินการนี้จะเขียนทับไฟล์ object-cache.php ที่มีอยู่ (ถ้ามี)'
                    : 'คุณแน่ใจหรือไม่ว่าต้องการปิดใช้งาน Persistent Cache? ระบบจะกลับไปใช้แคชฐานข้อมูลซึ่งอาจช้าลง';

                Swal.fire({
                    title: 'ยืนยันการดำเนินการ',
                    text: confirmText,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'ใช่, ดำเนินการเลย!',
                    cancelButtonText: 'ยกเลิก'
                }).then((result) => {
                    if (result.isConfirmed) {
                        toggleBackend(action);
                    }
                });
            });
        }
    });

    function toggleBackend(action) {
        showLoader('กำลังเปลี่ยนสถานะ', 'กรุณารอสักครู่...');
        const security = '<?php echo esc_js($cache_nonce); ?>';

        fetch(ajaxurl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                action: 'amp_toggle_object_cache_backend',
                security: security,
                backend_action: action
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: data.message,
                    icon: 'success'
                }).then(() => {
                    location.reload();
                });
            } else {
                throw new Error(data.message || 'เกิดข้อผิดพลาดที่ไม่รู้จัก');
            }
        })
        .catch(error => {
            Swal.fire({
                icon: 'error',
                title: 'เกิดข้อผิดพลาด',
                text: error.message
            });
        });
    }

    function saveDefaultExpiration(hours) {
        const hoursFloat = parseFloat(hours);
        if (isNaN(hoursFloat) || hoursFloat <= 0) {
            Swal.fire({
                title: 'ข้อมูลไม่ถูกต้อง',
                text: 'กรุณากรอกตัวเลขที่มากกว่า 0',
                icon: 'error',
                confirmButtonColor: '#dc2626'
            });
            return;
        }

        Swal.fire({
            title: 'บันทึกการตั้งค่า',
            text: `คุณต้องการตั้งค่าเวลาแคชหลักเป็น ${hoursFloat} ชั่วโมงใช่หรือไม่?`,
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#667eea',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'บันทึก',
            cancelButtonText: 'ยกเลิก',
            showLoaderOnConfirm: true,
            preConfirm: () => {
                return fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        action: 'amp_update_default_expiration',
                        expiration_hours: hoursFloat,
                        security: '<?php echo $cache_nonce; ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        throw new Error(data.data.message || 'เกิดข้อผิดพลาด');
                    }
                    return data;
                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire({
                    title: 'สำเร็จ!',
                    text: result.value.message || 'บันทึกค่าเรียบร้อยแล้ว',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });
            }
        }).catch((error) => {
            Swal.fire({
                title: 'เกิดข้อผิดพลาด!',
                text: error.message,
                icon: 'error'
            });
        });
    }
    </script>
    <?php
}
