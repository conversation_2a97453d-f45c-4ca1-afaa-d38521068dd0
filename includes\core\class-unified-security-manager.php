<?php
namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

class UnifiedSecurityManager {
    private static $instance = null;
    private $context = 'public';
    private $security_level = 'maximum';
    private $session_id = null;
    private $user_capabilities = [];
    private $access_log = [];
    private $rate_limits = [];
    private $position_manager = null;
    private $user_manager = null;
    private $current_nonce = null;
    private $is_admin_user = false;
    private $user_id = 0;

    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_security_context();
        $this->init_session_management();
        $this->init_rate_limiting();
        $this->init_managers();
        $this->register_hooks();
    }
    
    private function init_security_context() {
        $this->user_id = \get_current_user_id();
        $this->is_admin_user = $this->user_id && \current_user_can('manage_options');
        $this->context = $this->is_admin_user ? 'admin' : 'public';
        $this->session_id = $this->generate_secure_session_id();
        if ($this->user_id && !$this->is_admin_user) {
            $this->load_user_capabilities();
        }
        if (!$this->is_admin_user && $this->is_login_request()) {
            $this->log_access('context_init', [
                'context' => $this->context,
                'user_id' => $this->user_id,
                'ip' => \AMP_Utilities::get_client_ip(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        }
    }
    
    private function init_session_management() {
        if (!headers_sent()) {
            $this->set_security_headers();
        }
        \add_action('wp_login', [$this, 'on_user_login'], 10, 2);
        \add_action('wp_logout', [$this, 'on_user_logout']);
        \add_action('init', [$this, 'unified_session_check'], 1);
        \add_filter('auth_cookie_expiration', [$this, 'set_auth_cookie_expiration'], 10, 3);
        \add_action('wp_ajax_amp_check_session', [$this, 'check_session_ajax']);
        \add_action('wp_ajax_nopriv_amp_check_session', [$this, 'check_session_ajax']);
        \add_action('wp_ajax_amp_extend_session', [$this, 'extend_session_ajax']);
        \add_action('amp_cleanup_expired_sessions', [$this, 'cleanup_expired_sessions']);
    }
    
    private function init_rate_limiting() {
        if ($this->is_admin_user) {
            return;
        }
        $this->rate_limits = [
            'failed_attempts' => [
                'limit' => (int) get_option('amp_max_login_attempts', 5),
                'window' => (int) get_option('amp_login_lockout_duration', 5) * 60,
                'count' => 0,
                'reset' => time() + ((int) get_option('amp_login_lockout_duration', 5) * 60)
            ]
        ];
    }
    
    private function init_managers() {
        if (!class_exists('AMP_Cache_Manager')) {
            require_once AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';
        }
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-user-manager.php';
        $this->position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance($this->context);
        $this->user_manager = new \AdManagementPro\Modules\Shared\UserManager($this->context);
        if (method_exists($this->position_manager, 'set_security_manager')) {
            $this->position_manager->set_security_manager($this);
        }
        if (method_exists($this->user_manager, 'set_security_manager')) {
            $this->user_manager->set_security_manager($this);
        }
        if (!has_action('wp_head', [$this, 'inject_security_scripts'])) {
            \add_action('wp_head', [$this, 'conditional_inject_security_scripts'], 20);
        }
        if (!has_action('admin_head', [$this, 'inject_admin_security'])) {
            \add_action('admin_head', [$this, 'inject_admin_security'], 1);
        }
        if (!has_action('login_enqueue_scripts', [$this, 'inject_security_scripts'])) {
            add_action('login_enqueue_scripts', [$this, 'inject_security_scripts']);
        }
    }
    
    private function register_hooks() {
        if (!has_action('wp_ajax_nopriv_amp_security_check', [$this, 'public_security_check'])) {
            \add_action('wp_ajax_nopriv_amp_security_check', [$this, 'public_security_check']);
        }
        if (!has_filter('authenticate', [$this, 'enhanced_authentication'])) {
            \add_filter('authenticate', [$this, 'enhanced_authentication'], 30, 3);
        }
    }
    
    
    private function is_login_request() {
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $action = $_POST['action'] ?? '';
            $login_actions = ['amp_login', 'amp_register', 'amp_forgot_password', 'amp_reset_password', 'google_callback', 'verify_email'];
            return in_array($action, $login_actions);
        }
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($request_uri, '/login/') !== false || strpos($request_uri, 'wp-login.php') !== false ||
               strpos($request_uri, 'action=google_callback') !== false || strpos($request_uri, 'action=verify_email') !== false;
    }

    private function is_litespeed_cache_request() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $query_string = $_SERVER['QUERY_STRING'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (strpos($request_uri, '/wp-admin/admin-ajax.php') !== false) {
            if (strpos($query_string, 'action=async_litespeed') !== false) {
                return true;
            }
        }
        if (stripos($user_agent, 'LiteSpeed') !== false || 
            stripos($user_agent, 'lscache') !== false) {
            return true;
        }
        $litespeed_actions = [
            'async_litespeed',
            'litespeed_purge',
            'litespeed_cache',
            'lscache_purge',
            'lscache_refresh'
        ];
        foreach ($litespeed_actions as $action) {
            if (strpos($query_string, "action={$action}") !== false) {
                return true;
            }
        }
        return false;
    }

    private function is_authenticated_ajax_request() {
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $action = $_POST['action'] ?? '';
            $authenticated_actions = [
                'load_tab_content', 'add_to_cart', 'remove_cart_item', 'clear_cart', 'get_cart',
                'update_profile', 'update_password', 'upload_avatar', 'get_ad_stats', 'renew_ad',
                'update_exchange_rate', 'get_exchange_rate',
                'get_pricing_data', 'process_checkout', 'amp_add_to_cart',
                'sync_reservation_timer',
                'amp_get_cart', 'amp_get_cart_content', 'amp_remove_from_cart', 'amp_clear_cart',
                'clear_all_statistics', 'refresh_ga_data', 'add_new_user', 'delete_user', 'update_user',
                'get_user_data', 'update_user_settings', 'amp_create_tables', 'toggle_ad_status'
            ];
            return in_array($action, $authenticated_actions);
        }
        return false;
    }

    private function is_public_ajax_request() {
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $action = isset($_POST['action']) ? sanitize_text_field($_POST['action']) : '';
            $public_actions = [
                'get_discount_rates', 'amp_google_callback', 'amp_verify_email', 'amp_refresh_nonces',
                'amp_login', 'amp_register', 'amp_forgot_password', 'amp_reset_password',
                'amp_check_session', 'amp_extend_session', 'amp_google_login', 'amp_check_session_status', 'amp_check_login_status'
            ];
            return in_array($action, $public_actions);
        }
        return false;
    }

    private function is_file_edit_request() {
        if (!current_user_can('edit_files')) {
            return false;
        }
        $file_edit_actions = [
            'edit-theme-plugin-file',
            'update-file',
            'wp_ajax_edit-theme-plugin-file'
        ];
        $action = isset($_POST['action']) ? sanitize_text_field($_POST['action']) : '';
        if (in_array($action, $file_edit_actions)) {
            return true;
        }
        $page = isset($_GET['page']) ? sanitize_text_field($_GET['page']) : '';
        if (in_array($page, ['theme-editor.php', 'plugin-editor.php'])) {
            return true;
        }
        if (isset($_POST['newcontent']) || isset($_POST['file'])) {
            return true;
        }
        return false;
    }

    private function validate_request() {
        if ($this->is_litespeed_cache_request()) {
            return true;
        }
        if ($this->detect_malicious_patterns()) {
            return false;
        }
        if ($this->detect_sql_injection()) {
            return false;
        }
        if ($this->detect_xss_attempt()) {
            return false;
        }
        return true;
    }
    
    private function check_login_rate_limits() {
        $identifier = $this->get_rate_limit_identifier();
        $current_time = time();
        if (isset($this->rate_limits['failed_attempts'])) {
            $limit_config = $this->rate_limits['failed_attempts'];
            if (!$this->check_individual_rate_limit('failed_attempts', $identifier, $limit_config, $current_time)) {
                return false;
            }
        }
        return true;
    }

    private function get_rate_limit_identifier() {
        if (\is_user_logged_in()) {
            return 'user_' . \get_current_user_id();
        }
        return 'ip_' . \AMP_Utilities::get_client_ip();
    }

    private function check_individual_rate_limit($type, $identifier, $limit_config, $current_time) {
        $cache_key = "rate_limit_{$type}_{$identifier}";
        $cache_manager = \AMP_Cache_Manager::instance();
        $rate_data = $cache_manager->get($cache_key, 'rate_limits');
        if ($rate_data === false || $current_time > $rate_data['reset']) {
            $rate_data = [
                'count' => 0,
                'reset' => $current_time + $limit_config['window']
            ];
        }
        if ($rate_data['count'] >= $limit_config['limit']) {
            $this->log_security_event('rate_limit_exceeded', [
                'type' => $type,
                'identifier' => $identifier,
                'limit' => $limit_config['limit'],
                'window' => $limit_config['window'],
                'current_count' => $rate_data['count']
            ]);
            return false;
        }
        $rate_data['count']++;
        $cache_manager->set($cache_key, $rate_data, $limit_config['window'], 'rate_limits');
        return true;
    }
       
    public function get_position_manager() {
        if (!$this->validate_manager_access('position')) {
            return null;
        }
        return $this->position_manager;
    }
    
    public function get_user_manager() {
        if (!$this->validate_manager_access('user')) {
            return null;
        }
        return $this->user_manager;
    }
    
    private function validate_manager_access($manager_type) {
        if ($this->is_admin_user) {
            return true;
        }
        $required_permissions = [
            'position' => ['manage_options'],
            'user' => ['manage_options']
        ];
        if ($this->context === 'public') {
            $required_permissions[$manager_type] = ['read'];
        }
        foreach ($required_permissions[$manager_type] as $permission) {
            if (!\current_user_can($permission) && $permission !== 'read') {
                return false;
            }
        }
        return true;
    }
    
    public function validate_ajax_request($action, $nonce = null) {
        if ($this->is_admin_user) {
            return true;
        }
        $login_actions = ['amp_login', 'amp_register', 'amp_forgot_password', 'amp_reset_password', 'amp_google_login', 'amp_google_callback'];
        if (in_array($action, $login_actions)) {
            if (!$this->check_login_rate_limits()) {
                return false;
            }
        }
        if ($nonce && !\wp_verify_nonce($nonce, $action)) {
            $this->log_security_event('invalid_nonce', [
                'action' => $action,
                'provided_nonce' => substr($nonce, 0, 8) . '...'
            ]);
            return false;
        }
        if (!$this->validate_ajax_origin()) {
            return false;
        }
        if (in_array($action, $login_actions) || $this->context === 'admin') {
            $this->log_access('ajax_request', [
                'action' => $action,
                'context' => $this->context
            ]);
        }
        return true;
    }
    
    public function sanitize_data($data, $rules = []) {
        if (empty($rules)) {
            $rules = $this->get_default_sanitization_rules();
        }
        $sanitized = [];
        foreach ($data as $key => $value) {
            $rule = $rules[$key] ?? 'text';
            $sanitized[$key] = $this->sanitize_value($value, $rule);
        }
        return $sanitized;
    }
    
    private function sanitize_value($value, $rule) {
        switch ($rule) {
            case 'email':
                return \sanitize_email($value);
            case 'url':
                return \esc_url_raw($value);
            case 'int':
                return intval($value);
            case 'float':
                return floatval($value);
            case 'bool':
                return (bool) $value;
            case 'textarea':
                return \sanitize_textarea_field($value);
            case 'html':
                return \wp_kses_post($value);
            case 'slug':
                return \sanitize_title($value);
            case 'user':
                return \sanitize_user($value);
            case 'sql':
                return $this->sanitize_sql_value($value);
            default:
                return \sanitize_text_field($value);
        }
    }
    
    private function sanitize_sql_value($value) {
        global $wpdb;
        return $wpdb->_real_escape($value);
    }
    
    private function set_security_headers() {
        if (!$this->should_load_plugin_scripts()) {
            return;
        }
        if (headers_sent()) {
            return;
        }
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
        if (is_ssl()) {
            header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
        }

        $csp_enabled = get_option('amp_csp_enabled', true);
        if ($csp_enabled && $this->should_apply_csp()) {
            $nonce = base64_encode(random_bytes(16));
            $this->current_nonce = $nonce;
            if (\is_admin()) {
                $this->set_admin_csp_policy($nonce);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('AMP CSP: Applied admin CSP policy');
                }
            } elseif ($this->is_dashboard_request()) {
                $this->set_dashboard_csp_policy($nonce);
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log('AMP CSP: Applied dashboard CSP policy');
                }
            }
        } elseif (defined('WP_DEBUG') && WP_DEBUG && $csp_enabled) {
            $request_uri = $_SERVER['REQUEST_URI'] ?? '';
            error_log('AMP CSP: Skipped CSP for frontend request: ' . $request_uri);
        }
    }

    private function should_apply_csp() {
        if (\is_admin()) {
            return true;
        }

        if ($this->is_dashboard_request()) {
            return true;
        }

        if ($this->is_frontend_request()) {
            return false;
        }

        return false;
    }

    private function is_dashboard_request() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';

        if (strpos($request_uri, '/dashboard/') !== false) {
            return true;
        }

        if (strpos($request_uri, '/login/') !== false) {
            return true;
        }

        global $wp;
        if (isset($wp->request)) {
            $current_url = home_url($wp->request);

            if (strpos($current_url, '/dashboard') !== false) {
                return true;
            }

            if (strpos($current_url, '/login') !== false) {
                return true;
            }
        }

        return false;
    }

    private function is_frontend_request() {
        if (\is_admin()) {
            return false;
        }

        $request_uri = $_SERVER['REQUEST_URI'] ?? '';

        if (strpos($request_uri, '/wp-admin') !== false) {
            return false;
        }

        if (strpos($request_uri, '/dashboard') !== false) {
            return false;
        }

        if (strpos($request_uri, '/login') !== false) {
            return false;
        }

        if (\is_home() || \is_front_page() || \is_single() || \is_page() || \is_category() || \is_tag() || \is_archive() || \is_search()) {
            return true;
        }

        return true;
    }

    private function set_admin_csp_policy($nonce) {
        $trusted_domains = [
            'cdnjs.cloudflare.com',
            'cdn.jsdelivr.net',
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.google-analytics.com',
            'www.googletagmanager.com',
            'googletagmanager.com',
            'analytics.google.com',
            'stats.g.doubleclick.net',
            'www.google.com',
            'pagespeed.web.dev',
            'developers.google.com',
            'search.google.com',
            'www.gstatic.com',
            'ssl.gstatic.com',
            'csi.gstatic.com',
            'ajax.googleapis.com',
            'code.jquery.com',
            'stackpath.bootstrapcdn.com',
            'maxcdn.bootstrapcdn.com',
            'use.fontawesome.com',
            'kit.fontawesome.com',
            'pro.fontawesome.com',
            'cdn.datatables.net',
            'unpkg.com',
            'polyfill.io',
            'www.recaptcha.net',
            'www.gstatic.com/recaptcha',
            'api.wordpress.org',
            'downloads.wordpress.org',
            's.w.org',
            'wordpress.org',
            'wp.com',
            'gravatar.com',
            'secure.gravatar.com',
            '0.gravatar.com',
            '1.gravatar.com',
            '2.gravatar.com'
        ];

        $seo_domains = $this->get_seo_plugin_domains();
        $trusted_domains = array_merge($trusted_domains, $seo_domains);

        if (\get_option('amp_google_login_enabled', false)) {
            $trusted_domains[] = 'accounts.google.com';
            $trusted_domains[] = 'apis.google.com';
        }
        if (\get_option('amp_turnstile_enabled', false)) {
            $trusted_domains[] = 'challenges.cloudflare.com';
            $trusted_domains[] = '*.cloudflare.com';
        }

        $script_src = "'self' 'unsafe-inline' 'unsafe-eval'";
        foreach ($trusted_domains as $domain) {
            $script_src .= " https://" . $domain;
        }
        $style_src = "'self' 'unsafe-inline'";
        foreach ($trusted_domains as $domain) {
            $style_src .= " https://" . $domain;
        }
        $connect_src = "'self' " . admin_url('admin-ajax.php') . " https:";
        $frame_src = "'self' https:";
        if (\get_option('amp_turnstile_enabled', false)) {
            $connect_src .= " https://challenges.cloudflare.com https://*.cloudflare.com";
            $frame_src .= " https://challenges.cloudflare.com https://*.cloudflare.com";
        }
        $img_src = "'self' data: https: blob:";
        $font_src = "'self' data: https:";
        $worker_src = "'self' blob:";
        $csp_policy = "default-src 'self'; script-src {$script_src}; style-src {$style_src}; font-src {$font_src}; img-src {$img_src}; connect-src {$connect_src}; worker-src {$worker_src}; frame-src {$frame_src}; object-src 'none'; base-uri 'self'";
        header("Content-Security-Policy: {$csp_policy}");
    }

    private function set_dashboard_csp_policy($nonce) {
        $trusted_domains = [
            'cdnjs.cloudflare.com',
            'cdn.jsdelivr.net',
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'ajax.googleapis.com',
            'code.jquery.com',
            'stackpath.bootstrapcdn.com',
            'maxcdn.bootstrapcdn.com',
            'use.fontawesome.com',
            'kit.fontawesome.com',
            'cdn.datatables.net',
            'unpkg.com',
            'polyfill.io',
            'www.recaptcha.net',
            'www.gstatic.com/recaptcha',
            'gravatar.com',
            'secure.gravatar.com',
            '0.gravatar.com',
            '1.gravatar.com',
            '2.gravatar.com'
        ];

        if (\get_option('amp_google_login_enabled', false)) {
            $trusted_domains[] = 'accounts.google.com';
            $trusted_domains[] = 'apis.google.com';
        }
        if (\get_option('amp_turnstile_enabled', false)) {
            $trusted_domains[] = 'challenges.cloudflare.com';
            $trusted_domains[] = '*.cloudflare.com';
        }

        $script_src = "'self' 'unsafe-inline'";
        foreach ($trusted_domains as $domain) {
            $script_src .= " https://" . $domain;
        }
        $style_src = "'self' 'unsafe-inline'";
        foreach ($trusted_domains as $domain) {
            $style_src .= " https://" . $domain;
        }
        $connect_src = "'self' https:";
        $frame_src = "'self' https:";
        if (\get_option('amp_turnstile_enabled', false)) {
            $connect_src .= " https://challenges.cloudflare.com https://*.cloudflare.com";
            $frame_src .= " https://challenges.cloudflare.com https://*.cloudflare.com";
        }
        $img_src = "'self' data: https:";
        $font_src = "'self' data: https:";
        $worker_src = "'self' blob:";
        $csp_policy = "default-src 'self'; script-src {$script_src}; style-src {$style_src}; font-src {$font_src}; img-src {$img_src}; connect-src {$connect_src}; worker-src {$worker_src}; frame-src {$frame_src}; object-src 'none'; form-action 'self' https:";
        header("Content-Security-Policy: {$csp_policy}");
    }

    private function get_seo_plugin_domains() {
        $seo_domains = [];

        $seo_mode_enabled = get_option('amp_csp_seo_mode', 1);
        if (!$seo_mode_enabled) {
            return $seo_domains;
        }

        if (is_plugin_active('wordpress-seo/wp-seo.php') ||
            is_plugin_active('wordpress-seo-premium/wp-seo-premium.php')) {
            $seo_domains = array_merge($seo_domains, [
                'yoast.com',
                'my.yoast.com',
                'cdn.yoast.com',
                'yoastcdn.com'
            ]);
        }

        if (is_plugin_active('all-in-one-seo-pack/all_in_one_seo_pack.php') ||
            is_plugin_active('all-in-one-seo-pack-pro/all_in_one_seo_pack.php')) {
            $seo_domains = array_merge($seo_domains, [
                'aioseo.com',
                'semperplugins.com'
            ]);
        }

        if (is_plugin_active('seo-by-rank-math/rank-math.php') ||
            is_plugin_active('seo-by-rank-math-pro/rank-math-pro.php')) {
            $seo_domains = array_merge($seo_domains, [
                'rankmath.com',
                'mythemeshop.com'
            ]);
        }

        if (is_plugin_active('squirrly-seo/squirrly.php')) {
            $seo_domains = array_merge($seo_domains, [
                'squirrly.co',
                'howto12.squirrly.co'
            ]);
        }

        if (is_plugin_active('the-seo-framework/autodescription.php')) {
            $seo_domains = array_merge($seo_domains, [
                'theseoframework.com'
            ]);
        }

        if (is_plugin_active('seopress/seopress.php') ||
            is_plugin_active('wp-seopress-pro/seopress-pro.php')) {
            $seo_domains = array_merge($seo_domains, [
                'seopress.org',
                'wp-seopress.com'
            ]);
        }

        if (is_plugin_active('litespeed-cache/litespeed-cache.php')) {
            $seo_domains = array_merge($seo_domains, [
                'litespeedtech.com',
                'quic.cloud',
                'cdn.quic.cloud',
                'toolbox.quic.cloud'
            ]);
        }

        if (is_plugin_active('perfmatters/perfmatters.php')) {
            $seo_domains = array_merge($seo_domains, [
                'perfmatters.io',
                'forgemedia.io'
            ]);
        }

        if (is_plugin_active('wp-rocket/wp-rocket.php')) {
            $seo_domains = array_merge($seo_domains, [
                'wp-rocket.me',
                'rocketcdn.me'
            ]);
        }

        if (is_plugin_active('w3-total-cache/w3-total-cache.php')) {
            $seo_domains = array_merge($seo_domains, [
                'w3-edge.com',
                'boldgrid.com'
            ]);
        }

        if (is_plugin_active('wp-fastest-cache/wpFastestCache.php') ||
            is_plugin_active('wp-fastest-cache-premium/wpFastestCachePremium.php')) {
            $seo_domains = array_merge($seo_domains, [
                'wpfastestcache.com'
            ]);
        }

        if (is_plugin_active('autoptimize/autoptimize.php')) {
            $seo_domains = array_merge($seo_domains, [
                'autoptimize.com',
                'optimizingmatters.com'
            ]);
        }

        if (is_plugin_active('wp-super-cache/wp-cache.php')) {
            $seo_domains = array_merge($seo_domains, [
                'automattic.com',
                'wordpress.com'
            ]);
        }

        return array_unique($seo_domains);
    }

    private function detect_malicious_patterns() {
        if ($this->is_login_request() || $this->is_authenticated_ajax_request() || $this->is_public_ajax_request()) {
            return false;
        }
        if ($this->is_litespeed_cache_request()) {
            return false;
        }
        if ($this->is_file_edit_request()) {
            return false;
        }
        $patterns = [
            '/(?:union|select|insert|update|delete|drop|create|alter|exec)\s+/i',
            '/<script[^>]*src\s*=\s*["\'](?!https?:\/\/(cdnjs\.cloudflare\.com|cdn\.jsdelivr\.net|fonts\.googleapis\.com))/i',
            '/javascript:\s*(?!void\(0\))/i',
            '/data:text\/html/i',
            '/\.\.\//i',
            '/\/etc\/passwd/i',
            '/\/proc\/self\/environ/i'
        ];
        $request_data = $_REQUEST;
        foreach ($patterns as $pattern) {
            foreach ($request_data as $key => $value) {
                if (is_string($value) && preg_match($pattern, $value)) {
                    $this->log_security_event('malicious_pattern_detected', [
                        'pattern' => $pattern,
                        'key' => sanitize_text_field($key),
                        'value_sample' => substr(sanitize_text_field($value), 0, 100)
                    ]);
                    return true;
                } elseif (is_array($value)) {
                    $array_string = wp_json_encode($value);
                    if ($array_string && preg_match($pattern, $array_string)) {
                        $this->log_security_event('malicious_pattern_detected', [
                            'pattern' => $pattern,
                            'key' => sanitize_text_field($key),
                            'array_sample' => substr($array_string, 0, 100)
                        ]);
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    private function detect_sql_injection() {
        if ($this->is_login_request() || $this->is_authenticated_ajax_request() || $this->is_public_ajax_request()) {
            return false;
        }
        if ($this->is_litespeed_cache_request()) {
            return false;
        }
        if ($this->is_file_edit_request()) {
            return false;
        }
        $sql_patterns = [
            '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
            '/\b(and|or)\b\s+\d+\s*=\s*\d+/i',
            '/\b(and|or)\b\s+[\'"]?\w+[\'"]?\s*=\s*[\'"]?\w+[\'"]?/i',
            '/(\bselect\b.*\bfrom\b.*\bwhere\b)/i',
            '/(\bdrop\b.*\btable\b)|(\bdelete\b.*\bfrom\b)/i'
        ];
        foreach ($_REQUEST as $value) {
            if (is_string($value)) {
                foreach ($sql_patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->log_security_event('sql_injection_detected', [
                            'pattern' => $pattern,
                            'value' => substr($value, 0, 100)
                        ]);
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    private function detect_xss_attempt() {
        if ($this->is_login_request() || $this->is_authenticated_ajax_request() || $this->is_public_ajax_request()) {
            return false;
        }
        if ($this->is_litespeed_cache_request()) {
            return false;
        }
        $xss_patterns = [
            '/<script[^>]*>(?!.*(?:jQuery|wp\.|window\.wp))/i',
            '/javascript:\s*(?!void\(0\))/i',
            '/on\w+\s*=\s*["\'][^"\']*(?:alert|eval|document\.cookie)/i',
            '/<iframe[^>]*src\s*=\s*["\'](?!https?:\/\/)/i',
            '/<object[^>]*>/i',
            '/<embed[^>]*>/i'
        ];
        foreach ($_REQUEST as $key => $value) {
            if (is_string($value) && !in_array($key, ['action', 'page', '_wpnonce'])) {
                foreach ($xss_patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $this->log_security_event('xss_attempt_detected', [
                            'pattern' => $pattern,
                            'value' => substr($value, 0, 100),
                            'key' => $key
                        ]);
                        return true;
                    }
                }
            }
        }
        return false;
    }
    
    private function detect_session_hijacking() {
        if (!\is_user_logged_in() || $this->is_admin_user) {
            return false;
        }
        $user_id = \get_current_user_id();
        $session_data = get_transient('amp_session_' . $user_id);
        if (!$session_data || !isset($session_data['fingerprint'])) {
            return false;
        }
        $last_login = get_user_meta($user_id, 'amp_last_login', true);
        $is_recent_login = $last_login && (time() - $last_login) < 120;
        $current_fingerprint = $this->generate_session_fingerprint();
        if ($session_data['fingerprint'] !== $current_fingerprint) {
            if ($is_recent_login) {
                $session_data['fingerprint'] = $current_fingerprint;
                $session_duration = isset($session_data['is_admin']) && $session_data['is_admin'] ? (365 * 24 * HOUR_IN_SECONDS) : (24 * HOUR_IN_SECONDS);
                set_transient('amp_session_' . $user_id, $session_data, $session_duration);
                error_log('AMP Session: Fingerprint updated for recent login - user ' . $user_id);
                return false;
            }
            $fingerprint_mismatch_count = get_user_meta($user_id, 'amp_fingerprint_mismatch_count', true) ?: 0;
            $fingerprint_mismatch_count++;
            update_user_meta($user_id, 'amp_fingerprint_mismatch_count', $fingerprint_mismatch_count);
            if ($fingerprint_mismatch_count >= 5) {
                $this->log_security_event('session_hijacking_detected', [
                    'original_fingerprint' => $session_data['fingerprint'],
                    'current_fingerprint' => $current_fingerprint,
                    'mismatch_count' => $fingerprint_mismatch_count
                ]);
                delete_user_meta($user_id, 'amp_fingerprint_mismatch_count');
                return true;
            } else {
                $session_data['fingerprint'] = $current_fingerprint;
                $session_duration = isset($session_data['is_admin']) && $session_data['is_admin'] ? (365 * 24 * HOUR_IN_SECONDS) : (24 * HOUR_IN_SECONDS);
                set_transient('amp_session_' . $user_id, $session_data, $session_duration);
                error_log('AMP Session: Fingerprint mismatch #' . $fingerprint_mismatch_count . ' for user ' . $user_id . ' - updating fingerprint');
                return false;
            }
        } else {
            delete_user_meta($user_id, 'amp_fingerprint_mismatch_count');
        }
        return false;
    }

    private function update_last_activity() {
        if (!\is_user_logged_in()) {
            return;
        }
        $user_id = \get_current_user_id();
        $is_admin = \current_user_can('manage_options');

        if ($is_admin) {
            $session_data = get_transient('amp_session_' . $user_id);
            if ($session_data) {
                $session_data['last_activity'] = time();
                set_transient('amp_session_' . $user_id, $session_data, 365 * 24 * HOUR_IN_SECONDS);
            }
            return;
        }

        $session_data = get_transient('amp_session_' . $user_id);
        if ($session_data) {
            $session_data['last_activity'] = time();
            set_transient('amp_session_' . $user_id, $session_data, 24 * HOUR_IN_SECONDS);
        }
    }
    
    private function generate_session_fingerprint() {
        $fingerprint_data = [
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 200),
            'accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            'ip' => $this->get_real_ip_with_proxy_check(),
            'accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            'connection' => $_SERVER['HTTP_CONNECTION'] ?? ''
        ];
        return hash('sha256', implode('|', $fingerprint_data) . SECURE_AUTH_SALT);
    }

    private function get_real_ip_with_proxy_check() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'REMOTE_ADDR'];
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if ($key === 'HTTP_X_FORWARDED_FOR') {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    private function generate_secure_session_id() {
        return hash('sha256', uniqid() . random_bytes(32) . microtime(true));
    }
    
    private function validate_ajax_origin() {
        if ($this->is_admin_user) {
            return true;
        }
        if ($this->is_login_request() || $this->is_authenticated_ajax_request() || $this->is_public_ajax_request()) {
            return true;
        }
        $allowed_origins = [
            home_url(),
            admin_url(),
            site_url()
        ];
        $origin = $_SERVER['HTTP_ORIGIN'] ?? $_SERVER['HTTP_REFERER'] ?? '';
        foreach ($allowed_origins as $allowed) {
            if (strpos($origin, $allowed) === 0) {
                return true;
            }
        }
        return false;
    }
    
    private function increment_rate_limit($type) {
        $identifier = $this->get_rate_limit_identifier();
        $cache_key = "rate_limit_{$type}_{$identifier}";
        $cache_manager = \AMP_Cache_Manager::instance();
        $rate_data = $cache_manager->get($cache_key, 'rate_limits');
        if ($rate_data === false) {
            $limit_config = $this->rate_limits[$type] ?? ['window' => 60];
            $rate_data = [
                'count' => 0,
                'reset' => time() + $limit_config['window']
            ];
        }
        $rate_data['count']++;
        $limit_config = $this->rate_limits[$type] ?? ['window' => 60];
        if ($this->is_admin_user && $type === 'failed_attempts') {
            $limit_config['limit'] = ($limit_config['limit'] ?? 5) * 3;
        }
        $cache_manager->set($cache_key, $rate_data, $limit_config['window'], 'rate_limits');
    }
    
    private function handle_security_violation($violation_type) {
        $this->log_security_event('security_violation', [
            'type' => $violation_type,
            'ip' => \AMP_Utilities::get_client_ip(),
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'user_id' => $this->is_admin_user ? 'admin_' . hash('sha256', (string)$this->user_id . SECURE_AUTH_SALT) : ($this->user_id ? 'user_' . hash('sha256', (string)$this->user_id . SECURE_AUTH_SALT) : 'guest'),
            'is_admin' => $this->is_admin_user
        ]);
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($request_uri, '/wp-admin/') !== false) {
            \wp_safe_redirect(\home_url('/login/?error=unauthorized&redirect=' . urlencode($request_uri)));
            exit;
        }
        switch ($violation_type) {
            case 'rate_limit_exceeded':
                \wp_die('Rate limit exceeded. Please try again later.', 'Security Error', ['response' => 429]);
                break;
            case 'unauthorized_admin_access':
                \wp_safe_redirect(\home_url('/login/?error=unauthorized'));
                exit;
                break;
            case 'invalid_request':
                \wp_die('Invalid request detected.', 'Security Error', ['response' => 400]);
                break;
            default:
                \wp_die('Security violation detected.', 'Security Error', ['response' => 403]);
        }
    }
    
    private function log_security_event($event_type, $data) {
        $log_data = [
            'timestamp' => time(),
            'event_type' => $event_type,
            'ip' => \AMP_Utilities::get_client_ip(),
            'user_agent' => substr($_SERVER['HTTP_USER_AGENT'] ?? 'unknown', 0, 100),
            'data' => $data
        ];
        error_log('AMP Security Event: ' . json_encode($log_data));

        $this->check_security_alerts($event_type, $data);
    }

    private function check_security_alerts($event_type, $data) {
        $critical_events = ['security_violation', 'failed_login', 'privilege_escalation', 'sql_injection_detected', 'xss_attempt_detected'];
        if (in_array($event_type, $critical_events)) {
            $cache_manager = \AMP_Cache_Manager::instance();
            $alert_key = "security_alert_{$event_type}_" . date('Y-m-d-H');
            $alert_count = $cache_manager->get($alert_key, 'security_alerts') ?: 0;
            $alert_count++;
            $cache_manager->set($alert_key, $alert_count, 3600, 'security_alerts');

            if ($alert_count >= 10) {
                $this->send_security_alert($event_type, $alert_count, $data);
            }
        }
    }

    private function send_security_alert($event_type, $count, $data) {
        $admin_email = get_option('admin_email');
        if ($admin_email) {
            $subject = "AMP Security Alert: {$event_type}";
            $message = "Security event '{$event_type}' occurred {$count} times in the last hour.\n\n";
            $message .= "Latest event data: " . json_encode($data, JSON_PRETTY_PRINT);
            wp_mail($admin_email, $subject, $message);
        }
    }
    
    private function log_access($access_type, $data) {
        $this->access_log[] = [
            'type' => $access_type,
            'data' => $data,
            'timestamp' => microtime(true)
        ];
        if (count($this->access_log) > 100) {
            array_shift($this->access_log);
        }
    }
    
    private function load_user_capabilities() {
        $user = \wp_get_current_user();
        $this->user_capabilities = $user->allcaps ?? [];
    }
    
    private function get_default_sanitization_rules() {
        return [
            'id' => 'int',
            'user_id' => 'int',
            'position_id' => 'int',
            'name' => 'text',
            'title' => 'text',
            'description' => 'textarea',
            'content' => 'html',
            'email' => 'email',
            'url' => 'url',
            'link' => 'url',
            'image' => 'url',
            'slug' => 'slug',
            'username' => 'user',
            'price' => 'float',
            'amount' => 'float',
            'width' => 'int',
            'height' => 'int',
            'status' => 'text',
            'type' => 'text'
        ];
    }
    
    public function on_user_login($user_login, $user) {
        update_user_meta($user->ID, 'amp_last_login', time());
        $is_admin = user_can($user, 'manage_options');
        $current_time = time();

        if ($is_admin) {
            $existing_session = get_transient('amp_session_' . $user->ID);
            if ($existing_session && isset($existing_session['is_admin']) && $existing_session['is_admin']) {
                $existing_session['last_activity'] = $current_time;
                $existing_session['login_timestamp'] = $current_time;
                $existing_session['fingerprint'] = $this->generate_session_fingerprint();
                $existing_session['ip_address'] = \AMP_Utilities::get_client_ip();
                $existing_session['login_context'] = $this->context;

                set_transient('amp_session_' . $user->ID, $existing_session, 365 * 24 * HOUR_IN_SECONDS);
                $this->sync_session_across_contexts($user->ID, $existing_session);

                error_log('AMP Session: Admin session reused and updated for user ' . $user->ID . ' (' . $user_login . ') - Context: ' . $this->context);
                return;
            }
            error_log('AMP Session: Administrator login for user ' . $user->ID . ' (' . $user_login . ') - creating new admin session');
        } else {
            error_log('AMP Session: User login for user ' . $user->ID . ' (' . $user_login . ') - standard session management');
            $this->clear_all_user_sessions($user->ID);
        }

        delete_user_meta($user->ID, 'amp_fingerprint_mismatch_count');
        $session_data = [
            'fingerprint' => $this->generate_session_fingerprint(),
            'start_time' => $current_time,
            'last_activity' => $current_time,
            'user_id' => $user->ID,
            'login_timestamp' => $current_time,
            'ip_address' => \AMP_Utilities::get_client_ip(),
            'session_id' => $this->generate_secure_session_id(),
            'login_context' => $this->context,
            'is_admin' => $is_admin
        ];
        $session_duration = $is_admin ? (365 * 24 * HOUR_IN_SECONDS) : (24 * HOUR_IN_SECONDS);
        set_transient('amp_session_' . $user->ID, $session_data, $session_duration);
        $session_timeout = $is_admin ? (365 * 24 * 60) : (int) get_option('amp_session_timeout', 60);
        update_user_meta($user->ID, 'amp_session_start', $current_time);
        update_user_meta($user->ID, 'amp_session_timeout', $session_timeout * 60);
        $this->sync_session_across_contexts($user->ID, $session_data);
        error_log('AMP Session: New session created for user ' . $user->ID . ' (' . $user_login . ') - Type: ' . ($is_admin ? 'Admin' : 'User'));
    }

    public function sync_session_across_contexts($user_id, $session_data) {
        $sync_data = [
            'user_id' => $user_id,
            'session_id' => $session_data['session_id'],
            'login_timestamp' => $session_data['login_timestamp'],
            'fingerprint' => $session_data['fingerprint'],
            'context' => $this->context,
            'is_admin' => $session_data['is_admin'] ?? false
        ];
        $sync_duration = $session_data['is_admin'] ? (365 * 24 * HOUR_IN_SECONDS) : HOUR_IN_SECONDS;
        set_transient('amp_session_sync_' . $user_id, $sync_data, $sync_duration);
        error_log('AMP Session: Session synced across contexts for user ' . $user_id);
    }
    
    public function on_user_logout() {
        $user_id = get_current_user_id();
        if ($user_id) {
            $this->clear_all_user_sessions($user_id);
        }
    }

    public function pre_login_cleanup($username) {
        $user = get_user_by('login', $username);
        if (!$user) {
            $user = get_user_by('email', $username);
        }
        if ($user) {
            $this->clear_all_user_sessions($user->ID);
        }
    }
    
    public function clear_all_user_sessions($user_id) {
        if (!$user_id) {
            return;
        }
        delete_transient('amp_session_' . $user_id);
        delete_transient('amp_session_sync_' . $user_id);
        delete_user_meta($user_id, 'amp_session_start');
        delete_user_meta($user_id, 'amp_session_timeout');
        delete_user_meta($user_id, 'amp_session_last_active');
    }
    
    private function destroy_user_auth_cookies($user_id) {
        $sessions = \WP_Session_Tokens::get_instance($user_id);
        $sessions->destroy_all();
    }
    
    public function unified_session_check() {
        if (!\is_user_logged_in()) {
            return;
        }
        $user_id = \get_current_user_id();
        $is_admin = \current_user_can('manage_options');
        if ($is_admin) {
            return;
        }
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $ajax_actions = ['amp_google_login', 'amp_check_session', 'amp_extend_session'];
            if (isset($_POST['action']) && in_array($_POST['action'], $ajax_actions)) {
                return;
            }
        }
        $last_login = get_user_meta($user_id, 'amp_last_login', true);
        $grace_period = 30;
        $is_recent_login = $last_login && (time() - $last_login) < $grace_period;
        if (!$is_recent_login) {
            if ($this->detect_session_hijacking()) {
                $this->force_logout('session_hijacking');
                return;
            }
            if ($this->is_session_expired()) {
                $this->force_logout('session_timeout');
                return;
            }
        }
        $this->update_last_activity();
        $this->load_user_capabilities();
    }

    public function validate_user_session() {
        $this->unified_session_check();
    }
    
    public function enhanced_authentication($user, $username, $password) {
        if (is_wp_error($user)) {
            $this->increment_rate_limit('failed_attempts');
            $this->log_security_event('failed_login', [
                'user_hash' => hash('sha256', $username . SECURE_AUTH_SALT),
                'ip' => \AMP_Utilities::get_client_ip(),
                'timestamp' => time(),
                'error_code' => $user->get_error_code()
            ]);
        }
        return $user;
    }
    
    public function conditional_inject_security_scripts() {
        if (!$this->should_load_plugin_scripts()) {
            return;
        }
        $this->inject_security_scripts();
    }
    
    private function should_load_plugin_scripts() {
        if (is_admin()) {
            return true;
        }
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $parsed_url = parse_url($request_uri);
        $path = $parsed_url['path'] ?? '';
        $plugin_pages = [
            '/dashboard',
            '/login'
        ];
        foreach ($plugin_pages as $page) {
            if (strpos($path, $page) !== false) {
                return true;
            }
        }
        return false;
    }

    public function inject_security_scripts() {
        $security_data = [
            'ajax_url' => \admin_url('admin-ajax.php'),
            'context' => $this->context,
            'max_login_attempts' => (int) get_option('amp_max_login_attempts', 5)
        ];
        \wp_register_script('amp-csrf-protection', AMP_PLUGIN_URL . 'admin/assets/js/csrf-protection.js', [], AMP_VERSION, true);
        \wp_localize_script('amp-csrf-protection', 'ampSecurityData', $security_data);
        \wp_enqueue_script('amp-csrf-protection');
    }
    
    public function inject_admin_security() {
        if (\is_admin()) {
            $nonce = $this->current_nonce ?? base64_encode(random_bytes(16));
            echo '<script nonce="' . esc_attr($nonce) . '">window.ampSecurityContext = "admin";</script>';
        }
    }

    public function get_current_nonce() {
        return $this->current_nonce;
    }
    
    public function public_security_check() {
        \wp_send_json_success([
            'context' => $this->context,
            'session_valid' => true,
            'timestamp' => time()
        ]);
    }

    public function generate_unified_nonces() {
        return [
            'loginNonce' => \wp_create_nonce('amp_login_action'),
            'registerNonce' => \wp_create_nonce('amp_register_action'),
            'forgotPasswordNonce' => \wp_create_nonce('amp_forgot_password_action'),
            'resetPasswordNonce' => \wp_create_nonce('amp_reset_password_action'),
            'dashboardNonce' => \wp_create_nonce('amp_dashboard_action'),
            'authNonce' => \wp_create_nonce('amp_auth_action'),
            'exchangeRateNonce' => \wp_create_nonce('refresh_exchange_rate')
        ];
    }

    public function sync_security_data_across_contexts() {
        $nonces = $this->generate_unified_nonces();
        $encryption_manager = \AMP_Encryption_Manager::instance();
        $security_data = [
            'nonces' => $nonces,
            'timestamp' => time(),
            'session_id' => session_id(),
            'user_id' => \get_current_user_id(),
            'is_admin' => $this->is_admin_user,
            'session_timeout_minutes' => $this->is_admin_user ? 0 : (int) get_option('amp_session_timeout', 60),
            'google_login_enabled' => (bool) get_option('amp_google_login_enabled', false),
            'google_client_id' => $encryption_manager->get_secret('amp_google_client_id'),
            'turnstile_enabled' => (bool) get_option('amp_turnstile_enabled', false),
            'turnstile_site_key' => $encryption_manager->get_secret('amp_turnstile_site_key')
        ];
        return $security_data;
    }

    public function get_session_sync_status($user_id = null) {
        if (!$user_id) {
            $user_id = \get_current_user_id();
        }
        if (!$user_id) {
            return false;
        }
        $session_data = get_transient('amp_session_' . $user_id);
        $sync_data = get_transient('amp_session_sync_' . $user_id);
        return [
            'session_exists' => !empty($session_data),
            'sync_exists' => !empty($sync_data),
            'session_data' => $session_data,
            'sync_data' => $sync_data,
            'is_synced' => !empty($session_data) && !empty($sync_data)
        ];
    }

    public function validate_session_sync($user_id = null) {
        if (!$user_id) {
            $user_id = \get_current_user_id();
        }
        if (\current_user_can('manage_options')) {
            return true;
        }
        $sync_status = $this->get_session_sync_status($user_id);
        if (!$sync_status['is_synced']) {
            error_log('AMP Session: Session sync validation failed for user ' . $user_id);
            return false;
        }
        $session_data = $sync_status['session_data'];
        $sync_data = $sync_status['sync_data'];
        if ($session_data['session_id'] !== $sync_data['session_id']) {
            error_log('AMP Session: Session ID mismatch detected for user ' . $user_id);
            return false;
        }
        return true;
    }

    public function set_auth_cookie_expiration($expire, $user_id, $scheme) {
        if (user_can($user_id, 'manage_options')) {
            return 365 * 24 * 60 * 60;
        }
        $session_timeout = (int) get_option('amp_session_timeout', 60);
        if ($session_timeout < 5) {
            error_log('AMP Warning: Session timeout is set to ' . $session_timeout . ' minutes, which may cause frequent logouts');
        }
        return $session_timeout * 60;
    }

    public function check_session_timeout() {
        return;
    }

    public function is_session_expired() {
        if (!\is_user_logged_in()) {
            return false;
        }
        $user_id = \get_current_user_id();
        if (\current_user_can('manage_options')) {
            return false;
        }
        $last_login = get_user_meta($user_id, 'amp_last_login', true);
        $is_recent_login = $last_login && (time() - $last_login) < 60;
        if ($is_recent_login) {
            return false;
        }
        $session_start = get_user_meta($user_id, 'amp_session_start', true);
        $session_timeout_minutes = (int) get_option('amp_session_timeout', 60);
        $session_timeout = $session_timeout_minutes * 60;
        if (!$session_start) {
            $session_data = get_transient('amp_session_' . $user_id);
            if ($session_data && isset($session_data['start_time'])) {
                $session_start = $session_data['start_time'];
            } else {
                return false;
            }
        }
        $current_time = time();
        $session_duration = $current_time - $session_start;
        return $session_duration > $session_timeout;
    }

    public function force_logout($reason = 'session_expired') {
        $user_id = \get_current_user_id();
        if (\current_user_can('manage_options')) {
            return;
        }
        $session_start = 0;
        $session_data = get_transient('amp_session_' . $user_id);
        if ($session_data && isset($session_data['start_time'])) {
            $session_start = $session_data['start_time'];
        }
        $this->log_security_event('forced_logout', [
            'reason' => $reason,
            'user_id' => $user_id,
            'session_duration' => time() - $session_start
        ]);
        error_log('AMP Session: Force logout for user ' . $user_id . ' - reason: ' . $reason);
        $this->clear_all_user_sessions($user_id);
        \wp_clear_auth_cookie();
        \wp_logout();
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $current_url = $_SERVER['REQUEST_URI'] ?? '';
            $is_dashboard = strpos($current_url, '/dashboard') !== false;
            $referer = $_SERVER['HTTP_REFERER'] ?? '';
            $is_from_login = strpos($referer, '/login') !== false;
            \wp_send_json_error([
                'message' => 'เซสชันหมดอายุ กรุณาเข้าสู่ระบบใหม่',
                'redirect' => \home_url('/login/?error=session_expired&from=' . urlencode($current_url)),
                'force_reload' => !$is_dashboard && !$is_from_login,
                'show_popup' => $is_dashboard && !$is_from_login,
                'reason' => $reason
            ]);
        } else {
            $redirect_url = $this->get_smart_redirect_url($reason);
            \wp_safe_redirect($redirect_url);
            exit;
        }
    }

    private function get_smart_redirect_url($reason = 'session_expired') {
        $current_url = $_SERVER['REQUEST_URI'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        $is_dashboard = strpos($current_url, '/dashboard') !== false;
        $is_login = strpos($current_url, '/login') !== false;
        $is_from_login = strpos($referer, '/login') !== false;
        if ($is_login && $is_from_login) {
            return \home_url('/dashboard/');
        }
        if ($is_dashboard) {
            return \home_url('/login/?error=' . $reason . '&redirect_to=' . urlencode($current_url));
        }
        return \home_url('/login/?error=' . $reason);
    }

    public function check_session_ajax() {
        if (!\is_user_logged_in()) {
            \wp_send_json_error(['message' => 'Not logged in']);
            return;
        }
        $user_id = \get_current_user_id();
        $is_admin = \current_user_can('manage_options');
        if ($is_admin) {
            \wp_send_json_success([
                'context' => $this->context,
                'session_valid' => true,
                'is_admin' => true,
                'remaining_time' => null,
                'session_timeout_minutes' => 0,
                'timestamp' => time()
            ]);
            return;
        }
        if ($this->is_session_expired()) {
            \wp_send_json_error([
                'message' => 'Session expired',
                'redirect' => \home_url('/login/?error=session_expired')
            ]);
            return;
        }
        $session_start = get_user_meta($user_id, 'amp_session_start', true);
        $session_timeout_minutes = (int) get_option('amp_session_timeout', 60);
        $session_timeout = $session_timeout_minutes * 60;
        $session_start = $session_start ? (int) $session_start : time();
        $remaining_time = $session_timeout - (time() - $session_start);
        \wp_send_json_success([
            'context' => $this->context,
            'session_valid' => true,
            'is_admin' => false,
            'remaining_time' => max(0, $remaining_time),
            'session_timeout_minutes' => $session_timeout_minutes,
            'timestamp' => time()
        ]);
    }

    public function extend_session_ajax() {
        if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }
        if (!\is_user_logged_in()) {
            \wp_send_json_error(['message' => 'User not logged in']);
            return;
        }
        $user_id = \get_current_user_id();
        if (\current_user_can('manage_options')) {
            \wp_send_json_success(['message' => 'Admin session - no extension needed']);
            return;
        }
        global $wpdb;
        $global_table = $wpdb->prefix . 'ad_price_global_settings';
        $timeout_row = $wpdb->get_row($wpdb->prepare(
            "SELECT setting_value FROM {$global_table} WHERE setting_name = %s",
            'reservation_timeout'
        ));
        $extension_minutes = $timeout_row ? intval($timeout_row->setting_value) : 3;
        $current_time = time();
        $session_start = get_user_meta($user_id, 'amp_session_start', true);
        $session_timeout_minutes = (int) get_option('amp_session_timeout', 60);
        $session_timeout = $session_timeout_minutes * 60;
        if (!$session_start) {
            $session_data = get_transient('amp_session_' . $user_id);
            if ($session_data && isset($session_data['start_time'])) {
                $session_start = $session_data['start_time'];
            } else {
                $session_start = $current_time;
            }
        }
        $elapsed_time = $current_time - $session_start;
        $remaining_time = max(0, $session_timeout - $elapsed_time);
        $extension_seconds = $extension_minutes * 60;
        $new_session_duration = $remaining_time + $extension_seconds;
        $new_session_start = $current_time - ($session_timeout - $new_session_duration);
        update_user_meta($user_id, 'amp_session_start', $new_session_start);
        update_user_meta($user_id, 'amp_session_last_active', $current_time);
        $session_data = get_transient('amp_session_' . $user_id);
        if ($session_data) {
            $session_data['start_time'] = $new_session_start;
            $session_data['last_activity'] = $current_time;
            set_transient('amp_session_' . $user_id, $session_data, 24 * HOUR_IN_SECONDS);
        }
        \wp_send_json_success([
            'message' => 'Session extended successfully',
            'extension_minutes' => $extension_minutes,
            'remaining_time_before' => $remaining_time,
            'new_total_time' => $new_session_duration
        ]);
    }

    public function cleanup_expired_sessions() {
        global $wpdb;
        $session_timeout_minutes = (int) get_option('amp_session_timeout', 60);
        $session_timeout = $session_timeout_minutes * 60;
        $current_time = time();
        $expiration_threshold = $current_time - $session_timeout;
        $expired_users = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT um.user_id, um.meta_value, u.ID FROM {$wpdb->usermeta} um
                LEFT JOIN {$wpdb->users} u ON um.user_id = u.ID
                LEFT JOIN {$wpdb->usermeta} um2 ON u.ID = um2.user_id AND um2.meta_key = '{$wpdb->prefix}capabilities'
                WHERE um.meta_key = 'amp_session_start'
                AND CAST(um.meta_value AS UNSIGNED) < %d
                AND (um2.meta_value IS NULL OR um2.meta_value NOT LIKE %s)",
                $expiration_threshold,
                '%manage_options%'
            )
        );
        $cleaned_count = 0;
        foreach ($expired_users as $user_meta) {
            $user_id = $user_meta->user_id;
            delete_user_meta($user_id, 'amp_session_start');
            delete_user_meta($user_id, 'amp_session_timeout');
            delete_user_meta($user_id, 'amp_session_last_active');
            $cleaned_count++;
        }
        error_log("AMP Session Cleanup: Cleaned {$cleaned_count} expired sessions");
        return $cleaned_count;
    }
}