<?php

if (!defined('WPINC')) {
    die;
}

function ad_management_add_page_templates($templates) {
    $templates['ad-dashboard-template.php'] = 'Ad Dashboard Template';
    $templates['ad-login-template.php'] = 'Ad Login Template';
    $templates['ad-sale-template.php'] = 'Ad Sale Page Template';

    return $templates;
}
add_filter('theme_page_templates', 'ad_management_add_page_templates');

function ad_management_load_page_templates($template) {
    global $post;

    if (!$post) {
        return $template;
    }
    $template_name = get_post_meta($post->ID, '_wp_page_template', true);
    if ('ad-dashboard-template.php' === $template_name) {
        $template = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/dashboard.php';
    } elseif ('ad-login-template.php' === $template_name) {
        $login_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/login/index.php';
        if (file_exists($login_path)) {
            return $login_path;
        }
        $template = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/login/index.php';
    } elseif ('ad-sale-template.php' === $template_name) {
        $template = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/sale-page.php';
    }

    return $template;
}
add_filter('page_template', 'ad_management_load_page_templates');

function ad_management_template_override() {
    global $wp_query;

    if (is_page('login')) {
        $template_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/login/index.php';
        if (file_exists($template_path)) {
            load_template($template_path);
            exit;
        }
    } elseif (is_page('sale-page')) {
        $template_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/sale-page.php';
        if (file_exists($template_path)) {
            load_template($template_path);
            exit;
        }
    }

    if (is_page('dashboard')) {
        $template_path = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/dashboard.php';
        if (file_exists($template_path)) {
            load_template($template_path);
            exit;
        }
    }
}
add_action('template_redirect', 'ad_management_template_override', 1);

function ad_management_create_pages() {
    $pages_created = false;

    $dashboard_page = get_page_by_path('dashboard');
    if (!$dashboard_page) {
        $dashboard_page_id = wp_insert_post(array(
            'post_title'     => 'Dashboard',
            'post_content'   => '',
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'comment_status' => 'closed',
            'ping_status'    => 'closed'
        ));

        if ($dashboard_page_id && !is_wp_error($dashboard_page_id)) {
            update_post_meta($dashboard_page_id, '_wp_page_template', 'ad-dashboard-template.php');
            $pages_created = true;
            error_log('AMP: Dashboard page created with ID: ' . $dashboard_page_id);
        }
    }

    $login_page = get_page_by_path('login');
    if (!$login_page) {
        $login_page_id = wp_insert_post(array(
            'post_title'     => 'Login',
            'post_content'   => '',
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'comment_status' => 'closed',
            'ping_status'    => 'closed'
        ));

        if ($login_page_id && !is_wp_error($login_page_id)) {
            update_post_meta($login_page_id, '_wp_page_template', 'ad-login-template.php');
            $pages_created = true;
            error_log('AMP: Login page created with ID: ' . $login_page_id);
        }
    }

    $sale_page = get_page_by_path('sale-page');
    if (!$sale_page) {
        $sale_page_id = wp_insert_post(array(
            'post_title'     => 'Sale Page',
            'post_content'   => '',
            'post_status'    => 'publish',
            'post_type'      => 'page',
            'comment_status' => 'closed',
            'ping_status'    => 'closed'
        ));

        if ($sale_page_id && !is_wp_error($sale_page_id)) {
            update_post_meta($sale_page_id, '_wp_page_template', 'ad-sale-template.php');
            $pages_created = true;
            error_log('AMP: Sale page created with ID: ' . $sale_page_id);
        }
    }

    if ($pages_created) {
        flush_rewrite_rules();
        error_log('AMP: Rewrite rules flushed after page creation');
    }

    return ad_management_verify_pages_accessibility();
}

function ad_management_verify_pages_accessibility() {
    $results = array();

    $pages_to_check = array(
        'dashboard' => home_url('/dashboard/'),
        'login' => home_url('/login/'),
        'sale-page' => home_url('/sale-page/')
    );

    foreach ($pages_to_check as $page_name => $url) {
        $page = get_page_by_path($page_name);
        $results[$page_name] = array(
            'exists' => !empty($page),
            'id' => $page ? $page->ID : null,
            'url' => $url,
            'accessible' => false
        );

        if ($page) {
            $template_file = '';
            switch ($page_name) {
                case 'dashboard':
                    $template_file = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/dashboard.php';
                    break;
                case 'login':
                    $template_file = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/login/index.php';
                    break;
                case 'sale-page':
                    $template_file = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/sale-page.php';
                    break;
            }

            $results[$page_name]['accessible'] = file_exists($template_file);
            $results[$page_name]['template_file'] = $template_file;
        }
    }

    error_log('AMP: Page accessibility check results: ' . json_encode($results));
    return $results;
}

function ad_management_create_directories() {
    
    $public_dir = plugin_dir_path(dirname(dirname(__FILE__))) . 'public/tabs';
    if (!file_exists($public_dir)) {
        wp_mkdir_p($public_dir);
    }
}




