<?php

if (!defined('WPINC')) {
    die;
}

if (!function_exists('get_current_user_id')) {
    require_once(ABSPATH . 'wp-includes/pluggable.php');
}

class AMP_Bootstrap {
    
    private static $initialized = false;
    private static $core_instances = [];
    
    public static function init() {
        if (self::$initialized) {
            return;
        }
        
        self::load_core_classes();
        self::initialize_core_systems();
        self::register_hooks();
        self::setup_email_verification_cron();

        self::$initialized = true;
    }
    
    private static function load_core_classes() {
        $core_classes = array(
            'class-amp-options',
            'class-database',
            'class-utilities',
            'class-unified-security-manager',
            'class-data-manager',

            'class-ajax-handlers',
            'class-public-core-manager'
        );

        $plugin_classes = array(
            'class-plugin'
        );
        
        $additional_files = array(
            'page-templates',
            'roles'
        );

        foreach ($core_classes as $class) {
            $file_path = __DIR__ . '/core/' . $class . '.php';
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        foreach ($plugin_classes as $class) {
            $file_path = __DIR__ . '/core/' . $class . '.php';
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
        
        foreach ($additional_files as $file) {
            $file_path = __DIR__ . '/core/' . $file . '.php';
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        $payment_files = [
            __DIR__ . '/modules/payments/class-plisio-api.php',
            __DIR__ . '/modules/payments/class-payment-manager.php',
            __DIR__ . '/modules/payments/class-payment-handler.php'
        ];
        
        foreach ($payment_files as $payment_file) {
            if (file_exists($payment_file)) {
                try {
                    require_once $payment_file;
                } catch (\Exception $e) {
                }
            }
        }

        $utils = array(
            'google-analytics',
            'click-statistics'
        );

        foreach ($utils as $util) {
            $file_path = __DIR__ . '/utils/' . $util . '.php';
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        require_once AMP_PLUGIN_DIR . 'includes/core/class-encryption-manager.php';
        require_once AMP_PLUGIN_DIR . 'includes/core/class-ajax-handlers.php';
        require_once AMP_PLUGIN_DIR . 'includes/core/class-price-calculator.php';
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';
        require_once AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';
        require_once AMP_PLUGIN_DIR . 'includes/cache/cache-ajax-handlers.php';
        require_once AMP_PLUGIN_DIR . 'includes/cache/test-cache-system.php';

        if (defined('AMP_IS_ADMIN') && AMP_IS_ADMIN) {
            require_once AMP_PLUGIN_DIR . 'admin/admin-assets.php';
            \AdManagementPro\Admin\AdminAssets::instance();
        }

        require_once AMP_PLUGIN_DIR . 'includes/core/class-shortcodes.php';
        \AdManagementPro\Core\Shortcodes::instance();
    }
    
    private static function initialize_core_systems() {
        try {

            if (class_exists('AMP_Database')) {
                self::$core_instances['database'] = AMP_Database::instance();
            } elseif (class_exists('\AdManagementPro\Core\Database')) {
                self::$core_instances['database'] = \AdManagementPro\Core\Database::instance();
            }            
            if (class_exists('AMP_Cache_Manager')) {
                self::$core_instances['cache'] = \AMP_Cache_Manager::instance();
            } elseif (class_exists('AMP_Cache')) {
                self::$core_instances['cache'] = AMP_Cache::instance();
            }
            if (class_exists('AMP_Utilities')) {
                self::$core_instances['utilities'] = AMP_Utilities::instance();
            }           
            if (class_exists('\AdManagementPro\Core\UnifiedSecurityManager')) {
                try {
                    self::$core_instances['security'] = \AdManagementPro\Core\UnifiedSecurityManager::instance();
                } catch (\Exception $e) {
                    error_log('AMP Security Manager Error: ' . $e->getMessage());
                }
            }

            if (class_exists('AMP_Encryption_Manager')) {
                try {
                    self::$core_instances['encryption'] = \AMP_Encryption_Manager::instance();
                    if (current_user_can('manage_options')) {
                        self::$core_instances['encryption']->migrate_existing_secrets();
                    }
                } catch (\Exception $e) {
                    error_log('AMP Encryption Manager Error: ' . $e->getMessage());
                }
            }

            if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
                try {
                    self::$core_instances['ajax_handlers'] = \AdManagementPro\Core\AjaxHandlers::instance();
                } catch (\Exception $e) {
                    error_log('AMP Ajax Handlers Error: ' . $e->getMessage());
                }
            }

            if (self::should_load_public_manager()) {
                if (class_exists('\AdManagementPro\Core\PublicCoreManager')) {
                    try {
                        self::$core_instances['public_manager'] = \AdManagementPro\Core\PublicCoreManager::instance();
                    } catch (\Exception $e) {
                        error_log('AMP Public Core Manager Error: ' . $e->getMessage());
                    }
                }
            }
            
            if (class_exists('\AdManagementPro\Core\Plugin')) {
                try {
                    self::$core_instances['plugin'] = \AdManagementPro\Core\Plugin::instance();
                } catch (\Exception $e) {
                    error_log('AMP Plugin Error: ' . $e->getMessage());
                    self::$core_instances['plugin'] = null;
                }
            }

            if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
                try {
                    self::$core_instances['ajax'] = \AdManagementPro\Core\AjaxHandlers::instance();
                } catch (\Exception $e) {
                    error_log('AMP AJAX Handlers Error: ' . $e->getMessage());
                }
            } else {
                $ajax_file = AMP_PLUGIN_DIR . 'includes/core/class-ajax-handlers.php';
                if (file_exists($ajax_file)) {
                    require_once $ajax_file;
                    if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
                        try {
                            self::$core_instances['ajax'] = \AdManagementPro\Core\AjaxHandlers::instance();
                        } catch (\Exception $e) {
                            error_log('AMP Bootstrap: Manual AjaxHandlers init failed: ' . $e->getMessage());
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            error_log('AMP Bootstrap Error: ' . $e->getMessage());
        }
    }
    
    private static function should_load_public_manager() {
        if (current_user_can('manage_options')) {
            return wp_doing_ajax() || (defined('DOING_AJAX') && DOING_AJAX);
        }
        return true;
    }
    
    private static function register_hooks() {
        if (!has_action('init', array(__CLASS__, 'load_text_domain'))) {
            add_action('init', array(__CLASS__, 'load_text_domain'));
        }
        if (!has_action('init', array(__CLASS__, 'force_ajax_registration'))) {
            add_action('init', array(__CLASS__, 'force_ajax_registration'), 5);
        }

        if (!has_action('init', array(__CLASS__, 'setup_email_verification_cron'))) {
            add_action('init', array(__CLASS__, 'setup_email_verification_cron'));
        }

        if (!has_action('init', array(__CLASS__, 'setup_rewrite_rules'))) {
            add_action('init', array(__CLASS__, 'setup_rewrite_rules'));
        }

        if (!has_action('amp_cleanup_unverified_users', array(__CLASS__, 'run_cleanup_unverified_users'))) {
            add_action('amp_cleanup_unverified_users', array(__CLASS__, 'run_cleanup_unverified_users'));
        }

        if (!has_action('amp_cleanup_expired_password_reset_tokens', array(__CLASS__, 'run_cleanup_expired_password_reset_tokens'))) {
            add_action('amp_cleanup_expired_password_reset_tokens', array(__CLASS__, 'run_cleanup_expired_password_reset_tokens'));
        }

        if (!has_action('amp_cleanup_expired_sessions', array(__CLASS__, 'run_cleanup_expired_sessions'))) {
            add_action('amp_cleanup_expired_sessions', array(__CLASS__, 'run_cleanup_expired_sessions'));
        }

    }

    public static function force_ajax_registration() {
        if (!isset(self::$core_instances['ajax'])) {
            if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
                try {
                    self::$core_instances['ajax'] = \AdManagementPro\Core\AjaxHandlers::instance();
                } catch (\Exception $e) {
                    error_log('AMP Bootstrap: Force AJAX init failed: ' . $e->getMessage());
                }
            }
        }
    }

    public static function load_text_domain() {
        load_plugin_textdomain(
            'ad-management-pro',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages/'
        );
    }

    public static function get_plugin_instance() {
        return isset(self::$core_instances['plugin']) ? self::$core_instances['plugin'] : null;
    }
    
    public static function get_instance($class_name) {
        return isset(self::$core_instances[$class_name]) ? self::$core_instances[$class_name] : null;
    }

    public static function is_initialized() {
        return self::$initialized;
    }

    public static function setup_email_verification_cron() {
        if (!wp_next_scheduled('amp_cleanup_unverified_users')) {
            wp_schedule_event(time(), 'hourly', 'amp_cleanup_unverified_users');
        }

        if (!wp_next_scheduled('amp_cleanup_expired_password_reset_tokens')) {
            wp_schedule_event(time(), 'hourly', 'amp_cleanup_expired_password_reset_tokens');
        }

        if (!wp_next_scheduled('amp_cleanup_expired_sessions')) {
            wp_schedule_event(time(), 'hourly', 'amp_cleanup_expired_sessions');
        }
    }

    public static function run_cleanup_unverified_users() {
        if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
            \AdManagementPro\Core\AjaxHandlers::cleanup_unverified_users();
        }
    }

    public static function run_cleanup_expired_password_reset_tokens() {
        if (class_exists('\AdManagementPro\Core\AjaxHandlers')) {
            \AdManagementPro\Core\AjaxHandlers::cleanup_expired_password_reset_tokens();
        }
    }

    public static function run_cleanup_expired_sessions() {
        if (class_exists('\AdManagementPro\Core\UnifiedSecurityManager')) {
            $security_manager = \AdManagementPro\Core\UnifiedSecurityManager::instance();
            if ($security_manager) {
                $security_manager->cleanup_expired_sessions();
            }
        }
    }

    public static function setup_rewrite_rules() {
        add_rewrite_rule('^sale-page/?$', 'index.php?pagename=sale-page', 'top');
        add_rewrite_rule('^sale-page/(.+)/?$', 'index.php?pagename=sale-page&position=$matches[1]', 'top');
    }
}

AMP_Bootstrap::init();