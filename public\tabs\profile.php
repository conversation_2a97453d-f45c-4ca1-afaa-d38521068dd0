<?php

if (!defined('ABSPATH')) {
    exit;
}

$current_user = wp_get_current_user();
$phone = get_user_meta($current_user->ID, 'phone', true);
$telegram = get_user_meta($current_user->ID, 'telegram', true);
$facebook = get_user_meta($current_user->ID, 'facebook', true);

$is_google_user = get_user_meta($current_user->ID, '_amp_is_google_user', true);
$has_password_set = !empty($current_user->user_pass) && strlen($current_user->user_pass) > 20;
$requires_current_password = $has_password_set && !$is_google_user;

$unified_nonce = wp_create_nonce('amp_dashboard_action');
?>

<div class="profile-container">
    <div class="profile-header">
        <h2>👤 โปรไฟล์ของฉัน</h2>
        <p>จัดการข้อมูลส่วนตัวและการตั้งค่าบัญชีของคุณ</p>
    </div>

    <div class="profile-content">
        <div class="profile-card profile-info-card">
            <div class="profile-card-header">
                <h3><i class="fas fa-user-edit"></i> ข้อมูลส่วนตัว</h3>
                <p>อัปเดตข้อมูลส่วนตัวและข้อมูลติดต่อของคุณ</p>
            </div>

            <div class="profile-avatar-section">
                <div class="profile-avatar clickable-avatar">
                    <?php
                    $custom_avatar_id = get_user_meta($current_user->ID, 'custom_avatar', true);
                    if ($custom_avatar_id) {
                        $avatar_url = wp_get_attachment_image_url($custom_avatar_id, 'thumbnail');
                        if ($avatar_url) {
                            echo '<img src="' . esc_url($avatar_url) . '" alt="' . esc_attr($current_user->display_name) . '">';
                        } else {
                            echo get_avatar($current_user->ID, 120);
                        }
                    } else {
                        echo '<div class="avatar-placeholder"><i class="fas fa-user"></i><span>คลิกเพื่ออัพโหลดรูปภาพ</span></div>';
                    }
                    ?>
                    <div class="avatar-overlay">
                        <i class="fas fa-camera"></i>
                        <span>เปลี่ยนรูป</span>
                    </div>
                </div>
                <div class="profile-avatar-info">
                    <h4><?php echo esc_html($current_user->display_name); ?></h4>
                    <p><?php echo esc_html($current_user->user_email); ?></p>
                    <span class="user-role">ผู้โฆษณา</span>
                </div>
            </div>

            <div id="profile-message" class="profile-message" style="display: none;"></div>

            <form id="profile-form" method="post" class="profile-form">
                <input type="hidden" name="security" value="<?php echo esc_attr($unified_nonce); ?>">

                <div class="profile-form-grid">
                    <div class="profile-form-row">
                        <div class="profile-form-field">
                            <label for="username"><i class="fas fa-user-circle"></i> ชื่อผู้ใช้ (Username)</label>
                            <input type="text" id="username" name="username" value="<?php echo esc_attr($current_user->user_login); ?>">
                            <p class="field-description">ใช้สำหรับเข้าสู่ระบบ</p>
                        </div>
                        <div class="profile-form-field">
                            <label for="display_name"><i class="fas fa-user"></i> ชื่อที่แสดง</label>
                            <input type="text" id="display_name" name="display_name" value="<?php echo esc_attr($current_user->display_name); ?>" required>
                             <p class="field-description">ชื่อที่จะแสดงบนเว็บไซต์</p>
                        </div>
                    </div>

                    <div class="profile-form-row">
                        <div class="profile-form-field">
                            <label for="first_name"><i class="fas fa-id-card"></i> ชื่อจริง</label>
                            <input type="text" id="first_name" name="first_name" value="<?php echo esc_attr($current_user->first_name); ?>">
                        </div>
                        <div class="profile-form-field">
                            <label for="last_name"><i class="fas fa-id-card"></i> นามสกุล</label>
                            <input type="text" id="last_name" name="last_name" value="<?php echo esc_attr($current_user->last_name); ?>">
                        </div>
                    </div>

                    <div class="profile-form-row">
                        <div class="profile-form-field">
                            <label for="email"><i class="fas fa-envelope"></i> อีเมล</label>
                            <input type="email" id="email" name="email" value="<?php echo esc_attr($current_user->user_email); ?>" required>
                        </div>
                        <div class="profile-form-field">
                            <label for="phone"><i class="fas fa-phone"></i> เบอร์โทรศัพท์</label>
                            <input type="tel" id="phone" name="phone" value="<?php echo esc_attr($phone); ?>" placeholder="08X-XXX-XXXX">
                        </div>
                    </div>

                    <div class="profile-form-row">
                        <div class="profile-form-field">
                            <label for="telegram"><i class="fab fa-telegram"></i> Telegram</label>
                            <input type="text" id="telegram" name="telegram" value="<?php echo esc_attr($telegram); ?>" placeholder="@username">
                        </div>
                        <div class="profile-form-field">
                            <label for="facebook"><i class="fab fa-facebook"></i> Facebook</label>
                            <input type="text" id="facebook" name="facebook" value="<?php echo esc_attr($facebook); ?>" placeholder="URL โปรไฟล์ Facebook">
                        </div>
                    </div>
                </div>

                <div class="profile-form-actions">
                    <button type="submit" name="update_profile" class="profile-save-btn">
                        <i class="fas fa-save"></i> บันทึกการเปลี่ยนแปลง
                    </button>
                </div>
            </form>
        </div>

        <div class="profile-card">
            <div class="profile-card-header">
                <h3><i class="fas fa-key"></i> <?php echo $requires_current_password ? 'เปลี่ยนรหัสผ่าน' : 'ตั้งรหัสผ่าน'; ?></h3>
                <p><?php echo $requires_current_password ? 'อัปเดตรหัสผ่านเพื่อความปลอดภัยของบัญชี' : 'ตั้งรหัสผ่านใหม่สำหรับบัญชีของคุณ'; ?></p>
            </div>

            <div id="password-message" class="profile-message" style="display: none;"></div>

            <form id="password-form" method="post" class="profile-form">
                <input type="hidden" name="security" value="<?php echo esc_attr($unified_nonce); ?>">

                <?php if ($requires_current_password): ?>
                <div class="profile-form-field">
                    <label for="current_password"><i class="fas fa-lock"></i> รหัสผ่านปัจจุบัน</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>
                <?php else: ?>
                <div class="profile-info-notice">
                    <i class="fas fa-info-circle"></i>
                    <p>คุณยังไม่ได้ตั้งรหัสผ่าน (เช่น เข้าสู่ระบบผ่าน Google) กรุณาตั้งรหัสผ่านใหม่เพื่อความสะดวกในการเข้าสู่ระบบครั้งถัดไป</p>
                </div>
                <?php endif; ?>

                <div class="profile-form-field">
                    <label for="new_password"><i class="fas fa-key"></i> รหัสผ่านใหม่</label>
                    <input type="password" id="new_password" name="new_password" required>
                    <div class="password-strength-meter">
                        <div class="password-strength-bar"></div>
                    </div>
                    <p class="field-description">รหัสผ่านควรมีความยาวอย่างน้อย 8 ตัวอักษร</p>
                </div>

                <div class="profile-form-field">
                    <label for="confirm_password"><i class="fas fa-check-circle"></i> ยืนยันรหัสผ่านใหม่</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>

                <div class="profile-form-actions">
                    <button type="submit" name="update_password" class="profile-save-btn password-btn">
                         <i class="fas fa-key"></i> <?php echo $requires_current_password ? 'เปลี่ยนรหัสผ่าน' : 'ตั้งรหัสผ่าน'; ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
if (typeof jQuery !== 'undefined') {
    jQuery(document).ready(function($) {  
        function displayMessage(container, message, isSuccess) {
            container.text(message).removeClass('success error').addClass(isSuccess ? 'success' : 'error').slideDown();
            setTimeout(() => container.slideUp(), 5000);
        }
        $('#new_password').on('input', function() {
            const password = $(this).val();
            const strengthBar = $('.password-strength-bar');
            let strength = 0;
            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            strengthBar.removeClass('weak medium strong very-strong');          
            if (strength <= 1) {
                strengthBar.addClass('weak');
            } else if (strength <= 2) {
                strengthBar.addClass('medium');
            } else if (strength <= 3) {
                strengthBar.addClass('strong');
            } else {
                strengthBar.addClass('very-strong');
            }
        });

        function initLocalProfileHandlers() {
            $('#profile-form').off('submit.profile').on('submit.profile', function(e) {
                e.preventDefault();

                const formData = $(this).serialize() + '&action=update_profile';
                const $button = $(this).find('button[type="submit"]');
                const originalText = $button.html();
                const $messageContainer = $('#profile-message');

                $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');
                $messageContainer.hide();

                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $messageContainer.removeClass('error').addClass('success')
                                .html('<i class="fas fa-check-circle"></i> บันทึกข้อมูลเรียบร้อยแล้ว')
                                .slideDown();

                            if (typeof window.showMiniPopup === 'function') {
                                window.showMiniPopup('บันทึกข้อมูลเรียบร้อยแล้ว', 'success');
                            }

                            setTimeout(() => {
                                $messageContainer.slideUp();
                            }, 5000);
                        } else {
                            $messageContainer.removeClass('success').addClass('error')
                                .html('<i class="fas fa-exclamation-circle"></i> ' + (response.data.message || 'เกิดข้อผิดพลาด'))
                                .slideDown();

                            if (typeof window.showMiniPopup === 'function') {
                                window.showMiniPopup(response.data.message || 'เกิดข้อผิดพลาด', 'error');
                            }
                        }
                    },
                    error: function() {
                        $messageContainer.removeClass('success').addClass('error')
                            .html('<i class="fas fa-exclamation-circle"></i> ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้')
                            .slideDown();

                        if (typeof window.showMiniPopup === 'function') {
                            window.showMiniPopup('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้', 'error');
                        }
                    },
                    complete: function() {
                        $button.prop('disabled', false).html(originalText);
                    }
                });
            });

    
            $('#password-form').off('submit.password').on('submit.password', function(e) {
                e.preventDefault();

                const formData = $(this).serialize() + '&action=update_password';
                const $button = $(this).find('button[type="submit"]');
                const originalText = $button.html();
                const $messageContainer = $('#password-message');

                $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังเปลี่ยน...');
                $messageContainer.hide();

                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            $messageContainer.removeClass('error').addClass('success')
                                .html('<i class="fas fa-check-circle"></i> เปลี่ยนรหัสผ่านเรียบร้อยแล้ว')
                                .slideDown();

                            if (typeof window.showMiniPopup === 'function') {
                                window.showMiniPopup('เปลี่ยนรหัสผ่านเรียบร้อยแล้ว', 'success');
                            }

                            $('#password-form')[0].reset();

                            setTimeout(() => {
                                $messageContainer.slideUp();
                            }, 5000);
                        } else {
                            $messageContainer.removeClass('success').addClass('error')
                                .html('<i class="fas fa-exclamation-circle"></i> ' + (response.data.message || 'เกิดข้อผิดพลาด'))
                                .slideDown();

                            if (typeof window.showMiniPopup === 'function') {
                                window.showMiniPopup(response.data.message || 'เกิดข้อผิดพลาด', 'error');
                            }
                        }
                    },
                    error: function() {
                        $messageContainer.removeClass('success').addClass('error')
                            .html('<i class="fas fa-exclamation-circle"></i> ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้')
                            .slideDown();

                        if (typeof window.showMiniPopup === 'function') {
                            window.showMiniPopup('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้', 'error');
                        }
                    },
                    complete: function() {
                        $button.prop('disabled', false).html(originalText);
                    }
                });
            });

    
            $('.clickable-avatar, .profile-avatar').off('click.avatar').on('click.avatar', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const fileInput = $('<input type="file" accept="image/*" style="display: none;">');
                $('body').append(fileInput);

                fileInput.on('change', function() {
                    const file = this.files[0];
                    if (file) {
                        if (typeof window.uploadAvatar === 'function') {
                            window.uploadAvatar(file);
                        }
                    }
                    $(this).remove();
                });

                fileInput.click();
            });
        }


        initLocalProfileHandlers();


        if (typeof window.initProfileFormHandlers === 'function') {
            window.initProfileFormHandlers();
        }


        window.initLocalProfileHandlers = initLocalProfileHandlers;
    });
}
</script>

<style>
.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 20px;
}

.profile-header {
    margin-bottom: 40px;
    text-align: center;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.03), rgba(114, 9, 183, 0.03));
    border-radius: 24px;
    padding: 40px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(67, 97, 238, 0.1);
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.05) 75%);
    background-size: 30px 30px;
    opacity: 0.3;
    pointer-events: none;
}

.profile-header h2 {
    font-size: 32px;
    margin-bottom: 15px;
    color: var(--text-color);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.profile-header p {
    color: var(--light-text);
    font-size: 18px;
    font-weight: 500;
    position: relative;
    z-index: 1;
    margin: 0;
}

.profile-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.profile-card {
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.02));
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(67, 97, 238, 0.1);
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.profile-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.1), rgba(114, 9, 183, 0.1));
    opacity: 0;
    transition: opacity 0.4s ease;
    border-radius: 18px;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(67, 97, 238, 0.15), 0 0 0 2px var(--primary-color);
    border-color: var(--primary-color);
}

.profile-card:hover::before {
    opacity: 1;
}

.profile-card-header {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
    background: rgba(67, 97, 238, 0.03);
    position: relative;
    z-index: 2;
}

.profile-card-header h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-card-header p {
    margin: 0;
    font-size: 14px;
    color: var(--light-text);
    line-height: 1.5;
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 30px;
    border-bottom: 1px solid var(--border-color);
    gap: 20px;
    position: relative;
    z-index: 2;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    flex-shrink: 0;
    margin: 0 auto;
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(67, 97, 238, 0.4);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--input-bg), rgba(67, 97, 238, 0.05));
    color: var(--light-text);
}

.avatar-placeholder i {
    font-size: 40px;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.avatar-placeholder span {
    font-size: 11px;
    text-align: center;
    padding: 0 5px;
    line-height: 1.2;
    font-weight: 500;
}

.avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    font-size: 12px;
    font-weight: 600;
}

.profile-avatar:hover .avatar-overlay {
    opacity: 1;
}

.avatar-overlay i {
    font-size: 20px;
    margin-bottom: 5px;
}

.profile-avatar-info {
    text-align: center;
}

.profile-avatar-info h4 {
    margin: 0 0 8px 0;
    font-size: 24px;
    color: var(--text-color);
    font-weight: 700;
}

.profile-avatar-info p {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: var(--light-text);
}

.user-role {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-message {
    padding: 16px 20px;
    margin: 20px 25px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    z-index: 2;
    display: none;
    animation: slideInDown 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.profile-message.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border-left: 4px solid #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.profile-message.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
    border-left: 4px solid #dc3545;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.profile-message i {
    margin-right: 8px;
    font-size: 16px;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.profile-form {
    padding: 25px;
    position: relative;
    z-index: 2;
}

.profile-form-grid {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 30px;
}

.profile-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
}

.profile-form-field {
    margin-bottom: 0;
}

.profile-form-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--text-color);
    font-weight: 600;
}

.profile-form-field label i {
    color: var(--primary-color);
    width: 16px;
}

.profile-form-field input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 14px;
    background: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.profile-form-field input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    outline: none;
    background: rgba(67, 97, 238, 0.02);
}

.field-description {
    font-size: 12px;
    color: var(--light-text);
    margin-top: 6px;
    margin-bottom: 0;
    line-height: 1.4;
}

.password-strength-meter {
    height: 6px;
    background: var(--border-color);
    margin-top: 10px;
    border-radius: 3px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    background: #e74c3c;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.password-strength-bar.weak {
    width: 25%;
    background: #e74c3c;
}

.password-strength-bar.medium {
    width: 50%;
    background: #f39c12;
}

.password-strength-bar.strong {
    width: 75%;
    background: #3498db;
}

.password-strength-bar.very-strong {
    width: 100%;
    background: #27ae60;
}

.profile-form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.profile-save-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.profile-save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
    background: linear-gradient(135deg, var(--primary-hover), var(--secondary-color));
}

.profile-save-btn:active {
    transform: translateY(0);
}

.profile-save-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.password-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.password-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

.profile-info-notice {
    background: linear-gradient(135deg, #e8f4fd, #d1ecf1);
    border: 1px solid #bee5eb;
    border-left: 4px solid #17a2b8;
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    box-shadow: 0 2px 10px rgba(23, 162, 184, 0.1);
}

.profile-info-notice i {
    color: #17a2b8;
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.profile-info-notice p {
    margin: 0;
    color: #0c5460;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

@media (max-width: 768px) {
    .profile-container {
        padding: 15px;
    }
    
    .profile-header {
        padding: 25px;
    }
    
    .profile-header h2 {
        font-size: 24px;
    }
    
    .profile-form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .profile-avatar-section {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .profile-card-header,
    .profile-form {
        padding: 20px;
    }
}

.profile-form-field .fa-facebook {
    color: #1877F2;
}
</style>
