<?php

if (!defined('WPINC')) {
    die;
}

function display_security_settings_content() {
    if (!current_user_can('manage_options')) {
        wp_die('Administrator access required for security settings');
    }

    $notice = '';

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $turnstile_site_key = $encryption_manager->get_secret('amp_turnstile_site_key');
    $turnstile_secret_key = $encryption_manager->get_secret('amp_turnstile_secret_key');
    $turnstile_enabled = get_option('amp_turnstile_enabled', 0);
    $turnstile_login_required = get_option('amp_turnstile_login_required', 1);

    $max_login_attempts = get_option('amp_max_login_attempts', 5);
    $account_lock_duration = get_option('amp_account_lock_duration', 15);
    $session_timeout = get_option('amp_session_timeout', 60);
    $password_min_length = get_option('amp_password_min_length', 8);
    $password_complexity = get_option('amp_password_complexity', 1);
    $email_verification_enabled = get_option('amp_email_verification_enabled', 0);

    $login_activity_logging = get_option('amp_login_activity_logging', 1);
    $login_activity_logs_retention = get_option('amp_login_activity_logs_retention', 30);

    $csp_enabled = get_option('amp_csp_enabled', 1);
    $csp_seo_mode = get_option('amp_csp_seo_mode', 1);

    $security_nonce = wp_create_nonce('save_security_settings');

    ?>
        <?php echo $notice; ?>

        <form method="post" action="">
            <input type="hidden" name="security_settings_nonce" value="<?php echo esc_attr($security_nonce); ?>">

            <div class="plisio-settings-grid">
                <div class="plisio-card">
                    <h3>🛡️ การตั้งค่า Content Security Policy (CSP) - หลังบ้านเท่านั้น</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="csp_enabled">เปิดใช้งาน CSP</label></th>
                            <td>
                                <label>
                                    <input type="checkbox" id="csp_enabled" name="csp_enabled" <?php checked($csp_enabled, 1); ?>>
                                    เปิดใช้งาน Content Security Policy สำหรับระบบหลังบ้านเท่านั้น
                                </label>
                                <p class="description">
                                    <strong>⚠️ สำคัญ:</strong> CSP จะทำงานเฉพาะใน <strong>/wp-admin/</strong> และ <strong>/dashboard/</strong> เท่านั้น<br>
                                    <strong>✅ ปลอดภัย:</strong> ไม่มีผลกระทบต่อหน้าบ้าน (frontend) และ SEO ของเว็บไซต์
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="csp_seo_mode">โหมดรองรับ Admin Plugins</label></th>
                            <td>
                                <label>
                                    <input type="checkbox" id="csp_seo_mode" name="csp_seo_mode" <?php checked($csp_seo_mode, 1); ?>>
                                    เปิดใช้งานโหมดที่รองรับ plugins ในหน้า wp-admin
                                </label>
                                <p class="description">
                                    รองรับ plugins ที่ทำงานในหน้า wp-admin เช่น SEO plugins, Performance plugins
                                </p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">ขอบเขตการทำงาน</th>
                            <td>
                                <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; border-left: 4px solid #4caf50;">
                                    <strong>✅ CSP ทำงานใน:</strong><br>
                                    • <code>/wp-admin/</code> - หน้า WordPress Admin<br>
                                    • <code>/dashboard/</code> - หน้า Dashboard ของ Plugin<br>
                                    • <code>/login/</code> - หน้า Login ของ Plugin<br><br>

                                    <strong>❌ CSP ไม่ทำงานใน:</strong><br>
                                    • หน้าแรกของเว็บไซต์<br>
                                    • หน้าโพสต์และหน้าต่างๆ<br>
                                    • Template และ Theme files<br>
                                    • Frontend ทั้งหมด
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Plugins ที่รองรับใน wp-admin</th>
                            <td>
                                <div style="background: #f9f9f9; padding: 15px; border-radius: 5px;">
                                    <strong>🔍 SEO Plugins:</strong><br>
                                    • Yoast SEO / Yoast SEO Premium<br>
                                    • RankMath / RankMath Pro<br>
                                    • All in One SEO Pack / Pro<br>
                                    • The SEO Framework<br>
                                    • SEOPress / SEOPress Pro<br>
                                    • Squirrly SEO<br><br>

                                    <strong>⚡ Performance Plugins:</strong><br>
                                    • LiteSpeed Cache<br>
                                    • Perfmatters<br>
                                    • WP Rocket<br>
                                    • W3 Total Cache<br>
                                    • WP Fastest Cache<br>
                                    • Autoptimize<br>
                                    • WP Super Cache
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="plisio-card">
                    <h3>การตั้งค่า Cloudflare Turnstile</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="turnstile_enabled">เปิดใช้งาน Turnstile</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="turnstile_enabled" name="turnstile_enabled" <?php checked(get_option('amp_turnstile_enabled', 0), 1); ?>>
                                เปิดใช้งาน Cloudflare Turnstile สำหรับฟอร์มเข้าสู่ระบบและลงทะเบียน
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="turnstile_login_required">บังคับใช้ Turnstile สำหรับการเข้าสู่ระบบ</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="turnstile_login_required" name="turnstile_login_required" <?php checked(get_option('amp_turnstile_login_required', 1), 1); ?>>
                                บังคับให้ผู้ใช้ต้องผ่านการยืนยัน Turnstile ก่อนเข้าสู่ระบบ
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="turnstile_site_key">Site Key</label></th>
                        <td>
                            <?php
                            $encryption_manager = \AMP_Encryption_Manager::instance();
                            $has_site_key = !empty($encryption_manager->get_secret('amp_turnstile_site_key'));
                            if ($has_site_key): ?>
                                <div class="secret-key-display">
                                    <span class="secret-placeholder">••••••••••••••••••••••••••••••••</span>
                                    <button type="button" class="button button-secondary delete-secret-btn" data-secret="amp_turnstile_site_key" data-name="Turnstile Site Key">
                                        🗑️ ลบ
                                    </button>
                                </div>
                            <?php else: ?>
                                <input type="text" id="turnstile_site_key" name="turnstile_site_key" value="" class="regular-text" placeholder="กรอก Turnstile Site Key">
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="turnstile_secret_key">Secret Key</label></th>
                        <td>
                            <?php
                            $has_secret_key = !empty($encryption_manager->get_secret('amp_turnstile_secret_key'));
                            if ($has_secret_key): ?>
                                <div class="secret-key-display">
                                    <span class="secret-placeholder">••••••••••••••••••••••••••••••••</span>
                                    <button type="button" class="button button-secondary delete-secret-btn" data-secret="amp_turnstile_secret_key" data-name="Turnstile Secret Key">
                                        🗑️ ลบ
                                    </button>
                                </div>
                            <?php else: ?>
                                <input type="password" id="turnstile_secret_key" name="turnstile_secret_key" value="" class="regular-text" placeholder="กรอก Turnstile Secret Key">
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="turnstile_test">ทดสอบการตั้งค่า Turnstile</label></th>
                        <td>
                            <button type="button" id="test_turnstile" class="ad-btn ad-btn-secondary">ทดสอบ Turnstile</button>
                            <span id="turnstile_test_result" style="margin-left: 10px;"></span>
                            <script>
                                jQuery(document).ready(function($) {
                                    $('#test_turnstile').on('click', function() {
                                        var siteKey = $('#turnstile_site_key').val() || 'USE_STORED_ENCRYPTED';
                                        var secretKey = $('#turnstile_secret_key').val() || 'USE_STORED_ENCRYPTED';

                                        $('#turnstile_test_result').html('<span style="color: blue;">กำลังทดสอบ...</span>');
                                        $.ajax({
                                            url: ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'amp_test_turnstile',
                                                site_key: siteKey,
                                                secret_key: secretKey,
                                                nonce: '<?php echo esc_js(wp_create_nonce('amp_test_turnstile')); ?>'
                                            },
                                            success: function(response) {
                                                if (response.success) {
                                                    $('#turnstile_test_result').html('<span style="color: green;">สำเร็จ! Turnstile ถูกตั้งค่าอย่างถูกต้อง</span>');
                                                } else {
                                                    var errorMessage = (response.data && response.data.message) ? response.data.message : 'การทดสอบล้มเหลว';
                                                    $('#turnstile_test_result').html('<span style="color: red;">ข้อผิดพลาด: ' + errorMessage + '</span>');
                                                }
                                            },
                                            error: function() {
                                                $('#turnstile_test_result').html('<span style="color: red;">ข้อผิดพลาด: ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้</span>');
                                            }
                                        });
                                    });
                                });
                            </script>
                        </td>
                    </tr>
                </table>
                </div>

                <div class="plisio-card">
                    <h3>ความปลอดภัยในการเข้าสู่ระบบ</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="email_verification_enabled">บังคับยืนยันอีเมล</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="email_verification_enabled" name="email_verification_enabled" <?php checked($email_verification_enabled, 1); ?>>
                                บังคับให้ผู้ใช้ใหม่ต้องยืนยันอีเมลก่อนเข้าสู่ระบบ (ลบบัญชีอัตโนมัติหากไม่ยืนยันภายใน 24 ชั่วโมง)
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="max_login_attempts">จำนวนครั้งสูงสุดที่ล็อกอินผิดพลาด</label></th>
                        <td>
                            <input type="number" id="max_login_attempts" name="max_login_attempts" value="<?php echo esc_attr($max_login_attempts); ?>" min="1" max="10" class="small-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="account_lock_duration">ระยะเวลาล็อกบัญชี</label></th>
                        <td>
                            <input type="number" id="account_lock_duration" name="account_lock_duration" value="<?php echo esc_attr($account_lock_duration); ?>" min="1" class="small-text">
                            <span>นาที</span>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="session_timeout">ระยะเวลาหมดอายุของเซสชัน</label></th>
                        <td>
                            <input type="number" id="session_timeout" name="session_timeout" value="<?php echo esc_attr($session_timeout); ?>" min="1" class="small-text">
                            <span>นาที</span>
                            <p class="description">
                                ผู้ใช้จะถูกออกจากระบบอัตโนมัติเมื่อไม่มีกิจกรรมเกินเวลาที่กำหนด<br>
                                <strong>สถานะปัจจุบัน:</strong> ระบบจะแจ้งเตือนเมื่อเหลือเวลา 5 นาที และออกจากระบบอัตโนมัติเมื่อหมดเวลา
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="password_min_length">ความยาวรหัสผ่านขั้นต่ำ</label></th>
                        <td>
                            <input type="number" id="password_min_length" name="password_min_length" value="<?php echo esc_attr($password_min_length); ?>" min="8" class="small-text">
                            <span>ตัวอักษร</span>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="password_complexity">ความซับซ้อนของรหัสผ่าน</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="password_complexity" name="password_complexity" <?php checked($password_complexity, 1); ?>>
                                ต้องการรหัสผ่านที่ซับซ้อน (ตัวพิมพ์ใหญ่, ตัวพิมพ์เล็ก, ตัวเลข และอักขระพิเศษ)
                            </label>
                        </td>
                    </tr>

                </table>
                </div>

                <div class="plisio-card">
                    <h3>การบันทึกกิจกรรม</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="login_activity_logging">การบันทึกกิจกรรมการเข้าสู่ระบบ</label></th>
                        <td>
                            <label>
                                <input type="checkbox" id="login_activity_logging" name="login_activity_logging" <?php checked($login_activity_logging, 1); ?>>
                                เปิดใช้งานการบันทึกความพยายามในการเข้าสู่ระบบและเหตุการณ์ด้านความปลอดภัย
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="login_activity_logs_retention">ระยะเวลาการเก็บบันทึก</label></th>
                        <td>
                            <input type="number" id="login_activity_logs_retention" name="login_activity_logs_retention" value="<?php echo esc_attr($login_activity_logs_retention); ?>" min="1" class="small-text">
                            <span>วัน</span>
                        </td>
                    </tr>
                </table>
                </div>

                <div class="plisio-card">
                    <h3>🔐 ระบบเข้ารหัสข้อมูลลับ</h3>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label>ทดสอบระบบเข้ารหัส</label></th>
                            <td>
                                <button type="button" id="test_encryption_system" class="ad-btn ad-btn-secondary">🧪 ทดสอบการเข้ารหัส/ถอดรหัส</button>
                                <span id="encryption_test_result" style="margin-left: 10px;"></span>
                                <p class="description">ทดสอบการทำงานของระบบเข้ารหัสและถอดรหัสข้อมูลลับ</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label>ตรวจสอบข้อมูลลับทั้งหมด</label></th>
                            <td>
                                <button type="button" id="verify_all_secrets" class="ad-btn ad-btn-secondary">🔍 ตรวจสอบสถานะข้อมูลลับ</button>
                                <span id="verify_secrets_result" style="margin-left: 10px;"></span>
                                <div id="secrets_status_table" style="margin-top: 10px;"></div>
                                <p class="description">ตรวจสอบสถานะการเข้ารหัสของข้อมูลลับทั้งหมดในระบบ</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label>สถิติระบบเข้ารหัส</label></th>
                            <td>
                                <button type="button" id="get_encryption_stats" class="ad-btn ad-btn-secondary">📊 ดูสถิติระบบเข้ารหัส</button>
                                <span id="encryption_stats_result" style="margin-left: 10px;"></span>
                                <div id="encryption_stats_display" style="margin-top: 10px;"></div>
                                <p class="description">แสดงสถิติและข้อมูลเกี่ยวกับระบบเข้ารหัสข้อมูลลับ</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label>สำรองข้อมูลลับ</label></th>
                            <td>
                                <button type="button" id="backup_secrets" class="ad-btn ad-btn-warning">💾 สำรองข้อมูลลับ</button>
                                <span id="backup_secrets_result" style="margin-left: 10px;"></span>
                                <p class="description">สำรองข้อมูลลับทั้งหมดในรูปแบบ JSON (เฉพาะผู้ดูแลระบบเท่านั้น)</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <p class="submit">
                <button type="submit" name="submit_security_settings" id="save-security-settings" class="ad-btn ad-btn-primary">บันทึกการตั้งค่า</button>
                <span id="security-settings-status"></span>
            </p>
        </form>

    <link rel="stylesheet" href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-unified.css?v=' . filemtime(plugin_dir_path(dirname(__FILE__)) . 'assets/css/admin-unified.css'); ?>" type="text/css">

    <style>
        .secret-key-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .secret-placeholder {
            font-family: monospace;
            background: #f1f1f1;
            padding: 8px 12px;
            border-radius: 4px;
            color: #666;
            border: 1px solid #ddd;
            min-width: 200px;
        }
        .delete-secret-btn {
            background: #dc3545 !important;
            color: white !important;
            border: none !important;
            padding: 6px 12px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 12px !important;
        }
        .delete-secret-btn:hover {
            background: #c82333 !important;
        }
        .ad-btn-warning {
            background: #ffc107 !important;
            color: #212529 !important;
            border: none !important;
            padding: 8px 16px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 14px !important;
        }
        .ad-btn-warning:hover {
            background: #e0a800 !important;
        }
        @media (max-width: 768px) {
            .secret-key-display {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>

    <script>
    jQuery(document).ready(function($) {
        $('.delete-secret-btn').off('click.security-settings').on('click.security-settings', function(e) {
            e.preventDefault();

            const secretKey = $(this).data('secret');
            const secretName = $(this).data('name');
            const button = $(this);

            Swal.fire({
                title: 'ยืนยันการลบ',
                text: `คุณต้องการลบ ${secretName} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: '🗑️ ลบ',
                cancelButtonText: '❌ ยกเลิก',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    button.prop('disabled', true).html('🔄 กำลังลบ...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_secret_key',
                            nonce: '<?php echo wp_create_nonce('delete_secret_key'); ?>',
                            secret_key: secretKey
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'สำเร็จ!',
                                    text: 'ลบ Secret Key เรียบร้อยแล้ว',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'เกิดข้อผิดพลาด!',
                                    text: response.data.message || 'ไม่สามารถลบได้',
                                    icon: 'error'
                                });
                                button.prop('disabled', false).html('🗑️ ลบ');
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด!',
                                text: 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
                                icon: 'error'
                            });
                            button.prop('disabled', false).html('🗑️ ลบ');
                        }
                    });
                }
            });
        });

        $('#test_encryption_system').on('click', function() {
            const button = $(this);
            const resultEl = $('#encryption_test_result');

            button.prop('disabled', true).html('🔄 กำลังทดสอบ...');
            resultEl.html('');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_encryption_system',
                    nonce: '<?php echo wp_create_nonce('test_encryption_system'); ?>'
                },
                success: function(response) {
                    button.prop('disabled', false).html('🧪 ทดสอบการเข้ารหัส/ถอดรหัส');

                    if (response.success) {
                        resultEl.html('<span style="color: green;">✅ ' + response.data.message + '</span>');
                    } else {
                        resultEl.html('<span style="color: red;">❌ ' + (response.data.message || 'การทดสอบล้มเหลว') + '</span>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('🧪 ทดสอบการเข้ารหัส/ถอดรหัส');
                    resultEl.html('<span style="color: red;">❌ เกิดข้อผิดพลาดในการเชื่อมต่อ</span>');
                }
            });
        });

        $('#verify_all_secrets').on('click', function() {
            const button = $(this);
            const resultEl = $('#verify_secrets_result');
            const tableEl = $('#secrets_status_table');

            button.prop('disabled', true).html('🔄 กำลังตรวจสอบ...');
            resultEl.html('');
            tableEl.html('');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'verify_all_secrets',
                    nonce: '<?php echo wp_create_nonce('verify_all_secrets'); ?>'
                },
                success: function(response) {
                    button.prop('disabled', false).html('🔍 ตรวจสอบสถานะข้อมูลลับ');

                    if (response.success && response.data.results) {
                        resultEl.html('<span style="color: green;">✅ ตรวจสอบเสร็จสิ้น</span>');

                        let tableHtml = '<table class="widefat" style="margin-top: 10px;"><thead><tr><th>Secret Key</th><th>สถานะ</th><th>รายละเอียด</th></tr></thead><tbody>';

                        $.each(response.data.results, function(key, data) {
                            const statusColor = data.status === 'OK' ? 'green' : 'red';
                            const statusIcon = data.status === 'OK' ? '✅' : '❌';

                            tableHtml += `<tr>
                                <td><code>${key}</code></td>
                                <td style="color: ${statusColor};">${statusIcon} ${data.status}</td>
                                <td>
                                    เข้ารหัส: ${data.has_encrypted ? '✅' : '❌'} |
                                    Marker: ${data.plain_is_encrypted_marker ? '✅' : '❌'} |
                                    ถอดรหัส: ${data.can_decrypt ? '✅' : '❌'}
                                </td>
                            </tr>`;
                        });

                        tableHtml += '</tbody></table>';
                        tableEl.html(tableHtml);
                    } else {
                        resultEl.html('<span style="color: red;">❌ ' + (response.data.message || 'การตรวจสอบล้มเหลว') + '</span>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('🔍 ตรวจสอบสถานะข้อมูลลับ');
                    resultEl.html('<span style="color: red;">❌ เกิดข้อผิดพลาดในการเชื่อมต่อ</span>');
                }
            });
        });

        $('#get_encryption_stats').on('click', function() {
            const button = $(this);
            const resultEl = $('#encryption_stats_result');
            const displayEl = $('#encryption_stats_display');

            button.prop('disabled', true).html('🔄 กำลังโหลด...');
            resultEl.html('');
            displayEl.html('');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_encryption_stats',
                    nonce: '<?php echo wp_create_nonce('get_encryption_stats'); ?>'
                },
                success: function(response) {
                    button.prop('disabled', false).html('📊 ดูสถิติระบบเข้ารหัส');

                    if (response.success && response.data.stats) {
                        const stats = response.data.stats;
                        resultEl.html('<span style="color: green;">✅ โหลดสถิติเสร็จสิ้น</span>');

                        let statsHtml = `
                            <div style="background: #f9f9f9; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-top: 10px;">
                                <h4>📊 สถิติระบบเข้ารหัส</h4>
                                <table class="widefat">
                                    <tr><td><strong>จำนวน Secret Keys ทั้งหมด:</strong></td><td>${stats.total_secrets}</td></tr>
                                    <tr><td><strong>เข้ารหัสแล้ว:</strong></td><td style="color: green;">${stats.encrypted_count}</td></tr>
                                    <tr><td><strong>ยังไม่เข้ารหัส:</strong></td><td style="color: orange;">${stats.unencrypted_count}</td></tr>
                                    <tr><td><strong>ว่างเปล่า:</strong></td><td style="color: gray;">${stats.empty_count}</td></tr>
                                    <tr><td><strong>Encryption Key:</strong></td><td>${stats.encryption_key_exists ? '✅ มี' : '❌ ไม่มี'}</td></tr>
                                    <tr><td><strong>วิธีการเข้ารหัส:</strong></td><td><code>${stats.cipher_method}</code></td></tr>
                                </table>
                            </div>
                        `;
                        displayEl.html(statsHtml);
                    } else {
                        resultEl.html('<span style="color: red;">❌ ' + (response.data.message || 'ไม่สามารถโหลดสถิติได้') + '</span>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('📊 ดูสถิติระบบเข้ารหัส');
                    resultEl.html('<span style="color: red;">❌ เกิดข้อผิดพลาดในการเชื่อมต่อ</span>');
                }
            });
        });

        $('#backup_secrets').on('click', function() {
            const button = $(this);
            const resultEl = $('#backup_secrets_result');

            if (!confirm('คุณต้องการสำรองข้อมูลลับทั้งหมดหรือไม่?\n\nข้อมูลจะถูกดาวน์โหลดในรูปแบบ JSON')) {
                return;
            }

            button.prop('disabled', true).html('🔄 กำลังสำรอง...');
            resultEl.html('');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'backup_secrets',
                    nonce: '<?php echo wp_create_nonce('backup_secrets'); ?>'
                },
                success: function(response) {
                    button.prop('disabled', false).html('💾 สำรองข้อมูลลับ');

                    if (response.success && response.data.data) {
                        resultEl.html('<span style="color: green;">✅ สำรองข้อมูลสำเร็จ (' + response.data.count + ' รายการ)</span>');

                        const blob = new Blob([response.data.data], { type: 'application/json' });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'amp-secrets-backup-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.json';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        window.URL.revokeObjectURL(url);
                    } else {
                        resultEl.html('<span style="color: red;">❌ ' + (response.data.message || 'การสำรองข้อมูลล้มเหลว') + '</span>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('💾 สำรองข้อมูลลับ');
                    resultEl.html('<span style="color: red;">❌ เกิดข้อผิดพลาดในการเชื่อมต่อ</span>');
                }
            });
        });

        $('#csp_enabled').on('change', function() {
            const isEnabled = $(this).is(':checked');
            $('#csp_seo_mode').prop('disabled', !isEnabled);
            if (!isEnabled) {
                $('#csp_seo_mode').prop('checked', false);
            }
        });

        $('#csp_enabled').trigger('change');

        $('form').on('submit', function(e) {
            e.preventDefault();

            const button = $('#save-security-settings');
            const statusEl = $('#security-settings-status');

            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');
            statusEl.removeClass('success error').hide();

            const formData = {
                action: 'save_security_settings',
                nonce: '<?php echo $security_nonce; ?>',
                csp_enabled: $('#csp_enabled').is(':checked') ? 1 : 0,
                csp_seo_mode: $('#csp_seo_mode').is(':checked') ? 1 : 0,
                turnstile_site_key: $('#turnstile_site_key').val(),
                turnstile_secret_key: $('#turnstile_secret_key').val(),
                turnstile_enabled: $('#turnstile_enabled').is(':checked') ? 1 : 0,
                turnstile_login_required: $('#turnstile_login_required').is(':checked') ? 1 : 0,
                email_verification_enabled: $('#email_verification_enabled').is(':checked') ? 1 : 0,
                max_login_attempts: $('#max_login_attempts').val(),
                account_lock_duration: $('#account_lock_duration').val(),
                session_timeout: $('#session_timeout').val(),
                password_min_length: $('#password_min_length').val(),
                password_complexity: $('#password_complexity').is(':checked') ? 1 : 0,
                login_activity_logging: $('#login_activity_logging').is(':checked') ? 1 : 0,
                login_activity_logs_retention: $('#login_activity_logs_retention').val()
            };

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่า');

                    if (response.success) {
                        var successMessage = response.data.message || 'บันทึกสำเร็จ';
                        statusEl.addClass('success').html('<i class="fas fa-check-circle"></i> ' + successMessage).show();
                        setTimeout(function() { statusEl.fadeOut(); }, 3000);
                    } else {
                        var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                        statusEl.addClass('error').html('<i class="fas fa-exclamation-circle"></i> ' + errorMessage).show();
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่า');
                    statusEl.addClass('error').html('<i class="fas fa-exclamation-circle"></i> เกิดข้อผิดพลาดในการบันทึก').show();
                }
            });
        });
    });
    </script>
    <?php
}

function handle_save_security_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_security_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $csp_enabled = isset($_POST['csp_enabled']) ? intval($_POST['csp_enabled']) : 0;
    $csp_seo_mode = isset($_POST['csp_seo_mode']) ? intval($_POST['csp_seo_mode']) : 0;

    update_option('amp_csp_enabled', $csp_enabled);
    update_option('amp_csp_seo_mode', $csp_seo_mode);

    $turnstile_site_key = sanitize_text_field($_POST['turnstile_site_key']);
    $turnstile_secret_key = sanitize_text_field($_POST['turnstile_secret_key']);
    $turnstile_enabled = isset($_POST['turnstile_enabled']) ? intval($_POST['turnstile_enabled']) : 0;
    $turnstile_login_required = isset($_POST['turnstile_login_required']) ? intval($_POST['turnstile_login_required']) : 0;

    $encryption_manager = \AMP_Encryption_Manager::instance();

    if (!empty($turnstile_site_key)) {
        $encryption_manager->set_secret('amp_turnstile_site_key', $turnstile_site_key);
    }

    if (!empty($turnstile_secret_key)) {
        $encryption_manager->set_secret('amp_turnstile_secret_key', $turnstile_secret_key);
    }
    update_option('amp_turnstile_enabled', $turnstile_enabled);
    update_option('amp_turnstile_login_required', $turnstile_login_required);

    $email_verification_enabled = isset($_POST['email_verification_enabled']) ? intval($_POST['email_verification_enabled']) : 0;
    update_option('amp_email_verification_enabled', $email_verification_enabled);

    $max_login_attempts = intval($_POST['max_login_attempts']);
    $account_lock_duration = intval($_POST['account_lock_duration']);
    $session_timeout = intval($_POST['session_timeout']);
    $password_min_length = intval($_POST['password_min_length']);
    $password_complexity = isset($_POST['password_complexity']) ? intval($_POST['password_complexity']) : 0;

    if ($max_login_attempts < 1) $max_login_attempts = 5;
    if ($account_lock_duration < 1) $account_lock_duration = 15;
    if ($session_timeout < 1) $session_timeout = 60;
    if ($password_min_length < 8) $password_min_length = 8;

    update_option('amp_max_login_attempts', $max_login_attempts);
    update_option('amp_account_lock_duration', $account_lock_duration);
    update_option('amp_session_timeout', $session_timeout);
    update_option('amp_password_min_length', $password_min_length);
    update_option('amp_password_complexity', $password_complexity);

    $login_activity_logging = isset($_POST['login_activity_logging']) ? intval($_POST['login_activity_logging']) : 0;
    $login_activity_logs_retention = intval($_POST['login_activity_logs_retention']);

    if ($login_activity_logs_retention < 1) $login_activity_logs_retention = 30;

    update_option('amp_login_activity_logging', $login_activity_logging);
    update_option('amp_login_activity_logs_retention', $login_activity_logs_retention);

    wp_send_json_success(['message' => 'บันทึกการตั้งค่าความปลอดภัยเรียบร้อยแล้ว']);
}
function handle_test_turnstile() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'amp_test_turnstile')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $site_key = sanitize_text_field($_POST['site_key'] ?? '');
    $secret_key = sanitize_text_field($_POST['secret_key'] ?? '');

    if ($site_key === 'USE_STORED_ENCRYPTED' || $secret_key === 'USE_STORED_ENCRYPTED') {
        $encryption_manager = \AMP_Encryption_Manager::instance();

        if ($site_key === 'USE_STORED_ENCRYPTED') {
            $site_key = $encryption_manager->get_secret('amp_turnstile_site_key');
        }

        if ($secret_key === 'USE_STORED_ENCRYPTED') {
            $secret_key = $encryption_manager->get_secret('amp_turnstile_secret_key');
        }
    }

    if (empty($site_key) || empty($secret_key)) {
        wp_send_json_error(['message' => 'ไม่พบ Turnstile Keys ที่เข้ารหัสไว้ กรุณาตั้งค่าใหม่']);
        return;
    }

    $test_response = wp_remote_post('https://challenges.cloudflare.com/turnstile/v0/siteverify', [
        'body' => [
            'secret' => $secret_key,
            'response' => 'test-token',
            'remoteip' => sanitize_text_field($_SERVER['REMOTE_ADDR'] ?? '')
        ],
        'timeout' => 15,
        'headers' => [
            'Content-Type' => 'application/x-www-form-urlencoded'
        ]
    ]);

    if (is_wp_error($test_response)) {
        wp_send_json_error(['message' => 'ไม่สามารถเชื่อมต่อกับ Cloudflare ได้: ' . $test_response->get_error_message()]);
        return;
    }

    $response_code = wp_remote_retrieve_response_code($test_response);
    $body = wp_remote_retrieve_body($test_response);
    $data = json_decode($body, true);

    if ($response_code !== 200) {
        wp_send_json_error(['message' => 'HTTP Error: ' . $response_code]);
        return;
    }

    if (!$data) {
        wp_send_json_error(['message' => 'ไม่สามารถอ่านข้อมูลจาก Cloudflare ได้']);
        return;
    }

    if (isset($data['error-codes']) && in_array('invalid-input-secret', $data['error-codes'])) {
        wp_send_json_error(['message' => 'Secret Key ไม่ถูกต้อง']);
        return;
    }

    if (isset($data['error-codes']) && in_array('invalid-input-response', $data['error-codes'])) {
        wp_send_json_success(['message' => 'การตั้งค่า Turnstile ถูกต้อง (Test token invalid เป็นปกติ)']);
        return;
    }

    if (isset($data['success']) && $data['success'] === false) {
        $error_codes = isset($data['error-codes']) ? implode(', ', $data['error-codes']) : 'Unknown error';
        wp_send_json_error(['message' => 'Turnstile Error: ' . $error_codes]);
        return;
    }

    wp_send_json_success(['message' => 'การตั้งค่า Turnstile ถูกต้อง']);
}

function handle_delete_secret_key() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'delete_secret_key')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $secret_key = isset($_POST['secret_key']) ? sanitize_text_field($_POST['secret_key']) : '';

    if (empty($secret_key)) {
        wp_send_json_error(['message' => 'Secret key parameter is required']);
        return;
    }

    $allowed_secrets = [
        'amp_turnstile_site_key',
        'amp_turnstile_secret_key',
        'amp_google_client_id',
        'amp_google_client_secret',
        'plisio_api_key'
    ];

    if (!in_array($secret_key, $allowed_secrets)) {
        wp_send_json_error(['message' => 'Invalid secret key']);
        return;
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $result = $encryption_manager->delete_secret($secret_key);

    if ($result) {
        wp_send_json_success(['message' => 'Secret key deleted successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to delete secret key']);
    }
}

function handle_test_encryption_system() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'test_encryption_system')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $test_result = $encryption_manager->test_encryption_system();

    if ($test_result['success']) {
        wp_send_json_success($test_result);
    } else {
        wp_send_json_error($test_result);
    }
}

function handle_verify_all_secrets() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'verify_all_secrets')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $results = $encryption_manager->verify_all_secrets();

    if ($results) {
        wp_send_json_success(['results' => $results]);
    } else {
        wp_send_json_error(['message' => 'Failed to verify secrets']);
    }
}

function handle_backup_secrets() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'backup_secrets')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $result = $encryption_manager->backup_all_secrets();

    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}

function handle_get_encryption_stats() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_encryption_stats')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();
    $stats = $encryption_manager->get_encryption_stats();

    if ($stats) {
        wp_send_json_success(['stats' => $stats]);
    } else {
        wp_send_json_error(['message' => 'Failed to get encryption stats']);
    }
}

add_action('wp_ajax_save_security_settings', 'handle_save_security_settings');
add_action('wp_ajax_amp_test_turnstile', 'handle_test_turnstile');
add_action('wp_ajax_delete_secret_key', 'handle_delete_secret_key');
add_action('wp_ajax_test_encryption_system', 'handle_test_encryption_system');
add_action('wp_ajax_verify_all_secrets', 'handle_verify_all_secrets');
add_action('wp_ajax_backup_secrets', 'handle_backup_secrets');
add_action('wp_ajax_get_encryption_stats', 'handle_get_encryption_stats');


