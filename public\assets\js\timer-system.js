(function($) {
    'use strict';

    window.TimerSystem = {
        userTimer: null,
        syncInterval: null,
        syncIntervalTime: 5000,
        isInitialized: false,
        lastInitTime: 0,
        lastClosedWasMainCheckout: false,
        lockKey: 'amp_timer_lock_' + (window.adDashboardData?.user_id || 'guest'),
        lastMarkTime: null,
        ajaxNavigationTimeout: null,

        init: function() {
            const currentTime = Date.now();

            if (this.isInitialized && (currentTime - this.lastInitTime) < 1000) {
                return;
            }

            if (this.isInitialized) {
                this.cleanup();
            }

            this.isInitialized = true;
            this.lastInitTime = currentTime;
            this.isAjaxNavigation = false;

            try {
                this.bindEvents();
            } catch (error) {
                console.error('Timer System: Error during initialization:', error);
                this.isInitialized = false;
            }
        },
        
        bindEvents: function() {
            $(window).on('beforeunload', (e) => {
                if (this.userTimer && !this.isAjaxNavigation) {
                    const message = 'คุณมีการจองตำแหน่งโฆษณาที่ยังไม่เสร็จสิ้น หากออกจากหน้านี้การจองจะถูกยกเลิก';
                    e.preventDefault();
                    e.returnValue = message;
                    return message;
                }
            });

            $(document).on('swal2:open', (_, popup) => {
                this.hideTimerWidget();

                if (popup && popup.classList && popup.classList.contains('new-checkout-popup')) {
                    this.lastClosedWasMainCheckout = true;
                    window.checkoutPopupOpen = true;
                }
            });

            $(document).on('swal2:close', (_, popup) => {
                setTimeout(() => {
                    const currentTab = this.getCurrentTab();
                    const shouldShow = this.shouldShowTimer(currentTab);
                    const wasMainCheckout = popup && popup.classList && popup.classList.contains('new-checkout-popup');

                    if (wasMainCheckout) {
                        window.checkoutPopupOpen = false;
                        this.lastClosedWasMainCheckout = true;
                    }

                    if (shouldShow && this.lastClosedWasMainCheckout && !window.checkoutPopupOpen) {
                        this.showTimerWidget();
                        this.lastClosedWasMainCheckout = false;
                    }

                    if (wasMainCheckout && window.lastCheckoutData) {
                        setTimeout(() => {
                            if (!Swal.isVisible() && window.lastCheckoutData) {
                                this.restoreCheckoutPopup();
                            }
                        }, 500);
                    }
                }, 300);
            });

            $(document).on('amp:checkUserTimer', (_, options) => {
                this.checkExistingTimer(options.autoShow || false);
            });
        },
        
        startUserTimer: function(timeoutSeconds = 180, showWidget = false, callback) {
            if (typeof adDashboardData === 'undefined') {
                console.error('Timer System: adDashboardData not available');
                if (typeof callback === 'function') {
                    callback(false, 'adDashboardData not available');
                }
                return;
            }

            if (this.userTimer) {
                this.stopUserTimer();
            }

            const lockValue = 'amp_timer_' + Date.now();

            if (typeof adDashboardData === 'undefined') {
                setTimeout(() => {
                    this.startUserTimer(timeoutSeconds, showWidget, callback);
                }, 500);
                return;
            }

            const timeoutMinutes = Math.floor(timeoutSeconds / 60);

            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'create_reservation',
                    timeout_minutes: timeoutMinutes,
                    security: adDashboardData.nonce,
                    force_new: 1
                },
                success: (response) => {
                    if (response.success) {
                        const serverRemainingTime = parseInt(response.data.remaining_time);
                        
                        this.userTimer = {
                            timeLeft: serverRemainingTime,
                            interval: setInterval(() => {
                                this.updateUserTimer(false);
                            }, 1000),
                            element: null,
                            lockValue: lockValue,
                            timerId: response.data.timer_id,
                            durationSeconds: response.data.duration_seconds,
                            startedAt: response.data.server_timestamp
                        };

                        localStorage.setItem(this.lockKey, lockValue);

                        setTimeout(() => {
                            this.startSyncInterval();
                        }, 2000);

                        this.updateTimerDisplay();



                        if (typeof callback === 'function') {
                            callback(true);
                        }
                    } else {
                        console.error('Timer System: Failed to create server countdown timer -', response.data?.message || 'Unknown error');

                        if (response.data?.message && typeof window.showMiniPopup === 'function') {
                            window.showMiniPopup(response.data.message, 'error');
                        }

                        if (typeof callback === 'function') {
                            callback(false, response.data?.message);
                        }
                    }
                },
                error: (_, __, error) => {
                    console.error('Timer System: Failed to start countdown timer:', error);
                    if (typeof callback === 'function') {
                        callback(false, 'AJAX error: ' + error);
                    }
                }
            });
        },
        
        stopUserTimer: function() {
            if (this.userTimer) {
                clearInterval(this.userTimer.interval);
                this.removeTimerElement();
                this.userTimer = null;

                if (this.syncInterval) {
                    clearInterval(this.syncInterval);
                    this.syncInterval = null;
                }

                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                    this.heartbeatInterval = null;
                }


            }

            localStorage.removeItem(this.lockKey);
        },
        
        updateUserTimer: function(updateDisplay = true) {
            if (!this.userTimer) {
                return;
            }

            this.userTimer.timeLeft--;

            if (updateDisplay) {
                this.updateTimerDisplay();
            }

            if (this.userTimer.timeLeft <= 0) {
                this.handleTimerExpired();
            }
        },
        
        updateTimerDisplay: function() {
            if (!this.userTimer) {
                return;
            }

            const minutes = Math.floor(this.userTimer.timeLeft / 60);
            const seconds = this.userTimer.timeLeft % 60;
            const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            const timerWidget = $('.timer-widget');
            if (timerWidget.length > 0) {
                const timerDisplay = timerWidget.find('.timer-display');
                if (timerDisplay.length) {
                    timerDisplay.text(timeString);
                }
            }

            const reservationTimer = document.querySelector('#reservation-timer');
            if (reservationTimer) {
                const minutesElement = reservationTimer.querySelector('.timer-minutes');
                const secondsElement = reservationTimer.querySelector('.timer-seconds');

                if (minutesElement && secondsElement) {
                    minutesElement.textContent = minutes.toString().padStart(2, '0');
                    secondsElement.textContent = seconds.toString().padStart(2, '0');
                }
            }

            if (this.userTimer.element) {
                const timerElement = this.userTimer.element.find('.timer-time');
                if (timerElement.length) {
                    timerElement.text(timeString);
                }
            }
        },
        
        handleTimerExpired: function() {
            this.stopUserTimer();
            
            $(document).trigger('timerExpired');
            $(document).trigger('positionReservationUpdated', {
                action: 'expired'
            });
            
            this.showExpirationMessage();
        },
        
        showExpirationMessage: function() {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '',
                    html: `
                        <div class="modern-session-warning">
                            <div class="warning-icon-container">
                                <div class="warning-icon-bg" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                                    <i class="fas fa-clock warning-icon"></i>
                                </div>
                            </div>
                            <h2 class="warning-title" style="background: linear-gradient(135deg, #e74c3c, #c0392b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">หมดเวลาการจอง</h2>
                            <p class="warning-message">เวลาการจองตำแหน่งโฆษณาหมดแล้ว</p>
                            <div class="warning-info-box" style="background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1)); border: 1px solid rgba(231, 76, 60, 0.3);">
                                <i class="fas fa-info-circle" style="color: #e74c3c;"></i>
                                <span>กรุณาเริ่มกระบวนการใหม่อีกครั้ง</span>
                            </div>
                        </div>
                    `,
                    confirmButtonText: '<i class="fas fa-redo"></i> เริ่มใหม่',
                    customClass: {
                        popup: 'modern-session-popup',
                        confirmButton: 'modern-btn modern-btn-primary'
                    },
                    buttonsStyling: false,
                    allowOutsideClick: false,
                    backdrop: 'rgba(0, 0, 0, 0.8)',
                    showClass: {
                        popup: 'animate__animated animate__zoomIn animate__faster'
                    },
                    hideClass: {
                        popup: 'animate__animated animate__zoomOut animate__faster'
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        this.clearCartAndGoToBuyTab();
                    }
                });
            } else if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup('หมดเวลาการจอง กรุณาเริ่มใหม่', 'warning');
                setTimeout(() => {
                    this.clearCartAndGoToBuyTab();
                }, 2000);
            }
        },

        restoreCheckoutPopup: function() {
            if (window.lastCheckoutData && typeof window.openCheckoutPopup === 'function') {

                setTimeout(() => {
                    if (!Swal.isVisible()) {
                        window.openCheckoutPopup(true);
                    }
                }, 100);
            }
        },

        clearCartAndGoToBuyTab: function() {
            if (typeof adDashboardData === 'undefined') {

                if (typeof window.loadTabContent === 'function') {
                    window.loadTabContent('buy');
                } else {
                    window.location.reload();
                }
                return;
            }

            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'clear_cart',
                    security: adDashboardData.nonce
                },
                success: () => {
                    if (typeof window.updateCartCount === 'function') {
                        window.updateCartCount(0);
                    }

                    if (typeof window.refreshCartCount === 'function') {
                        window.refreshCartCount();
                    }

                    if (typeof window.loadTabContent === 'function') {
                        window.loadTabContent('buy');
                    } else {
                        window.location.reload();
                    }
                },
                error: () => {
                    if (typeof window.updateCartCount === 'function') {
                        window.updateCartCount(0);
                    }

                    if (typeof window.loadTabContent === 'function') {
                        window.loadTabContent('buy');
                    } else {
                        window.location.reload();
                    }
                }
            });
        },
        
        checkExistingTimer: function(autoShow = false) {
            if (typeof adDashboardData === 'undefined') {
                return;
            }

            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_reservation_status',
                    security: adDashboardData.nonce
                },
                success: (response) => {
                    if (response.success && response.data.status === 'active' && response.data.remaining_time > 0) {
                        const remainingTime = parseInt(response.data.remaining_time);
                        const lockValue = 'amp_timer_' + Date.now();

                        this.userTimer = {
                            timeLeft: remainingTime,
                            interval: setInterval(() => {
                                this.updateUserTimer(true);
                            }, 1000),
                            element: null,
                            lockValue: lockValue,
                            timerId: response.data.timer_id,
                            durationSeconds: response.data.duration_seconds,
                            startedAt: response.data.started_at
                        };

                        localStorage.setItem(this.lockKey, lockValue);

                        setTimeout(() => {
                            this.startSyncInterval();
                        }, 2000);

                        if (autoShow) {
                            this.showTimerWidget();
                        }
                    }
                },
                error: () => {}
            });
        },
        
        createTimerElement: function() {
            const existingElement = $('.timer-widget');

            if (existingElement.length) {
                return existingElement;
            }

            const element = $(`
                <div class="timer-widget" style="cursor: pointer;">
                    <div class="timer-header">
                        <i class="fas fa-clock"></i>
                        <span class="timer-title">การจองของคุณ</span>
                        <button class="timer-close">×</button>
                    </div>
                    <div class="timer-body">
                        <div class="timer-display">00:00</div>
                    </div>
                </div>
            `);

            element.find('.timer-close').on('click', (e) => {
                e.stopPropagation();
                if (typeof window.showCancelConfirmation === 'function') {
                    window.showCancelConfirmation();
                }
            });

            element.on('click', () => {
                this.hideTimerWidget();
                this.removeTimerElement();

                if (typeof window.openCheckoutPopup === 'function') {
                    window.openCheckoutPopup(true);
                }
            });

            $('body').append(element);
            return element;
        },

        getCurrentTab: function() {
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');
            if (tab) {
                return tab;
            }

            const activeTab = $('.ad-dashboard-nav .nav-item.active, .ad-dashboard-nav li.active');
            if (activeTab.length) {
                const href = activeTab.find('a').attr('href');
                if (href && href.includes('tab=')) {
                    const tabFromHref = href.split('tab=')[1].split('&')[0];
                    return tabFromHref;
                }
            }

            if ($('body').hasClass('cart-page') || $('[data-tab="cart"]').length) {
                return 'cart';
            }

            if ($('.cart-container').length > 0) {
                return 'cart';
            }

            return 'unknown';
        },

        shouldShowTimer: function() {
            const hasActivePopup = $('.swal2-container:visible').length > 0;
            const hasTimer = !!this.userTimer;
            const hasMainCheckoutPopup = $('.new-checkout-popup').length > 0;
            const hasLoadingPopup = $('.modern-loading-popup').length > 0;
            const hasTimerWidget = $('.timer-widget:visible').length > 0;

            return !hasActivePopup && !hasMainCheckoutPopup && !hasLoadingPopup && hasTimer && !hasTimerWidget;
        },
        
        removeTimerElement: function() {
            $('.timer-widget').remove();
        },

        hideTimerWidget: function() {
            $('.timer-widget').hide();
        },

        showTimerWidget: function() {
            if (!this.userTimer) {
                return;
            }

            if ($('.swal2-container:visible').length > 0) {
                return;
            }

            $('.timer-widget').remove();

            const widget = this.createTimerElement();
            if (this.userTimer && widget) {
                this.userTimer.element = widget;
                this.userTimer.widgetCreated = true;
            }

            if (widget && widget.length > 0) {
                widget.show();
                this.updateTimerDisplay();

            }
        },
        
        startSyncInterval: function() {
            if (this.syncInterval) {
                clearInterval(this.syncInterval);
                this.syncInterval = null;
            }

            this.syncInterval = setInterval(() => {
                if (!this.userTimer) {
                    clearInterval(this.syncInterval);
                    this.syncInterval = null;
                    return;
                }

                const currentLock = localStorage.getItem(this.lockKey);
                if (currentLock !== this.userTimer.lockValue) {
                    const timeSinceAjaxMark = this.ajaxNavigationTime ? (Date.now() - this.ajaxNavigationTime) : 999999;
                    const recentAjaxNavigation = timeSinceAjaxMark < 15000;
                    const isInTabSystem = window.location.href.includes('dashboard.php') || 
                                        window.location.href.includes('public/') ||
                                        document.querySelector('.dashboard-container') !== null ||
                                        document.querySelector('[data-tab]') !== null;

                    if (this.isAjaxNavigation || recentAjaxNavigation || isInTabSystem) {
                        this.userTimer.lockValue = currentLock;
                        this.isAjaxNavigation = false;
                    } else {
                        this.stopUserTimer();
                        return;
                    }
                }
               
                this.syncWithServer();
            }, this.syncIntervalTime);
        },

        
        syncWithServer: function() {
            if (!this.userTimer || typeof adDashboardData === 'undefined') {
                return;
            }

            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'sync_reservation_timer',
                    security: adDashboardData.nonce
                },
                success: (response) => {
                    if (response.success) {
                        if (response.data.status === 'completed') {
                            this.stopUserTimer();
                            if (typeof window.showPaymentSuccessPopup === 'function') {
                                window.showPaymentSuccessPopup(response.data);
                            }
                        } else if (response.data.status === 'cancelled' || response.data.status === 'expired' ||
                                   response.data.status === 'error' || response.data.status === 'mismatch') {
                            this.stopUserTimer();
                            this.showPaymentStatusPopup(response.data.status);
                        } else if (response.data.status === 'inactive') {
                            this.stopUserTimer();
                            this.showPaymentStatusPopup('expired');
                        } else if (response.data.status === 'active' && response.data.remaining_time > 0) {
                            const serverRemainingTime = parseInt(response.data.remaining_time);
                            const clientRemainingTime = this.userTimer.timeLeft;
                            const timeDiff = Math.abs(serverRemainingTime - clientRemainingTime);
                            if (timeDiff > 5) {
                                this.userTimer.timeLeft = serverRemainingTime;
                            }
                            this.updateTimerDisplay();
                            if (serverRemainingTime <= 0) {
                                this.handleTimerExpired();
                            }
                        }
                    }
                },
                error: () => {}
            });
        },

        cancelUserTimer: function() {
            if (typeof adDashboardData === 'undefined') {
                this.stopUserTimer();
                return;
            }
            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'cancel_reservation',
                    security: adDashboardData.nonce
                },
                success: () => {},
                error: () => {}
            });
            this.stopUserTimer();
        },
        
        cleanup: function() {
            try {
                this.stopUserTimer();
                if (this.syncInterval) {
                    clearInterval(this.syncInterval);
                    this.syncInterval = null;
                }
                if (this.ajaxNavigationTimeout) {
                    clearTimeout(this.ajaxNavigationTimeout);
                    this.ajaxNavigationTimeout = null;
                }
                this.removeTimerElement();
                this.isInitialized = false;

            } catch (error) {
                console.error('Timer System: Error during cleanup:', error);
                this.isInitialized = false;
            }
        },

        getUserTimer: function() {
            return this.userTimer ? {
                timeLeft: this.userTimer.timeLeft,
                isActive: true
            } : {
                timeLeft: 0,
                isActive: false
            };
        },

        markAjaxNavigation: function() {
            const currentTime = Date.now();          
            if (this.lastMarkTime && (currentTime - this.lastMarkTime) < 1000) {
                return;
            }
            this.lastMarkTime = currentTime;
            this.isAjaxNavigation = true;
            this.ajaxNavigationTime = currentTime;
            const newLockValue = currentTime.toString();
            localStorage.setItem(this.lockKey, newLockValue);
            if (this.userTimer) {
                this.userTimer.lockValue = newLockValue;
            }
            if (this.ajaxNavigationTimeout) {
                clearTimeout(this.ajaxNavigationTimeout);
            }
            this.ajaxNavigationTimeout = setTimeout(() => {
                this.isAjaxNavigation = false;
            }, 8000);
        },

        showPaymentStatusPopup: function(status) {
            const statusConfig = this.getStatusConfig(status);
            
            const successHtml = `
                <div class="amp-status-popup-container">
                    <div class="amp-status-icon">
                        <svg class="status-icon-svg ${statusConfig.svgClass}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                            <circle class="status-circle" cx="26" cy="26" r="25" fill="none"/>
                            <path class="status-icon-path" fill="none" d="${statusConfig.svgPath}"/>
                            ${statusConfig.additionalPath ? `<path class="status-icon-additional" fill="none" d="${statusConfig.additionalPath}"/>` : ''}
                        </svg>
                    </div>
                    <div class="amp-status-header">
                        <h2>${statusConfig.title}</h2>
                    </div>
                    <div class="amp-status-message">
                        <p>${statusConfig.message}</p>
                    </div>
                    ${statusConfig.details ? `
                    <div class="amp-status-details">
                        <p><i class="fas fa-info-circle"></i> ${statusConfig.details}</p>
                    </div>
                    ` : ''}
                    <div class="amp-status-actions">
                        <button class="amp-status-btn amp-status-btn-primary" onclick="handleStatusAction('${statusConfig.action}')">
                            <i class="${statusConfig.buttonIcon}"></i>
                            ${statusConfig.buttonText}
                        </button>
                    </div>
                </div>
            `;

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    html: successHtml,
                    showConfirmButton: false,
                    showCancelButton: false,
                    customClass: {
                        popup: `amp-status-popup ${statusConfig.popupClass}`
                    },
                    allowOutsideClick: false,
                    buttonsStyling: false
                });
            } else {
                alert(statusConfig.title + ': ' + statusConfig.message);
                this.handleStatusAction(statusConfig.action);
            }
        },

        handleStatusAction: function(action) {
            Swal.close();
            
            if (typeof window.clearCartAfterPurchase === 'function') {
                window.clearCartAfterPurchase();
            }
            
            if (action === 'reload') {
                window.location.reload();
            } else if (action === 'myads') {
                if (typeof window.goToMyAds === 'function') {
                    window.goToMyAds();
                } else if (typeof window.loadTabContent === 'function') {
                    window.loadTabContent('my-ads');
                } else {
                    window.location.href = window.location.pathname + '?tab=my-ads';
                }
            } else if (action === 'buy') {
                if (typeof window.loadTabContent === 'function') {
                    window.loadTabContent('buy');
                } else {
                    window.location.href = window.location.pathname + '?tab=buy';
                }
            }
        },

        getStatusConfig: function(status) {
            const configs = {
                'cancelled': {
                    title: 'การชำระเงินถูกยกเลิก',
                    message: 'คุณได้ยกเลิกการชำระเงิน การจองตำแหน่งโฆษณาจะหมดอายุ',
                    details: 'คุณสามารถเริ่มการซื้อใหม่ได้ตลอดเวลา',
                    svgClass: 'cancelled-icon',
                    svgPath: 'M16 16 L36 36 M36 16 L16 36',
                    popupClass: 'payment-cancelled-popup',
                    buttonText: 'เข้าใจแล้ว',
                    buttonIcon: 'fas fa-shopping-cart',
                    action: 'buy'
                },
                'expired': {
                    title: 'การชำระเงินหมดอายุ',
                    message: 'เวลาสำหรับการชำระเงินหมดแล้ว การจองตำแหน่งโฆษณาถูกยกเลิก',
                    details: 'กรุณาเริ่มกระบวนการซื้อใหม่',
                    svgClass: 'expired-icon',
                    svgPath: 'M26 10 L26 26 L36 36',
                    additionalPath: 'M26 6 L26 10',
                    popupClass: 'payment-expired-popup',
                    buttonText: 'เริ่มใหม่',
                    buttonIcon: 'fas fa-redo',
                    action: 'buy'
                },
                'error': {
                    title: 'เกิดข้อผิดพลาด',
                    message: 'เกิดข้อผิดพลาดในระบบการชำระเงิน',
                    details: 'กรุณาติดต่อฝ่ายสนับสนุนหากปัญหายังคงเกิดขึ้น',
                    svgClass: 'error-icon',
                    svgPath: 'M26 16 L26 32 M26 36 L26 40',
                    popupClass: 'payment-error-popup',
                    buttonText: 'รีเฟรชหน้า',
                    buttonIcon: 'fas fa-refresh',
                    action: 'reload'
                },
                'mismatch': {
                    title: 'การชำระเงินสำเร็จ (ตรวจสอบแล้ว)',
                    message: 'การชำระเงินของคุณสำเร็จแล้ว แต่มีข้อมูลไม่ตรงกันเล็กน้อย',
                    details: 'ระบบได้ตรวจสอบและยืนยันการชำระเงินแล้ว',
                    svgClass: 'success-icon',
                    svgPath: 'M14.1 27.2l7.1 7.2 16.7-16.8',
                    popupClass: 'payment-success-popup',
                    buttonText: 'ดูโฆษณาของฉัน',
                    buttonIcon: 'fas fa-eye',
                    action: 'myads'
                }
            };

            return configs[status] || {
                title: 'สถานะไม่ทราบ',
                message: 'ได้รับสถานะที่ไม่รู้จัก: ' + status,
                details: '',
                svgClass: 'unknown-icon',
                svgPath: 'M26 20 Q26 16 23 16 Q20 16 20 20 M26 28 L26 32',
                popupClass: 'payment-unknown-popup',
                buttonText: 'ตกลง',
                buttonIcon: 'fas fa-check',
                action: 'reload'
            };
        }
    };

    window.handleStatusAction = function(action) {
        window.TimerSystem.handleStatusAction(action);
    };
})(jQuery);
