<?php

if (!defined('ABSPATH')) {
    exit;
}

$current_user_id = get_current_user_id();
require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
require_once AMP_PLUGIN_DIR . 'includes/core/class-price-calculator.php';

$position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
$price_calculator = AMP_Price_Calculator::instance();

$all_system_positions = $position_manager->get_positions(['limit' => 1000, 'status' => 'any']);
$total_positions = $position_manager->count_positions(['status' => 'any']);

$available_positions_data = $position_manager->get_purchasable_positions($current_user_id);

foreach ($available_positions_data as $position) {
    if (!isset($position->base_price) || $position->base_price == 0) {
        $position->base_price = $price_calculator->get_base_price($position->name ?? $position->ad_position);
        $position->price = $position->base_price;
    }
}

require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-user-manager.php';
$user_manager = new \AdManagementPro\Modules\Shared\UserManager('public');

$user_cart = $user_manager->get_user_cart($current_user_id);
$cart_positions = array_map(function($key) {
    return str_replace('pos_', '', $key);
}, array_keys($user_cart));

$available_count = 0;
$owned_count = 0;
$reserved_count = 0;

$cache_manager = \AMP_Cache_Manager::instance();
$cache_manager->clear_group('position_data');

foreach ($all_system_positions as $position) {
    $visibility = $position_manager->get_position_visibility_state($position->name, $current_user_id);
    $ownership = $position_manager->get_position_ownership_state($position->name, $current_user_id);

    if ($visibility['is_purchasable']) {
        $available_count++;
    } elseif ($ownership['is_owned_by_current_user']) {
        $owned_count++;
    } elseif ($ownership['is_reserved']) {
        $reserved_count++;
    }
}

$unavailable_count = $total_positions - $available_count - $owned_count - $reserved_count;
?>

<style>
.position-info .position-details {
    width: 100%;
}

.buy-tab-container {
    background: var(--body-bg);
    width: 100%;
    min-height: 100vh;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.buy-hero-section {
    background: var(--theme-gradient);
    border-radius: 24px;
    padding: 40px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.buy-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.1;
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
    margin-right: 5px;
    color: white;
    transition: all 0.3s ease;
}

.stat-number.stat-updated {
    transform: scale(1.1);
    color: #ffd700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    animation: statPulse 0.6s ease-in-out;
}

.stat-label {
    font-size: 0.95rem;
    opacity: 0.9;
    color: white;
}

.search-filter-section {
    background: var(--card-bg);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.search-box {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid var(--border-color);
    border-radius: 15px;
    font-size: 16px;
    background: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--muted-text);
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.search-icon:hover {
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.1);
}

.positions-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 30px;
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .positions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1200px) {
    .positions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    .positions-grid.layout-4-cols {
        grid-template-columns: repeat(4, 1fr);
    }
}

.position-card {
    background: var(--card-bg);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    min-height: 420px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.position-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
}

.position-preview {
    height: 160px;
    background: var(--theme-gradient-light);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
}

.position-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: 30px 30px;
    opacity: 0.1;
}

.preview-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: var(--primary-color);
}

.preview-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.preview-size {
    font-size: 1.1rem;
    font-weight: 600;
}

.position-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.position-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 10px;
}

.position-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
    flex: 1;
    min-width: 150px;
}

.status-badge {
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.position-card:hover .status-badge::before {
    left: 100%;
}

.status-available {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.status-owned {
    background: linear-gradient(135deg, #cce7ff, #b3d9ff);
    color: #004085;
}

.status-reserved {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.status-unavailable {
    background: linear-gradient(135deg, #f8d7da, #f1b5bb);
    color: #721c24;
}

.status-expiring {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    animation: pulse-warning 2s infinite;
}

.status-inactive {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

.status-reserved-by-user {
    background: linear-gradient(135deg, #ffc107, #ff9800);
    color: white;
    animation: pulse-warning 2s infinite;
}

.status-owned-by-user {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.status-expired {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes statPulse {
    0% {
        transform: scale(1);
        color: white;
        text-shadow: none;
    }
    50% {
        transform: scale(1.15);
        color: #ffd700;
        text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(1.1);
        color: #ffd700;
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    }
}

.detail-label {
    color: var(--muted-text);
    font-weight: 500;
}

.detail-value {
    color: var(--text-color);
    font-weight: 600;
}

.price-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.price-label {
    font-size: 0.9rem;
    color: var(--muted-text);
}

.position-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: auto;
    padding-top: 15px;
    width: 100%;
}

.position-actions.single-button {
    grid-template-columns: 1fr;
}

.action-btn.single-button {
    grid-column: 1 / -1;
    width: 100%;
}

.action-btn {
    padding: 12px 16px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    white-space: nowrap;
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn i {
    font-size: 0.9rem;
    flex-shrink: 0;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.action-btn:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

.btn-primary {
    background: var(--theme-gradient);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--text-color);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-disabled {
    background: var(--border-color);
    color: var(--muted-text);
    cursor: not-allowed;
}

.position-actions.single-button .btn-disabled {
    width: 100% !important;
    grid-column: 1 / -1;
}

.btn-success {
    background: linear-gradient(135deg, #ae9b27, #2ecc71);
    color: white;
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.3);
}

.in-cart-btn {
    position: relative;
    overflow: hidden;
}

.in-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.in-cart-btn:hover::before {
    left: 100%;
}

.btn-warning {
    background: linear-gradient(135deg,rgb(255, 7, 7), #ff9800);
    color: white;
    animation: pulse-warning 2s infinite;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #b654cbcc, #970000b5);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.3);
}

.text-warning {
    color: #856404;
    font-weight: 600;
}

.expiring-warning {
    background: rgba(255, 193, 7, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 3px solid #ffc107;
    width: 100%;
    box-sizing: border-box;
}

.owner-info {
    background: rgba(67, 97, 238, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    margin-top: 5px;
    width: 100%;
    box-sizing: border-box;
}

.owner-info .detail-value {
    color: var(--primary-color);
    font-weight: 700;
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: var(--card-bg);
    border-radius: 20px;
    border: 2px dashed var(--border-color);
    margin: 20px 0;
}

.empty-icon {
    font-size: 4rem;
    color: var(--muted-text);
    margin-bottom: 20px;
    opacity: 0.7;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 10px;
}

.empty-message {
    color: var(--muted-text);
    font-size: 1rem;
    line-height: 1.5;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 0.05;
    animation: float 6s ease-in-out infinite;
}

.floating-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.floating-icon:nth-child(2) { top: 20%; right: 20%; animation-delay: 1s; }
.floating-icon:nth-child(3) { bottom: 30%; left: 15%; animation-delay: 2s; }
.floating-icon:nth-child(4) { bottom: 20%; right: 10%; animation-delay: 3s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(90deg); }
    50% { transform: translateY(-40px) rotate(180deg); }
    75% { transform: translateY(-20px) rotate(270deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 0.05; }
    50% { opacity: 0.15; }
}

.floating-icon {
    animation: float 6s ease-in-out infinite, pulse 3s ease-in-out infinite;
}

.filter-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.filter-pill {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    border-radius: 20px;
    background: var(--card-bg);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-pill:hover,
.filter-pill.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}



.sort-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 25px;
    justify-content: center;
    align-items: center;
}

.sort-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
    white-space: nowrap;
}

.sort-select {
    padding: 8px 15px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: var(--card-bg);
    color: var(--text-color);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.sort-select:hover {
    border-color: var(--primary-color);
}

@media (max-width: 768px) {
    .buy-tab-container {
        padding: 15px;
    }
    
    .buy-hero-section {
        padding: 25px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    }
   
    .sort-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .sort-group {
    justify-content: center;
        width: 100%;
    }
    
    .sort-select {
        min-width: 200px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.7rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .position-card {
        min-height: auto;
    }

.position-details {
    flex: 1;
    }   
}

@media (max-width: 380px) {
    .action-btn {
        font-size: 0.8rem;
        padding: 10px 8px;
    }

    .position-name {
        font-size: 1.2rem;
    }

    .price-amount {
        font-size: 1.6rem;
    }
}

.dark-mode .status-available {
    background: linear-gradient(135deg, #1e3a2e, #2d5a3d);
    color: #a3d5a3;
}

.dark-mode .status-owned {
    background: linear-gradient(135deg, #1e2a3a, #2d3d5a);
    color: #a3c7f0;
}

.dark-mode .status-reserved {
    background: linear-gradient(135deg, #3a351e, #5a4d2d);
    color: #f0d5a3;
}

.status-reserved-by-user {
    background: linear-gradient(135deg, #ffc107, #ff8c00);
    color: white;
    border: 2px solid #ffc107;
    font-weight: 600;
}

.status-reserved {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border: 2px solid #6c757d;
    font-weight: 600;
}

.dark-mode .status-unavailable {
    background: linear-gradient(135deg, #3a1e1e, #5a2d2d);
    color: #f0a3a3;
}

.dark-mode .status-expiring {
    background: linear-gradient(135deg, #3a351e, #5a4d2d);
    color: #f0d5a3;
}

.dark-mode .text-warning {
    color: #f0d5a3;
}

.dark-mode .expiring-warning {
    background: rgba(255, 193, 7, 0.2);
    border-left-color: #f0d5a3;
}

.position-preview {
    position: relative;
    overflow: hidden;
}

.position-preview::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.position-card:hover .position-preview::after {
    opacity: 1;
}

.position-preview .preview-content {
    transition: all 0.3s ease;
}

.position-card:hover .preview-content {
    transform: scale(1.1);
}

.price-display {
    background: var(--theme-gradient-light);
    padding: 18px;
    text-align: center;
    border: 2px solid rgba(67, 97, 238, 0.1);
    width: 100%;
    border-radius: 10px;
}

.price-label {
    font-size: 0.9rem;
    color: var(--muted-text);
    font-weight: 500;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.95rem;
    padding: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    color: var(--muted-text);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.detail-value {
    color: var(--text-color);
    font-weight: 600;
    text-align: right;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes cardPulse {
    0%, 100% {
        box-shadow: var(--shadow);
    }
    50% {
        box-shadow: var(--shadow-lg);
    }
}

.position-card.adding-to-cart {
    animation: cardPulse 0.6s ease-in-out;
}

.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.buy-tab-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.buy-tab-loading-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    text-align: center;
    min-width: 400px;
    max-width: 500px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.buy-loading-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
}

.buy-loading-header i {
    font-size: 2rem;
    color: #fff;
    animation: pulse 2s infinite;
}

.buy-loading-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #fff;
    margin: 0;
}

.buy-progress-bar-container {
    margin-bottom: 20px;
}

.buy-progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.buy-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 10px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(79, 172, 254, 0.5);
}

.buy-progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    font-size: 0.9rem;
}

.buy-progress-percentage {
    font-weight: 600;
    font-size: 1.1rem;
}

.buy-progress-status {
    opacity: 0.9;
}

@media (prefers-color-scheme: dark) {
    .buy-tab-loading-container {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .buy-progress-bar {
        background: rgba(255, 255, 255, 0.1);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.buy-now-btn {
    background: var(--theme-gradient);
    color: white !important;
    font-weight: 700 !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.buy-now-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
    background: linear-gradient(135deg, #00c90b, #009190) !important;
}

.buy-now-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.buy-now-btn:hover::before {
    left: 100%;
}

.buy-now-btn i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-3px);
    }
    60% {
        transform: translateY(-2px);
    }
}

.filter-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0,0,0,0.1);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    to { transform: translate(-50%, -50%) rotate(360deg); }
}

.positions-grid.filtering::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.5);
    z-index: 9;
}
</style>

<div class="buy-tab-container">
    <div class="floating-elements">
        <i class="fas fa-shopping-cart floating-icon"></i>
        <i class="fas fa-star floating-icon"></i>
        <i class="fas fa-gem floating-icon"></i>
        <i class="fas fa-bolt floating-icon"></i>
    </div>

    <div class="buy-hero-section">
        <div class="hero-content">
            <h1 class="hero-title">🛒 ร้านค้าตำแหน่งโฆษณา</h1>
            <p class="hero-subtitle">เลือกตำแหน่งโฆษณาที่เหมาะสมกับธุรกิจของคุณ</p>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-icon">✅</span>
                    <div class="stat-number"><?php echo $available_count; ?></div>
                    <div class="stat-label">พร้อมใช้งาน</div>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">👤</span>
                    <div class="stat-number"><?php echo $owned_count; ?></div>
                    <div class="stat-label">ของคุณ</div>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">⏰</span>
                    <div class="stat-number"><?php echo $reserved_count; ?></div>
                    <div class="stat-label">ถูกจอง</div>
                </div>
                <div class="stat-card">
                    <span class="stat-icon">📊</span>
                    <div class="stat-number"><?php echo $total_positions; ?></div>
                    <div class="stat-label">ทั้งหมด</div>
                </div>
            </div>
        </div>
    </div>

    <div class="search-filter-section">
        <div class="search-box">
            <input type="text" id="position-search" class="search-input" placeholder="🔍 ค้นหาตำแหน่งโฆษณา... (กด Enter เพื่อค้นหา)">
            <i class="fas fa-search search-icon"></i>
        </div>
        
        <div class="filter-pills">
            <span class="filter-pill active" data-filter="all">🌟 ทั้งหมด</span>
            <span class="filter-pill" data-filter="available">✅ พร้อมซื้อ</span>
            <span class="filter-pill ajax-nav-btn" data-tab="my-ads">👤 ของฉัน</span>
            <span class="filter-pill" data-filter="expiring">⏰ ใกล้หมดอายุ</span>
            <span class="filter-pill" data-filter="reserved">🔒 ถูกจอง</span>
        </div>
        
        <div class="sort-controls">
            <div class="sort-group">
                <label class="sort-label">🔤 เรียงตาม:</label>
                <select id="sort-by" class="sort-select">
                    <option value="name-asc">ชื่อ A-Z</option>
                    <option value="name-desc">ชื่อ Z-A</option>
                    <option value="price-asc">ราคา ต่ำ-สูง</option>
                    <option value="price-desc">ราคา สูง-ต่ำ</option>
                    <option value="size-asc">ขนาด เล็ก-ใหญ่</option>
                    <option value="size-desc">ขนาด ใหญ่-เล็ก</option>
                </select>
            </div>
            <div class="sort-group">
                <label class="sort-label">💰 ช่วงราคา:</label>
                <select id="price-range" class="sort-select">
                    <option value="all">ทุกราคา</option>
                    <option value="0-50">0 - 50 USDT</option>
                    <option value="50-100">50 - 100 USDT</option>
                    <option value="100-200">100 - 200 USDT</option>
                    <option value="200-500">200 - 500 USDT</option>
                    <option value="500-999999">500+ USDT</option>
                </select>
            </div>
        </div>
    </div>

    <div class="positions-grid" id="positions-container">
        <?php if (empty($available_positions_data)): ?>
            <div class="empty-state">
                <i class="fas fa-box-open empty-icon"></i>
                <h3 class="empty-title">ไม่มีตำแหน่งโฆษณา</h3>
                <p class="empty-message">ขณะนี้ไม่มีตำแหน่งโฆษณาให้เลือกซื้อ กรุณาลองใหม่ภายหลัง</p>
            </div>
        <?php else: ?>
            <?php foreach ($available_positions_data as $position): ?>
                <?php 
                $position_name = $position->name ?? 'Unknown';
                $position_width = $position->width ?? 0;
                $position_height = $position->height ?? 0;
                $position_type = $position->type ?? 'banner';
                $position_price = $position->base_price ?? $position->price ?? 0;
                
                $visibility = $position->visibility;
                $ownership = $position->ownership;
                $reason = $visibility['reason'] ?? 'unavailable';
                $is_owner = $ownership['is_owned_by_current_user'] ?? false;
                $is_in_cart = in_array($position_name, $cart_positions);

                $final_filter_attr = 'unavailable';

                $show_primary_button = true;
                $show_secondary_button = false;
                $show_third_button = false;

                if ($reason === 'available' || ($reason === 'owned_expired' && !$is_owner)) {
                    $final_filter_attr = 'available';
                } elseif ($reason === 'reserved' || $reason === 'reserved_by_user') {
                    $final_filter_attr = 'reserved';
                } elseif ($is_owner && ($reason === 'expiring_soon' || $reason === 'owned_expired')) {
                    $final_filter_attr = 'expiring';
                } elseif ($is_owner) {
                    $final_filter_attr = 'owned_other'; 
                }

                $status_text = 'ไม่พร้อมใช้งาน';
                $status_class = 'status-unavailable';
                $button_text = 'ไม่พร้อมใช้งาน';
                $button_icon = 'fa-lock';
                $button_class = 'btn-disabled';
                $button_action = '';
                $action_disabled = true;
                $show_secondary_button = false;
                $secondary_button_text = '';
                $secondary_button_icon = '';
                $secondary_button_class = '';
                $secondary_button_action = '';
                $owner_display_name = '';
                $expiring_warning_text = '';

                switch ($reason) {
                    case 'available':
                        $status_text = 'พร้อมใช้งาน';
                        $status_class = 'status-available';
                        $button_text = 'ซื้อสินค้า';
                        $button_icon = 'fa-shopping-cart';
                        $button_class = 'btn-primary buy-now-btn';
                        $button_action = "buyNow('".esc_js($position_name)."', ".$position_price.", this)";
                        $action_disabled = false;
                        if ($is_in_cart) {
                            $button_text = 'อยู่ในตะกร้า';
                            $button_icon = 'fa-check-circle';
                            $button_class = 'btn-success in-cart-btn';
                            $button_action = "removeFromCart('".esc_js($position_name)."', this)";
                        }
                        
                        $show_secondary_button = true;
                        $secondary_button_text = 'ตัวอย่าง';
                        $secondary_button_icon = 'fa-search-plus';
                        $secondary_button_class = 'btn-info quick-preview-btn';
                        $secondary_button_action = "quickPreview('".esc_js($position_name)."', this)";
                        break;
                    
                    case 'owned_expired':
                        $is_owner = $ownership['is_owned_by_current_user'] ?? false;
                        if ($is_owner) {
                            $status_text = 'หมดอายุ';
                            $status_class = 'status-expiring';
                            $button_text = 'ต่ออายุ';
                            $button_icon = 'fa-clock';
                            $button_class = 'btn-warning';
                            $button_action = "extendPosition('".esc_js($position_name)."')";
                            $action_disabled = false;
                        } else {
                            $status_text = 'ว่าง (หมดอายุ)';
                            $status_class = 'status-available';
                            $button_text = 'ซื้อสินค้า';
                            $button_icon = 'fa-shopping-cart';
                            $button_class = 'btn-primary buy-now-btn';
                            $button_action = "buyNow('".esc_js($position_name)."', ".$position_price.", this)";
                            $action_disabled = false;
                            
                            $show_secondary_button = true;
                            $secondary_button_text = 'ตัวอย่าง';
                            $secondary_button_icon = 'fa-search-plus';
                            $secondary_button_class = 'btn-info quick-preview-btn';
                            $secondary_button_action = "quickPreview('".esc_js($position_name)."', this)";
                        }
                        break;

                    case 'expiring_soon':
                        $status_text = 'ใกล้หมดอายุ';
                        $status_class = 'status-expiring';
                        $days_remaining = $visibility['days_remaining'] ?? 0;
                        $expiring_warning_text = "เหลืออีก {$days_remaining} วัน";
                        
                        if ($ownership['is_owned_by_current_user']) {
                            $button_text = 'ต่ออายุ';
                            $button_icon = 'fa-clock';
                            $button_class = 'btn-warning';
                            $button_action = "extendPosition('".esc_js($position_name)."')";
                            $action_disabled = false;
                        } else {
                            $show_primary_button = false;
                            $show_secondary_button = true;
                            $secondary_button_text = 'เจ้าของ';
                            $secondary_button_icon = 'fa-user';
                            $secondary_button_class = 'btn-info';

                            if ($ownership['owner_id']) {
                                $owner_user = get_userdata($ownership['owner_id']);
                                if ($owner_user) {
                                    $owner_display_name = $owner_user->display_name ?: $owner_user->user_login;
                                    $owner_details = [
                                        'เจ้าของ' => $owner_display_name,
                                        'หมดอายุ' => date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ownership['expiration_date'])),
                                        'เหลืออีก' => $expiring_warning_text
                                    ];
                                    $owner_details_json = esc_attr(json_encode($owner_details));
                                    $secondary_button_action = "showPositionDetails({$owner_details_json})";
                                } else {
                                    $secondary_button_action = "showPositionDetails({'เจ้าของ': 'ไม่พบข้อมูล'})";
                                }
                            } else {
                                $secondary_button_action = "showPositionDetails({'เจ้าของ': 'ไม่พบข้อมูล'})";
                            }

                            $show_third_button = true;
                            $third_button_text = 'รายละเอียด';
                            $third_button_icon = 'fa-info-circle';
                            $third_button_class = 'btn-secondary';
                            $third_button_action = "quickPreview('".esc_js($position_name)."', this)";
                        }

                        if ($ownership['owner_id']) {
                            $owner_user = get_userdata($ownership['owner_id']);
                            if ($owner_user) {
                                $owner_display_name = $owner_user->display_name ?: $owner_user->user_login;
                            }
                        }
                        break;

                    case 'reserved':
                        $status_text = 'ถูกจอง';
                        $status_class = 'status-reserved';
                        break;
                }
                
                ?>
                
                <div class="position-card" data-position="<?php echo esc_attr($position_name); ?>" data-filter="<?php echo esc_attr($final_filter_attr); ?>" data-price="<?php echo $position_price; ?>" data-size="<?php echo ($position_width * $position_height); ?>">
                    <div class="position-preview">
                        <div class="preview-content">
                            <i class="fas fa-ad preview-icon"></i>
                            <div class="preview-size"><?php echo esc_html($position_width); ?>×<?php echo esc_html($position_height); ?></div>
                        </div>
                    </div>
                    
                    <div class="position-info">
                        <div class="position-header">
                            <h3 class="position-name"><?php echo esc_html($position_name); ?></h3>
                            <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                        </div>
                        
                        <div class="position-details">
                            <div class="detail-row">
                                <span class="detail-label">🏷️ ประเภท:</span>
                                <span class="detail-value"><?php echo esc_html($position_type); ?></span>
                            </div>
                            <?php 
                            $details_to_show = [];
                            if ($ownership['expiration_date'] && in_array($reason, ['owned_expired', 'expiring_soon'])) {
                                $details_to_show['หมดอายุ'] = esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($ownership['expiration_date'])));
                            }
                            if (!empty($owner_display_name)) {
                                $details_to_show['เจ้าของ'] = esc_html($owner_display_name);
                            }
                            if (!empty($expiring_warning_text)) {
                                $details_to_show['เตือน'] = esc_html($expiring_warning_text);
                            }
                            ?>
                        </div>
                        
                        <?php if ($position_price > 0): ?>
                            <div class="price-display">
                                <div class="price-amount"><?php echo number_format($position_price, 0); ?> USDT</div>
                                <div class="price-label">ราคาเริ่มต้น / เดือน</div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="position-actions <?php echo ($show_secondary_button || !empty($details_to_show) || isset($show_third_button)) ? '' : 'single-button'; ?>">
                            <?php if (isset($show_primary_button) && !$show_primary_button): ?>
                            <?php else: ?>
                                <button class="action-btn <?php echo $button_class; ?>" <?php echo $action_disabled ? 'disabled' : ''; ?> onclick="<?php echo $button_action; ?>">
                                    <i class="fas <?php echo $button_icon; ?>"></i>
                                    <?php echo $button_text; ?>
                                </button>
                            <?php endif; ?>

                            <?php if ($show_secondary_button): ?>
                               <button class="action-btn <?php echo $secondary_button_class; ?>" onclick="<?php echo $secondary_button_action; ?>">
                                    <i class="fas <?php echo $secondary_button_icon; ?>"></i>
                                    <?php echo $secondary_button_text; ?>
                                </button>
                            <?php elseif (!empty($details_to_show)): ?>
                                <?php
                                $details_json = esc_attr(json_encode($details_to_show));
                                ?>
                                <button class="action-btn btn-secondary" onclick='showPositionDetails(<?php echo $details_json; ?>)'>
                                    <i class="fas fa-info-circle"></i>
                                    ดูรายละเอียด
                                </button>
                            <?php endif; ?>

                            <?php if (isset($show_third_button) && $show_third_button): ?>
                                <button class="action-btn <?php echo $third_button_class; ?>" onclick="<?php echo $third_button_action; ?>">
                                    <i class="fas <?php echo $third_button_icon; ?>"></i>
                                    <?php echo $third_button_text; ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<script>
window.initBuyPageFunctions = function() {
    const searchInput = document.getElementById('position-search');
    const filterPillsContainer = document.querySelector('.filter-pills');
    const sortBySelect = document.getElementById('sort-by');
    const priceRangeSelect = document.getElementById('price-range');
    const positionsContainer = document.getElementById('positions-container');

    if (!positionsContainer) {
        console.error("Filter system cannot initialize: positions container not found.");
        return;
    }

    window.BuyTabPolling.startPositionPolling();

    const allPositionCards = Array.from(document.querySelectorAll('.position-card'));

    let state = {
        search: '',
        filter: 'all',
        sort: 'name-asc',
        price: 'all'
    };

    const naturalSort = (a, b) => {
        const re = /(\d+)/g;
        const aName = a.dataset.position;
        const bName = b.dataset.position;
        const aParts = aName.split(re);
        const bParts = bName.split(re);

        for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
            const aPart = aParts[i];
            const bPart = bParts[i];

            if (i % 2 === 1) {
                const aNum = parseInt(aPart, 10);
                const bNum = parseInt(bPart, 10);
                if (aNum !== bNum) {
                    return aNum - bNum;
                }
            } else {
                if (aPart.toLowerCase() !== bPart.toLowerCase()) {
                    return aPart.localeCompare(bPart);
                }
            }
        }
        return aParts.length - bParts.length;
    };

    const getPrice = card => parseFloat(card.dataset.price) || 0;
    const getSize = card => parseInt(card.dataset.size) || 0;

    const renderPositions = () => {
        const container = positionsContainer;
        
        const filteredCards = allPositionCards.filter(card => {
            const name = card.dataset.position.toLowerCase();
            const filterAttr = card.dataset.filter;
            const price = getPrice(card);

            const matchesSearch = name.includes(state.search.toLowerCase());

            let matchesFilter = true;
            if (state.filter !== 'all') {
                matchesFilter = filterAttr === state.filter;
            }

            let matchesPrice = true;
            if (state.price !== 'all') {
                const [min, max] = state.price.split('-').map(Number);
                matchesPrice = price >= min && (max === 999999 || price <= max);
            }

            return matchesSearch && matchesFilter && matchesPrice;
        });

        switch (state.sort) {
            case 'name-asc': filteredCards.sort(naturalSort); break;
            case 'name-desc': filteredCards.sort((a, b) => naturalSort(b, a)); break;
            case 'price-asc': filteredCards.sort((a, b) => getPrice(a) - getPrice(b)); break;
            case 'price-desc': filteredCards.sort((a, b) => getPrice(b) - getPrice(a)); break;
            case 'size-asc': filteredCards.sort((a, b) => getSize(a) - getSize(b)); break;
            case 'size-desc': filteredCards.sort((a, b) => getSize(b) - getSize(a)); break;
        }

        const itemCount = filteredCards.length;
        container.className = 'positions-grid';
        if (itemCount >= 4) {
            container.classList.add('layout-4-cols');
        }

        container.innerHTML = '';
        container.style.minHeight = '';
        container.style.position = '';

        if (filteredCards.length > 0) {
            filteredCards.forEach(card => container.appendChild(card));
        } else {
            container.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <i class="fas fa-search empty-icon"></i>
                    <h3 class="empty-title">ไม่พบตำแหน่งที่ค้นหา</h3>
                    <p class="empty-message">ลองปรับเงื่อนไขการค้นหาหรือกรองใหม่</p>
                </div>`;
        }
    };

    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            state.search = searchInput.value;
            showLoadingAndRender('search');
        }
    });

    const searchIcon = document.querySelector('.search-icon');
    if (searchIcon) {
        searchIcon.addEventListener('click', () => {
            state.search = searchInput.value;
            showLoadingAndRender('search');
        });
        searchIcon.style.cursor = 'pointer';
    }

    sortBySelect.addEventListener('change', () => {
        state.sort = sortBySelect.value;
        showLoadingAndRender('sort', 'เรียงลำดับ');
    });

    priceRangeSelect.addEventListener('change', () => {
        state.price = priceRangeSelect.value;
        showLoadingAndRender('sort', 'กรองราคา');
    });

    function showLoadingAndRender(type, actionName = '') {
        const container = positionsContainer;

        const loadingConfig = {
            search: {
                icon: 'fa-search',
                title: 'กำลังค้นหา...',
                initialStatus: 'เริ่มต้นการค้นหา...',
                steps: [
                    { percent: 30, status: "วิเคราะห์คำค้นหา..." },
                    { percent: 60, status: "ค้นหาในฐานข้อมูล..." },
                    { percent: 90, status: "จัดเรียงผลลัพธ์..." }
                ],
                interval: 100,
                totalTime: 400,
                delay: 200
            },
            sort: {
                icon: 'fa-sort',
                title: `กำลัง${actionName}...`,
                initialStatus: 'เริ่มต้นการประมวลผล...',
                steps: [
                    { percent: 25, status: "วิเคราะห์เงื่อนไข..." },
                    { percent: 50, status: "ประมวลผลข้อมูล..." },
                    { percent: 75, status: "จัดเรียงใหม่..." },
                    { percent: 95, status: "เตรียมการแสดงผล..." }
                ],
                interval: 120,
                totalTime: 500,
                delay: 250
            },
            filter: {
                icon: 'fa-filter',
                title: `กำลังคัดกรอง${getFilterDisplayName(state.filter)}...`,
                initialStatus: 'เริ่มต้นการคัดกรอง...',
                steps: [
                    { percent: 20, status: "ตรวจสอบเงื่อนไข..." },
                    { percent: 40, status: "วิเคราะห์ข้อมูล..." },
                    { percent: 60, status: "คัดกรองตำแหน่ง..." },
                    { percent: 80, status: "จัดเรียงผลลัพธ์..." },
                    { percent: 95, status: "เตรียมการแสดงผล..." }
                ],
                interval: 150,
                totalTime: 800,
                delay: 300
            }
        };

        const config = loadingConfig[type];
        if (!config) return;

        const overlay = document.createElement('div');
        overlay.className = 'buy-tab-loading-overlay';
        overlay.id = 'buy-loading-overlay';

        overlay.innerHTML = `
            <div class="buy-tab-loading-container">
                <div class="buy-loading-header">
                    <i class="fas ${config.icon}"></i>
                    <h3 class="buy-loading-title">${config.title}</h3>
                </div>
                <div class="buy-progress-bar-container">
                    <div class="buy-progress-bar">
                        <div class="buy-progress-fill" id="buy-progress-fill"></div>
                    </div>
                    <div class="buy-progress-text">
                        <span class="buy-progress-percentage" id="buy-progress-percentage">0%</span>
                        <span class="buy-progress-status" id="buy-progress-status">${config.initialStatus}</span>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        const progressFill = document.getElementById('buy-progress-fill');
        const progressPercentage = document.getElementById('buy-progress-percentage');
        const progressStatus = document.getElementById('buy-progress-status');

        let stepIndex = 0;
        const progressInterval = setInterval(() => {
            if (stepIndex < config.steps.length) {
                const step = config.steps[stepIndex];
                if (progressFill) progressFill.style.width = step.percent + '%';
                if (progressPercentage) progressPercentage.textContent = step.percent + '%';
                if (progressStatus) progressStatus.textContent = step.status;
                stepIndex++;
            }
        }, config.interval);

        setTimeout(() => {
            clearInterval(progressInterval);
            if (progressFill) progressFill.style.width = '100%';
            if (progressPercentage) progressPercentage.textContent = '100%';
            if (progressStatus) progressStatus.textContent = 'เสร็จสิ้น!';

            setTimeout(() => {
                document.body.removeChild(overlay);
                renderPositions();
            }, config.delay);
        }, config.totalTime);
    }



    if (filterPillsContainer) {
        filterPillsContainer.addEventListener('click', function(e) {
            const pill = e.target.closest('.filter-pill');
            if (!pill) return;

            e.preventDefault();

            if (pill.classList.contains('ajax-nav-btn')) {
                if (typeof window.loadTabContent === 'function') {
                    const tab = pill.getAttribute('data-tab');
                    window.loadTabContent(tab);
                }
                return;
            }

            if (this.querySelector('.active')) {
               this.querySelector('.active').classList.remove('active');
            }
            pill.classList.add('active');

            state.filter = pill.dataset.filter;
            showLoadingAndRender('filter');
        });
    }



    function getFilterDisplayName(filter) {
        const filterNames = {
            'all': 'ทั้งหมด',
            'available': 'พร้อมซื้อ',
            'expiring': 'ใกล้หมดอายุ',
            'reserved': 'ถูกจอง',
            'owned_other': 'ของคุณ'
        };
        return filterNames[filter] || 'ข้อมูล';
    }

    renderPositions();
};

function buyNow(positionName, basePrice, btn) {
    if (typeof adDashboardData === 'undefined') {
        if (typeof showMiniPopup === 'function') {
            showMiniPopup('ไม่สามารถเชื่อมต่อกับระบบได้', 'error');
        } else {
            alert('ไม่สามารถเชื่อมต่อกับระบบได้');
        }
        return;
    }

    $.ajax({
        url: adDashboardData.ajaxurl,
        type: 'POST',
        data: {
            action: 'check_development_mode',
            security: adDashboardData.nonce
        },
        success: function(response) {
            if (response.success && response.data.is_enabled) {
                if (typeof showMiniPopup === 'function') {
                    showMiniPopup('🚧 ระบบอยู่ในโหมดพัฒนา ไม่สามารถซื้อสินค้าได้ในขณะนี้', 'warning');
                } else {
                    alert('🚧 ระบบอยู่ในโหมดพัฒนา ไม่สามารถซื้อสินค้าได้ในขณะนี้');
                }
                return;
            }

            if (typeof window.showDurationPopup === 'function') {
                window.showDurationPopup(positionName, basePrice, btn);
            } else {
                if (typeof showMiniPopup === 'function') {
                    showMiniPopup('ระบบซื้อสินค้ายังไม่พร้อมใช้งาน', 'error');
                } else {
                    alert('ระบบซื้อสินค้ายังไม่พร้อมใช้งาน');
                }
            }
        },
        error: function() {
            if (typeof showMiniPopup === 'function') {
                showMiniPopup('ไม่สามารถตรวจสอบสถานะระบบได้', 'error');
            } else {
                alert('ไม่สามารถตรวจสอบสถานะระบบได้');
            }
        }
    });
}

window.refreshBuyTabAfterPurchase = function() {
    if (typeof window.loadTabContent === 'function') {
        window.loadTabContent('buy');
    } else {
        window.location.reload();
    }
};

function cancelReservation(positionName) {
    if (typeof adDashboardData === 'undefined') {
        return;
    }

    $.ajax({
        url: adDashboardData.ajaxurl,
        type: 'POST',
        data: {
            action: 'cancel_reservation',
            security: adDashboardData.nonce
        },
        success: function(response) {
            if (response.success) {
                if (typeof showMiniPopup === 'function') {
                    showMiniPopup('ยกเลิกการจองเรียบร้อยแล้ว', 'success');
                }
                window.BuyTabPolling.fetchPositionUpdates();
            } else {
                if (typeof showMiniPopup === 'function') {
                    showMiniPopup('ไม่สามารถยกเลิกการจองได้', 'error');
                }
            }
        },
        error: function(xhr, status, error) {
            if (typeof showMiniPopup === 'function') {
                showMiniPopup('เกิดข้อผิดพลาดในการยกเลิกการจอง', 'error');
            }
        }
    });
}

function extendPosition(positionName) {
    if (typeof showMiniPopup === 'function') {
        showMiniPopup('ฟีเจอร์การต่ออายุยังไม่พร้อมใช้งาน', 'info');
    }
}

function managePosition(positionName) {
    if (typeof showMiniPopup === 'function') {
        showMiniPopup('ฟีเจอร์การจัดการยังไม่พร้อมใช้งาน', 'info');
    }
}

function managePosition(positionName) {
    if (typeof showMiniPopup === 'function') {
        showMiniPopup(`กำลังเปิดหน้าจัดการ ${positionName}...`, 'info');
    }
}

function extendPosition(positionName) {
    if (typeof window.showDurationPopup === 'function') {
        window.showDurationPopup(positionName, 0, null);
    } else {
        if (typeof showMiniPopup === 'function') {
            showMiniPopup(`กำลังต่ออายุ ${positionName}...`, 'info');
        }
    }
}

function quickPreview(positionName, buttonElement) {
    const card = buttonElement.closest('.position-card');
    const size = card.querySelector('.preview-size').textContent;
    const price = card.querySelector('.price-amount') ? card.querySelector('.price-amount').textContent : 'ไม่ระบุ';
    const status = card.querySelector('.status-badge').textContent;
    const basePrice = parseFloat(card.getAttribute('data-price')) || 0;

    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: `📺 ${positionName}`,
            html: `
                <div style="text-align: left; font-size: 16px;">
                    <div style="margin-bottom: 15px;">
                        <strong>💰 ราคา:</strong> ${price}/เดือน
                    </div>
                    <div style="margin-bottom: 15px;">
                        <strong>📊 สถานะ:</strong> <span style="color: #28a745;">${status}</span>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <strong>🎯 ตัวอย่างตำแหน่งโฆษณา</strong><br>
                        <div style="width: 200px; height: 100px; background: linear-gradient(45deg, #4361ee, #7209b7);
                                    border-radius: 8px; margin: 10px auto; display: flex; align-items: center;
                                    justify-content: center; color: white; font-weight: bold;">
                            ${positionName}<br>
                            <small>${size}</small>
                        </div>
                    </div>
                </div>
            `,
            showConfirmButton: true,
            confirmButtonText: '🛒 ซื้อสินค้า',
            showCancelButton: true,
            cancelButtonText: 'ปิด',
            confirmButtonColor: '#4361ee',
            width: 500
        }).then((result) => {
            if (result.isConfirmed) {
                buyNow(positionName, basePrice, buttonElement);
            }
        });
    } else {
        alert(`ตำแหน่ง: ${positionName}\\nราคา: ${price}/เดือน\\nสถานะ: ${status}`);
    }
}

function showPositionDetails(details) {
    if (typeof Swal !== 'undefined') {
        let htmlContent = '<div style="text-align: left; font-size: 16px;">';
        for (const [key, value] of Object.entries(details)) {
            htmlContent += `<div style="margin-bottom: 15px;"><strong>${key}:</strong> ${value}</div>`;
        }
        htmlContent += '</div>';

        Swal.fire({
            title: '📜 รายละเอียดเพิ่มเติม',
            html: htmlContent,
            icon: 'info',
            confirmButtonText: 'ปิด',
            confirmButtonColor: '#4361ee'
        });
    } else {
        let alertContent = '';
        for (const [key, value] of Object.entries(details)) {
            alertContent += `${key}: ${value}\\n`;
        }
        alert(alertContent);
    }
}

window.onCartItemAdded = function(position) {
    if (typeof jQuery !== 'undefined') {
        const buyButton = jQuery(`.buy-now-btn[onclick*="'${position}'"]`);
        if (buyButton.length > 0) {
            const newButton = jQuery(`<button class="action-btn btn-success in-cart-btn" onclick="removeFromCart('${position}', this)">
                <i class="fas fa-check-circle"></i>
                อยู่ในตะกร้า
            </button>`);
            buyButton.replaceWith(newButton);
        }
    }
};

window.BuyTabPolling = {
    positionStatusInterval: null,

    startPositionPolling: function() {
        if (this.positionStatusInterval) return;
        this.fetchPositionUpdates();
        this.positionStatusInterval = setInterval(() => this.fetchPositionUpdates(), 7500);
    },

    stopPositionPolling: function() {
        if (this.positionStatusInterval) {
            clearInterval(this.positionStatusInterval);
            this.positionStatusInterval = null;
        }
    },

    getCurrentFilterState: function() {
        const activeFilter = document.querySelector('.filter-pill.active');
        const searchInput = document.getElementById('position-search');
        const sortSelect = document.getElementById('sort-by');
        const priceSelect = document.getElementById('price-range');

        return {
            filter: activeFilter ? activeFilter.dataset.filter : 'all',
            search: searchInput ? searchInput.value.toLowerCase() : '',
            sort: sortSelect ? sortSelect.value : 'name-asc',
            price: priceSelect ? priceSelect.value : 'all'
        };
    },

    cardMatchesFilter: function(card, filterState) {
        const name = card.dataset.position.toLowerCase();
        const filterAttr = card.dataset.filter;
        const price = parseFloat(card.dataset.price) || 0;

        if (filterState.search && !name.includes(filterState.search)) {
            return false;
        }

        if (filterState.filter !== 'all' && filterAttr !== filterState.filter) {
            return false;
        }

        if (filterState.price !== 'all') {
            const [min, max] = filterState.price.split('-').map(Number);
            if (max && (price < min || price > max)) return false;
            if (!max && price < min) return false;
        }

        return true;
    },

    cardMatchesSearchAndPrice: function(card, filterState) {
        const name = card.dataset.position.toLowerCase();
        const price = parseFloat(card.dataset.price) || 0;

        if (filterState.search && !name.includes(filterState.search)) {
            return false;
        }

        if (filterState.price !== 'all') {
            const [min, max] = filterState.price.split('-').map(Number);
            if (max && (price < min || price > max)) return false;
            if (!max && price < min) return false;
        }

        return true;
    },



    reApplyCurrentFilterWithServerData: function(statuses) {
        const positionsContainer = document.getElementById('positions-container');
        if (!positionsContainer) return;

        const filterState = this.getCurrentFilterState();
        const allCards = positionsContainer.querySelectorAll('.position-card');

        allCards.forEach(card => {
            const positionName = card.getAttribute('data-position');
            const serverStatus = statuses[positionName];

            if (serverStatus) {
                const shouldShow = this.cardMatchesServerFilter(serverStatus, filterState, card);
                card.style.display = shouldShow ? 'block' : 'none';
            } else {
                const shouldShow = this.cardMatchesFilter(card, filterState);
                card.style.display = shouldShow ? 'block' : 'none';
            }
        });
    },

    cardMatchesServerFilter: function(serverStatus, filterState, card) {
        if (!this.cardMatchesSearchAndPrice(card, filterState)) {
            return false;
        }

        if (filterState.filter === 'all') return true;

        if (filterState.filter === 'available') {
            return serverStatus.can_add_to_cart;
        }

        if (filterState.filter === 'reserved') {
            return serverStatus.reserved_by_user || serverStatus.reserved_by_other;
        }

        if (filterState.filter === 'owned_other') {
            return serverStatus.owned_by_current_user && !serverStatus.is_expired;
        }

        if (filterState.filter === 'expiring') {
            return (serverStatus.owned_by_current_user && serverStatus.is_expired) || 
                   (serverStatus.owned_by_current_user && serverStatus.expiring_soon);
        }

        return false;
    },

    fetchPositionUpdates: function() {
        const positionsContainer = document.getElementById('positions-container');
        if (!positionsContainer || typeof adDashboardData === 'undefined' || !adDashboardData.ajaxurl) {
            this.stopPositionPolling();
            return;
        }

        $.ajax({
            url: adDashboardData.ajaxurl,
            type: 'POST',
            data: { action: 'get_all_positions_status', security: adDashboardData.nonce },
            success: function(response) {
                if (response.success) {
                    window.BuyTabPolling.updatePositionsUI(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('BuyTab AJAX Error:', error);
            }
        });
    },

    updatePositionsUI: function(statuses) {
        const positionsContainer = document.getElementById('positions-container');
        if (!positionsContainer) return;

        for (const positionName in statuses) {
            if (statuses.hasOwnProperty(positionName)) {
                const card = positionsContainer.querySelector(`.position-card[data-position="${positionName}"]`);
                if (card) {
                    const statusData = statuses[positionName];
                    const isNowReserved = statusData.reserved_by_user || statusData.reserved_by_other;
                    const wasReserved = card.getAttribute('data-filter') === 'reserved';

                    if (isNowReserved || wasReserved) {
                        this.updatePositionCard(card, statusData);
                    }
                }
            }
        }

        this.updateReservationStatistics(statuses);
        this.reApplyCurrentFilterWithServerData(statuses);
    },

    updateReservationStatistics: function(statuses) {
        const statsContainer = document.querySelector('.stats-grid');
        if (!statsContainer) return;

        let availableCount = 0;
        let ownedCount = 0;
        let reservedCount = 0;
        let totalCount = 0;

        for (const positionName in statuses) {
            if (statuses.hasOwnProperty(positionName)) {
                const status = statuses[positionName];
                totalCount++;

                if (status.can_add_to_cart) {
                    availableCount++;
                } else if (status.owned_by_current_user) {
                    ownedCount++;
                } else if (status.reserved_by_other || status.reserved_by_user) {
                    reservedCount++;
                }
            }
        }

        const statCards = statsContainer.querySelectorAll('.stat-card .stat-number');
        if (statCards.length >= 4) {
            const availableCard = statCards[0];
            const ownedCard = statCards[1];
            const reservedCard = statCards[2];
            const totalCard = statCards[3];

            let statsChanged = false;

            if (availableCard && availableCard.textContent != availableCount) {
                availableCard.textContent = availableCount;
                availableCard.classList.add('stat-updated');
                setTimeout(() => availableCard.classList.remove('stat-updated'), 1000);
                statsChanged = true;
            }

            if (ownedCard && ownedCard.textContent != ownedCount) {
                ownedCard.textContent = ownedCount;
                ownedCard.classList.add('stat-updated');
                setTimeout(() => ownedCard.classList.remove('stat-updated'), 1000);
                statsChanged = true;
            }

            if (reservedCard && reservedCard.textContent != reservedCount) {
                reservedCard.textContent = reservedCount;
                reservedCard.classList.add('stat-updated');
                setTimeout(() => reservedCard.classList.remove('stat-updated'), 1000);
                statsChanged = true;
            }

            if (totalCard && totalCard.textContent != totalCount) {
                totalCard.textContent = totalCount;
                totalCard.classList.add('stat-updated');
                setTimeout(() => totalCard.classList.remove('stat-updated'), 1000);
                statsChanged = true;
            }


        }
    },

    updatePositionCard: function(card, statusData) {
        const statusBadge = card.querySelector('.status-badge');
        const actionsContainer = card.querySelector('.position-actions');
        if (!statusBadge || !actionsContainer) return false;

        const positionName = card.getAttribute('data-position');
        const currentStatusText = statusBadge.textContent.trim();
        const currentStatusClass = statusBadge.className;

        let newStatusText = '';
        let newStatusClass = '';
        let newActionsHTML = '';
        let newFilterAttr = '';
        let hasChanged = false;

        if (statusData.reserved_by_user) {
            newStatusText = 'คุณจอง';
            newStatusClass = 'status-badge status-reserved-by-user';
            newActionsHTML = `<button class="action-btn btn-warning" onclick="cancelReservation('${positionName}')"><i class="fas fa-times"></i> ยกเลิกการจอง</button>`;
            newFilterAttr = 'reserved';
        } else if (statusData.reserved_by_other) {
            newStatusText = 'ถูกจอง';
            newStatusClass = 'status-badge status-reserved';
            newActionsHTML = `<button class="action-btn btn-disabled" disabled><i class="fas fa-lock"></i> ถูกจอง</button>`;
            newFilterAttr = 'reserved';
        } else if (statusData.owned_by_current_user) {
            if (statusData.is_expired) {
                newStatusText = 'หมดอายุ';
                newStatusClass = 'status-badge status-expired';
                newActionsHTML = `<button class="action-btn btn-warning" onclick="extendPosition('${positionName}')"><i class="fas fa-clock"></i> ต่ออายุ</button>`;
                newFilterAttr = 'expiring';
            } else if (statusData.expiring_soon) {
                const daysRemaining = statusData.days_remaining || 0;
                newStatusText = `ใกล้หมดอายุ (${daysRemaining} วัน)`;
                newStatusClass = 'status-badge status-expiring';
                newActionsHTML = `<button class="action-btn btn-warning" onclick="extendPosition('${positionName}')"><i class="fas fa-clock"></i> ต่ออายุ</button>`;
                newFilterAttr = 'expiring';
            } else {
                newStatusText = 'ของคุณ';
                newStatusClass = 'status-badge status-owned-by-user';
                newActionsHTML = `<button class="action-btn btn-info" onclick="managePosition('${positionName}')"><i class="fas fa-cog"></i> จัดการ</button>`;
                newFilterAttr = 'owned_other';
            }
        } else if (statusData.expiring_soon && !statusData.owned_by_current_user) {
            const daysRemaining = statusData.days_remaining || 0;
            newStatusText = `ใกล้หมดอายุ (${daysRemaining} วัน)`;
            newStatusClass = 'status-badge status-expiring';

            const ownerInfo = statusData.owner_display_name || 'ไม่พบข้อมูล';
            const expirationDate = statusData.expiration_date || 'ไม่ระบุ';
            const ownerDetails = JSON.stringify({
                'เจ้าของ': ownerInfo,
                'หมดอายุ': expirationDate,
                'เหลืออีก': `${daysRemaining} วัน`
            });

            newActionsHTML = `
                <button class="action-btn btn-info" onclick='showPositionDetails(${ownerDetails})'>
                    <i class="fas fa-user"></i> เจ้าของ
                </button>
                <button class="action-btn btn-secondary" onclick="quickPreview('${positionName}', this)">
                    <i class="fas fa-info-circle"></i> รายละเอียด
                </button>`;
            newFilterAttr = 'expiring';
        } else if (statusData.can_add_to_cart) {
            newStatusText = 'พร้อมใช้งาน';
            newStatusClass = 'status-badge status-available';
            const basePrice = card.getAttribute('data-price') || 0;
            newActionsHTML = `
                <button class="action-btn btn-primary buy-now-btn" onclick="buyNow('${positionName}', ${basePrice}, this)">
                    <i class="fas fa-shopping-cart"></i> ซื้อสินค้า
                </button>
                <button class="action-btn btn-info quick-preview-btn" onclick="quickPreview('${positionName}', this)">
                    <i class="fas fa-search-plus"></i> ตัวอย่าง
                </button>`;
            newFilterAttr = 'available';
        } else {
            newStatusText = 'ไม่พร้อมใช้งาน';
            newStatusClass = 'status-badge status-unavailable';
            newActionsHTML = `<button class="action-btn btn-disabled" disabled><i class="fas fa-lock"></i> ไม่พร้อมใช้งาน</button>`;
            newFilterAttr = 'unavailable';
        }

        if (currentStatusText !== newStatusText || currentStatusClass !== newStatusClass) {
            statusBadge.textContent = newStatusText;
            statusBadge.className = newStatusClass;
            actionsContainer.innerHTML = newActionsHTML;
            card.setAttribute('data-filter', newFilterAttr);

            const isSingleButton = actionsContainer.querySelectorAll('.action-btn').length === 1;
            actionsContainer.classList.toggle('single-button', isSingleButton);

            hasChanged = true;
        }

        return hasChanged;
    }
};

window.removeFromCart = function(position, btn) {
    if (typeof adDashboardData === 'undefined' || typeof jQuery === 'undefined') {
        return;
    }

    const $btn = jQuery(btn);
    $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังลบ...');

    jQuery.ajax({
        url: adDashboardData.ajaxurl,
        type: 'POST',
        data: {
            action: 'remove_cart_item',
            position: position,
            security: adDashboardData.nonce
        },
        success: function(response) {
            if (response.success) {
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(response.data.cart_count);
                }

                const newButton = jQuery(`<button class="action-btn btn-primary buy-now-btn" onclick="buyNow('${position}', 0, this)">
                    <i class="fas fa-shopping-cart"></i>
                    ซื้อสินค้า
                </button>`);
                $btn.replaceWith(newButton);

                if (typeof window.showMiniPopup === 'function') {
                    window.showMiniPopup('ลบออกจากตะกร้าแล้ว', 'success');
                }
            } else {
                $btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i> อยู่ในตะกร้า');
                if (typeof window.showMiniPopup === 'function') {
                    window.showMiniPopup('ไม่สามารถลบออกจากตะกร้าได้', 'error');
                }
            }
        },
        error: function() {
            $btn.prop('disabled', false).html('<i class="fas fa-check-circle"></i> อยู่ในตะกร้า');
            if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup('เกิดข้อผิดพลาด', 'error');
            }
        }
    });
};
</script>