<?php
if (!defined('WPINC')) {
    die;
}

if (!function_exists('get_user_position_clicks')) {
    require_once plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/utils/click-statistics.php';
}

function get_ad_positions_table_html() {
    require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $is_administrator = current_user_can('manage_options');
    $is_advertiser = current_user_can('amp_advertiser_access');
    $can_manage_ownership = $is_administrator;
    $can_manage_expiration = $is_administrator;
    $can_delete_positions = $is_administrator;
    $can_reset_positions = $is_administrator;
    $can_modify_content = $is_administrator || $is_advertiser;
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $all_positions_objects = $position_manager->get_positions(['limit' => 9999, 'status' => 'any', 'include_ownership' => true]);
    
    error_log("AMP: get_ad_positions_table_html() found " . count($all_positions_objects) . " positions");
    
    if (empty($all_positions_objects)) {
        error_log("AMP: No positions found via PositionManager");
        global $wpdb;
        $database = \AdManagementPro\Core\Database::instance();
        $table_name = $database->get_table('ad_positions');
        $db_count = $wpdb->get_var("SELECT COUNT(*) FROM `{$table_name}`");
        error_log("AMP: Database has " . $db_count . " position records");

        if ($db_count > 0) {
            error_log("AMP: Fallback to direct database query");
            $raw_positions = $wpdb->get_results("SELECT * FROM `{$table_name}` ORDER BY ad_position ASC");
            
            if (!empty($raw_positions)) {
                $all_positions_objects = [];
                foreach ($raw_positions as $raw_position) {
                    $position = new \stdClass();
                    $position->name = $raw_position->ad_position;
                    $position->width = $raw_position->width ?? 300;
                    $position->height = $raw_position->height ?? 250;
                    $position->status = $raw_position->status ?? 'active';
                    $position->image_url = $raw_position->image_url ?? '';
                    $position->target_url = $raw_position->target_url ?? '';
                    $position->website_name = $raw_position->website_name ?? '';
                    $position->click_count = $raw_position->click_count ?? 0;
                    $position->ownership = ['expiration_date' => $raw_position->expiration_date];
                    $all_positions_objects[] = $position;
                }
                error_log("AMP: Fallback loaded " . count($all_positions_objects) . " positions");
            }
        }
        
        if (empty($all_positions_objects)) {
            return '<tr><td colspan="10" style="text-align:center;">No ad positions found. Click "Add New Position" to create your first position.</td></tr>';
        }
    }
    $unique_positions = [];
    $seen_names = [];
    foreach ($all_positions_objects as $position) {
        if (!in_array($position->name, $seen_names)) {
            $unique_positions[] = $position;
            $seen_names[] = $position->name;
        }
    }
    $all_positions_objects = $unique_positions;
    usort($all_positions_objects, function($a, $b) {
        return strnatcasecmp($a->name, $b->name);
    });
    $output = '';
    foreach ($all_positions_objects as $position) {
        $position->ownership = $position_manager->get_position_ownership_state($position->name, get_current_user_id());
        $owner_id = $position_manager->get_position_owner_from_usermeta($position->name);
        $owner_login = '-';
        if ($owner_id) {
            $user_data = get_userdata($owner_id);
            if ($user_data) {
                $owner_login = $user_data->user_login;
            }
        }
        $image = $position->image_url ?? '';
        $link = $position->target_url ?? '';
        $website_name = $position->website_name ?: '-';

        $clicks_30_days = 0;
        if (function_exists('get_user_position_clicks') && $owner_id) {
            $user_click_stats = get_user_position_clicks($owner_id, true);
            if (is_array($user_click_stats)) {
                foreach ($user_click_stats as $stat) {
                    if (isset($stat['ad_position']) && $stat['ad_position'] === $position->name) {
                        $clicks_30_days = intval($stat['total_clicks']);
                        break;
                    }
                }
            }
        }

        $status_class = ($position->status === 'active') ? 'status-active' : 'status-inactive';
        $status_text = ucfirst($position->status);
        $output .= '<tr>';
        $output .= '<td class="checkbox-cell">';
        if ($can_delete_positions) {
            $output .= '<input type="checkbox" class="position-checkbox" value="' . esc_attr($position->name) . '" data-position="' . esc_attr($position->name) . '">';
        }
        $output .= '</td>';
        $output .= '<td>' . esc_html($position->name) . '</td>';
        $output .= '<td>' . esc_html($owner_login);
        $output .= '</td>';
        $output .= '<td>' . ($image ? '<img src="' . esc_url($image) . '" width="100" />' : '-') . '</td>';
        $output .= '<td>' . ($link ? '<a href="' . esc_url($link) . '">' . esc_html(substr($link, 0, 30)).'...' . '</a>' : '-') . '</td>';
        $output .= '<td>' . esc_html($website_name) . '</td>';
        $output .= '<td>' . esc_html($position->width) . 'x' . esc_html($position->height);
        $output .= '</td>';
        $expiration_date = '-';
        if (isset($position->ownership) && !empty($position->ownership['expiration_date'])) {
            $expiration_date = date('Y-m-d', strtotime($position->ownership['expiration_date']));
        }
        $output .= '<td>' . esc_html($expiration_date);
        $output .= '</td>';
        $output .= '<td>' . esc_html($clicks_30_days) . '</td>';
        $output .= '<td>';
        $output .= '<span class="status-text ' . $status_class . '">' . $status_text . '</span><br>';
        if ($can_manage_ownership) {
            $output .= '<label class="status-toggle">';
            $output .= '<input type="checkbox" data-position="' . esc_attr($position->name) . '" ' . checked($position->status, 'active', false) . '>';
            $output .= '<span class="status-slider"></span>';
            $output .= '</label>';
        }
        $output .= '</td>';
        $output .= '<td class="actions-cell">';
        $output .= '<div class="action-buttons">';
        if ($can_modify_content) {
            $output .= '<button class="action-btn edit-ad" data-position="' . esc_attr($position->name) . '" title="Edit position"><i class="fas fa-edit"></i></button>';
        }
        if ($can_reset_positions) {
            $output .= '<button class="action-btn reset-ad" data-position="' . esc_attr($position->name) . '" title="Reset position ownership"><i class="fas fa-undo"></i></button>';
        }
        if ($can_delete_positions) {
            $output .= '<button class="action-btn delete-ad" data-position="' . esc_attr($position->name) . '" title="Delete position permanently"><i class="fas fa-trash"></i></button>';
        }  
        $output .= '</div>';
        $output .= '</td>';
        $output .= '</tr>';
    }
    return $output;
}

function get_user_ad_positions_table_html($user_login) {
    $user_login = sanitize_text_field($user_login);
    $user = get_user_by('login', $user_login);
    if (!$user) { 
        return '<table class="widefat"><tbody><tr><td colspan="8" style="text-align:center;">User not found.</td></tr></tbody></table>'; 
    }
    
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    
    $user_positions = get_user_meta($user->ID, 'ad_positions', true);
    if (!is_array($user_positions)) {
        $user_positions = [];
    }
    
    $user_ads = [];
    foreach ($user_positions as $position_name) {
        $position_data = $position_manager->get_position($position_name);
        if ($position_data && $position_data->owner_id == $user->ID) {
            $user_ads[] = [
                'position' => $position_name,
                'image' => $position_data->image_url ?? '',
                'link' => $position_data->target_url ?? '',
                'website_name' => $position_data->website_name ?? ''
            ];
        }
    }

    usort($user_ads, function($a, $b) {
        return strnatcasecmp($a['position'], $b['position']);
    });
    $o = '<table class="widefat"><thead><tr>';
    $o .= '<th>Position</th>';
    $o .= '<th>Image</th>';
    $o .= '<th>Link</th>';
    $o .= '<th>SEO Keyword</th>';
    $o .= '<th>Expiration Date</th>';
    $o .= '<th>Clicks in Last 30 Days</th>';
    $o .= '<th>Status</th>';
    $o .= '<th>Actions</th>';
    $o .= '</tr></thead><tbody>';
    if (is_array($user_ads) && !empty($user_ads)) {
        foreach ($user_ads as $ad) {
            if (!is_array($ad) || !isset($ad['position'])) continue;
            
            $position = sanitize_text_field($ad['position']);
            $image = isset($ad['image']) ? $ad['image'] : '';
            $link = isset($ad['link']) ? $ad['link'] : '';
            $website_name = isset($ad['website_name']) ? sanitize_text_field($ad['website_name']) : '';
            
            $position_data = $position_manager->get_position($position);
            $expiration_date = '-';
            $active = 1;
            
            if ($position_data) {
                if ($position_data->expiration_date && $position_data->expiration_date !== '0000-00-00 00:00:00') {
                    $expiration_date = date('Y-m-d', strtotime($position_data->expiration_date));
                }
                $active = ($position_data->status === 'active') ? 1 : 0;
            }
            
            $clicks_30_days = 0;
            if (function_exists('get_user_position_clicks')) {
                $user_click_stats = get_user_position_clicks($user->ID, true);
                if (is_array($user_click_stats)) {
                    foreach ($user_click_stats as $stat) {
                        if (isset($stat['ad_position']) && $stat['ad_position'] === $position) {
                            $clicks_30_days = intval($stat['total_clicks']);
                            break;
                        }
                    }
                }
            }
            
            $has_data = !empty($image) || !empty($link) || !empty($website_name);
            $status_class = $active ? 'status-active' : 'status-inactive';
            $status_text = $active ? 'Active' : 'Inactive';
            
            $o .= '<tr>';
            $o .= '<td>' . esc_html($position) . '</td>';
            $o .= '<td>' . ($image ? '<img src="' . esc_url($image) . '" width="100" />' : '-') . '</td>';
            $o .= '<td>' . ($link ? '<a href="' . esc_url($link) . '">' . esc_html($link) . '</a>' : '-') . '</td>';
            $o .= '<td>' . esc_html($website_name) . '</td>';
            $o .= '<td>' . esc_html($expiration_date) . '</td>';
            $o .= '<td>' . esc_html($clicks_30_days) . '</td>';
            $o .= '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
            $o .= '<td>';
            if ($has_data) {
                $o .= '<span class="dashicons dashicons-edit edit-ad" data-position="' . esc_attr($position) . '" data-user="' . esc_attr($user_login) . '" data-image="' . esc_attr($image) . '" data-link="' . esc_attr($link) . '" data-seo="' . esc_attr($website_name) . '"></span> | ';
                $o .= '<span class="dashicons dashicons-trash delete-ad" data-position="' . esc_attr($position) . '"></span>';
            } else {
                $o .= '<span class="dashicons dashicons-plus add-ad" data-position="' . esc_attr($position) . '"></span>';
            }
            $o .= '</td>';
            $o .= '</tr>';
        }
    } else {
        $o .= '<tr><td colspan="8" style="text-align:center;">No ad positions available. Please contact the administrator.</td></tr>';
    }
    $o .= '</tbody></table>';
    return $o;
}


