<?php
if (!defined('WPINC')) {
    die;
}

require_once __DIR__ . '/admin-menu.php';
require_once plugin_dir_path(__FILE__) . 'views/admin-page.php';
require_once plugin_dir_path(__FILE__) . 'views/modern-dashboard.php';
require_once plugin_dir_path(__FILE__) . 'views/customer-management.php';
require_once plugin_dir_path(__FILE__) . 'views/export-import-positions.php';
require_once plugin_dir_path(__FILE__) . 'handlers/admin-ajax.php';
require_once plugin_dir_path(__FILE__) . 'handlers/admin-ajax-handlers.php';
require_once plugin_dir_path(__FILE__) . 'handlers/customer-ajax-handlers.php';
require_once plugin_dir_path(__FILE__) . 'handlers/user-ajax-handlers.php';
require_once plugin_dir_path(__FILE__) . 'settings/general-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/payment-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/price-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/discount-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/google-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/security-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/cloudflare-settings.php';
require_once plugin_dir_path(__FILE__) . 'settings/cache-management.php';
require_once plugin_dir_path(__FILE__) . 'amp-uninstall-handler.php';

if (!function_exists('amp_get_database')) {
    function amp_get_database() {
        if (class_exists('AMP_Database')) {
            return AMP_Database::instance();
        } elseif (class_exists('\AdManagementPro\Core\Database')) {
            return \AdManagementPro\Core\Database::instance();
        }
        throw new \Exception('AMP Database class not found. The plugin cannot function.');
    }
}

if (!function_exists('amp_get_utilities')) {
    function amp_get_utilities() {
        return AMP_Utilities::instance();
    }
}

global $amp_google_analytics;
if (!isset($amp_google_analytics) && class_exists('AMP_Google_Analytics')) {
    $amp_google_analytics = new AMP_Google_Analytics();
}

try {
    $admin_assets_file = plugin_dir_path(__FILE__) . 'admin-assets.php';
    if (file_exists($admin_assets_file)) {
        require_once $admin_assets_file;
        if (class_exists('\AdManagementPro\Admin\AdminAssets')) {
            \AdManagementPro\Admin\AdminAssets::instance();
        }
    }
} catch (Exception $e) {
    error_log('AMP: Failed to load AdminAssets: ' . $e->getMessage());
}

function amp_admin_init() {
    register_setting('amp_general_settings_group', 'site_logo_url');
    register_setting('amp_general_settings_group', 'telegram_default_link');
}
add_action('admin_init', 'amp_admin_init');

add_action('admin_notices', 'amp_check_installation_status');

function amp_check_installation_status() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $manual_refresh_needed = get_option('amp_manual_permalink_refresh_needed', false);

    if ($manual_refresh_needed) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>Ad Management Pro:</strong> หน้า Dashboard และ Login อาจไม่สามารถเข้าถึงได้ กรุณาไป <a href="' . admin_url('options-permalink.php') . '">Settings > Permalinks</a> และกดปุ่ม "Save Changes" เพื่อรีเฟรช URL structure</p>';
        echo '<p><a href="' . admin_url('options-permalink.php') . '" class="button button-primary">ไปที่ Permalink Settings</a></p>';
        echo '</div>';
    }
}

function amp_check_after_permalink_save() {
    if (!current_user_can('manage_options')) {
        return;
    }

    require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';

    wp_schedule_single_event(time() + 2, 'amp_verify_after_permalink_save');
    add_action('amp_verify_after_permalink_save', function() {
        $verification_results = ad_management_verify_pages_accessibility();

        $all_accessible = true;
        foreach ($verification_results as $page_name => $result) {
            if (!$result['accessible']) {
                $all_accessible = false;
            }
        }

        if ($all_accessible) {
            update_option('amp_installation_verified', true);
            delete_option('amp_manual_permalink_refresh_needed');
            error_log('AMP: Pages verified accessible after permalink save');
        }
    });
}
add_action('load-options-permalink.php', 'amp_check_after_permalink_save');

function amp_auto_test_pages_on_init() {
    if (wp_doing_ajax()) {
        return;
    }

    $installation_verified = get_option('amp_installation_verified', null);

    if ($installation_verified === null || $installation_verified === false) {
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';

        $verification_results = ad_management_verify_pages_accessibility();

        $all_accessible = true;
        foreach ($verification_results as $page_name => $result) {
            if (!$result['accessible']) {
                $all_accessible = false;
            }
        }

        if ($all_accessible) {
            update_option('amp_installation_verified', true);
            delete_option('amp_manual_permalink_refresh_needed');
        }
    }
}
add_action('wp', 'amp_auto_test_pages_on_init', 1);

function amp_auto_check_installation_on_admin_load() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $last_check = get_option('amp_last_installation_check', 0);
    $check_interval = 3600; // 1 hour

    if ((time() - $last_check) > $check_interval) {
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';

        $verification_results = ad_management_verify_pages_accessibility();

        $all_accessible = true;
        foreach ($verification_results as $page_name => $result) {
            if (!$result['accessible']) {
                $all_accessible = false;
                break;
            }
        }

        if ($all_accessible) {
            update_option('amp_installation_verified', true);
            delete_option('amp_manual_permalink_refresh_needed');
        } else {
            update_option('amp_installation_verified', false);
        }

        update_option('amp_last_installation_check', time());
    }
}
add_action('admin_init', 'amp_auto_check_installation_on_admin_load');

