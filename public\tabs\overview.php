<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';
require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';

$current_user = wp_get_current_user();
$current_user_id = $current_user->ID;

if (!$current_user_id) {
    echo '<div class="overview-container"><div class="overview-empty">กรุณาเข้าสู่ระบบ</div></div>';
    return;
}

$position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
$database = \AdManagementPro\Core\Database::instance();

$user_positions = $position_manager->get_user_positions_with_details($current_user_id);

if (empty($user_positions)) {
    $user_ads = [];
    $expirations = [];
} else {
    $user_ads = [];
    $expirations = [];

    foreach ($user_positions as $position) {
        $position_name = $position->name;

        $user_ads[] = [
            'position' => $position_name,
            'user' => $current_user->user_login,
            'image' => $position->image_url ?? '',
            'link' => $position->target_url ?? '',
            'website_name' => $position->website_name ?? '',
            'alt' => $position->alt_text ?? ''
        ];

        $expiration_date = $position->ownership['expiration_date'] ?? null;
        $days_remaining = 0;
        if ($expiration_date) {
            $days_remaining = max(0, floor((strtotime($expiration_date) - time()) / (60 * 60 * 24)));
        }

        $expirations[] = [
            'ad_position' => $position_name,
            'expiration_date' => $expiration_date,
            'days_remaining' => $days_remaining,
            'user_id' => $current_user_id
        ];
    }
}

$total_user_clicks = function_exists('get_user_total_clicks') ? get_user_total_clicks($current_user_id, false) : 0;
$total_user_clicks_30_days = function_exists('get_user_total_clicks') ? get_user_total_clicks($current_user_id, true) : 0;
$click_stats = get_user_position_clicks($current_user->ID, false);
$click_stats_30_days = get_user_position_clicks($current_user->ID, true);

$formatted_clicks = [];
foreach ($click_stats as $stat) {
    $formatted_clicks[$stat['ad_position']] = $stat['total_clicks'];
}

$formatted_clicks_30_days = [];
foreach ($click_stats_30_days as $stat) {
    $formatted_clicks_30_days[$stat['ad_position']] = $stat['total_clicks'];
}

$cpc_cpm_data = calculate_user_cpc_cpm($current_user_id, $user_positions, $database);
$thb_rate_row = $database->get_row("SELECT setting_value FROM {$database->get_table('ad_price_global_settings')} WHERE setting_name = 'thb_rate'");
$thb_rate = $thb_rate_row ? (float)$thb_rate_row->setting_value : 35.5;

$use_ga = get_option('use_ga_for_pricing', 'no');
$monthly_visitors = 0;
$monthly_pageviews = 0;
$realtime_users = 0;
$using_ga4_data = false;

if ($use_ga === 'yes') {
    $ga_file = plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/utils/google-analytics.php';
    if (file_exists($ga_file)) {
        require_once($ga_file);
        if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
            $realtime_users = function_exists('amp_get_realtime_users') ? amp_get_realtime_users() : 0;
            $monthly_visitors = function_exists('amp_get_monthly_users') ? amp_get_monthly_users(31) : 0;
            $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no');
            if ($exclude_homepage === 'yes' && function_exists('amp_get_monthly_pageviews_exclude_homepage')) {
                $monthly_pageviews = amp_get_monthly_pageviews_exclude_homepage(31);
            } else if (function_exists('amp_get_monthly_pageviews')) {
                $monthly_pageviews = amp_get_monthly_pageviews(31);
            }

            $using_ga4_data = true;
        }
    }
}

if ($monthly_visitors == 0) {
    $visitors_setting = $database->get_var(
        "SELECT setting_value FROM {ad_price_global_settings} WHERE setting_name = %s",
        ['monthly_visitors']
    );
    $monthly_visitors = $visitors_setting ? intval($visitors_setting) : 600000;
}

// คำนวณ page views เฉพาะในกรณีที่ยังไม่ได้ตั้งค่าจาก GA
if ($monthly_pageviews == 0) {
    $monthly_pageviews = $monthly_visitors * 4;
}

$ctr = 0;
if ($monthly_pageviews > 0 && $total_user_clicks_30_days > 0) {
    $ctr = ($total_user_clicks_30_days / $monthly_pageviews) * 100;
}

$total_monthly_cost = 0;
$average_cpc = 0;
$average_cpm = 0;

if (!empty($user_ads)) {
    foreach ($user_ads as $ad) {
        $position = $ad['position'];
        $price_data = $database->get_row(
            "SELECT usdt_price FROM {ad_price_calculation} WHERE ad_position = %s",
            [$position]
        );
        $monthly_price = $price_data ? floatval($price_data->usdt_price) : 0;
        $total_monthly_cost += $monthly_price;
    }

    if ($total_user_clicks_30_days > 0) {
        $average_cpc = $total_monthly_cost / $total_user_clicks_30_days;
    }

    if ($monthly_pageviews > 0) {
        $average_cpm = ($total_monthly_cost / $monthly_pageviews) * 1000;
    }
}

$expiring_soon = 0;
if (!empty($expirations)) {
    foreach ($expirations as $exp) {
        if ($exp['days_remaining'] <= 7 && $exp['days_remaining'] > 0) {
            $expiring_soon++;
        }
    }
}
?>

<div class="overview-container">
    <div class="overview-hero">
        <div class="hero-content">
            <div class="hero-text">
                <h1>ยินดีต้อนรับ, <?php echo esc_html($current_user->display_name); ?>! <i class="fas fa-hand-wave" style="color: #f39c12;"></i></h1>
                <p>แดชบอร์ดการจัดการป้ายโฆษณาแบบมืออาชีพ</p>
            </div>
            <div class="hero-stats">
                <div class="hero-stat">
                    <div class="stat-number"><?php echo is_array($user_ads) ? count($user_ads) : 0; ?></div>
                    <div class="stat-label">ป้ายโฆษณา</div>
                </div>
                <div class="hero-stat">
                    <div class="stat-number"><?php echo is_numeric($total_user_clicks_30_days) ? intval($total_user_clicks_30_days) : 0; ?></div>
                    <div class="stat-label">คลิก 30 วัน</div>
                </div>
                <div class="hero-stat">
                    <div class="stat-number"><?php echo is_numeric($ctr) ? number_format(floatval($ctr), 2) : '0.00'; ?>%</div>
                    <div class="stat-label">CTR</div>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($user_ads)): ?>
    <div class="charts-section">
        <div class="section-header">
            <h3><i class="fas fa-chart-line"></i> กราฟการวิเคราะห์ประสิทธิภาพ</h3>
            <p>การแสดงผลข้อมูลในรูปแบบกราฟแบบเรียลไทม์</p>
        </div>

        <div class="charts-grid">
            <div class="chart-container performance-chart">
                <div class="chart-header">
                    <h4><i class="fas fa-chart-pie"></i> ประสิทธิภาพโฆษณา</h4>
                    <div class="chart-legend">
                        <span class="legend-item cpc">CPC</span>
                        <span class="legend-item cpm">CPM</span>
                        <span class="legend-item ctr">CTR</span>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <canvas id="performanceChart" width="400" height="300"></canvas>
                </div>
            </div>

            <div class="chart-container clicks-chart">
                <div class="chart-header">
                    <h4><span id="clicksChartTitle"><i class="fas fa-mouse-pointer"></i> คลิกต่อตำแหน่ง (30 วัน)</span></h4>
                    <div class="chart-actions">
                        <select id="positionSelector" class="position-selector">
                            <?php foreach ($user_ads as $ad): ?>
                                <option value="<?php echo esc_attr($ad['position']); ?>"><?php echo esc_html($ad['position']); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="chart-type-toggle">
                            <button class="type-btn active" data-chart-type="bar"><i class="fas fa-chart-bar"></i></button>
                            <button class="type-btn" data-chart-type="line"><i class="fas fa-chart-line"></i></button>
                        </div>
                    </div>
                </div>
                <div class="chart-wrapper">
                    <div class="chart-loader">
                        <div class="loader-spinner"></div>
                        <p>กำลังเตรียมข้อมูลกราฟ...</p>
                    </div>
                    <canvas id="clicksChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="dashboard-grid">
        <div class="analytics-section">
            <div class="section-header">
                <h3><i class="fas fa-analytics"></i> การวิเคราะห์ประสิทธิภาพ</h3>
                <p>ข้อมูลสถิติและการวิเคราะห์แบบเรียลไทม์</p>
            </div>

            <div class="analytics-cards">
                <div class="analytics-card primary" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-icon"><i class="fas fa-globe-americas"></i></div>
                    <div class="card-content">
                        <div class="card-value counter" data-target="<?php echo is_numeric($monthly_visitors) ? intval($monthly_visitors) : 0; ?>">0</div>
                        <div class="card-label">ผู้เข้าชม/เดือน</div>
                        <?php if ($using_ga4_data): ?>
                        <div class="card-source">Google Analytics</div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="analytics-card success" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-icon"><i class="fas fa-file-alt"></i></div>
                    <div class="card-content">
                        <div class="card-value counter" data-target="<?php echo is_numeric($monthly_pageviews) ? intval($monthly_pageviews) : 0; ?>">0</div>
                        <div class="card-label">เพจวิว/เดือน</div>
                    </div>
                </div>

                <div class="analytics-card warning" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-icon"><i class="fas fa-mouse-pointer"></i></div>
                    <div class="card-content">
                        <div class="card-value counter" data-target="<?php echo is_numeric($total_user_clicks_30_days) ? intval($total_user_clicks_30_days) : 0; ?>">0</div>
                        <div class="card-label">คลิก 30 วัน</div>
                    </div>
                </div>

                <div class="analytics-card info" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-icon"><i class="fas fa-bullseye"></i></div>
                    <div class="card-content">
                        <div class="card-value"><?php echo is_numeric($ctr) ? number_format($ctr, 3) : '0.000'; ?>%</div>
                        <div class="card-label">อัตราการคลิก (CTR)</div>
                    </div>
                </div>
            </div>

            <?php if (!empty($user_ads)): ?>
            <div class="performance-metrics">
                <div class="metric-card" data-aos="zoom-in" data-aos-delay="500">
                    <div class="metric-header">
                        <span class="metric-icon"><i class="fas fa-dollar-sign"></i></span>
                        <h4>ต้นทุนต่อคลิก (CPC)</h4>
                    </div>
                                            <div class="metric-value">
                            <?php echo is_numeric($average_cpc) ? number_format(floatval($average_cpc), 4) : '0.0000'; ?> USDT
                            <span class="metric-sub">(<?php echo is_numeric($average_cpc) && is_numeric($thb_rate) ? number_format(floatval($average_cpc) * floatval($thb_rate), 2) : '0.00'; ?> บาท)</span>
                        </div>
                    <div class="metric-trend positive">↓ ต่ำกว่าค่าเฉลี่ยตลาด</div>
                </div>

                <div class="metric-card" data-aos="zoom-in" data-aos-delay="600">
                    <div class="metric-header">
                        <span class="metric-icon"><i class="fas fa-eye"></i></span>
                        <h4>ต้นทุนต่อ 1000 วิว (CPM)</h4>
                    </div>
                                            <div class="metric-value">
                            <?php echo is_numeric($average_cpm) ? number_format(floatval($average_cpm), 4) : '0.0000'; ?> USDT
                            <span class="metric-sub">(<?php echo is_numeric($average_cpm) && is_numeric($thb_rate) ? number_format(floatval($average_cpm) * floatval($thb_rate), 2) : '0.00'; ?> บาท)</span>
                        </div>
                    <div class="metric-trend positive">↓ ประหยัดกว่าแพลตฟอร์มอื่น</div>
                </div>

                <div class="metric-card" data-aos="zoom-in" data-aos-delay="700">
                    <div class="metric-header">
                        <span class="metric-icon"><i class="fas fa-bullseye"></i></span>
                        <h4>อัตราการคลิก (CTR)</h4>
                    </div>
                    <div class="metric-value">
                        <?php echo is_numeric($ctr) ? number_format(floatval($ctr), 4) : '0.0000'; ?>%
                        <span class="metric-sub">
                            <?php $safe_ctr = is_numeric($ctr) ? floatval($ctr) : 0; ?>
                            <?php if ($safe_ctr > 0.1): ?>
                                <span style="color: #27ae60;">● เยี่ยมมาก</span>
                            <?php elseif ($safe_ctr > 0.05): ?>
                                <span style="color: #f39c12;">● ดี</span>
                            <?php else: ?>
                                <span style="color: #e74c3c;">● ต้องปรับปรุง</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="metric-trend <?php echo $safe_ctr > 0.1 ? 'positive' : ($safe_ctr > 0.05 ? 'neutral' : 'negative'); ?>">
                        <?php if ($safe_ctr > 0.1): ?>
                            ↑ สูงกว่าค่าเฉลี่ยอุตสาหกรรม
                        <?php elseif ($safe_ctr > 0.05): ?>
                            → ใกล้เคียงค่าเฉลี่ยอุตสาหกรรม
                        <?php else: ?>
                            ↓ ต่ำกว่าค่าเฉลี่ยอุตสาหกรรม
                        <?php endif; ?>
                    </div>
                </div>

                <div class="metric-card" data-aos="zoom-in" data-aos-delay="800">
                    <div class="metric-header">
                        <span class="metric-icon"><i class="fas fa-money-bill-wave"></i></span>
                        <h4>ค่าใช้จ่ายรายเดือน</h4>
                    </div>
                    <div class="metric-value">
                        <?php echo is_numeric($total_monthly_cost) ? number_format(floatval($total_monthly_cost), 2) : '0.00'; ?> USDT
                        <span class="metric-sub">(<?php echo is_numeric($total_monthly_cost) && is_numeric($thb_rate) ? number_format(floatval($total_monthly_cost) * floatval($thb_rate), 0) : '0'; ?> บาท)</span>
                    </div>
                    <div class="metric-trend neutral">→ ตามแผนการลงทุน</div>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <?php if (empty($user_ads)): ?>
        <div class="chart-section">
            <div class="section-header">
                <h3><i class="fas fa-chart-area"></i> กราฟแสดงผล</h3>
                <p>การแสดงผลข้อมูลในรูปแบบกราฟ</p>
            </div>

            <div class="empty-chart">
                <div class="empty-icon"><i class="fas fa-chart-bar"></i></div>
                <h4>ยังไม่มีข้อมูลกราฟ</h4>
                <p>เริ่มต้นลงโฆษณาเพื่อดูกราฟการวิเคราะห์</p>
                <button class="cta-button ajax-nav-btn" data-tab="buy">ซื้อป้ายโฆษณา</button>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <?php if (!empty($user_ads)): ?>
    <div class="quick-stats-section">
        <div class="section-header">
            <h3><i class="fas fa-bolt"></i> สถิติด่วน</h3>
            <p>ข้อมูลสำคัญที่ควรติดตาม</p>
        </div>

        <div class="quick-stats-grid">
            <div class="quick-stat-card ads-count">
                <div class="stat-icon"><i class="fas fa-ad"></i></div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo is_array($user_ads) ? count($user_ads) : 0; ?></div>
                    <div class="stat-title">ป้ายโฆษณาทั้งหมด</div>
                </div>
            </div>

            <div class="quick-stat-card total-clicks">
                <div class="stat-icon"><i class="fas fa-hand-pointer"></i></div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo is_numeric($total_user_clicks) ? intval($total_user_clicks) : 0; ?></div>
                    <div class="stat-title">คลิกทั้งหมด</div>
                </div>
            </div>

            <div class="quick-stat-card expiring-ads">
                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                <div class="stat-info">
                    <div class="stat-number <?php echo is_numeric($expiring_soon) && intval($expiring_soon) > 0 ? 'warning' : ''; ?>"><?php echo is_numeric($expiring_soon) ? intval($expiring_soon) : 0; ?></div>
                    <div class="stat-title">ใกล้หมดอายุ (7 วัน)</div>
                </div>
            </div>

            <div class="quick-stat-card realtime-users">
                <div class="stat-icon"><i class="fas fa-users-cog"></i></div>
                <div class="stat-info">
                    <div class="stat-number"><?php echo is_numeric($realtime_users) ? intval($realtime_users) : 0; ?></div>
                    <div class="stat-title">ผู้ใช้ออนไลน์</div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <style>
    @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');
    
    .overview-container {
        background: var(--body-bg);
        min-height: 100vh;
        padding: 20px;
        position: relative;
        overflow: hidden;
    }

    .overview-hero {
        background: var(--theme-gradient);
        border-radius: 24px;
        padding: 40px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-lg);
    }

    .overview-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--diagonal-stripe);
        background-size: var(--diagonal-stripe-size);
        opacity: 0.2;
    }

    .hero-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .hero-text h1 {
        font-size: 32px;
        font-weight: 700;
        color: white;
        margin: 0 0 10px 0;
    }

    .hero-text h1 i {
        margin-left: 10px;
        animation: wave 2s ease-in-out infinite;
    }

    @keyframes wave {
        0%, 100% { transform: rotate(0deg); }
        25% { transform: rotate(20deg); }
        75% { transform: rotate(-20deg); }
    }

    .hero-text p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
    }

    .hero-stats {
        display: flex;
        gap: 30px;
    }

    .hero-stat {
        text-align: center;
        color: white;
    }

    .hero-stat .stat-number {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .hero-stat .stat-label {
        font-size: 14px;
        opacity: 0.9;
    }

    .charts-section {
        background: var(--card-bg);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
    }

    .charts-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--theme-gradient);
    }

    .section-header h3 i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    .charts-grid {
        display: grid;
        grid-template-columns: 3fr 7fr;
        gap: 30px;
    }

    .chart-container {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 16px;
        padding: 25px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .chart-container::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(30%, -30%);
    }

    .chart-container:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
        flex-wrap: wrap;
        gap: 15px;
    }

    .chart-header h4 i {
        margin-right: 8px;
        color: var(--primary-color);
    }

    .chart-legend {
        display: flex;
        gap: 15px;
    }

    .legend-item {
        font-size: 12px;
        font-weight: 500;
        padding: 6px 12px;
        border-radius: 8px;
        position: relative;
        transition: all 0.3s ease;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .legend-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .legend-item.cpc {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.15), rgba(114, 9, 183, 0.1));
        color: rgba(67, 97, 238, 1);
        border: 1px solid rgba(67, 97, 238, 0.2);
    }

    .legend-item.cpm {
        background: linear-gradient(135deg, rgba(114, 9, 183, 0.15), rgba(240, 98, 146, 0.1));
        color: rgba(114, 9, 183, 1);
        border: 1px solid rgba(114, 9, 183, 0.2);
    }

    .legend-item.ctr {
        background: linear-gradient(135deg, rgba(46, 204, 113, 0.15), rgba(26, 188, 156, 0.1));
        color: rgba(46, 204, 113, 1);
        border: 1px solid rgba(46, 204, 113, 0.2);
    }

    .chart-info .info-text {
        font-size: 12px;
        color: var(--light-text);
        font-style: italic;
    }

    .chart-actions {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .chart-type-toggle {
        display: flex;
        background-color: var(--body-bg);
        border-radius: 8px;
        padding: 4px;
    }

    .chart-type-toggle .type-btn {
        background: transparent;
        border: none;
        color: var(--light-text);
        padding: 6px 10px;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .chart-type-toggle .type-btn:hover {
        background-color: var(--hover-bg);
        color: var(--text-color);
    }
    
    .chart-type-toggle .type-btn.active {
        background-color: var(--primary-color);
        color: white;
        box-shadow: 0 2px 10px rgba(67, 97, 238, 0.3);
    }

    .chart-header h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
    }

    .card-icon i {
        font-size: 24px;
        color: white;
    }

    .metric-icon i {
        font-size: 20px;
        color: var(--primary-color);
    }

    .stat-icon i {
        font-size: 28px;
        color: var(--primary-color);
    }

    .empty-icon i {
        font-size: 48px;
        color: var(--light-text);
        opacity: 0.7;
    }

    .chart-wrapper {
        position: relative;
        height: 350px;
        z-index: 1;
        background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%234361ee' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zM30 10c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z' stroke='%234361ee' stroke-width='0.5' stroke-opacity='0.1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        background-size: 80px 80px;
        background-repeat: repeat;
        border-radius: 12px;
    }

    [data-theme="dark"] .chart-wrapper {
        background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zM30 10c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z' stroke='%23ffffff' stroke-width='0.5' stroke-opacity='0.05'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .chart-loader {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 10;
        border-radius: 16px;
        color: var(--text-color);
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.4s ease, visibility 0.4s ease;
        backdrop-filter: blur(4px);
    }
    
    [data-theme="dark"] .chart-loader {
        background-color: rgba(20, 20, 20, 0.95);
    }

    .chart-loader.active {
        opacity: 1;
        visibility: visible;
    }

    .loader-spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border-left-color: var(--primary-color);
        animation: spin 1s ease infinite;
        margin-bottom: 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .position-selector-wrapper {
        position: relative;
    }

    .position-selector {
        background-color: var(--card-bg);
        color: var(--text-color);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 8px 12px;
        font-size: 14px;
        cursor: pointer;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        padding-right: 30px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23888' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        transition: all 0.3s ease;
    }

    .position-selector:hover {
        border-color: var(--primary-color);
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .analytics-section, .chart-section {
        background: var(--card-bg);
        border-radius: 20px;
        padding: 30px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .section-header {
        margin-bottom: 25px;
    }

    .section-header h3 {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-color);
        margin: 0 0 8px 0;
    }

    .section-header p {
        font-size: 14px;
        color: var(--light-text);
        margin: 0;
    }

    .analytics-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-bottom: 30px;
    }

    .analytics-card {
        background: var(--card-bg);
        border-radius: 16px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 15px;
        border: 2px solid transparent;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .analytics-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        transition: all 0.3s ease;
    }

    .analytics-card.primary::before { background: linear-gradient(90deg, #4361ee, #7209b7); }
    .analytics-card.success::before { background: linear-gradient(90deg, #2ecc71, #27ae60); }
    .analytics-card.warning::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
    .analytics-card.info::before { background: linear-gradient(90deg, #3498db, #2980b9); }

    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .card-icon {
        font-size: 24px;
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--theme-gradient-light);
        color: white;
        flex-shrink: 0;
    }

    .card-content {
        flex: 1;
    }

    .card-value {
        font-size: 24px;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 5px;
    }

    .card-label {
        font-size: 14px;
        color: var(--light-text);
        margin-bottom: 3px;
    }

    .card-source {
        font-size: 12px;
        color: var(--primary-color);
        font-style: italic;
    }

    .performance-metrics {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }

    .metric-card {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
        border-radius: 16px;
        padding: 20px;
        border: 1px solid rgba(67, 97, 238, 0.1);
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(67, 97, 238, 0.15);
    }

    .metric-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
    }

    .metric-icon {
        font-size: 20px;
    }

    .metric-header h4 {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
    }

    .metric-value {
        font-size: 20px;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 8px;
    }

    .metric-sub {
        font-size: 12px;
        color: var(--light-text);
        font-weight: 400;
        display: block;
    }

    .metric-trend {
        font-size: 12px;
        font-weight: 500;
        padding: 4px 8px;
        border-radius: 6px;
        display: inline-block;
    }

    .metric-trend.positive {
        background: rgba(46, 204, 113, 0.1);
        color: #27ae60;
    }

    .metric-trend.neutral {
        background: rgba(52, 152, 219, 0.1);
        color: #2980b9;
    }

    .metric-trend.negative {
        background: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
    }

    .chart-container {
        background: var(--card-bg);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid var(--border-color);
    }

    .empty-chart {
        text-align: center;
        padding: 60px 20px;
        color: var(--light-text);
    }

    .empty-icon {
        font-size: 48px;
        margin-bottom: 20px;
        opacity: 0.7;
    }

    .empty-chart h4 {
        font-size: 18px;
        color: var(--text-color);
        margin-bottom: 10px;
    }

    .empty-chart p {
        margin-bottom: 25px;
    }

    .cta-button {
        background: var(--primary-color);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .cta-button:hover {
        background: var(--primary-hover);
        transform: translateY(-2px);
    }

    .quick-stats-section {
        background: var(--card-bg);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
    }

    .quick-stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }

    .quick-stat-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 16px;
        padding: 25px 20px;
        text-align: center;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .quick-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--theme-gradient);
    }

    .quick-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .quick-stat-card .stat-icon {
        font-size: 28px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        text-align: center;
    }

    .quick-stat-card .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: var(--text-color);
        margin-bottom: 8px;
    }

    .quick-stat-card .stat-number.warning {
        color: #e74c3c;
    }

    .quick-stat-card .stat-title {
        font-size: 14px;
        color: var(--light-text);
        font-weight: 500;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: var(--card-bg);
        border-radius: 20px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        margin-bottom: 30px;
    }

    .empty-icon {
        font-size: 64px;
        margin-bottom: 20px;
        opacity: 0.7;
        animation: bounce 2s infinite;
    }

    .empty-state h3 {
        font-size: 24px;
        color: var(--text-color);
        margin-bottom: 10px;
        font-weight: 600;
    }

    .empty-state p {
        color: var(--light-text);
        margin-bottom: 30px;
        font-size: 16px;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    @media (max-width: 1200px) {
        .charts-grid {
            grid-template-columns: 1fr;
        }

        .dashboard-grid {
            grid-template-columns: 1fr;
        }

        .analytics-cards {
            grid-template-columns: repeat(2, 1fr);
        }

        .performance-metrics {
            grid-template-columns: repeat(2, 1fr);
        }

        .quick-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .hero-content {
            flex-direction: column;
            text-align: center;
            gap: 20px;
        }

        .charts-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .chart-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .chart-actions {
            width: 100%;
            justify-content: space-between;
        }

        .chart-legend {
            flex-wrap: wrap;
            gap: 10px;
        }

        .analytics-cards {
            grid-template-columns: 1fr;
        }

        .performance-metrics {
            grid-template-columns: 1fr;
        }

        .quick-stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .overview-container {
            padding: 15px;
        }

        .charts-section, .analytics-section, .chart-section {
            padding: 20px;
        }

        .chart-wrapper {
            height: 250px;
        }

        .quick-stats-grid {
            grid-template-columns: 1fr;
        }

        .hero-stats {
            flex-direction: column;
            gap: 15px;
        }

        .hero-text h1 {
            font-size: 24px;
        }
    }
    </style>

    <?php if (!empty($user_ads)) : ?>
        <div class="ads-overview-section">
            <div class="section-header">
                <h3>🎯 ป้ายโฆษณาของคุณ</h3>
                <p>รายละเอียดและประสิทธิภาพของป้ายโฆษณาแต่ละตำแหน่ง</p>
            </div>

            <div class="ads-grid">
                <?php foreach ($user_ads as $ad) :
                    $position = $ad['position'];
                    $expiration_info = array_filter($expirations, function($exp) use ($position) {
                        return $exp['ad_position'] === $position;
                    });
                    $expiration_info = reset($expiration_info);
                    $days_remaining = isset($expiration_info['days_remaining']) ? $expiration_info['days_remaining'] : 0;

                    $clicks_30_days = isset($formatted_clicks_30_days[$position]) ? $formatted_clicks_30_days[$position] : 0;
                    $clicks_all_time = isset($formatted_clicks[$position]) ? $formatted_clicks[$position] : 0;

                    $ad_cpc = isset($cpc_cpm_data['positions'][$position]) ? $cpc_cpm_data['positions'][$position]['cpc'] : 0;
                    $ad_cpm = isset($cpc_cpm_data['positions'][$position]) ? $cpc_cpm_data['positions'][$position]['cpm'] : 0;
                ?>
                <div class="ad-card">
                    <div class="ad-preview">
                        <?php if (!empty($ad['image'])) : ?>
                            <img src="<?php echo esc_url($ad['image']); ?>" alt="<?php echo esc_attr($ad['position']); ?>">
                        <?php else : ?>
                            <div class="ad-placeholder">
                                <div class="placeholder-icon"><i class="fas fa-image"></i></div>
                                <div class="placeholder-text">ไม่มีรูปภาพ</div>
                                <div class="placeholder-subtext">จัดการใน "ป้ายโฆษณาของฉัน"</div>
                            </div>
                        <?php endif; ?>
                        <div class="ad-status <?php echo $days_remaining > 0 ? 'active' : 'expired'; ?>">
                            <?php echo $days_remaining > 0 ? '✅ ใช้งานอยู่' : '❌ หมดอายุ'; ?>
                        </div>
                    </div>

                    <div class="ad-info">
                        <div class="ad-header">
                            <h4><?php echo esc_html($ad['position']); ?></h4>
                            <div class="ad-days">
                                <?php if ($days_remaining > 0): ?>
                                    <span class="days-count"><?php echo $days_remaining; ?></span>
                                    <span class="days-label">วันเหลือ</span>
                                <?php else: ?>
                                    <span class="expired-label">หมดอายุ</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (!empty($ad['website_name']) || !empty($ad['link'])): ?>
                        <div class="ad-details">
                            <?php if (!empty($ad['website_name'])): ?>
                                <div class="detail-item">
                                    <span class="detail-icon">🌐</span>
                                    <span><?php echo esc_html($ad['website_name']); ?></span>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($ad['link'])): ?>
                                <div class="detail-item">
                                    <span class="detail-icon">🔗</span>
                                    <a href="<?php echo esc_url($ad['link']); ?>" target="_blank">
                                        <?php echo esc_html(parse_url($ad['link'], PHP_URL_HOST)); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>

                        <div class="ad-metrics">
                            <div class="metric-item">
                                <span class="metric-value"><?php echo is_numeric($clicks_30_days) ? intval($clicks_30_days) : 0; ?></span>
                                <span class="metric-label">คลิก 30 วัน</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-value"><?php echo is_numeric($ad_cpc) ? number_format($ad_cpc, 2) : '0.00'; ?></span>
                                <span class="metric-label">CPC (USDT)</span>
                            </div>
                        </div>

                        <div class="ad-actions">
                            <button class="action-btn primary ajax-nav-btn" data-tab="my-ads">จัดการ</button>
                            <?php if ($days_remaining <= 7 && $days_remaining > 0) : ?>
                                <button class="action-btn warning overview-renew-btn" data-position="<?php echo esc_attr($position); ?>">ต่ออายุ</button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <style>
            .ads-overview-section {
                background: var(--card-bg);
                border-radius: 20px;
                padding: 30px;
                margin-bottom: 30px;
                box-shadow: var(--shadow-lg);
                border: 1px solid var(--border-color);
            }

            .ads-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 25px;
            }

            .ad-card {
                background: var(--card-bg);
                border-radius: 16px;
                overflow: hidden;
                border: 1px solid var(--border-color);
                transition: all 0.3s ease;
                position: relative;
            }

            .ad-card:hover {
                transform: translateY(-5px);
                box-shadow: var(--shadow-lg);
            }

            .ad-preview {
                height: 160px;
                position: relative;
                overflow: hidden;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            .ad-preview img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            .ad-card:hover .ad-preview img {
                transform: scale(1.05);
            }

            .ad-placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: rgba(255, 255, 255, 0.7);
                text-align: center;
                background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            }

            .placeholder-icon {
                font-size: 3rem;
                margin-bottom: 8px;
                opacity: 0.7;
            }

            .placeholder-icon i {
                color: rgba(255, 255, 255, 0.8);
            }

            .placeholder-text {
                font-size: 14px;
                font-weight: 500;
                opacity: 0.9;
            }
            
            .placeholder-subtext {
                font-size: 12px;
                opacity: 0.7;
                margin-top: 4px;
            }

            .ad-status {
                position: absolute;
                top: 12px;
                right: 12px;
                padding: 6px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 600;
                backdrop-filter: blur(10px);
            }

            .ad-status.active {
                background: rgba(46, 204, 113, 0.9);
                color: white;
            }

            .ad-status.expired {
                background: rgba(231, 76, 60, 0.9);
                color: white;
            }

            .ad-info {
                padding: 20px;
            }

            .ad-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
            }

            .ad-header h4 {
                font-size: 18px;
                font-weight: 600;
                color: var(--text-color);
                margin: 0;
            }

            .ad-days {
                text-align: right;
            }

            .days-count {
                font-size: 20px;
                font-weight: 700;
                color: var(--primary-color);
                display: block;
            }

            .days-label {
                font-size: 12px;
                color: var(--light-text);
            }

            .expired-label {
                font-size: 14px;
                color: #e74c3c;
                font-weight: 600;
            }

            .ad-details {
                margin-bottom: 15px;
            }

            .detail-item {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;
                font-size: 14px;
                color: var(--light-text);
            }

            .detail-icon {
                font-size: 12px;
                color: var(--primary-color);
            }

            .detail-item a {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
            }

            .detail-item a:hover {
                text-decoration: underline;
            }

            .ad-metrics {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                margin-bottom: 20px;
                padding: 15px;
                background: rgba(67, 97, 238, 0.05);
                border-radius: 12px;
                border: 1px solid rgba(67, 97, 238, 0.1);
            }

            .metric-item {
                text-align: center;
            }

            .metric-value {
                font-size: 18px;
                font-weight: 700;
                color: var(--text-color);
                display: block;
                margin-bottom: 4px;
            }

            .metric-label {
                font-size: 12px;
                color: var(--light-text);
            }

            .ad-actions {
                display: flex;
                gap: 10px;
            }

            .action-btn {
                flex: 1;
                padding: 10px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                text-align: center;
            }

            .action-btn.primary {
                background: var(--primary-color);
                color: white;
            }

            .action-btn.primary:hover {
                background: var(--primary-hover);
                transform: translateY(-1px);
            }

            .action-btn.warning {
                background: rgba(255, 193, 7, 0.1);
                color: #f39c12;
                border: 1px solid rgba(255, 193, 7, 0.3);
            }

            .action-btn.warning:hover {
                background: rgba(255, 193, 7, 0.2);
            }

            @media (max-width: 768px) {
                .ads-grid {
                    grid-template-columns: 1fr;
                }
            }
            </style>
        </div>
    <?php else : ?>
        <div class="empty-state">
            <div class="empty-icon">🛒</div>
            <h3>คุณยังไม่มีป้ายโฆษณา</h3>
            <p>เริ่มต้นซื้อป้ายโฆษณาเพื่อเพิ่มการมองเห็นของคุณ</p>
            <button class="cta-button ajax-nav-btn" data-tab="buy">ซื้อป้ายโฆษณา</button>
        </div>
    <?php endif; ?>

    <div class="professional-insights-section">
        <div class="section-header">
            <h3>🎯 ข้อมูลเชิงลึกและการวิเคราะห์</h3>
            <p>ข้อมูลที่น่าเชื่อถือและการวิเคราะห์แบบมืออาชีพ</p>
        </div>

        <div class="insights-grid">
            <div class="insight-card market-analysis">
                <div class="card-header">
                    <div class="card-icon">📊</div>
                    <h4>การวิเคราะห์ตลาด</h4>
                </div>
                <div class="card-content">
                    <div class="metric-row">
                        <span class="metric-label">ราคา CPC เฉลี่ยตลาด:</span>
                        <span class="metric-value">$0.50 - $2.00</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">ราคา CPM เฉลี่ยตลาด:</span>
                        <span class="metric-value">$1.00 - $5.00</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">CTR เฉลี่ยอุตสาหกรรม:</span>
                        <span class="metric-value">0.05% - 0.15%</span>
                    </div>
                    <div class="comparison-note">
                        <span class="comparison-icon">✅</span>
                        <span>ราคาของเราต่ำกว่าค่าเฉลี่ยตลาด 60-80%</span>
                    </div>
                </div>
            </div>

            <div class="insight-card platform-stats">
                <div class="card-header">
                    <div class="card-icon">🌐</div>
                    <h4>สถิติแพลตฟอร์ม</h4>
                </div>
                <div class="card-content">
                    <div class="metric-row">
                        <span class="metric-label">ผู้เข้าชมรายเดือน:</span>
                        <span class="metric-value"><?php echo is_numeric($monthly_visitors) ? intval($monthly_visitors) : 0; ?>+</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">เพจวิวรายเดือน:</span>
                        <span class="metric-value"><?php echo is_numeric($monthly_pageviews) ? intval($monthly_pageviews) : 0; ?>+</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">อัตราการกลับมาเยือน:</span>
                        <span class="metric-value">65%</span>
                    </div>
                    <div class="comparison-note">
                        <span class="comparison-icon">🚀</span>
                        <span>การเติบโตเฉลี่ย 15% ต่อเดือน</span>
                    </div>
                </div>
            </div>

            <div class="insight-card security-trust">
                <div class="card-header">
                    <div class="card-icon">🔒</div>
                    <h4>ความปลอดภัยและความน่าเชื่อถือ</h4>
                </div>
                <div class="card-content">
                    <div class="trust-badges">
                        <div class="trust-badge">
                            <span class="badge-icon">🛡️</span>
                            <span class="badge-text">SSL Secured</span>
                        </div>
                        <div class="trust-badge">
                            <span class="badge-icon">💳</span>
                            <span class="badge-text">PCI Compliant</span>
                        </div>
                        <div class="trust-badge">
                            <span class="badge-icon">⚡</span>
                            <span class="badge-text">99.9% Uptime</span>
                        </div>
                        <div class="trust-badge">
                            <span class="badge-icon">📈</span>
                            <span class="badge-text">Real-time Analytics</span>
                        </div>
                    </div>
                    <div class="security-note">
                        <span class="comparison-icon">🔐</span>
                        <span>ข้อมูลของคุณได้รับการปกป้องด้วยเทคโนโลยีระดับธนาคาร</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="performance-tips-section">
        <div class="section-header">
            <h3>💡 เคล็ดลับเพิ่มประสิทธิภาพ</h3>
            <p>คำแนะนำจากผู้เชี่ยวชาญเพื่อเพิ่มผลตอบแทนจากการลงโฆษณา</p>
        </div>

        <div class="tips-grid">
            <div class="tip-card">
                <div class="tip-icon">🎨</div>
                <h4>การออกแบบโฆษณา</h4>
                <p>ใช้สีที่สะดุดตาและข้อความที่ชัดเจน เพื่อเพิ่มอัตราการคลิก</p>
                <div class="tip-stat">
                    <span class="stat-number">+35%</span>
                    <span class="stat-label">เพิ่ม CTR</span>
                </div>
            </div>

            <div class="tip-card">
                <div class="tip-icon">⏰</div>
                <h4>เวลาที่เหมาะสม</h4>
                <p>ลงโฆษณาในช่วง 9:00-11:00 และ 14:00-16:00 เพื่อผลลัพธ์ที่ดีที่สุด</p>
                <div class="tip-stat">
                    <span class="stat-number">+25%</span>
                    <span class="stat-label">เพิ่มการมองเห็น</span>
                </div>
            </div>

            <div class="tip-card">
                <div class="tip-icon">📱</div>
                <h4>Mobile Optimization</h4>
                <p>ออกแบบโฆษณาให้เหมาะกับมือถือ เนื่องจาก 70% ของผู้ใช้เข้าผ่านมือถือ</p>
                <div class="tip-stat">
                    <span class="stat-number">+40%</span>
                    <span class="stat-label">เพิ่มการแปลง</span>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    (function() {
        if (window.overviewChartsInitialized) {
            return;
        }

        let chartInstances = {
            performance: null,
            clicks: null
        };

        let isProcessing = false;
        
        function cleanupCharts() {
            Object.values(chartInstances).forEach(chart => {
                if (chart && typeof chart.destroy === 'function') {
                    try {
                        chart.destroy();
                    } catch (e) {}
                }
            });
            chartInstances = { performance: null, clicks: null };
        }

        function initializeCharts() {
            if (typeof Chart === 'undefined') {
                setTimeout(initializeCharts, 100);
                return;
            }

            if (isProcessing) return;
            isProcessing = true;

            function animateCounter(element) {
                if (element.hasAttribute('data-animated')) {
                    return;
                }
                element.setAttribute('data-animated', 'true');

                const target = parseInt(element.getAttribute('data-target'));
                const duration = 2000;
                const increment = target / (duration / 16);
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(current).toLocaleString();
                }, 16);
            }

            const counters = document.querySelectorAll('.counter:not([data-animated])');
            if (counters.length > 0) {
                const observerOptions = {
                    threshold: 0.5,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            animateCounter(entry.target);
                            observer.unobserve(entry.target);
                        }
                    });
                }, observerOptions);

                counters.forEach(counter => {
                    observer.observe(counter);
                });
            }

            <?php if (!empty($user_ads)): ?>
            const doughnutLabel = {
                id: 'doughnutLabel',
                afterDraw(chart, args, options) {
                    const { ctx, chartArea: { width, height } } = chart;
                    ctx.save();
                    const topText = "Performance";
                    const mainValue = "<?php echo is_numeric($total_monthly_cost) ? round(floatval($total_monthly_cost), 2) : '0.00'; ?> USDT";
                    const subText = "CTR: <?php echo is_numeric($ctr) ? round(floatval($ctr), 3) : '0.000'; ?>%";
                    
                    ctx.font = '600 12px "Plus Jakarta Sans", sans-serif';
                    ctx.fillStyle = 'var(--light-text)';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(topText, width / 2, height / 2 - 20);
                    
                    ctx.font = '700 18px "Plus Jakarta Sans", sans-serif';
                    ctx.fillStyle = 'var(--text-color)';
                    ctx.fillText(mainValue, width / 2, height / 2);
                    
                    ctx.font = '500 11px "Plus Jakarta Sans", sans-serif';
                    ctx.fillStyle = 'var(--primary-color)';
                    ctx.fillText(subText, width / 2, height / 2 + 20);
                    
                    ctx.restore();
                }
            };

            setTimeout(function() {
                const performanceCtx = document.getElementById('performanceChart');
                if (performanceCtx && performanceCtx.getContext && !chartInstances.performance) {
                    const ctx = performanceCtx.getContext('2d');
                    
                    if (!ctx) {
                        console.warn('Unable to get 2D context for performance chart');
                        isProcessing = false;
                        return;
                    }

                    const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
                    gradient1.addColorStop(0, 'rgba(67, 97, 238, 1)');
                    gradient1.addColorStop(0.5, 'rgba(114, 9, 183, 0.9)');
                    gradient1.addColorStop(1, 'rgba(114, 9, 183, 0.7)');

                    const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
                    gradient2.addColorStop(0, 'rgba(114, 9, 183, 1)');
                    gradient2.addColorStop(0.5, 'rgba(240, 98, 146, 0.9)');
                    gradient2.addColorStop(1, 'rgba(255, 159, 64, 0.7)');
                    
                    const gradient3 = ctx.createLinearGradient(0, 0, 0, 300);
                    gradient3.addColorStop(0, 'rgba(46, 204, 113, 1)');
                    gradient3.addColorStop(0.5, 'rgba(26, 188, 156, 0.9)');
                    gradient3.addColorStop(1, 'rgba(39, 174, 96, 0.7)');

                    chartInstances.performance = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['CPC (USDT)', 'CPM (USDT)', 'CTR (%)'],
                            datasets: [{
                                data: [
                                    <?php echo is_numeric($average_cpc) ? floatval($average_cpc) : 0; ?>,
                                    <?php echo is_numeric($average_cpm) ? floatval($average_cpm) : 0; ?>,
                                    <?php echo is_numeric($ctr) ? floatval($ctr) : 0; ?>
                                ],
                                backgroundColor: [gradient1, gradient2, gradient3],
                                borderColor: ['rgba(67, 97, 238, 0.3)', 'rgba(114, 9, 183, 0.3)', 'rgba(46, 204, 113, 0.3)'],
                                borderWidth: 3,
                                hoverOffset: 25,
                                cutout: '70%',
                                spacing: 2,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                animateRotate: true,
                                animateScale: true,
                                duration: 2500,
                                easing: 'easeOutBounce'
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    enabled: true,
                                    backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                    titleColor: 'white',
                                    bodyColor: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.3)',
                                    borderWidth: 2,
                                    cornerRadius: 12,
                                    displayColors: true,
                                    padding: 15,
                                    titleFont: { size: 14, weight: '600' },
                                    bodyFont: { size: 13, weight: '500' },
                                    callbacks: {
                                        label: function(context) {
                                            const label = context.label;
                                            const value = context.formattedValue;
                                            if (label.includes('CTR')) {
                                                return ` ${label}: ${value}%`;
                                            }
                                            return ` ${label}: ${value} USDT`;
                                        }
                                    }
                                }
                            },
                            elements: {
                                arc: {
                                    borderRadius: 8,
                                    shadowOffsetX: 2,
                                    shadowOffsetY: 4,
                                    shadowBlur: 12,
                                    shadowColor: 'rgba(0,0,0,0.15)'
                                }
                            }
                        },
                        plugins: [doughnutLabel]
                    });
                }

                const clicksCtx = document.getElementById('clicksChart');
                if (clicksCtx && clicksCtx.getContext && !chartInstances.clicks) {
                    const ctx2 = clicksCtx.getContext('2d');
                    
                    if (!ctx2) {
                        console.warn('Unable to get 2D context for clicks chart');
                        isProcessing = false;
                        return;
                    }

                    const barGradient = ctx2.createLinearGradient(0, 0, 0, 350);
                    barGradient.addColorStop(0, 'rgba(67, 97, 238, 0.8)');
                    barGradient.addColorStop(1, 'rgba(67, 97, 238, 0.1)');
                    
                    const pointGradient = ctx2.createLinearGradient(0, 0, 0, 350);
                    pointGradient.addColorStop(0, 'rgba(114, 9, 183, 1)');
                    pointGradient.addColorStop(1, 'rgba(114, 9, 183, 0.5)');
                    
                    const lineBorderGradient = ctx2.createLinearGradient(0, 0, 800, 0);
                    lineBorderGradient.addColorStop(0, 'rgba(67, 97, 238, 1)');
                    lineBorderGradient.addColorStop(1, 'rgba(114, 9, 183, 1)');

                    chartInstances.clicks = new Chart(ctx2, {
                        type: 'bar',
                        data: {
                            labels: [],
                            datasets: []
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: { duration: 1500, easing: 'easeOutQuart' },
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    mode: 'index',
                                    intersect: false,
                                    backgroundColor: 'rgba(0, 0, 0, 0.85)',
                                    titleColor: 'white',
                                    bodyColor: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.2)',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    displayColors: false,
                                    padding: 10
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: { color: 'var(--border-color)', drawBorder: false },
                                    ticks: {
                                        font: { family: "'Plus Jakarta Sans', sans-serif", size: 12, weight: '500' },
                                        color: 'var(--light-text)',
                                        precision: 0
                                    }
                                },
                                x: {
                                    grid: { display: false },
                                    ticks: {
                                        font: { family: "'Plus Jakarta Sans', sans-serif", size: 11, weight: '500' },
                                        color: 'var(--light-text)'
                                    }
                                }
                            },
                            interaction: { intersect: false, mode: 'index' }
                        }
                    });

                    const positionSelector = document.getElementById('positionSelector');
                    const chartLoader = document.querySelector('.clicks-chart .chart-loader');
                    const chartTypeButtons = document.querySelectorAll('.chart-type-toggle .type-btn');
                    let currentChartType = 'bar';

                    function getChartDataset(chartType, data) {
                        if (chartType === 'line') {
                            return [{
                                label: 'คลิกรายวัน',
                                data: data,
                                borderColor: lineBorderGradient,
                                borderWidth: 3,
                                tension: 0.4,
                                pointBackgroundColor: pointGradient,
                                pointRadius: 4,
                                pointHoverRadius: 8,
                                fill: true,
                                backgroundColor: barGradient,
                            }];
                        }
                        return [{
                            label: 'คลิกรายวัน',
                            data: data,
                            backgroundColor: barGradient,
                            borderColor: 'rgba(67, 97, 238, 1)',
                            borderWidth: 2,
                            borderRadius: 6,
                            borderSkipped: false,
                        }];
                    }

                    async function updateClicksChart(positionName) {
                        if (!positionName || !chartInstances.clicks) return;

                        if (chartLoader) chartLoader.classList.add('active');

                        try {
                            const formData = new FormData();
                            formData.append('action', 'get_daily_position_clicks');
                            formData.append('position', positionName);
                            formData.append('security', window.adDashboardData ? window.adDashboardData.nonce : '');

                            const response = await fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                                method: 'POST',
                                body: formData
                            });

                            if (!response.ok) {
                                throw new Error('Network response was not ok.');
                            }

                            const result = await response.json();

                            if (result.success) {
                                setTimeout(() => {
                                    if (chartInstances.clicks && chartInstances.clicks.canvas && chartInstances.clicks.canvas.parentNode) {
                                        chartInstances.clicks.data.labels = result.data.labels;
                                        chartInstances.clicks.data.datasets = getChartDataset(currentChartType, result.data.data);
                                        chartInstances.clicks.update();
                                    }
                                    if (chartLoader) chartLoader.classList.remove('active');
                                }, 300);
                            } else {
                                throw new Error(result.data.message || 'Failed to load chart data.');
                            }
                        } catch (error) {
                            console.error('Error updating chart:', error);
                            if (chartLoader) chartLoader.classList.remove('active');
                        }
                    }

                    if (chartTypeButtons.length > 0) {
                        chartTypeButtons.forEach(button => {
                            button.addEventListener('click', () => {
                                const newType = button.dataset.chartType;
                                if (newType === currentChartType || !chartInstances.clicks) return;

                                if (!chartInstances.clicks.canvas || !chartInstances.clicks.canvas.parentNode) {
                                    console.warn('Chart canvas not available for type change');
                                    return;
                                }

                                chartTypeButtons.forEach(btn => btn.classList.remove('active'));
                                button.classList.add('active');

                                currentChartType = newType;
                                chartInstances.clicks.config.type = newType;
                                chartInstances.clicks.data.datasets = getChartDataset(newType, chartInstances.clicks.data.datasets[0].data);
                                chartInstances.clicks.update();
                            });
                        });
                    }

                    if (positionSelector) {
                        positionSelector.addEventListener('change', (e) => {
                            updateClicksChart(e.target.value);
                        });

                        if (positionSelector.options.length > 0) {
                            updateClicksChart(positionSelector.value);
                        }
                    }
                }

                isProcessing = false;
            }, 500);
            <?php else: ?>
            isProcessing = false;
            <?php endif; ?>
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeCharts);
        } else {
            setTimeout(initializeCharts, 100);
        }

        window.addEventListener('beforeunload', function() {
            cleanupCharts();
            window.overviewChartsInitialized = false;
        });

        window.overviewChartsInitialized = true;
    })();
    </script>

    <style>
    .professional-insights-section, .performance-tips-section {
        background: var(--card-bg);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
    }

    .professional-insights-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--theme-gradient);
    }

    .insights-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 25px;
    }

    .insight-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 16px;
        padding: 25px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .insight-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #4361ee, #7209b7);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }

    .insight-card:hover::before {
        transform: scaleX(1);
    }

    .insight-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }

    .card-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .card-icon {
        font-size: 24px;
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: var(--theme-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        animation: float 3s ease-in-out infinite;
    }

    .card-header h4 {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color);
        margin: 0;
    }

    .metric-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .metric-row:last-child {
        border-bottom: none;
    }

    .metric-label {
        font-size: 14px;
        color: var(--light-text);
        font-weight: 500;
    }

    .metric-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
    }

    .comparison-note, .security-note {
        background: rgba(46, 204, 113, 0.1);
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: #27ae60;
        font-weight: 500;
    }

    .comparison-icon {
        font-size: 16px;
    }

    .trust-badges {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-bottom: 15px;
    }

    .trust-badge {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px;
        background: rgba(67, 97, 238, 0.1);
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
        color: var(--text-color);
    }

    .badge-icon {
        font-size: 16px;
    }

    .tips-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 25px;
    }

    .tip-card {
        background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
        border-radius: 16px;
        padding: 25px;
        border: 1px solid rgba(67, 97, 238, 0.1);
        transition: all 0.3s ease;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .tip-card::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
    }

    .tip-card:hover::after {
        left: 100%;
    }

    .tip-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(67, 97, 238, 0.15);
    }

    .tip-icon {
        font-size: 32px;
        margin-bottom: 15px;
        display: block;
        animation: pulse 2s infinite;
    }

    .tip-card h4 {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color);
        margin: 0 0 15px 0;
    }

    .tip-card p {
        font-size: 14px;
        color: var(--light-text);
        line-height: 1.6;
        margin-bottom: 20px;
    }

    .tip-stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 5px;
        padding: 15px;
        background: rgba(46, 204, 113, 0.1);
        border-radius: 12px;
        border: 1px solid rgba(46, 204, 113, 0.2);
    }

    .stat-number {
        font-size: 24px;
        font-weight: 700;
        color: #27ae60;
    }

    .stat-label {
        font-size: 12px;
        color: #27ae60;
        font-weight: 500;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @media (max-width: 1200px) {
        .performance-metrics {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .insights-grid, .tips-grid {
            grid-template-columns: 1fr;
        }

        .trust-badges {
            grid-template-columns: 1fr;
        }

        .performance-metrics {
            grid-template-columns: 1fr;
        }
    }
    </style>
</div>