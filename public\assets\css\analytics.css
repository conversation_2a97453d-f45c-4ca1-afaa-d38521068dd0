.analytics-dashboard {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    background: var(--theme-gradient);
    border-radius: 24px;
    box-shadow: var(--shadow-lg);
    padding: 35px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: none;
}

.analytics-dashboard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.analytics-header {
    margin-bottom: 25px;
}

.analytics-header h2 {
    font-size: 24px;
    margin-bottom: 10px;
    color: white;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.analytics-header p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 15px;
    position: relative;
    z-index: 1;
}

.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-stat-card {
    background: #ffffff !important;
    background-image: none !important;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
    border: 1px solid #eeeeee;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.analytics-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.analytics-stat-card {
    background-color: var(--card-bg) !important;
    background-image: none !important;
}

:root .analytics-stat-card {
    background-color: #ffffff !important;
}

.dark-mode .analytics-stat-card {
    background-color: #2d3748 !important;
    background-image: none !important;
    border-color: var(--divider-color) !important;
}

.stat-icon {
    width: 55px;
    height: 55px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.stat-icon i {
    color: white;
    font-size: 22px;
}

.realtime-users .stat-icon {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.daily-users .stat-icon {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
}

.monthly-users .stat-icon {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.pageviews .stat-icon {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
}

.stat-content {
    flex-grow: 1;
}

.stat-value {
    font-size: 26px;
    font-weight: 700;
    color: var(--text-color) !important;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 16px;
    color: #ffffff !important;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-description {
    font-size: 13px;
    color: var(--light-text);
    line-height: 1.4;
}

.stat-source {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    font-style: italic;
}

.analytics-section {
    margin-bottom: 30px;
}

.analytics-section h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: var(--text-color);
    font-weight: 600;
}

.analytics-table-container {
    overflow-x: auto;
    margin-top: 20px;
    background: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 15px;
}

.analytics-chart-container {
    margin-top: 20px;
    background: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    height: 450px;
}

.analytics-chart-container * {
    color: var(--text-color) !important;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th,
.analytics-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.analytics-table th {
    background-color: rgba(var(--primary-rgb), 0.05);
    font-weight: 600;
    color: var(--text-color) !important;
}

.analytics-table td {
    color: var(--text-color) !important;
}

.analytics-table tr:hover {
    background-color: rgba(var(--primary-rgb), 0.02);
}

@media (max-width: 1200px) {
    .analytics-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) {
    .analytics-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .analytics-stats-grid {
        grid-template-columns: 1fr;
    }

    .analytics-dashboard {
        padding: 20px;
    }
}
