<?php
namespace AdManagementPro\Admin;

if (!defined('WPINC')) {
    die;
}

class AdminAssets {
    
    private static $instance = null;
    private $assets_version;
    private $loaded_assets = [];
    
    private function __construct() {
        $this->assets_version = defined('AMP_VERSION') ? AMP_VERSION : time();
        $this->init_hooks();
    }
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function init_hooks() {
        \add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        \add_action('admin_head', [$this, 'add_admin_inline_styles']);
        \add_action('admin_footer', [$this, 'add_admin_inline_scripts']);
    }
    
    public function enqueue_admin_assets($hook) {
        if (!$this->should_load_assets($hook)) {
            return;
        }
        
        $this->enqueue_core_assets();
        $this->enqueue_external_libraries();
        $this->enqueue_admin_scripts();
        $this->localize_scripts();
    }
    
    private function should_load_assets($hook) {
        $allowed_pages = [
            'toplevel_page_ad-management-dashboard',
            'toplevel_page_ad-management-pro',
            'ad-management_page_amp-positions',
            'ad-management_page_amp-analytics',
            'ad-management_page_amp-settings',
            'user-edit.php',
            'profile.php'
        ];
        
        return \in_array($hook, $allowed_pages) || \strpos($hook, 'ad-management') !== false;
    }
    
    private function enqueue_core_assets() {
        \wp_enqueue_script('jquery');
        \wp_enqueue_media();
        \wp_enqueue_script('wp-util');
        
        $plugin_url = plugin_dir_url(__FILE__);
        \wp_enqueue_style(
            'admin-unified-css',
            $plugin_url . 'assets/css/admin-unified.css',
            [],
            filemtime(plugin_dir_path(__FILE__) . 'assets/css/admin-unified.css')
        );

        $this->loaded_assets['core_css'] = true;
    }
    
    private function enqueue_external_libraries() {
        if (!\wp_script_is('sweetalert2', 'registered')) {
            \wp_enqueue_script(
                'sweetalert2',
                'https://cdn.jsdelivr.net/npm/sweetalert2@11.14.5/dist/sweetalert2.all.min.js',
                [],
                '11.14.5',
                true
            );

            \wp_script_add_data('sweetalert2', 'crossorigin', 'anonymous');
        }

        if (!\wp_style_is('font-awesome', 'registered')) {
            \wp_enqueue_style(
                'font-awesome',
                'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css',
                [],
                '6.5.0'
            );

            \wp_style_add_data('font-awesome', 'crossorigin', 'anonymous');
        }

        if (!\wp_script_is('select2', 'registered')) {
            \wp_enqueue_style(
                'select2-css',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                [],
                '4.1.0'
            );

            \wp_enqueue_script(
                'select2-js',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
                ['jquery'],
                '4.1.0',
                true
            );

            \wp_style_add_data('select2-css', 'crossorigin', 'anonymous');
            \wp_script_add_data('select2-js', 'crossorigin', 'anonymous');
        }

        $this->loaded_assets['external_libs'] = true;
    }
    
    private function enqueue_admin_scripts() {
        $plugin_url = plugin_dir_url(__FILE__);

        if (!isset($this->loaded_assets['csrf_protection']) || !$this->loaded_assets['csrf_protection']) {
            \wp_enqueue_script(
                'amp-csrf-protection',
                $plugin_url . 'assets/js/csrf-protection.js',
                ['jquery', 'sweetalert2'],
                filemtime(plugin_dir_path(__FILE__) . 'assets/js/csrf-protection.js'),
                true
            );

            $this->loaded_assets['csrf_protection'] = true;
        }

        $this->loaded_assets['admin_scripts'] = true;
    }
    
    private function localize_scripts() {
        $nonces = $this->generate_secure_nonces();
        
        if (\wp_script_is('jquery', 'enqueued')) {
            \wp_localize_script('jquery', 'ampAdmin', [
                'ajaxUrl' => \admin_url('admin-ajax.php'),
                'nonces' => $nonces,
                'text' => [
                    'confirm_delete' => \__('Are you sure you want to delete this item?', 'ad-management-pro'),
                    'error' => \__('Error', 'ad-management-pro'),
                    'success' => \__('Success', 'ad-management-pro'),
                    'loading' => \__('Loading...', 'ad-management-pro'),
                    'saved' => \__('Saved successfully', 'ad-management-pro'),
                    'security_error' => \__('Security check failed', 'ad-management-pro')
                ],
                'config' => [
                    'debug' => \defined('WP_DEBUG') && WP_DEBUG,
                    'context' => 'admin',
                    'user_can_manage' => \current_user_can('manage_options'),
                    'max_file_size' => \wp_max_upload_size()
                ]
            ]);
        }
        
        $this->loaded_assets['localized'] = true;
    }
    
    private function generate_secure_nonces() {
        if (!\current_user_can('manage_options')) {
            return [];
        }
        
        $actions = [
            'admin_ajax_nonce' => 'amp-admin-ajax',
            'position_nonce' => 'amp-admin-position',
            'edit_position_nonce' => 'amp-admin-edit-position',
            'delete_position_nonce' => 'amp-admin-delete-position',
            'toggle_status_nonce' => 'amp-admin-toggle-status',
            'get_ad_data_nonce' => 'amp-admin-get-ad-data',
            'reset_position_nonce' => 'amp-admin-reset-position'
        ];
        
        $nonces = [];
        foreach ($actions as $key => $action) {
            $nonces[$key] = \wp_create_nonce($action);
        }
        
        return $nonces;
    }
    
    public function add_admin_inline_styles() {
        if (!isset($this->loaded_assets['core_css'])) {
            return;
        }
        
        if (!$this->should_add_inline_styles()) {
            return;
        }
        
        $custom_css = $this->get_admin_custom_styles();
        
        if (!empty($custom_css)) {
            echo '<style id="amp-admin-inline-styles">' . $custom_css . '</style>';
        }
    }
    
    private function should_add_inline_styles() {
        $screen = \get_current_screen();
        return $screen && \strpos($screen->id, 'ad-management') !== false;
    }
    
    private function get_admin_custom_styles() {
        return '';
    }

    public function add_admin_inline_scripts() {
        if (!isset($this->loaded_assets['localized']) || !$this->should_add_inline_styles()) {
            return;
        }

        $inline_js = $this->get_admin_inline_scripts();

        if (!empty($inline_js)) {
            echo '<script id="amp-admin-inline-scripts">' . $inline_js . '</script>';
        }
    }

    private function get_admin_inline_scripts() {
        return '
            jQuery(document).ready(function($) {
                $(\'select[multiple]\').select2();
            });
        ';
    }

    public function get_loaded_assets() {
        return $this->loaded_assets;
    }

    public function is_asset_loaded($asset) {
        return isset($this->loaded_assets[$asset]);
    }
}
