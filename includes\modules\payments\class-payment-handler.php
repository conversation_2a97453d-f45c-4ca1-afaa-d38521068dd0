<?php
if (!defined('ABSPATH')) {
    exit;
}

class AMP_Payment_Handler {
    private static $instance = null;
    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init_hooks();
        $this->init_webhooks();
        $this->load_reservation_system();
        $this->init_reservation_system();
        $this->init_cleanup_scheduler();
        $this->verify_database_schema();
        $this->setup_cleanup_cron();
    }

    private function load_plisio_api() {
        static $loaded = false;
        if (!$loaded) {
            require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-plisio-api.php';
            $loaded = true;
        }
    }

    private function load_price_calculator() {
        static $loaded = false;
        if (!$loaded) {
            require_once AMP_PLUGIN_DIR . 'includes/core/class-price-calculator.php';
            $loaded = true;
        }
    }

    private function load_reservation_system() {
        static $loaded = false;
        if (!$loaded) {
            require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-reservation-system.php';
            $loaded = true;
        }
    }

    private function load_position_manager() {
        static $loaded = false;
        if (!$loaded) {
            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $loaded = true;
        }
    }

    private function load_roles() {
        static $loaded = false;
        if (!$loaded) {
            require_once AMP_PLUGIN_DIR . 'includes/core/roles.php';
            $loaded = true;
        }
    }
    
    private function init_hooks() {
        add_action('wp_ajax_create_plisio_invoice', [$this, 'handle_create_plisio_invoice']);
        add_action('wp_ajax_handle_checkout', [$this, 'handle_checkout']);
        add_action('wp_ajax_check_user_bypass', [$this, 'check_user_bypass']);
        add_action('wp_ajax_process_bypass_checkout', [$this, 'handle_bypass_checkout']);
        add_action('wp_ajax_save_plisio_settings', [$this, 'save_plisio_settings_ajax']);
        add_action('wp_ajax_test_plisio_connection', [$this, 'test_plisio_connection_ajax']);
        add_action('wp_ajax_test_plisio_invoice', [$this, 'test_plisio_invoice_ajax']);
        add_action('wp_ajax_get_webhook_logs', [$this, 'get_webhook_logs_ajax']);
        add_action('wp_ajax_get_pending_payments', [$this, 'get_pending_payments_ajax']);
        add_action('wp_ajax_manual_verify_payment', [$this, 'manual_verify_payment_ajax']);
        add_action('wp_ajax_get_payment_history', [$this, 'get_payment_history_ajax']);
        add_action('wp_ajax_verify_transaction_api_only', [$this, 'verify_transaction_api_only_ajax']);
        add_action('wp_ajax_clear_webhook_logs', [$this, 'clear_webhook_logs_ajax']);
        add_action('wp_ajax_clear_payment_history', [$this, 'clear_payment_history_ajax']);
        add_action('wp_ajax_get_logs_stats', [$this, 'get_logs_stats_ajax']);
        add_action('wp_ajax_simulate_payment_webhook', [$this, 'simulate_payment_webhook_ajax']);
        add_action('wp_ajax_amp_get_plisio_currencies', [$this, 'get_plisio_currencies_ajax']);
        add_action('wp_ajax_retry_manual_verification', [$this, 'retry_manual_verification_ajax']);
        add_action('wp_ajax_get_position_ownership_details', [$this, 'get_position_ownership_details_ajax']);
        add_action('wp_ajax_get_payment_details', [$this, 'get_payment_details_ajax']);
        add_action('wp_ajax_get_signature_log_details', [$this, 'get_signature_log_details_ajax']);
        add_action('wp_ajax_delete_payment_record', [$this, 'delete_payment_record_ajax']);
    }
    
    private function init_webhooks() {
        add_action('rest_api_init', [$this, 'register_rest_routes']);
    }
    
    private function init_reservation_system() {
        AMP_Reservation_System::instance();
    }

    private function init_cleanup_scheduler() {
        if (!wp_next_scheduled('amp_cleanup_stuck_orders')) {
            wp_schedule_event(time(), 'amp_six_hourly', 'amp_cleanup_stuck_orders');
        }
        add_action('amp_cleanup_stuck_orders', [$this, 'cleanup_stuck_pending_orders']);

        add_filter('cron_schedules', function($schedules) {
            $schedules['amp_six_hourly'] = array(
                'interval' => 6 * 60 * 60,
                'display' => 'Every 6 Hours'
            );
            return $schedules;
        });
    }
    
    private function verify_database_schema() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ad_payments';

        $column_exists = $wpdb->get_var(
            "SHOW COLUMNS FROM `{$table_name}` LIKE 'purchase_type'"
        );

        if ($column_exists === null) {
            $wpdb->query(
                "ALTER TABLE `{$table_name}` ADD `purchase_type` VARCHAR(20) NOT NULL DEFAULT 'api' AFTER `status`"
            );
        }
    }
        
    public function get_plisio_setting($setting_name, $default = '') {
        $cache = \AdManagementPro\Core\Cache::instance();
        $cache_key = 'plisio_setting_' . $setting_name;
        $cached_value = $cache->get($cache_key, 'payment_settings');
        if (false !== $cached_value) {
            return $cached_value;
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'plisio_settings';
        $value = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM $table_name WHERE setting_name = %s",
            $setting_name
        ));

        $value = $value !== null ? $value : $default;
        $cache->set($cache_key, $value, 0, 'payment_settings');
        return $value;
    }
    
    public function update_plisio_setting($setting_name, $setting_value) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'plisio_settings';
        $result = $wpdb->replace(
            $table_name,
            array(
                'setting_name' => $setting_name,
                'setting_value' => $setting_value
            ),
            array('%s', '%s')
        );
        return $result !== false;
    }
    
    public function save_plisio_settings_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'save_plisio_settings')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        $test_mode = isset($_POST['test_mode']) ? sanitize_text_field($_POST['test_mode']) : '0';
        $selected_currency = isset($_POST['selected_currency']) ? sanitize_text_field($_POST['selected_currency']) : 'USDT_TRX';

        $encryption_manager = \AMP_Encryption_Manager::instance();

        if (!empty($api_key)) {
            $encryption_manager->set_secret('plisio_api_key', $api_key);
            $result1 = $this->update_plisio_setting('api_key', '[ENCRYPTED]');
        } else {
            $existing_key = $encryption_manager->get_secret('plisio_api_key');
            if (!empty($existing_key)) {
                $result1 = $this->update_plisio_setting('api_key', '[ENCRYPTED]');
            } else {
                $result1 = true;
            }
        }

        $result2 = $this->update_plisio_setting('test_mode', $test_mode);
        update_option('plisio_selected_currency', $selected_currency);
        $cache = \AdManagementPro\Core\Cache::instance();
        $cache->clear_group('payment_settings');

        if ($result1 !== false && $result2 !== false) {
            wp_send_json_success(['message' => 'บันทึกการตั้งค่าเรียบร้อยแล้ว']);
        } else {
            wp_send_json_error(['message' => 'ไม่สามารถบันทึกได้']);
        }
    }
    
    public function test_plisio_connection_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'save_plisio_settings')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key) || $api_key === 'USE_STORED_ENCRYPTED') {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            $api_key = $encryption_manager->get_secret('plisio_api_key');

            if (empty($api_key)) {
                wp_send_json_error(['message' => 'ไม่พบ API Key ที่เข้ารหัสไว้ กรุณาตั้งค่า API Key ใหม่']);
                return;
            }
        }

        $plisio = new \Plisio_API($api_key);
        $result = $plisio->test_connection();
        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        } else {
            wp_send_json_success(['message' => 'การเชื่อมต่อสำเร็จ - ระบบเข้ารหัส/ถอดรหัสทำงานปกติ']);
        }
    }
    
    public function test_plisio_invoice_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'save_plisio_settings')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 5.00;

        if (empty($api_key) || $api_key === 'USE_STORED_ENCRYPTED') {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            $api_key = $encryption_manager->get_secret('plisio_api_key');
        }

        if (empty($api_key)) {
            wp_send_json_error(['message' => 'ไม่พบ API Key ที่เข้ารหัสไว้ กรุณาตั้งค่า API Key ใหม่']);
            return;
        }
        if ($amount <= 0) {
            wp_send_json_error(['message' => 'จำนวนเงินต้องมากกว่า 0']);
            return;
        }
        $plisio = new \Plisio_API($api_key);
        $params = array(
            'order_name' => 'Test Invoice - ' . date('Y-m-d H:i:s'),
            'order_number' => 'TEST-' . time(),
            'source_amount' => $amount,
            'source_currency' => 'USD',
            'email' => 'test@' . parse_url(home_url(), PHP_URL_HOST)
        );
        $result = $plisio->create_invoice($params);
        if (is_wp_error($result)) {
            wp_send_json_error(['message' => $result->get_error_message()]);
        } else {
            wp_send_json_success(['message' => 'สร้างใบแจ้งหนี้ทดสอบสำเร็จ']);
        }
    }
    
    public function register_rest_routes() {
        register_rest_route('ad-management-pro/v1', '/plisio-webhook', array(
            'methods' => 'POST',
            'callback' => [$this, 'handle_plisio_webhook'],
            'permission_callback' => '__return_true',
            'args' => [],
        ));

        register_rest_route('ad-management-pro/v1', '/plisio-webhook-success', array(
            'methods' => 'GET',
            'callback' => [$this, 'handle_plisio_webhook_success'],
            'permission_callback' => '__return_true'
        ));

        register_rest_route('ad-management-pro/v1', '/plisio-webhook-fail', array(
            'methods' => 'GET',
            'callback' => [$this, 'handle_plisio_webhook_fail'],
            'permission_callback' => '__return_true'
        ));
    }
    
    public function handle_plisio_webhook($request) {
        $params = $request->get_params();       
        if (!$this->verify_plisio_callback($params)) {
             $this->log_webhook_event('webhook_signature_failed', [
                'ip_address' => \AMP_Utilities::get_client_ip(),
                'body' => $params
            ]);
            $order_number = $params['order_number'] ?? '';
            if ($order_number && preg_match('/AMP-(\\d+)-/', $order_number, $matches)) {
                $user_id = intval($matches[1]);
                $order_data = get_user_meta($user_id, 'amp_pending_order', true);
                if ($order_data && is_array($order_data)) {
                     $this->save_payment_history_to_file($user_id, $order_data, ($params['txn_id'] ?? ''), 'signature_failed');
                     delete_user_meta($user_id, 'amp_pending_order');
                }
            }
            return new WP_REST_Response('Signature verification failed. Logged for review.', 200);
        }
        if (!$this->check_webhook_rate_limit()) {
            return new WP_REST_Response('Rate limit exceeded', 429);
        }
        if (!isset($params['txn_id']) || !isset($params['status'])) {
            return new WP_REST_Response('Missing parameters', 400);
        }
        $txn_id = sanitize_text_field($params['txn_id']);
        $status = sanitize_text_field($params['status']);
        $order_number = isset($params['order_number']) ? sanitize_text_field($params['order_number']) : '';
        $received_amount = isset($params['amount']) ? floatval($params['amount']) : 0;
        $currency = isset($params['currency']) ? sanitize_text_field($params['currency']) : 'USD';       
        $is_test_mode = ($this->get_plisio_setting('test_mode', '0') === '1');
        $processing_mode = $is_test_mode ? 'test' : 'live';
        global $wpdb;
        $existing_payment = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}ad_payments WHERE transaction_id = %s AND status = 'completed'",
            $txn_id
        ));
        if ($existing_payment) {
            $this->log_webhook_event('duplicate_transaction', ['txn_id' => $txn_id, 'status' => $status, 'order_number' => $order_number]);
            return new WP_REST_Response('Transaction already processed', 200);
        }
        $user_id = null;
        $order_data = null;       
        if (!empty($order_number)) {
            if (preg_match('/AMP-(\d+)-/', $order_number, $matches)) {
                $user_id = intval($matches[1]);
                $order_data = get_user_meta($user_id, 'amp_pending_order', true);
                
                if (!$order_data || !is_array($order_data) || $order_data['order_id'] !== $order_number) {
                    $order_data = null;
                }
            }
        }
        if (!$user_id || !$order_data) {
            $this->log_webhook_event('webhook_order_not_found', [
                'txn_id' => $txn_id,
                'status' => $status,
                'order_number' => $order_number,
                'received_amount' => $received_amount,
                'currency' => $currency,
                'processing_mode' => $processing_mode,
                'note' => 'Could not find user or order data'
            ]);
            return new WP_REST_Response('Order not found', 404);
        }
        $this->log_webhook_event('webhook_received', $params);
        switch (strtolower($status)) {
            case 'completed':
            case 'success':
                if (!$is_test_mode) {
                    if (!$this->verify_transaction_with_plisio_api($txn_id, $order_data)) {
                        $this->log_webhook_event('payment_api_verification_failed', [
                            'txn_id' => $txn_id,
                            'order_number' => $order_number,
                            'note' => 'Server-to-server API verification failed. Payment will not be processed automatically. Please investigate.'
                        ]);
                        return new WP_REST_Response('API Verification Failed. Logged for review.', 200);
                    }
                }

                $this->log_webhook_event('payment_completed', [
                    'user_id' => $user_id,
                    'order_id' => $order_data['order_id'],
                    'amount' => $received_amount,
                    'currency' => $currency,
                    'transaction_id' => $txn_id,
                    'processing_mode' => $processing_mode
                ]);
                $this->process_successful_payment($user_id, $order_data, false, 'completed');
                break;

            case 'cancelled':
            case 'expired':
            case 'error':
                $this->log_webhook_event('payment_failed', [
                    'status' => $status,
                    'user_id' => $user_id,
                    'order_id' => $order_data['order_id'],
                    'amount' => $received_amount,
                    'currency' => $currency,
                    'transaction_id' => $txn_id,
                    'processing_mode' => $processing_mode
                ]);

                update_user_meta($user_id, 'amp_last_payment_status', $status);
                update_user_meta($user_id, 'amp_last_payment_date', current_time('mysql'));
                $reservation_system = \AMP_Reservation_System::instance();
                $reservation_system->cancel_user_timer($user_id, 'payment_failed_' . $status);
                $this->save_payment_history_to_file($user_id, $order_data, $txn_id, $status . '_failed');
                delete_user_meta($user_id, 'amp_pending_order');
                break;

            case 'mismatch':
                $this->log_webhook_event('payment_mismatch', [
                    'user_id' => $user_id,
                    'order_id' => $order_data['order_id'],
                    'expected_amount' => $order_data['total'] ?? 0,
                    'received_amount' => $received_amount,
                    'currency' => $currency,
                    'transaction_id' => $txn_id,
                    'processing_mode' => $processing_mode,
                    'note' => 'Overpayment detected but payment accepted'
                ]);

                update_user_meta($user_id, 'amp_last_payment_status', 'mismatch');
                update_user_meta($user_id, 'amp_last_payment_date', current_time('mysql'));

                $this->process_successful_payment($user_id, $order_data, false, 'mismatch');
                break;

            case 'new':
            case 'pending':
                $this->log_webhook_event('payment_waiting', [
                    'status' => $status,
                    'user_id' => $user_id,
                    'order_id' => $order_data['order_id'],
                    'amount' => $received_amount,
                    'currency' => $currency,
                    'transaction_id' => $txn_id,
                    'processing_mode' => $processing_mode,
                    'note' => 'Payment waiting - no action taken'
                ]);
                break;

            default:
                $this->log_webhook_event('payment_unknown_status', [
                    'status' => $status,
                    'user_id' => $user_id,
                    'order_id' => $order_data['order_id'],
                    'amount' => $received_amount,
                    'currency' => $currency,
                    'transaction_id' => $txn_id,
                    'processing_mode' => $processing_mode,
                    'note' => 'Unknown status received'
                ]);
                break;
        }

        return new WP_REST_Response('OK', 200);
    }

    private function verify_plisio_callback(array $post_data): bool {
        $encryption_manager = \AMP_Encryption_Manager::instance();
        $secret_key = $encryption_manager->get_secret('plisio_api_key');

        if (empty($secret_key)) {
            $this->save_signature_log([
                'result' => 'failed',
                'reason' => 'Plisio Secret Key is not configured.'
            ]);
            return false;
        }
        $log_data = [
            'timestamp' => current_time('Y-m-d H:i:s'),
            'ip_address' => \AMP_Utilities::get_client_ip(),
            'received_data' => $post_data,
            'result' => 'failed',
        ];
        if (!isset($post_data['verify_hash'])) {
            $log_data['reason'] = 'No verify_hash found in POST data.';
            $this->save_signature_log($log_data);
            return false;
        }
        $received_hash = $post_data['verify_hash'];
        unset($post_data['verify_hash']);
        foreach ($post_data as $key => &$value) {
            $value = (string)$value;
        }
        unset($value);
        ksort($post_data);
        $string_to_hash = serialize($post_data);
        $calculated_hash = hash_hmac('sha1', $string_to_hash, $secret_key);        
        $log_data['string_to_hash'] = $string_to_hash;
        $log_data['received_signature'] = $received_hash;
        $log_data['calculated_signature'] = $calculated_hash;
        $log_data['algorithm'] = 'HMAC-SHA1-serialize';

        if (hash_equals($calculated_hash, $received_hash)) {
            $log_data['result'] = 'success';
            $log_data['reason'] = 'Signature matched';
            $this->save_signature_log($log_data);
            return true;
        }

        $log_data['reason'] = 'Signature mismatch';
        $this->save_signature_log($log_data);
        return false;
    }

    private function save_signature_log($log_data) {
        $upload_dir = wp_upload_dir();
        $logs_dir = $upload_dir['basedir'] . '/amp-logs/security';
        if (!wp_mkdir_p($logs_dir)) {
            error_log('Failed to create security log directory: ' . $logs_dir);
            return;
        }
        $log_file = $logs_dir . '/signature_verification.log';
        $log_entry = wp_json_encode($log_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . ",\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }

    private function verify_payment_amount($order_data, $received_amount) {
        $expected_amount = floatval($order_data['total'] ?? 0);
        $tolerance_percentage = 0.01;
        $tolerance_amount = $expected_amount * $tolerance_percentage;

        return abs($expected_amount - $received_amount) <= $tolerance_amount;
    }

    private function verify_order_ownership($user_id, $order_data) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }

        $created_at = $order_data['created_at'] ?? 0;
        $order_timeout = 24 * 60 * 60;
        
        if (time() - $created_at > $order_timeout) {
            return false;
        }

        $cart = $order_data['cart'] ?? [];
        if (empty($cart) || !is_array($cart)) {
            return false;
        }

        $this->load_position_manager();

        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
        foreach ($cart as $item) {
            $position_name = $item['position_name'] ?? '';
            if (!$position_name) {
                continue;
            }
            
            $visibility = $position_manager->get_position_visibility_state($position_name, $user_id);

            if ($visibility['is_purchasable']) {
                continue;
            }

            if ($visibility['reason'] === 'reserved' || $visibility['reason'] === 'reserved_by_user') {
                continue;
            }

            return false;
        }

        return true;
    }

    public function handle_plisio_webhook_success($request) {
        wp_redirect(home_url('/dashboard/?tab=my-ads&payment=success'));
        exit;
    }

    public function handle_plisio_webhook_fail($request) {
        wp_redirect(home_url('/dashboard/?tab=cart&payment=failed'));
        exit;
    }

    private function record_purchase($user_id, $position_name, $duration, $payment_method, $transaction_id, $purchase_type) {
        $this->load_price_calculator();
        $this->load_position_manager();

        $db = \AdManagementPro\Core\Database::instance();
        $price_calculator = \AMP_Price_Calculator::instance();
        $context = (current_user_can('manage_options') && (defined('DOING_AJAX') && DOING_AJAX)) ? 'admin' : 'public';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance($context);

        $price_details = $price_calculator->calculate_price_details($position_name, $duration);
        $amount_usdt = $price_details['final_price'];

        $payment_data = [
            'user_id' => $user_id,
            'ad_position' => $position_name,
            'amount' => $amount_usdt,
            'duration' => $duration,
            'payment_date' => current_time('mysql'),
            'payment_method' => $payment_method,
            'transaction_id' => $transaction_id,
            'status' => 'completed',
            'purchase_type' => $purchase_type,
        ];

        $result = $db->insert('ad_payments', $payment_data, ['%d', '%s', '%f', '%d', '%s', '%s', '%s', '%s', '%s']);
        if ($result === false) {
            throw new \Exception("Failed to insert payment record for {$position_name}. DB Error: " . $db->last_error());
        }

        $previous_owner_id = $position_manager->get_position_owner_from_usermeta($position_name);
        
        $expiration_date = $position_manager->calculate_renewal_expiration_date($position_name, $user_id, $duration);
        
        $update_result = $position_manager->update_position_ownership($position_name, $user_id, $expiration_date);
        if (is_wp_error($update_result)) {
            throw new \Exception("Failed to update ownership for {$position_name}: " . $update_result->get_error_message());
        }

        if ($previous_owner_id && $previous_owner_id !== $user_id) {
            if (!function_exists('clear_position_clicks')) {
                require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';
            }
            if (function_exists('clear_position_clicks')) {
                clear_position_clicks($position_name);
                error_log("AMP: Statistics reset for position '{$position_name}' during purchase - ownership changed from user {$previous_owner_id} to user {$user_id}");
            }
        }

        return true;
    }

    public function process_successful_payment($user_id, $order_data, $skip_ownership_check = false, $payment_status = 'completed') {
        if (!isset($order_data['cart']) || !is_array($order_data['cart'])) {
            $this->log_webhook_event('payment_processing_error', [
                'error' => 'Missing or invalid cart data',
                'user_id' => $user_id,
                'order_data' => $order_data,
                'skip_ownership_check' => $skip_ownership_check
            ]);
            return false;
        }

        $this->load_position_manager();

        global $wpdb;
        $db = \AdManagementPro\Core\Database::instance();
        $context = (current_user_can('manage_options') && (defined('DOING_AJAX') && DOING_AJAX)) ? 'admin' : 'public';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance($context);
        $db->start_transaction();

        try {
            $this->log_webhook_event('payment_processing_start', [
                'user_id' => $user_id,
                'order_id' => $order_data['order_id'] ?? 'N/A',
                'cart_count' => count($order_data['cart']),
                'skip_ownership_check' => $skip_ownership_check
            ]);

            foreach ($order_data['cart'] as $item) {
                $position_name = $item['position_name'] ?? null;
                $duration = (int)($item['duration'] ?? 30);

                if (!$position_name) {
                    throw new \Exception('Cart item is missing position_name.');
                }

                if (!$skip_ownership_check) {
                    $ownership_state = $position_manager->get_position_ownership_state($position_name);
                    if ($ownership_state['is_owned'] && !$ownership_state['is_expired'] && isset($ownership_state['user_id']) && $ownership_state['user_id'] != $user_id) {
                        throw new \Exception("Position {$position_name} is already owned by user ID {$ownership_state['user_id']}. Current owner expires on: " . ($ownership_state['expiration_date'] ?? 'N/A'));
                    }
                } else {
                    $this->log_webhook_event('admin_override_ownership_check', [
                        'position_name' => $position_name,
                        'user_id' => $user_id,
                        'order_id' => $order_data['order_id'] ?? 'N/A',
                        'reason' => 'Admin manual verification - ownership check bypassed'
                    ]);
                }

                $is_test_mode = ($this->get_plisio_setting('test_mode', '0') === '1');
                if (!$skip_ownership_check && !$is_test_mode) {
                    $existing_purchase = $wpdb->get_row($wpdb->prepare(
                        "SELECT id FROM {$wpdb->prefix}ad_payments
                         WHERE user_id = %d AND ad_position = %s AND status = 'completed'
                         AND payment_date > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
                        $user_id, $position_name
                    ));

                    if ($existing_purchase) {
                        throw new \Exception("User {$user_id} already purchased {$position_name} within the last hour. Duplicate purchase prevented.");
                    }
                }

                $this->record_purchase(
                    $user_id,
                    $position_name,
                    $duration,
                    'plisio',
                    $order_data['invoice_id'] ?? 'N/A',
                    'api'
                );
            }

            delete_user_meta($user_id, 'amp_countdown_timer');
            delete_user_meta($user_id, 'amp_cart');
            update_user_meta($user_id, 'amp_last_purchase_successful', true);
            update_user_meta($user_id, 'amp_last_purchase_order_id', $order_data['order_id'] ?? 'N/A');
            update_user_meta($user_id, 'amp_last_purchase_date', current_time('mysql'));
            $this->send_purchase_confirmation_email($user_id, $order_data);
            $this->save_payment_history_to_file($user_id, $order_data, $order_data['invoice_id'] ?? '', $payment_status);
            $db->commit();
            $reservation_system = \AMP_Reservation_System::instance();
            $reservation_system->cancel_user_timer($user_id, 'payment_success');
            delete_user_meta($user_id, 'amp_pending_order');

            do_action('amp_payment_success', $user_id, $order_data, $order_data['invoice_id'] ?? '');
            return true;
        } catch (\Exception $e) {
            $db->rollback();
            $this->log_webhook_event('payment_processing_exception', [
                'order_id' => $order_data['order_id'] ?? 'N/A',
                'user_id' => $user_id,
                'exception_message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'cart_data' => $order_data['cart'] ?? [],
                'skip_ownership_check' => $skip_ownership_check,
                'debug_trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    public function handle_create_plisio_invoice() {
        try {
            $development_mode = get_option('amp_development_mode', 'disabled');
            if ($development_mode === 'enabled') {
                wp_send_json_error(['message' => '🚧 ระบบอยู่ในโหมดพัฒนา ไม่สามารถทำการซื้อสินค้าได้ในขณะนี้']);
                return;
            }

            $nonce_field = $_POST['security'] ?? '';

            if (!wp_verify_nonce($nonce_field, 'amp_dashboard_action')) {
                error_log("AMP: Plisio invoice security check failed - Expected: amp_dashboard_action, Received: " . substr($nonce_field, 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => 'กรุณาเข้าสู่ระบบ']);
            }
            
            $selected_cid = get_option('plisio_selected_currency', 'USDT_TRX');
            $currency = $this->get_currency_from_cid($selected_cid);
            if (!$currency) {
                wp_send_json_error(['message' => 'ไม่พบข้อมูลสกุลเงินที่เลือก']);
                return;
            }

            $total_price = floatval($_POST['total_amount'] ?? 0);

            if ($total_price <= 0) {
                $cart = get_user_meta($user_id, 'amp_cart', true);
                if (empty($cart) || !is_array($cart)) {
                    wp_send_json_error(['message' => 'ตะกร้าสินค้าว่างเปล่าและไม่มียอดเงิน']);
                }

                $price_calculator = \AMP_Price_Calculator::instance();
                $total_price = 0;
                foreach ($cart as $item) {
                    $position_name = $item['position_name'] ?? '';
                    $duration = (int)($item['duration'] ?? 30);
                    $price_details = $price_calculator->calculate_price_details($position_name, $duration);
                    $total_price += $price_details['final_price'];
                }
            }

            $plisio = new \Plisio_API();
            
            if (empty($plisio->get_api_key())) {
                wp_send_json_error(['message' => 'ระบบชำระเงิน Crypto ยังไม่ได้ตั้งค่า']);
            }
            
            $user = wp_get_current_user();
            $order_id = 'AMP-' . $user_id . '-' . time();
            
            $invoice_params = [
                'order_number' => $order_id,
                'order_name' => 'Ad Management Pro - Cart Checkout',
                'source_amount' => $total_price,
                'source_currency' => 'USD',
                'email' => 'noreply@' . parse_url(home_url(), PHP_URL_HOST),
                'currency' => $currency,
                'psys_cid' => $selected_cid
            ];
            
            $result = $plisio->create_invoice($invoice_params);
            
            if (is_wp_error($result)) {
                wp_send_json_error(['message' => 'ไม่สามารถสร้าง Invoice ได้: ' . $result->get_error_message()]);
            }
            
            if (isset($result['status']) && $result['status'] === 'success') {
                $cart = get_user_meta($user_id, 'amp_cart', true);
                if (!is_array($cart)) {
                    $cart = [];
                }

                update_user_meta($user_id, 'amp_pending_order', [
                    'order_id' => $order_id,
                    'cart' => $cart,
                    'total' => $total_price,
                    'invoice_id' => $result['data']['txn_id'] ?? '',
                    'created_at' => time()
                ]);

                wp_send_json_success([
                    'payment_url' => $result['data']['invoice_url'] ?? '',
                    'txn_id' => $result['data']['txn_id'] ?? '',
                    'total_amount' => $total_price,
                    'order_id' => $order_id
                ]);
            } else {
                wp_send_json_error(['message' => 'ไม่สามารถสร้าง Invoice ได้']);
            }

        } catch (Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ: ' . $e->getMessage()]);
        }
    }
    
    public function handle_checkout() {
        try {
            $development_mode = get_option('amp_development_mode', 'disabled');
            if ($development_mode === 'enabled') {
                wp_send_json_error(['message' => '🚧 ระบบอยู่ในโหมดพัฒนา ไม่สามารถทำการซื้อสินค้าได้ในขณะนี้']);
            }

            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                wp_send_json_error(['message' => 'Security check failed']);
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => 'กรุณาเข้าสู่ระบบ']);
            }

            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');

            $cart = get_user_meta($user_id, 'amp_cart', true);
            if (empty($cart) || !is_array($cart)) {
                wp_send_json_error(['message' => 'ตะกร้าสินค้าว่างเปล่า']);
            }

            foreach ($cart as $item) {
                $position_name = $item['position_name'] ?? '';
                if (!empty($position_name)) {
                    $eligibility_check = $position_manager->validate_purchase_eligibility($position_name, $user_id);
                    if (is_wp_error($eligibility_check)) {
                        wp_send_json_error(['message' => "ตำแหน่ง {$position_name}: " . $eligibility_check->get_error_message()]);
                    }
                }
            }

            $selected_cid = get_option('plisio_selected_currency', 'USDT_TRX');
            $currency = $this->get_currency_from_cid($selected_cid);
            if (!$currency) {
                wp_send_json_error(['message' => 'ไม่พบข้อมูลสกุลเงินที่เลือก']);
                return;
            }

            $total_price = floatval($_POST['total_amount'] ?? 0);

            if ($total_price <= 0) {
                $price_calculator = \AMP_Price_Calculator::instance();
                $total_price = 0;
                foreach ($cart as $item) {
                    $position_name = $item['position_name'] ?? '';
                    $duration = (int)($item['duration'] ?? 30);
                    $price_details = $price_calculator->calculate_price_details($position_name, $duration);
                    $total_price += $price_details['final_price'];
                }
            }

            $plisio = new \Plisio_API();
            
            if (empty($plisio->get_api_key())) {
                wp_send_json_error(['message' => 'ระบบชำระเงิน Crypto ยังไม่ได้ตั้งค่า']);
            }
            
            $user = wp_get_current_user();
            $order_id = 'AMP-' . $user_id . '-' . time();
            
            $invoice_params = [
                'order_number' => $order_id,
                'order_name' => 'Ad Management Pro - Cart Checkout',
                'source_amount' => $total_price,
                'source_currency' => 'USD',
                'email' => 'noreply@' . parse_url(home_url(), PHP_URL_HOST),
                'currency' => $currency,
                'psys_cid' => $selected_cid
            ];
            
            $invoice = $plisio->create_invoice($invoice_params);
            
            if (is_wp_error($invoice)) {
                wp_send_json_error(['message' => 'ไม่สามารถสร้างใบแจ้งหนี้ได้: ' . $invoice->get_error_message()]);
            }
            
            if (isset($invoice['status']) && $invoice['status'] === 'success') {
                $cart = get_user_meta($user_id, 'amp_cart', true);
                if (!is_array($cart)) {
                    $cart = [];
                }

                update_user_meta($user_id, 'amp_pending_order', [
                    'order_id' => $order_id,
                    'cart' => $cart,
                    'total' => $total_price,
                    'invoice_id' => $invoice['data']['txn_id'] ?? '',
                    'created_at' => time()
                ]);

                wp_send_json_success([
                    'payment_url' => $invoice['data']['invoice_url'] ?? '',
                    'txn_id' => $invoice['data']['txn_id'] ?? '',
                    'total_amount' => $total_price,
                    'order_id' => $order_id,
                ]);
            } else {
                wp_send_json_error(['message' => 'ไม่สามารถสร้างใบแจ้งหนี้ได้']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ: ' . $e->getMessage()]);
        }
    }
    
    public function check_user_bypass() {
        if (!is_user_logged_in()) {
            wp_send_json_error(['has_bypass' => false]);
            return;
        }

        if (!\current_user_can('manage_options') && !\current_user_can('amp_advertiser_access')) {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                wp_send_json_error(['message' => 'Security check failed'], 403);
                return;
            }
        }

        $user_id = get_current_user_id();
        $has_bypass = amp_user_has_bypass_permission($user_id);

        wp_send_json_success(['has_bypass' => $has_bypass]);
    }
    
    public function handle_bypass_checkout() {
        try {
            $nonce = $_POST['security'] ?? '';

            if (!wp_verify_nonce($nonce, 'amp_dashboard_action')) {
                error_log("AMP: Payment bypass checkout security check failed - Expected: amp_dashboard_action, Received: " . substr($nonce, 0, 10) . "...");
                wp_send_json_error(['message' => 'การตรวจสอบความปลอดภัยล้มเหลว']);
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => 'กรุณาเข้าสู่ระบบเพื่อดำเนินการต่อ']);
            }

            if (!amp_user_has_bypass_permission($user_id)) {
                wp_send_json_error(['message' => 'คุณไม่มีสิทธิ์ในการดำเนินการนี้']);
            }

            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');

            $cart = get_user_meta($user_id, 'amp_cart', true);
            if (empty($cart) || !is_array($cart)) {
                wp_send_json_error(['message' => 'ตะกร้าสินค้าของคุณว่างเปล่า']);
            }

            if (!user_can($user_id, 'manage_options')) {
                foreach ($cart as $item) {
                    $position_name = $item['position_name'] ?? '';
                    if (!empty($position_name)) {
                        $eligibility_check = $position_manager->validate_purchase_eligibility($position_name, $user_id);
                        if (is_wp_error($eligibility_check)) {
                            wp_send_json_error(['message' => "ตำแหน่ง {$position_name}: " . $eligibility_check->get_error_message()]);
                        }
                    }
                }
            }

            $this->load_price_calculator();
            $this->load_position_manager();

            $price_calculator = \AMP_Price_Calculator::instance();
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
            $db = \AdManagementPro\Core\Database::instance();
            $payments_table = $db->get_table('ad_payments');

            $db->start_transaction();
            
            $purchased_positions = [];

            foreach ($cart as $item) {
                $position_name = $item['position_name'] ?? null;
                $duration = (int)($item['duration'] ?? 30);
                
                if (!$position_name) continue;

                $this->record_purchase(
                    $user_id,
                    $position_name,
                    $duration,
                    'bypass',
                    'BYPASS-' . $user_id . '-' . time(),
                    'bypass'
                );
                
                $purchased_positions[] = $position_name;
            }
            
            update_user_meta($user_id, 'amp_cart', []);
            delete_user_meta($user_id, 'amp_countdown_timer');

            $db->commit();

            do_action('amp_payment_success', $user_id, ['cart' => $cart], 'BYPASS-' . $user_id . '-' . time());

            wp_send_json_success(['message' => 'การซื้อเสร็จสมบูรณ์ (โหมด Bypass)']);
            
        } catch (\Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function get_webhook_logs_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        try {
            $filter_mode = isset($_POST['filter_mode']) ? sanitize_text_field($_POST['filter_mode']) : null;
            $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 50;
            
            $logs = $this->read_webhook_logs_from_files($limit, $filter_mode);
            wp_send_json_success(['logs' => $logs]);
        } catch (Exception $e) {
            wp_send_json_error(['message' => 'Error reading logs: ' . $e->getMessage()]);
        }
    }

    private function read_webhook_logs_from_files($limit = 50, $filter_mode = null) {
        $upload_dir = wp_upload_dir();
        $logs_dir = $upload_dir['basedir'] . '/amp-logs/webhooks';

        if (!is_dir($logs_dir)) {
            return [];
        }

        $log_files = [];
        $years = glob($logs_dir . '/*', GLOB_ONLYDIR);

        if (empty($years)) {
            return [];
        }

        foreach ($years as $year_dir) {
            $months = glob($year_dir . '/*', GLOB_ONLYDIR);

            foreach ($months as $month_dir) {
                $files = glob($month_dir . '/*.log');

                foreach ($files as $file) {
                    if (is_readable($file)) {
                        $log_files[] = [
                            'file' => $file,
                            'mtime' => filemtime($file)
                        ];
                    }
                }
            }
        }

        if (empty($log_files)) {
            return [];
        }

        usort($log_files, function($a, $b) {
            return $b['mtime'] - $a['mtime'];
        });

        $formatted_logs = [];
        $count = 0;

        foreach ($log_files as $log_file) {
            if ($count >= $limit) break;

            $file_info = $this->parse_webhook_filename($log_file['file']);
            
            if ($filter_mode && $filter_mode !== 'all') {
                $is_official_ip = $this->is_plisio_official_ip($file_info['ip_address']);

                if ($filter_mode === 'official') {
                    if ($file_info['event_type'] !== 'webhook_received' || !$is_official_ip) {
                        continue;
                    }
                } elseif ($filter_mode === 'simulated') {
                    if ($file_info['event_type'] !== 'simulated_webhook') {
                        continue;
                    }
                }
            }

            $content = file_get_contents($log_file['file']);
            if ($content === false) {
                continue;
            }

            $raw_data = json_decode($content, true);
            if (!$raw_data) {
                continue;
            }

            $formatted_logs[] = [
                'date' => $file_info['timestamp'] ? date('Y-m-d H:i:s', strtotime($file_info['timestamp'])) : date('Y-m-d H:i:s', $log_file['mtime']),
                'event_type' => $file_info['event_type'],
                'transaction_id' => $raw_data['txn_id'] ?? $raw_data['order_number'] ?? $file_info['txn_id'],
                'ip' => $file_info['ip_address'],
                'processing_mode' => $file_info['processing_mode'],
                'data' => $raw_data,
                'file_path' => $log_file['file']
            ];

            $count++;
        }

        return $formatted_logs;
    }

    private function log_webhook_event($event_type, $data) {
        $ip_address = class_exists('AMP_Utilities') ? \AMP_Utilities::get_client_ip() : 'unknown';
        $processing_mode = ($this->get_plisio_setting('test_mode', '0') === '1') ? 'test' : 'live';
        
        $this->save_webhook_log_to_file($event_type, $data, $ip_address, $processing_mode);
    }

    private function save_webhook_log_to_file($event_type, $raw_data, $ip_address = 'unknown', $processing_mode = 'live') {
        $upload_dir = wp_upload_dir();
        $logs_dir = $upload_dir['basedir'] . '/amp-logs/webhooks';

        $year = date('Y');
        $month = date('m');
        $log_dir = $logs_dir . '/' . $year . '/' . $month;

        if (!wp_mkdir_p($log_dir)) {
            return false;
        }

        $id_part = 'no-id';
        if (!empty($raw_data['txn_id'])) {
            $id_part = substr(sanitize_key($raw_data['txn_id']), 0, 12);
        } elseif (!empty($raw_data['order_number'])) {
            $id_part = substr(sanitize_key($raw_data['order_number']), 0, 12);
        } elseif (!empty($raw_data['order_id'])) {
            $id_part = substr(sanitize_key($raw_data['order_id']), 0, 12);
        }

        $safe_ip = str_replace(['.', ':', '/', '[', ']'], '-', $ip_address);
        $timestamp = date('Ymd-His');
        
        $filename = "{$timestamp}_{$event_type}_{$id_part}_{$processing_mode}_{$safe_ip}.log";
        $filepath = $log_dir . '/' . $filename;

        $result = file_put_contents($filepath, wp_json_encode($raw_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        return $result !== false;
    }

    private function parse_webhook_filename($filepath) {
        $filename = basename($filepath);
        $pattern = '/^(\d{8}-\d{6})_([a-zA-Z0-9_-]+)_([a-zA-Z0-9_-]+)_(live|test)_(.+)\.log$/';
        $matches = [];

        if (preg_match($pattern, $filename, $matches)) {
            $timestamp_str = preg_replace('/(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})(\d{2})/', '$1-$2-$3 $4:$5:$6', $matches[1]);
            return [
                'timestamp' => $timestamp_str,
                'event_type' => $matches[2],
                'txn_id' => $matches[3] === 'no-id' ? 'unknown' : $matches[3],
                'processing_mode' => $matches[4],
                'ip_address' => str_replace('-', '.', $matches[5])
            ];
        }

        return [
            'timestamp' => date('Y-m-d H:i:s', filemtime($filepath)),
            'event_type' => 'unknown_format_fallback',
            'txn_id' => 'unknown',
            'processing_mode' => strpos($filename, '_test_') !== false ? 'test' : 'live',
            'ip_address' => 'unknown'
        ];
    }

    private function is_plisio_official_ip($ip) {
        $official_ips = ['*************'];
        $official_ranges = ['104.', '172.'];
    
        if (in_array($ip, $official_ips)) {
            return true;
        }

        foreach ($official_ranges as $range) {
            if (strpos($ip, $range) === 0) {
                return true;
            }
        }
        
        return false;
    }

    private function save_payment_history_to_file($user_id, $order_data, $transaction_id = '', $status = 'completed') {
        try {
            $upload_dir = wp_upload_dir();
            $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments';

            $year = date('Y');
            $month = date('m');
            $payment_dir = $payments_dir . '/' . $year . '/' . $month;

            if (!file_exists($payments_dir)) {
                if (!wp_mkdir_p($payments_dir)) {
                    error_log("AMP Payment: Failed to create base directory: {$payments_dir}");
                    return false;
                }
            }

            if (!file_exists($payment_dir)) {
                if (!wp_mkdir_p($payment_dir)) {
                    error_log("AMP Payment: Failed to create payment directory: {$payment_dir}");
                    return false;
                }
            }

            $order_id = $order_data['order_id'] ?? 'unknown';
            $timestamp = time();
            $filename = "payment_{$order_id}_{$timestamp}.json";
            $filepath = $payment_dir . '/' . $filename;

            $user = get_userdata($user_id);
            $payment_record = [
                'timestamp' => current_time('mysql'),
                'unix_timestamp' => $timestamp,
                'order_id' => $order_id,
                'user_id' => $user_id,
                'user_login' => $user ? $user->user_login : 'unknown',
                'user_email' => $user ? $user->user_email : 'unknown',
                'total_amount' => $order_data['total'] ?? 0,
                'transaction_id' => $transaction_id,
                'cart_items' => $order_data['cart'] ?? [],
                'status' => $status,
                'payment_method' => strpos($status, 'simulated') !== false ? 'simulated' : 'crypto',
                'purchase_type' => strpos($status, 'simulated') !== false ? 'simulation' : 'api',
                'simulation_type' => strpos($status, 'simulated') !== false ? str_replace('_simulated', '', $status) : null,
                'file_path' => $filepath,
                'saved_at' => current_time('mysql')
            ];

            $json_content = wp_json_encode($payment_record, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $result = file_put_contents($filepath, $json_content);

            if ($result === false) {
                error_log("AMP Payment: Failed to write payment file: {$filepath}");
                return false;
            }

            error_log("AMP Payment: Successfully saved payment history to: {$filepath}");
            return true;

        } catch (Exception $e) {
            error_log("AMP Payment: Exception in save_payment_history_to_file: " . $e->getMessage());
            return false;
        }
    }

    private function verify_transaction_with_plisio_api($txn_id, $expected_order_data) {
        try {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            $api_key = $encryption_manager->get_secret('plisio_api_key');

            if (empty($api_key)) {
                error_log('AMP: Plisio API key not found for transaction verification');
                return false;
            }

            $plisio_api = new \Plisio_API($api_key);
            $api_transaction = $plisio_api->get_transaction_details($txn_id);

            if (is_wp_error($api_transaction) || !isset($api_transaction['status']) || $api_transaction['status'] !== 'success') {
                error_log('AMP: Plisio API verification failed for txn_id: ' . $txn_id . ' - ' . (is_wp_error($api_transaction) ? $api_transaction->get_error_message() : 'Invalid response'));
                return false;
            }

            $transaction_data = $api_transaction['data'];

            if ($transaction_data['status'] !== 'completed') {
                error_log('AMP: Transaction status not completed for txn_id: ' . $txn_id . ' - Status: ' . ($transaction_data['status'] ?? 'unknown'));
                return false;
            }

            $expected_amount = (float) $expected_order_data['total'];
            $received_amount = (float) ($transaction_data['source_amount'] ?? $transaction_data['amount']);
            $tolerance_percentage = 0.01;
            $tolerance_amount = $expected_amount * $tolerance_percentage;

            if (abs($expected_amount - $received_amount) > $tolerance_amount) {
                return false;
            }
            
            $source_currency = $transaction_data['source_currency'] ?? 'USD';
            if (strtoupper($source_currency) !== 'USD') {
                return false;
            }

            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    private function send_purchase_confirmation_email($user_id, $order_data) {
        $user = get_userdata($user_id);
        if (!$user) {
            return;
        }

        $site_name = get_bloginfo('name');
        $site_url = home_url();
        $order_id = $order_data['order_id'] ?? 'N/A';

        $subject = "Advertiser System - [{$site_name}] การชำระเงินสำเร็จ";

        $positions_list = '';
        if (isset($order_data['cart']) && is_array($order_data['cart'])) {
            foreach ($order_data['cart'] as $item) {
                $position_name = $item['position_name'] ?? 'N/A';
                $duration = $item['duration'] ?? 30;
                $positions_list .= "- {$position_name} ({$duration} วัน)\n";
            }
        }

        $message = "สวัสดี {$user->display_name},\n\n";
        $message .= "การชำระเงินของคุณเสร็จสิ้นเรียบร้อยแล้ว\n\n";
        $message .= "รายละเอียดการสั่งซื้อ:\n";
        $message .= "หมายเลขคำสั่งซื้อ: {$order_id}\n";
        $message .= "ตำแหน่งโฆษณาที่ซื้อ:\n{$positions_list}\n";
        $message .= "วันที่ชำระเงิน: " . current_time('Y-m-d H:i:s') . "\n\n";
        $message .= "คุณสามารถจัดการโฆษณาของคุณได้ที่: {$site_url}\n\n";
        $message .= "ขอบคุณที่ใช้บริการ\n";
        $message .= "{$site_name}";

        wp_mail($user->user_email, $subject, $message);
    }

    private function check_webhook_rate_limit() {
        $cache_manager = \AdManagementPro\Core\Cache::instance();
        $cache_key = 'webhook_rate_limit_global';
        $current_count = $cache_manager->get($cache_key, 'payment_settings');

        if ($current_count === false) {
            $cache_manager->set($cache_key, 1, 60, 'payment_settings');
            return true;
        }

        if ($current_count >= 50) {
            return false;
        }

        $cache_manager->set($cache_key, $current_count + 1, 60, 'payment_settings');
        return true;
    }

    public function get_pending_payments_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        global $wpdb;

        $pending_orders_meta = $wpdb->get_results(
            "SELECT user_id, meta_value FROM {$wpdb->usermeta} WHERE meta_key = 'amp_pending_order'"
        );

        $orders = [];
        if (!empty($pending_orders_meta)) {
            foreach ($pending_orders_meta as $meta) {
                $order_data = maybe_unserialize($meta->meta_value);
                if (is_array($order_data)) {
                    $order_id = $order_data['order_id'] ?? '';
                    
                    if (!empty($order_id) && $this->is_order_verified($order_id)) {
                        delete_user_meta($meta->user_id, 'amp_pending_order');
                        continue;
                    }

                    $user_info = get_userdata($meta->user_id);
                    $orders[] = [
                        'user_id' => $meta->user_id,
                        'user_login' => $user_info->user_login,
                        'user_email' => $user_info->user_email,
                        'order_id' => $order_id,
                        'total' => $order_data['total'] ?? 0,
                        'invoice_id' => $order_data['invoice_id'] ?? 'N/A',
                        'created_at' => isset($order_data['created_at']) ? date('Y-m-d H:i:s', $order_data['created_at']) : 'N/A',
                        'cart_count' => is_array($order_data['cart'] ?? []) ? count($order_data['cart']) : 0,
                        'cart_items' => $order_data['cart'] ?? [],
                        'is_verified' => $this->is_order_verified($order_id)
                    ];
                }
            }
        }

        wp_send_json_success(['orders' => $orders]);
    }

    private function is_order_verified($order_id) {
        global $wpdb;
        $existing_payment = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}ad_payments WHERE transaction_id = %s AND status = 'completed'",
            $order_id
        ));
        return $existing_payment !== null;
    }

    public function manual_verify_payment_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $user_id = intval($_POST['user_id'] ?? 0);
        $order_id = sanitize_text_field($_POST['order_id'] ?? '');
        $txn_id = sanitize_text_field($_POST['txn_id'] ?? '');
        $force_manual = isset($_POST['force_manual']) && $_POST['force_manual'] === '1';

        if (!$user_id || !$order_id) {
            wp_send_json_error(['message' => 'ข้อมูลไม่ครบถ้วน']);
            return;
        }

        $this->log_webhook_event('admin_manual_verification_attempt', [
            'admin_user_id' => get_current_user_id(),
            'admin_user_login' => wp_get_current_user()->user_login,
            'target_user_id' => $user_id,
            'order_id' => $order_id,
            'transaction_id' => $txn_id,
            'force_manual' => $force_manual
        ]);

        $pending_order = get_user_meta($user_id, 'amp_pending_order', true);
        if (!$pending_order || !is_array($pending_order)) {
            wp_send_json_error(['message' => 'ไม่พบคำสั่งซื้อที่รอดำเนินการ สำหรับผู้ใช้ ID: ' . $user_id]);
            return;
        }

        if ($pending_order['order_id'] !== $order_id) {
            wp_send_json_error(['message' => 'หมายเลขคำสั่งซื้อไม่ตรงกัน (Expected: ' . $order_id . ', Found: ' . ($pending_order['order_id'] ?? 'N/A') . ')']);
            return;
        }

        global $wpdb;
        $existing_payment = $wpdb->get_row($wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}ad_payments WHERE transaction_id = %s AND status = 'completed'",
            $txn_id
        ));

        if ($existing_payment && !empty($txn_id)) {
            wp_send_json_error(['message' => 'Transaction นี้ถูกประมวลผลแล้ว (Payment ID: ' . $existing_payment->id . ')']);
            return;
        }

        $is_test_mode = ($this->get_plisio_setting('test_mode', '0') === '1');

        if (!$is_test_mode && !empty($txn_id) && !$force_manual) {
            $api_verification = $this->verify_transaction_with_plisio_api($txn_id, $pending_order);
            if (!$api_verification) {
                $this->log_webhook_event('admin_verification_api_failed', [
                    'admin_user_id' => get_current_user_id(),
                    'user_id' => $user_id,
                    'order_id' => $order_id,
                    'transaction_id' => $txn_id,
                    'note' => 'API verification failed - suggesting manual override option'
                ]);
                wp_send_json_error([
                    'message' => 'การตรวจสอบ API ไม่ผ่าน คุณสามารถกดยืนยันด้วยตนเองได้หากแน่ใจว่า Transaction นี้ถูกต้อง',
                    'show_manual_option' => true,
                    'api_failed' => true
                ]);
                return;
            }
        }

        try {
            $success = $this->process_successful_payment($user_id, $pending_order, true);
            if ($success) {
                $verification_method = $force_manual ? 'manual_override' : ($is_test_mode ? 'test_mode' : 'api_verified');
                
                $this->save_payment_history_to_file($user_id, $pending_order, $txn_id, 'manual_verification_' . $verification_method);
                
                $this->log_webhook_event('admin_manual_verification_success', [
                    'admin_user_id' => get_current_user_id(),
                    'admin_user_login' => wp_get_current_user()->user_login,
                    'user_id' => $user_id,
                    'order_id' => $order_id,
                    'transaction_id' => $txn_id,
                    'cart_items' => $pending_order['cart'] ?? [],
                    'total_amount' => $pending_order['total'] ?? 0,
                    'verification_method' => $verification_method
                ]);
                
                do_action('amp_payment_success', $user_id, $pending_order, $txn_id);
                
                $success_message = 'ยืนยันการชำระเงินสำเร็จ อีเมลยืนยันถูกส่งแล้ว';
                if ($force_manual) {
                    $success_message .= ' (ยืนยันด้วยตนเองโดย Admin)';
                }
                
                wp_send_json_success([
                    'message' => $success_message,
                    'details' => [
                        'user_id' => $user_id,
                        'order_id' => $order_id,
                        'transaction_id' => $txn_id,
                        'verified_by_admin' => get_current_user_id(),
                        'verification_method' => $verification_method
                    ]
                ]);
            } else {
                $error_logs = $this->get_recent_payment_error_logs($user_id, $pending_order['order_id']);
                $error_message = 'เกิดข้อผิดพลาดในการประมวลผล';
                if (!empty($error_logs)) {
                    $error_message .= ': ' . $error_logs;
                }
                
                $this->log_webhook_event('admin_manual_verification_failed', [
                    'admin_user_id' => get_current_user_id(),
                    'user_id' => $user_id,
                    'order_id' => $order_id,
                    'transaction_id' => $txn_id,
                    'error_message' => $error_message,
                    'error_logs' => $error_logs
                ]);
                
                wp_send_json_error(['message' => $error_message]);
            }
        } catch (Exception $e) {
            $this->log_webhook_event('admin_manual_verification_exception', [
                'admin_user_id' => get_current_user_id(),
                'user_id' => $user_id,
                'order_id' => $order_id,
                'transaction_id' => $txn_id,
                'exception_message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function get_payment_history_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $page = intval($_POST['page'] ?? 1);
        $per_page = 20;

        $payment_data = $this->read_payment_history_from_files($page, $per_page);

        wp_send_json_success($payment_data);
    }

    private function read_payment_history_from_files($page = 1, $per_page = 20) {
        $upload_dir = wp_upload_dir();
        $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments';

        if (!is_dir($payments_dir)) {
            return [
                'payments' => [],
                'total_count' => 0,
                'current_page' => $page,
                'total_pages' => 0
            ];
        }

        $payment_files = [];
        $years = glob($payments_dir . '/*', GLOB_ONLYDIR);

        foreach ($years as $year_dir) {
            $months = glob($year_dir . '/*', GLOB_ONLYDIR);
            foreach ($months as $month_dir) {
                $files = glob($month_dir . '/payment_*.json');
                foreach ($files as $file) {
                    $payment_files[] = [
                        'file' => $file,
                        'mtime' => filemtime($file)
                    ];
                }
            }
        }

        usort($payment_files, function($a, $b) {
            return $b['mtime'] - $a['mtime'];
        });

        $total_count = count($payment_files);
        $total_pages = ceil($total_count / $per_page);
        $offset = ($page - 1) * $per_page;

        $page_files = array_slice($payment_files, $offset, $per_page);
        $payments = [];

        foreach ($page_files as $payment_file) {
            $content = file_get_contents($payment_file['file']);
            if ($content === false) continue;

            $payment_data = json_decode($content, true);
            if (!$payment_data) continue;

            $payments[] = (object) [
                'payment_date' => $payment_data['timestamp'],
                'user_id' => $payment_data['user_id'],
                'user_login' => $payment_data['user_login'],
                'user_email' => $payment_data['user_email'],
                'order_id' => $payment_data['order_id'],
                'ad_position' => $this->format_cart_items_for_display($payment_data['cart_items']),
                'amount' => $payment_data['total_amount'],
                'duration' => $this->calculate_total_duration($payment_data['cart_items']),
                'transaction_id' => $payment_data['transaction_id'],
                'status' => $payment_data['status']
            ];
        }

        return [
            'payments' => $payments,
            'total_count' => $total_count,
            'current_page' => $page,
            'total_pages' => $total_pages
        ];
    }

    private function format_cart_items_for_display($cart_items) {
        if (!is_array($cart_items) || empty($cart_items)) {
            return 'N/A';
        }

        $positions = array_map(function($item) {
            return $item['position_name'] ?? 'Unknown';
        }, $cart_items);

        return implode(', ', $positions);
    }

    private function calculate_total_duration($cart_items) {
        if (!is_array($cart_items) || empty($cart_items)) {
            return 0;
        }

        $total_duration = 0;
        foreach ($cart_items as $item) {
            $total_duration += intval($item['duration'] ?? 30);
        }

        return $total_duration;
    }

    public function clear_webhook_logs_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $upload_dir = wp_upload_dir();
        $logs_dir = $upload_dir['basedir'] . '/amp-logs/webhooks';

        $deleted_count = $this->delete_directory_contents($logs_dir);

        wp_send_json_success([
            'message' => "ลบ Webhook Logs สำเร็จ ({$deleted_count} ไฟล์)",
            'deleted_count' => $deleted_count
        ]);
    }

    public function clear_payment_history_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $upload_dir = wp_upload_dir();
        $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments';

        $deleted_count = $this->delete_directory_contents($payments_dir);

        wp_send_json_success([
            'message' => "ลบ Payment History สำเร็จ ({$deleted_count} ไฟล์)",
            'deleted_count' => $deleted_count
        ]);
    }

    public function get_logs_stats_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        $upload_dir = wp_upload_dir();
        $logs_base_dir = $upload_dir['basedir'] . '/amp-logs';

        $webhook_stats = $this->get_directory_stats($logs_base_dir . '/webhooks');
        $payment_stats = $this->get_directory_stats($logs_base_dir . '/payments');

        wp_send_json_success([
            'webhook_logs' => $webhook_stats,
            'payment_history' => $payment_stats,
            'total_size' => $webhook_stats['size'] + $payment_stats['size'],
            'total_files' => $webhook_stats['files'] + $payment_stats['files']
        ]);
    }

    private function delete_directory_contents($dir) {
        if (!is_dir($dir)) {
            return 0;
        }

        $deleted_count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                if (unlink($file->getPathname())) {
                    $deleted_count++;
                }
            } elseif ($file->isDir()) {
                rmdir($file->getPathname());
            }
        }

        return $deleted_count;
    }

    private function get_directory_stats($dir) {
        if (!is_dir($dir)) {
            return ['files' => 0, 'size' => 0, 'size_formatted' => '0 B'];
        }

        $files = 0;
        $size = 0;

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $files++;
                $size += $file->getSize();
            }
        }

        return [
            'files' => $files,
            'size' => $size,
            'size_formatted' => $this->format_bytes($size)
        ];
    }

    private function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    public function cleanup_old_logs() {
        $upload_dir = wp_upload_dir();
        $logs_base_dir = $upload_dir['basedir'] . '/amp-logs';

        $one_year_ago = time() - (365 * 24 * 60 * 60);
        $deleted_count = 0;

        foreach (['webhooks', 'payments'] as $log_type) {
            $log_dir = $logs_base_dir . '/' . $log_type;
            if (!is_dir($log_dir)) continue;

            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($log_dir, RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getMTime() < $one_year_ago) {
                    if (unlink($file->getPathname())) {
                        $deleted_count++;
                    }
                }
            }
        }

        return $deleted_count;
    }

    private function setup_cleanup_cron() {
        if (!wp_next_scheduled('amp_cleanup_old_logs')) {
            wp_schedule_event(time(), 'daily', 'amp_cleanup_old_logs');
        }
        add_action('amp_cleanup_old_logs', [$this, 'cleanup_old_logs']);
    }

    public function verify_transaction_api_only_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            error_log("AMP: Payment verify transaction security check failed - Expected: amp_admin_nonce, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $txn_id = sanitize_text_field($_POST['txn_id'] ?? '');
        $user_id = intval($_POST['user_id'] ?? 0);
        $order_id = sanitize_text_field($_POST['order_id'] ?? '');

        if (empty($txn_id)) {
            wp_send_json_error(['message' => 'ไม่มี Transaction ID']);
            return;
        }

        if (!$user_id || !$order_id) {
            wp_send_json_error(['message' => 'ข้อมูลไม่ครบถ้วน']);
            return;
        }

        $pending_order = get_user_meta($user_id, 'amp_pending_order', true);
        if (!$pending_order || !is_array($pending_order)) {
            wp_send_json_error(['message' => 'ไม่พบคำสั่งซื้อที่รอดำเนินการ']);
            return;
        }

        if ($pending_order['order_id'] !== $order_id) {
            wp_send_json_error(['message' => 'หมายเลขคำสั่งซื้อไม่ตรงกัน']);
            return;
        }

        try {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            $api_key = $encryption_manager->get_secret('plisio_api_key');

            if (empty($api_key)) {
                wp_send_json_error(['message' => '❌ ไม่พบ Secret Key ของ Plisio สำหรับการตรวจสอบ API']);
                return;
            }

            $plisio_api = new \Plisio_API($api_key);
            $api_transaction = $plisio_api->get_transaction_details($txn_id);

            if (is_wp_error($api_transaction)) {
                wp_send_json_error(['message' => '❌ API Error: ' . $api_transaction->get_error_message()]);
                return;
            }

            if (!isset($api_transaction['status']) || $api_transaction['status'] !== 'success') {
                wp_send_json_error(['message' => 'API Response ไม่ถูกต้อง']);
                return;
            }

            $transaction_data = $api_transaction['data'];
            $expected_amount = (float) $pending_order['total'];
            $received_amount = (float) ($transaction_data['amount'] ?? 0);
            $tolerance = 0.01;
            $verification_details = [];
            $verification_details[] = "🔍 ผลการตรวจสอบ API:";
            $verification_details[] = "📋 Transaction ID: {$txn_id}";
            $verification_details[] = "💰 จำนวนเงินที่คาดหวัง: \${$expected_amount}";
            $verification_details[] = "💰 จำนวนเงินที่ได้รับ: \${$received_amount}";
            $verification_details[] = "💱 สกุลเงิน: " . strtoupper($transaction_data['currency'] ?? 'N/A');
            $verification_details[] = "📊 สถานะ: " . ($transaction_data['status'] ?? 'N/A');

            $is_valid = true;
            $errors = [];

            if (($transaction_data['status'] ?? 'N/A') !== 'completed') {
                $is_valid = false;
                $errors[] = "❌ สถานะไม่ใช่ 'completed'";
            }

            if (abs($expected_amount - $received_amount) > $tolerance) {
                $is_valid = false;
                $errors[] = "❌ จำนวนเงินไม่ตรงกัน (ต่างกัน: $" . abs($expected_amount - $received_amount) . ")";
            }

            if (strtoupper($transaction_data['currency'] ?? '') !== 'USDT') {
                $is_valid = false;
                $errors[] = "❌ สกุลเงินไม่ถูกต้อง (ควรเป็น USDT)";
            }

            if ($is_valid) {
                $verification_details[] = "✅ การตรวจสอบผ่านทุกเงื่อนไข";
                wp_send_json_success(['message' => implode("\n", $verification_details)]);
            } else {
                $verification_details = array_merge($verification_details, $errors);
                wp_send_json_error(['message' => implode("\n", $verification_details)]);
            }

        } catch (\Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    public function simulate_payment_webhook_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }
        $order_id = sanitize_text_field($_POST['order_id'] ?? '');
        $invoice_id = sanitize_text_field($_POST['invoice_id'] ?? '');
        $user_id = intval($_POST['user_id'] ?? 0);
        $amount = floatval($_POST['amount'] ?? 0);
        $simulate_status = sanitize_text_field($_POST['status'] ?? 'completed');
        $webhook_data = $this->create_official_plisio_webhook_data($invoice_id, $order_id, $amount, $simulate_status);
        $encryption_manager = \AMP_Encryption_Manager::instance();
        $secret_key = $encryption_manager->get_secret('plisio_api_key');
        if (empty($secret_key)) {
            wp_send_json_error(['message' => 'ไม่พบ Secret Key ของ Plisio สำหรับสร้าง Signature']);
            return;
        }
        $data_to_sign = $webhook_data;
        ksort($data_to_sign);
        $string_to_sign = serialize($data_to_sign);
        $verify_hash = hash_hmac('sha1', $string_to_sign, $secret_key);
        $webhook_data['verify_hash'] = $verify_hash;
        $target_url = rest_url('ad-management-pro/v1/plisio-webhook');
        $this->log_webhook_event('simulated_webhook_sent', $webhook_data);
        $post_body = http_build_query($webhook_data, '', '&');
        $response = wp_remote_post($target_url, [
            'method' => 'POST',
            'timeout' => 45,
            'redirection' => 5,
            'blocking' => true,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'User-Agent' => 'AMP-Webhook-Simulator/1.0'
            ],
            'body' => $post_body,
        ]);
        
        if (is_wp_error($response)) {
            wp_send_json_error([
                'message' => 'เกิดข้อผิดพลาดในการส่ง Webhook จำลอง: ' . $response->get_error_message(),
                'sent_data' => $webhook_data
            ]);
            return;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        $this->log_webhook_event('simulated_webhook_response', [
            'url' => $target_url,
            'response_code' => $response_code,
            'response_body' => $response_body
        ]);

        $history_saved = false;
        $status_updated = false;
        $final_message = '';
        $simulate_status = $webhook_data['status'] ?? 'unknown';

        if ($response_code == 200) {
            $order_id = $webhook_data['order_number'] ?? '';
            $user_id = 0;
            if ($order_id && preg_match('/AMP-(\\d+)-/', $order_id, $matches)) {
                $user_id = intval($matches[1]);
            }

            $upload_dir = wp_upload_dir();
            $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments/' . date('Y') . '/' . date('m');
            if ($order_id && is_dir($payments_dir)) {
                $files = glob($payments_dir . '/payment_' . $order_id . '_*.json');
                if (!empty($files)) {
                    $history_saved = true;
                }
            }
            if ($user_id && !get_user_meta($user_id, 'amp_pending_order', true)) {
                $status_updated = true;
            }
            
            $final_message = "จำลอง Webhook สำเร็จ: ระบบตอบรับและประมวลผลอย่างถูกต้อง (สถานะ: {$simulate_status})";

        } else {
            switch ($response_code) {
                case 403:
                    $final_message = 'จำลอง Webhook ล้มเหลว: Signature ไม่ถูกต้อง (ระบบป้องกันทำงานถูกต้อง)';
                    break;
                case 400:
                    $final_message = 'จำลอง Webhook ล้มเหลว: ข้อมูลที่ส่งไปไม่สมบูรณ์ (Bad Request)';
                    break;
                case 404:
                    $final_message = 'จำลอง Webhook ล้มเหลว: ระบบไม่พบข้อมูล Order ที่เกี่ยวข้องในระบบ';
                    break;
                default:
                    $final_message = "จำลอง Webhook สำเร็จ แต่ระบบตอบกลับมาด้วยสถานะผิดปกติ: {$response_code}";
                    break;
            }
        }
        
        wp_send_json_success([
            'message' => $final_message,
            'response_code' => $response_code,
            'response_body' => $response_body,
            'sent_data' => $webhook_data,
            'history_saved' => $history_saved,
            'status_updated' => $status_updated
        ]);
    }

    private function create_official_plisio_webhook_data($invoice_id, $order_id, $amount, $status) {
        $selected_cid = get_option('plisio_selected_currency', 'USDT_TRX');
        $currency = $this->get_currency_from_cid($selected_cid) ?? 'USDT_TRX';
        return [
            'txn_id' => $invoice_id,
            'status' => $status,
            'order_number' => $order_id,
            'order_name' => 'Ad Management Pro - Cart Checkout',
            'amount' => number_format((float)$amount, 8, '.', ''),
            'currency' => $currency,
            'source_amount' => number_format((float)$amount, 2, '.', ''),
            'source_currency' => 'USD',
            'psys_cid' => $selected_cid,
            'comment' => "Invoice details: https://plisio.net/account/transactions/{$invoice_id}",
            'ipn_type' => 'invoice',
            'merchant' => 'Ads Online',
            'merchant_id' => '682f27a722033a56ff04a389',
            'source_rate' => '0.999999',
        ];
    }

    private function get_currency_from_cid($cid_to_find) {
        $encryption_manager = \AMP_Encryption_Manager::instance();
        $api_key = $encryption_manager->get_secret('plisio_api_key');

        if (empty($api_key)) {
            return null;
        }

        $plisio_api = new Plisio_API($api_key);
        $response = $plisio_api->get_supported_currencies();
        if (is_wp_error($response) || !isset($response['status']) || $response['status'] !== 'success') {
            return null;
        }
        foreach ($response['data'] as $currency_data) {
            if (isset($currency_data['cid']) && $currency_data['cid'] === $cid_to_find) {
                return $currency_data['currency'] ?? null;
            }
        }
        return null;
    }

    public function cleanup_stuck_pending_orders() {
        global $wpdb;
        $stuck_orders = $wpdb->get_results(
            "SELECT user_id, meta_value FROM {$wpdb->usermeta}
             WHERE meta_key = 'amp_pending_order'"
        );
        $cleaned_count = 0;
        foreach ($stuck_orders as $order) {
            $order_data = maybe_unserialize($order->meta_value);
            $created_at = $order_data['created_at'] ?? 0;
            if ($created_at && (time() - $created_at) > (24 * 60 * 60)) {
                delete_user_meta($order->user_id, 'amp_pending_order');
                $cleaned_count++;
                $this->log_webhook_event('cleanup_stuck_order', [
                    'user_id' => $order->user_id,
                    'order_id' => $order_data['order_id'] ?? 'N/A',
                    'age_hours' => round((time() - $created_at) / 3600, 2)
                ]);
            }
        }
        return $cleaned_count;
    }

    public function get_plisio_currencies_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
        }
    
        check_ajax_referer('save_plisio_settings', 'security');
    
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key) || $api_key === 'USE_STORED_ENCRYPTED') {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            $api_key = $encryption_manager->get_secret('plisio_api_key');
        }

        if (empty($api_key)) {
            wp_send_json_error(['message' => 'ไม่พบ API Key ที่เข้ารหัสไว้ กรุณาตั้งค่า API Key ใหม่']);
        }
    
        require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-plisio-api.php';
        $plisio_api = new Plisio_API($api_key);
        $response = $plisio_api->get_supported_currencies();
    
        if (is_wp_error($response)) {
            wp_send_json_error(['message' => 'Failed to fetch currencies: ' . $response->get_error_message()]);
        }
    
        if (is_array($response) && isset($response['status']) && $response['status'] === 'success') {
            $available_currencies = [];
            foreach ($response['data'] as $currency) {
                if (isset($currency['hidden']) && $currency['hidden'] == 0 && isset($currency['cid']) && isset($currency['name'])) {
                    $available_currencies[] = [
                        'cid' => $currency['cid'],
                        'name' => $currency['name'] . ' (' . $currency['currency'] . ')'
                    ];
                }
            }
            wp_send_json_success(['currencies' => $available_currencies]);
        } else {
            wp_send_json_error(['message' => 'Invalid API response format.']);
        }
    }

    public function retry_manual_verification_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $user_id = intval($_POST['user_id'] ?? 0);
        $order_id = sanitize_text_field($_POST['order_id'] ?? '');
        $txn_id = sanitize_text_field($_POST['txn_id'] ?? '');

        if (!$user_id || !$order_id) {
            wp_send_json_error(['message' => 'ข้อมูลไม่ครบถ้วน (User ID หรือ Order ID)']);
            return;
        }

        try {
            $order_data = $this->get_history_data_by_order_id($order_id);

            if (!$order_data) {
                wp_send_json_error(['message' => 'ไม่พบข้อมูลประวัติสำหรับ Order ID นี้']);
                return;
            }

            $success = $this->process_successful_payment($user_id, $order_data, false);

            if ($success) {
                $this->save_payment_history_to_file($user_id, $order_data, $txn_id, 'manual_verification');
                
                $this->log_webhook_event('manual_reverification', [
                    'order_id' => $order_id,
                    'user_id' => $user_id,
                    'admin_user' => get_current_user_id()
                ]);

                wp_send_json_success(['message' => 'ยืนยันการชำระเงินซ้ำเรียบร้อยแล้ว']);
            } else {
                $error_logs = $this->get_recent_payment_error_logs($user_id, $order_id);
                $error_message = 'เกิดข้อผิดพลาดในการประมวลผลการชำระเงินซ้ำ';
                if (!empty($error_logs)) {
                    $error_message .= ': ' . $error_logs;
                }
                wp_send_json_error(['message' => $error_message]);
            }

        } catch (Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }

    private function get_recent_payment_error_logs($user_id, $order_id) {
        $upload_dir = wp_upload_dir();
        $logs_dir = $upload_dir['basedir'] . '/amp-logs/webhooks';
        
        if (!is_dir($logs_dir)) {
            return '';
        }

        $log_files = [];
        $years = glob($logs_dir . '/*', GLOB_ONLYDIR);
        
        foreach ($years as $year_dir) {
            $months = glob($year_dir . '/*', GLOB_ONLYDIR);
            foreach ($months as $month_dir) {
                $files = glob($month_dir . '/*payment_processing_exception*.log');
                foreach ($files as $file) {
                    $log_files[] = [
                        'file' => $file,
                        'mtime' => filemtime($file)
                    ];
                }
            }
        }

        usort($log_files, function($a, $b) {
            return $b['mtime'] - $a['mtime'];
        });

        foreach (array_slice($log_files, 0, 5) as $log_file) {
            $content = file_get_contents($log_file['file']);
            if ($content === false) continue;

            $log_data = json_decode($content, true);
            if (!$log_data) continue;

            if (isset($log_data['order_id']) && $log_data['order_id'] === $order_id) {
                return $log_data['exception_message'] ?? 'Unknown error';
            }
        }

        return '';
    }

    private function get_history_data_by_order_id($order_id) {
        $upload_dir = wp_upload_dir();
        $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments';

        if (!is_dir($payments_dir)) {
            return null;
        }

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($payments_dir, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match("/payment_{$order_id}_\d+\.json$/", $file->getFilename())) {
                $content = file_get_contents($file->getPathname());
                if ($content) {
                    return json_decode($content, true);
                }
            }
        }

        return null;
    }

    public function get_position_ownership_details_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $user_id = intval($_POST['user_id'] ?? 0);
        $order_id = sanitize_text_field($_POST['order_id'] ?? '');

        if (!$user_id || !$order_id) {
            wp_send_json_error(['message' => 'ข้อมูลไม่ครบถ้วน']);
            return;
        }

        $pending_order = get_user_meta($user_id, 'amp_pending_order', true);
        if (!$pending_order || !is_array($pending_order) || !isset($pending_order['cart'])) {
            wp_send_json_error(['message' => 'ไม่พบข้อมูลคำสั่งซื้อ']);
            return;
        }

        $this->load_position_manager();

        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
        $position_details = [];

        foreach ($pending_order['cart'] as $item) {
            $position_name = $item['position_name'] ?? '';
            if (empty($position_name)) continue;

            $ownership_state = $position_manager->get_position_ownership_state($position_name);
            
            $position_details[] = [
                'position_name' => $position_name,
                'duration' => $item['duration'] ?? 30,
                'is_owned' => $ownership_state['is_owned'],
                'is_expired' => $ownership_state['is_expired'],
                'current_owner_id' => isset($ownership_state['user_id']) ? $ownership_state['user_id'] : null,
                'expiration_date' => $ownership_state['expiration_date'] ?? null,
                'can_process' => !$ownership_state['is_owned'] || $ownership_state['is_expired'] || (isset($ownership_state['user_id']) && $ownership_state['user_id'] == $user_id),
                'status_message' => $this->get_position_status_message($ownership_state, $user_id)
            ];
        }

        wp_send_json_success([
            'positions' => $position_details,
            'order_info' => [
                'order_id' => $pending_order['order_id'],
                'total_amount' => $pending_order['total'],
                'created_at' => $pending_order['created_at'] ?? 0
            ],
            'admin_override_available' => true,
            'verification_notes' => 'Admin สามารถยืนยันการชำระเงินได้แม้ว่าตำแหน่งจะมีเจ้าของอยู่ ระบบจะ override การตรวจสอบ ownership'
        ]);
    }

    private function get_position_status_message($ownership_state, $target_user_id) {
        if (!$ownership_state['is_owned']) {
            return '✅ ว่าง - สามารถประมวลผลได้';
        } elseif ($ownership_state['is_expired']) {
            return '⏰ หมดอายุแล้ว - สามารถประมวลผลได้';
        } elseif (isset($ownership_state['user_id']) && $ownership_state['user_id'] == $target_user_id) {
            return '👤 เป็นเจ้าของเดิม - สามารถต่ออายุได้';
        } else {
            $expiry = $ownership_state['expiration_date'] ?? 'N/A';
            return '⚠️ มีเจ้าของ (User ID: ' . (isset($ownership_state['user_id']) ? $ownership_state['user_id'] : 'Unknown') . ') หมดอายุ: ' . $expiry . ' - ต้องใช้ Admin Override';
        }
    }

    public function get_payment_details_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $order_id = sanitize_text_field($_POST['order_id'] ?? '');
        if (empty($order_id)) {
            wp_send_json_error(['message' => 'Order ID ไม่ถูกต้อง']);
            return;
        }

        $payment_data = $this->get_history_data_by_order_id($order_id);
        if (!$payment_data) {
            wp_send_json_error(['message' => 'ไม่พบข้อมูลการชำระเงินสำหรับ Order ID นี้']);
            return;
        }

        wp_send_json_success($payment_data);
    }

    public function get_signature_log_details_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Permission denied']);
            return;
        }
        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $transaction_id = isset($_POST['transaction_id']) ? sanitize_text_field($_POST['transaction_id']) : '';
        if (empty($transaction_id)) {
            wp_send_json_error(['message' => 'Transaction ID is required.']);
            return;
        }

        $upload_dir = wp_upload_dir();
        $log_file = $upload_dir['basedir'] . '/amp-logs/security/signature_verification.log';

        if (!file_exists($log_file) || !is_readable($log_file)) {
            wp_send_json_error(['message' => 'Signature log file not found or not readable.']);
            return;
        }

        $log_content = file_get_contents($log_file);
        $log_entries_str = rtrim(trim($log_content), ',');
        $log_entries = json_decode('[' . $log_entries_str . ']', true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            wp_send_json_error(['message' => 'Error parsing log file. JSON error: ' . json_last_error_msg()]);
            return;
        }

        $found_log = null;
        if (is_array($log_entries)) {
            foreach (array_reverse($log_entries) as $entry) {
                if (isset($entry['transaction_id']) && $entry['transaction_id'] === $transaction_id) {
                    $found_log = $entry;
                    break;
                }
            }
        }

        if ($found_log) {
            wp_send_json_success(['log' => $found_log]);
        } else {
            wp_send_json_error(['message' => 'No signature verification log found for this transaction ID.']);
        }
    }

    public function delete_payment_record_ajax() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์เข้าถึง']);
            return;
        }

        if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_admin_nonce')) {
            wp_send_json_error(['message' => 'Security check failed']);
            return;
        }

        $order_id = sanitize_text_field($_POST['order_id'] ?? '');
        $user_id = intval($_POST['user_id'] ?? 0);
        
        if (empty($order_id)) {
            wp_send_json_error(['message' => 'Order ID ไม่ถูกต้อง']);
            return;
        }

        try {
            $deleted_files = 0;
            $deleted_db_records = 0;

            $upload_dir = wp_upload_dir();
            $payments_dir = $upload_dir['basedir'] . '/amp-logs/payments';

            if (is_dir($payments_dir)) {
                $iterator = new RecursiveIteratorIterator(
                    new RecursiveDirectoryIterator($payments_dir, RecursiveDirectoryIterator::SKIP_DOTS)
                );

                foreach ($iterator as $file) {
                    if ($file->isFile() && preg_match("/payment_{$order_id}_\d+\.json$/", $file->getFilename())) {
                        if (unlink($file->getPathname())) {
                            $deleted_files++;
                        }
                    }
                }
            }

            global $wpdb;
            $db_result = $wpdb->delete(
                $wpdb->prefix . 'ad_payments',
                ['order_id' => $order_id],
                ['%s']
            );

            if ($db_result !== false) {
                $deleted_db_records = $db_result;
            }

            $this->log_webhook_event('admin_delete_payment_record', [
                'admin_user_id' => get_current_user_id(),
                'admin_user_login' => wp_get_current_user()->user_login,
                'deleted_order_id' => $order_id,
                'deleted_files' => $deleted_files,
                'deleted_db_records' => $deleted_db_records
            ]);

            if ($deleted_files > 0 || $deleted_db_records > 0) {
                wp_send_json_success([
                    'message' => "ลบประวัติการชำระเงินสำเร็จ (ไฟล์: {$deleted_files}, ฐานข้อมูล: {$deleted_db_records} รายการ)",
                    'deleted_files' => $deleted_files,
                    'deleted_db_records' => $deleted_db_records
                ]);
            } else {
                wp_send_json_error(['message' => 'ไม่พบข้อมูลที่ต้องลบ']);
            }

        } catch (Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
        }
    }
}

AMP_Payment_Handler::instance();
