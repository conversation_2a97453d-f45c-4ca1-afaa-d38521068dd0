<?php
if (!defined('WPINC')) {
    die;
}

function display_cloudflare_settings_content() {
    $cloudflare_nonce = wp_create_nonce('save_cloudflare_settings');
    $cloudflare_email = get_option('amp_cloudflare_email', '');
    $cloudflare_domain = get_option('amp_cloudflare_domain', '');
    $cloudflare_api_token = get_option('amp_cloudflare_api_token', '');
    ?>
    <form method="post" action="" enctype="multipart/form-data">
        <input type="hidden" name="cloudflare_settings_nonce" value="<?php echo esc_attr($cloudflare_nonce); ?>">
        <table class="form-table">
            <tr>
                <th scope="row"><label for="cloudflare_email">CloudFlare Email</label></th>
                <td>
                    <input type="email" name="cloudflare_email" id="cloudflare_email" value="<?php echo esc_attr($cloudflare_email); ?>" class="regular-text" placeholder="<EMAIL>">
                    <p class="description">อีเมลที่ใช้ลงทะเบียน CloudFlare</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="cloudflare_domain">Domain</label></th>
                <td>
                    <input type="text" name="cloudflare_domain" id="cloudflare_domain" value="<?php echo esc_attr($cloudflare_domain); ?>" class="regular-text" placeholder="example.com">
                    <p class="description">โดเมนที่ต้องการเคลียร์แคช (ไม่ต้องใส่ www หรือ https://)</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="cloudflare_api_token">API Token</label></th>
                <td>
                    <input type="password" name="cloudflare_api_token" id="cloudflare_api_token" value="<?php echo esc_attr($cloudflare_api_token); ?>" class="regular-text" placeholder="Your CloudFlare API Token">
                    <button type="button" id="toggle-api-token" class="button">แสดง/ซ่อน</button>
                    <p class="description">API Token จาก CloudFlare Dashboard (ต้องมีสิทธิ์ Zone:Cache Purge)</p>
                </td>
            </tr>
        </table>
        <div class="cloudflare-test-section" style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-left: 4px solid #0073aa;">
            <h4>ทดสอบการเชื่อมต่อ</h4>
            <p>คลิกปุ่มด้านล่างเพื่อทดสอบการเชื่อมต่อกับ CloudFlare และเคลียร์แคช</p>
            <button type="button" id="test-cloudflare-connection" class="button button-secondary">ทดสอบการเชื่อมต่อ</button>
            <button type="button" id="clear-cloudflare-cache" class="button button-secondary">เคลียร์แคชทั้งหมด</button>
            <div id="cloudflare-test-result" style="margin-top: 10px;"></div>
        </div>
        <div class="cloudflare-info-section" style="margin-top: 20px; padding: 15px; background: #fff3cd; border-left: 4px solid #ffc107;">
            <h4>ข้อมูลการใช้งาน</h4>
            <p><strong>ระบบจะเคลียร์แคช CloudFlare อัตโนมัติในกรณีต่อไปนี้:</strong></p>
            <ul style="margin-left: 20px;">
                <li>เมื่อมีการซื้อสินค้า/ชำระเงินสำเร็จ</li>
                <li>เมื่อมีการบันทึกหรืออัปเดตป้ายโฆษณา</li>
                <li>เมื่อมีการเปลี่ยนสถานะป้ายโฆษณา (เปิด/ปิด)</li>
                <li>เมื่อมีการอัปโหลดไฟล์โฆษณาใหม่</li>
            </ul>
            <p><strong>หมายเหตุ:</strong> หากไม่ได้กรอกข้อมูล CloudFlare ระบบจะใช้การเคลียร์แคช WordPress ปกติแทน</p>
        </div>
        <p class="submit">
            <button type="button" id="save-cloudflare-settings" class="button button-primary">บันทึกการตั้งค่า</button>
            <span id="cloudflare-settings-status"></span>
        </p>
    </form>
    <script>
    jQuery(document).ready(function($) {
        $('#toggle-api-token').on('click', function() {
            const input = $('#cloudflare_api_token');
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                $(this).text('ซ่อน');
            } else {
                input.attr('type', 'password');
                $(this).text('แสดง');
            }
        });
        
        $('#save-cloudflare-settings').on('click', function() {
            const button = $(this);
            const statusEl = $('#cloudflare-settings-status');
            button.prop('disabled', true).html('กำลังบันทึก...');
            statusEl.removeClass('success error').hide();
            
            const formData = {
                action: 'save_cloudflare_settings',
                nonce: '<?php echo $cloudflare_nonce; ?>',
                cloudflare_email: $('#cloudflare_email').val(),
                cloudflare_domain: $('#cloudflare_domain').val(),
                cloudflare_api_token: $('#cloudflare_api_token').val()
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('บันทึกการตั้งค่า');
                    if (response.success) {
                        var successMessage = (response.data && response.data.message) ? response.data.message : 'บันทึกสำเร็จ';
                        statusEl.addClass('success').html(successMessage).show();
                        setTimeout(function() {
                            statusEl.fadeOut();
                        }, 3000);
                    } else {
                        var errorMessage = (response.data && response.data.message) ? response.data.message : 'เกิดข้อผิดพลาด';
                        statusEl.addClass('error').html(errorMessage).show();
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('บันทึกการตั้งค่า');
                    statusEl.addClass('error').html('เกิดข้อผิดพลาดในการบันทึก').show();
                }
            });
        });
        
        $('#test-cloudflare-connection').on('click', function() {
            const button = $(this);
            const resultEl = $('#cloudflare-test-result');
            button.prop('disabled', true).html('กำลังทดสอบ...');
            resultEl.html('');
            
            const formData = {
                action: 'test_cloudflare_connection',
                nonce: '<?php echo $cloudflare_nonce; ?>',
                cloudflare_email: $('#cloudflare_email').val(),
                cloudflare_domain: $('#cloudflare_domain').val(),
                cloudflare_api_token: $('#cloudflare_api_token').val()
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('ทดสอบการเชื่อมต่อ');
                    if (response.success) {
                        resultEl.html('<div style="color: green; font-weight: bold;">✓ เชื่อมต่อ CloudFlare สำเร็จ</div>');
                    } else {
                        var errorMessage = (response.data && response.data.message) ? response.data.message : 'ไม่สามารถเชื่อมต่อได้';
                        resultEl.html('<div style="color: red; font-weight: bold;">✗ ' + errorMessage + '</div>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('ทดสอบการเชื่อมต่อ');
                    resultEl.html('<div style="color: red; font-weight: bold;">✗ เกิดข้อผิดพลาดในการทดสอบ</div>');
                }
            });
        });
        
        $('#clear-cloudflare-cache').on('click', function() {
            const button = $(this);
            const resultEl = $('#cloudflare-test-result');
            button.prop('disabled', true).html('กำลังเคลียร์แคช...');
            resultEl.html('');
            
            const formData = {
                action: 'manual_clear_cloudflare_cache',
                nonce: '<?php echo $cloudflare_nonce; ?>'
            };
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('เคลียร์แคชทั้งหมด');
                    if (response.success) {
                        resultEl.html('<div style="color: green; font-weight: bold;">✓ เคลียร์แคชสำเร็จ</div>');
                    } else {
                        var errorMessage = (response.data && response.data.message) ? response.data.message : 'ไม่สามารถเคลียร์แคชได้';
                        resultEl.html('<div style="color: red; font-weight: bold;">✗ ' + errorMessage + '</div>');
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('เคลียร์แคชทั้งหมด');
                    resultEl.html('<div style="color: red; font-weight: bold;">✗ เกิดข้อผิดพลาดในการเคลียร์แคช</div>');
                }
            });
        });
    });
    </script>
    <?php
}

function handle_save_cloudflare_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_cloudflare_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    if (isset($_POST['cloudflare_email'])) {
        update_option('amp_cloudflare_email', sanitize_email($_POST['cloudflare_email']));
    }
    if (isset($_POST['cloudflare_domain'])) {
        $domain = sanitize_text_field($_POST['cloudflare_domain']);
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = preg_replace('/^www\./', '', $domain);
        update_option('amp_cloudflare_domain', $domain);
    }
    if (isset($_POST['cloudflare_api_token'])) {
        update_option('amp_cloudflare_api_token', sanitize_text_field($_POST['cloudflare_api_token']));
    }
    
    $cache_manager = \AMP_Cache_Manager::instance();
    $cache_manager->clear_group('cloudflare_zones');
    
    wp_send_json_success(['message' => 'บันทึกการตั้งค่า CloudFlare เรียบร้อยแล้ว']);
}

function handle_test_cloudflare_connection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_cloudflare_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    $email = sanitize_email($_POST['cloudflare_email']);
    $domain = sanitize_text_field($_POST['cloudflare_domain']);
    $api_token = sanitize_text_field($_POST['cloudflare_api_token']);
    
    if (empty($email) || empty($domain) || empty($api_token)) {
        wp_send_json_error(['message' => 'กรุณากรอกข้อมูลให้ครบถ้วน']);
        return;
    }
    
    $domain = preg_replace('/^https?:\/\//', '', $domain);
    $domain = preg_replace('/^www\./', '', $domain);
    
    require_once AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';
    $cache_manager = \AMP_Cache_Manager::instance();
    $zone_id = $cache_manager->get_cloudflare_zone_id($domain, $api_token);
    
    if (!$zone_id) {
        wp_send_json_error(['message' => 'ไม่พบโดเมนใน CloudFlare หรือ API Token ไม่ถูกต้อง']);
        return;
    }
    
    wp_send_json_success(['message' => 'เชื่อมต่อ CloudFlare สำเร็จ', 'zone_id' => $zone_id]);
}

function handle_manual_clear_cloudflare_cache() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_cloudflare_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    require_once AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';
    $cache_manager = \AMP_Cache_Manager::instance();
    $cache_manager->clear_cache_on_ad_update();
    $result = true;

    if (is_wp_error($result)) {
        wp_send_json_error(['message' => $result->get_error_message()]);
    } else {
        wp_send_json_success(['message' => 'ล้างแคช CloudFlare สำเร็จแล้ว']);
    }
}

add_action('wp_ajax_save_cloudflare_settings', 'handle_save_cloudflare_settings');
add_action('wp_ajax_test_cloudflare_connection', 'handle_test_cloudflare_connection');
add_action('wp_ajax_manual_clear_cloudflare_cache', 'handle_manual_clear_cloudflare_cache');
