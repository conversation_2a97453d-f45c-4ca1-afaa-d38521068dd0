<?php

namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

class Database {
    
    private static $instance = null;
    private $wpdb;
    private $cache_group = 'amp_db_cache';
    private $cache_expiry = 3600;
    public $prefix;
    private $tables = [];
    
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->prefix = $wpdb->prefix;
        $this->load_table_names();
    }
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function load_table_names() {
        $this->tables = [
            'ad_payments'           => $this->wpdb->prefix . 'ad_payments',
            'ad_positions'          => $this->wpdb->prefix . 'ad_positions',
            'ad_discount_rates'     => $this->wpdb->prefix . 'ad_discount_rates',
            'ad_price_calculation'  => $this->wpdb->prefix . 'ad_price_calculation',
            'ad_price_global_settings' => $this->wpdb->prefix . 'ad_price_global_settings',
            'plisio_settings'       => $this->wpdb->prefix . 'plisio_settings',
            'ad_login_attempts'     => $this->wpdb->prefix . 'ad_login_attempts',
        ];
    }
    
    public function get_table($name) {
        return isset($this->tables[$name]) ? $this->tables[$name] : false;
    }
    
    public function start_transaction() {
        $this->wpdb->query('START TRANSACTION');
    }

    public function commit() {
        $this->wpdb->query('COMMIT');
    }

    public function rollback() {
        $this->wpdb->query('ROLLBACK');
    }
    
    public function get_row($query, $args = [], $output = OBJECT) {
        $table_pattern = '/{([^}]+)}/';
        preg_match_all($table_pattern, $query, $matches);       
        if (!empty($matches[1])) {
            foreach ($matches[1] as $table_name) {
                $table = $this->get_table($table_name);
                if ($table) {
                    $query = str_replace("{{$table_name}}", $table, $query);
                }
            }
        }       
        if (!empty($args)) {
            $query = $this->wpdb->prepare($query, $args);
        }      
        return $this->wpdb->get_row($query, $output);
    }
    
    public function get_results($query, $args = [], $output = OBJECT) {
        $table_pattern = '/{([^}]+)}/';
        preg_match_all($table_pattern, $query, $matches);        
        if (!empty($matches[1])) {
            foreach ($matches[1] as $table_name) {
                $table = $this->get_table($table_name);
                if ($table) {
                    $query = str_replace("{{$table_name}}", $table, $query);
                }
            }
        }        
        if (!empty($args)) {
            $query = $this->wpdb->prepare($query, $args);
        }
       
        $result = $this->wpdb->get_results($query, $output);

        if ($this->wpdb->last_error) {
            error_log('Database Error in get_results: ' . $this->wpdb->last_error . ' | Query: ' . $query);
            return false;
        }

        return $result;
    }
    
    public function get_var($query, $args = [], $x = 0, $y = 0) {
        $table_pattern = '/{([^}]+)}/';
        preg_match_all($table_pattern, $query, $matches);

        if (!empty($matches[1])) {
            foreach ($matches[1] as $table_name) {
                $table = $this->get_table($table_name);
                if ($table) {
                    $query = str_replace("{{$table_name}}", $table, $query);
                }
            }
        }
        if (!empty($args)) {
            $query = $this->wpdb->prepare($query, $args);
        }

        $result = $this->wpdb->get_var($query, $x, $y);

        if ($this->wpdb->last_error) {
            error_log('Database Error in get_var: ' . $this->wpdb->last_error . ' | Query: ' . $query);
            return false;
        }

        return $result;
    }
    
    public function insert($table, $data, $format = null) {
        $table = $this->get_table($table);
        if (!$table) {
            return false;
        }
        if (!is_array($data) || empty($data)) {
            return false;
        }
        $sanitized_data = $this->sanitize_data_array($data);
        $result = $this->wpdb->insert($table, $sanitized_data, $format);

        if ($this->wpdb->last_error) {
            error_log('Database Error in insert: ' . $this->wpdb->last_error . ' | Table: ' . $table);
            return false;
        }

        return $result;
    }
    
    public function update($table, $data, $where, $format = null, $where_format = null) {
        $table = $this->get_table($table);
        if (!$table) {
            return false;
        }
        if (!is_array($data) || empty($data) || !is_array($where) || empty($where)) {
            return false;
        }
        $sanitized_data = $this->sanitize_data_array($data);
        $sanitized_where = $this->sanitize_data_array($where);
        return $this->wpdb->update($table, $sanitized_data, $sanitized_where, $format, $where_format);
    }
    
    public function delete($table, $where, $where_format = null) {
        $table = $this->get_table($table);
        if (!$table) {
            return false;
        }
        if (!is_array($where) || empty($where)) {
            return false;
        }
        $sanitized_where = $this->sanitize_data_array($where);
        return $this->wpdb->delete($table, $sanitized_where, $where_format);
    }
    
    public function insert_id() {
        return $this->wpdb->insert_id;
    }
    
    public function last_error() {
        return $this->wpdb->last_error;
    }
    
    public function get_wpdb() {
        return $this->wpdb;
    }

    private function sanitize_data_array($data) {
        if (!is_array($data)) {
            return $data;
        }

        $sanitized = [];
        foreach ($data as $key => $value) {
            $sanitized_key = sanitize_key($key);

            if (is_string($value)) {
                $sanitized[$sanitized_key] = sanitize_text_field($value);
            } elseif (is_numeric($value)) {
                $sanitized[$sanitized_key] = $value;
            } elseif (is_array($value)) {
                $sanitized[$sanitized_key] = $this->sanitize_data_array($value);
            } else {
                $sanitized[$sanitized_key] = $value;
            }
        }

        return $sanitized;
    }

    public function validate_table_name($table_name) {
        if (!is_string($table_name) || empty($table_name)) {
            return false;
        }
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $table_name)) {
            return false;
        }
        return true;
    }
    
    public static function create_tables() {
        global $wpdb;
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $charset_collate = $wpdb->get_charset_collate(); 
        $table_name = $wpdb->prefix . 'ad_payments';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) UNSIGNED NOT NULL,
            ad_position varchar(255) NOT NULL,
            amount decimal(10,2) NOT NULL DEFAULT 0.00,
            duration int(11) NOT NULL DEFAULT 30,
            payment_date datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            payment_method varchar(50) DEFAULT 'crypto',
            transaction_id varchar(255) DEFAULT '',
            status varchar(20) DEFAULT 'completed',
            purchase_type varchar(20) NOT NULL DEFAULT 'api',
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY ad_position (ad_position),
            KEY user_id (user_id),
            KEY payment_date (payment_date),
            KEY status (status),
            KEY user_status (user_id, status),
            KEY user_date (user_id, payment_date),
            KEY status_date (status, payment_date),
            KEY position_date (ad_position, payment_date)
        ) $charset_collate;";
        
        dbDelta($sql);
              
        $table_name = $wpdb->prefix . 'ad_positions';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            ad_position varchar(255) NOT NULL,
            type varchar(50) DEFAULT 'banner',
            width int(11) DEFAULT 300,
            height int(11) DEFAULT 250,
            description text,
            status varchar(20) DEFAULT 'active',
            start_date datetime DEFAULT NULL,
            expiration_date datetime DEFAULT NULL,
            image_url text,
            target_url text,
            website_name varchar(255) DEFAULT '',
            alt_text varchar(255) DEFAULT '',
            reserved_by int(11) DEFAULT NULL,
            reserved_until datetime DEFAULT NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY ad_position (ad_position),
            KEY status (status),
            KEY expiration_date (expiration_date),
            KEY reserved_by (reserved_by),
            KEY reserved_until (reserved_until),
            KEY type (type)
        ) $charset_collate;";

        dbDelta($sql);

        $table_name = $wpdb->prefix . 'ad_discount_rates';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            duration int(11) NOT NULL,
            discount_percentage decimal(5,2) NOT NULL DEFAULT 0.00,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY duration (duration)
        ) $charset_collate;";

        dbDelta($sql);

        $table_name = $wpdb->prefix . 'plisio_settings';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            setting_name varchar(255) NOT NULL,
            setting_value longtext,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY setting_name (setting_name)
        ) $charset_collate;";

        dbDelta($sql);

        $table_name = $wpdb->prefix . 'ad_login_attempts';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            ip_address varchar(45) NOT NULL,
            username varchar(255) DEFAULT NULL,
            attempt_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            success tinyint(1) NOT NULL DEFAULT 0,
            user_agent text,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY username (username),
            KEY ip_address (ip_address),
            KEY attempt_time (attempt_time),
            KEY success (success)
        ) $charset_collate;";

        dbDelta($sql);

        $table_name = $wpdb->prefix . 'ad_price_calculation';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            ad_position varchar(255) NOT NULL,
            usdt_price decimal(10,2) DEFAULT 1000.00,
            thb_price decimal(10,2) DEFAULT 35000.00,
            multiplier decimal(10,2) DEFAULT 1.00,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY ad_position (ad_position)
        ) $charset_collate;";

        dbDelta($sql);

        $table_name = $wpdb->prefix . 'ad_price_global_settings';
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id int(11) NOT NULL AUTO_INCREMENT,
            setting_name varchar(255) NOT NULL,
            setting_value longtext,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY setting_name (setting_name)
        ) $charset_collate;";

        dbDelta($sql);

        $settings_to_check = [
            'visitors_divisor' => '30',
            'monthly_visitors' => '300000',
            'thb_rate' => '35.5',
            'exchange_rate_last_updated' => time(),
            'reservation_timeout' => '3',
            'use_reservation_timer' => '1',
            'trial_multiplier' => '1.5',
            'minimum_price' => '6'
        ];

        foreach ($settings_to_check as $name => $default_value) {
            $table_name = $wpdb->prefix . 'ad_price_global_settings';
            $value = $wpdb->get_var($wpdb->prepare("SELECT setting_value FROM `{$table_name}` WHERE setting_name = %s", $name));
            if ($value === null) {
                $wpdb->insert($table_name, ['setting_name' => $name, 'setting_value' => $default_value]);
            }
        }

        $discount_table = $wpdb->prefix . 'ad_discount_rates';
        $count = $wpdb->get_var("SELECT COUNT(*) FROM `{$discount_table}`");
        if ($count == 0) {
            $default_rates = array(
                array('duration' => 1, 'discount_percentage' => 0.00),
                array('duration' => 3, 'discount_percentage' => 5.00),
                array('duration' => 6, 'discount_percentage' => 10.00),
                array('duration' => 12, 'discount_percentage' => 15.00)
            );
            
            foreach ($default_rates as $rate) {
                $wpdb->insert($discount_table, $rate);
            }
        }
        
        if (!wp_next_scheduled('amp_cleanup_login_attempts')) {
            wp_schedule_event(time(), 'daily', 'amp_cleanup_login_attempts');
        }
        return true;
    }

} 