<?php
if (!defined('WPINC')) {
    die;
}

require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';
require_once plugin_dir_path(__FILE__) . 'admin-ajax.php';

function toggle_ad_status() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-toggle-status')) {
        wp_send_json_error('Security check failed');
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error('ไม่อนุญาต', 403);
        return;
    }
    if (!isset($_POST['position']) || !isset($_POST['status'])) {
        wp_send_json_error('Missing required parameters');
        return;
    }
    $position = sanitize_text_field($_POST['position']);
    $status = (int)$_POST['status'] ? 'active' : 'inactive';
    if (empty($position)) {
        wp_send_json_error('Position name is required');
        return;
    }
    
    $database = \AdManagementPro\Core\Database::instance();
    $positions_table = $database->get_table('ad_positions');
    
    if ($positions_table) {
        $exists = $database->get_var(
            "SELECT COUNT(*) FROM {$positions_table} WHERE ad_position = %s", 
            [$position]
        );
        if ($exists) {
            $result = $database->update('ad_positions', ['status' => $status], ['ad_position' => $position]);
            if ($result === false) {
                wp_send_json_error('Failed to update position status');
                return;
            }

            do_action('amp_ad_status_changed', $position, $status);
        } else {
            wp_send_json_error('Position "' . $position . '" not found');
            return;
        }
    } else {
        wp_send_json_error('Database table not found');
        return;
    }

    wp_send_json_success([
        'position' => $position, 
        'status' => $status,
        'message' => 'Position status updated successfully'
    ]);
}
add_action('wp_ajax_toggle_ad_status', 'toggle_ad_status');

function get_ad_data() {
    ob_start();
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-get-ad-data')) {
        ob_end_clean();
        wp_send_json_error('Security check failed');
        return;
    }
    if (!current_user_can('manage_options')) {
        ob_end_clean();
        wp_send_json_error('Administrator access required', 403);
        return;
    }

    $position = isset($_POST['position']) ? sanitize_text_field($_POST['position']) : '';
    if (empty($position)) {
        ob_end_clean();
        wp_send_json_error('Invalid position received.');
        return;
    }

    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
        $database = \AdManagementPro\Core\Database::instance();

        $position_data = $database->get_row(
            "SELECT * FROM {$database->get_table('ad_positions')} WHERE ad_position = %s",
            [$position]
        );

        if (!$position_data) {
            ob_end_clean();
            wp_send_json_error('Position not found: ' . $position);
            return;
        }

        $width = $position_data->width ?? '';
        $height = $position_data->height ?? '';

        $user_id = $position_manager->get_position_owner_from_usermeta($position);
        $user_login = '';
        if ($user_id > 0) {
            $user = get_userdata($user_id);
            if ($user) {
                $user_login = $user->user_login;
            }
        }

        $expiration_date = '';
        if ($position_data->expiration_date && $position_data->expiration_date !== '0000-00-00 00:00:00') {
            $expiration_date = date('Y-m-d', strtotime($position_data->expiration_date));
        }

        $active = isset($position_data->status) && $position_data->status === 'active' ? 1 : 0;

        ob_end_clean();
        wp_send_json_success(array(
            'position' => $position,
            'user' => $user_login,
            'user_id' => $user_id,
            'image' => $position_data->image_url ?? '',
            'link' => $position_data->target_url ?? '',
            'seo_keyword' => $position_data->website_name ?? '',
            'width' => $width,
            'height' => $height,
            'expiration_date' => $expiration_date,
            'active' => $active
        ));

    } catch (Exception $e) {
        error_log("AMP: Error in get_ad_data: " . $e->getMessage());
        ob_end_clean();
        wp_send_json_error('Failed to get position data: ' . $e->getMessage());
    }
}
add_action('wp_ajax_get_ad_data', 'get_ad_data');

function handle_reset_all_positions() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-ajax')) {
        wp_send_json_error('Security check failed');
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Only administrators can reset all positions', 403);
        return;
    }
    $database = \AdManagementPro\Core\Database::instance();
    try {
        $database->start_transaction();
        
        $tables_to_truncate = [
            'ad_positions', 'ad_payments', 
            'ad_price_calculation', 'clicks'
        ];

        foreach ($tables_to_truncate as $table_key) {
            $table_name = $database->get_table($table_key);
            if ($table_name) {
                $database->get_wpdb()->query($database->get_wpdb()->prepare("TRUNCATE TABLE `%1s`", $table_name));
            }
        }

        global $wpdb;
        
        $meta_keys_to_delete = [
            'amp_countdown_timer',
            'amp_last_heartbeat',
            'amp_cart',
            'amp_reservation_lock',
            'ad_positions'
        ];

        foreach($meta_keys_to_delete as $key) {
            delete_metadata( 'user', 0, $key, '', true );
        }        
        $wpdb->query($wpdb->prepare("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE %s", 'amp_reservation_%'));
        $upload_dir = wp_upload_dir();
        $placeholder_dir = $upload_dir['basedir'] . '/ad_placeholders/';
        if (is_dir($placeholder_dir)) {
            $files = glob($placeholder_dir . '*.svg');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }        
        $database->commit();         
        $positions_count = $database->get_var("SELECT COUNT(*) FROM " . $database->get_table('ad_positions'));        
        wp_send_json_success([
            'message' => 'All positions have been completely deleted',
            'positions_deleted' => $positions_count,
            'action' => 'complete_deletion'
        ]);
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Reset All Positions Error: " . $e->getMessage());
        wp_send_json_error('Failed to reset positions: ' . $e->getMessage());
    }
}
add_action('wp_ajax_reset_all_positions', 'handle_reset_all_positions');

function handle_add_ad_position() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient privileges');
        return;
    }

    $name = sanitize_text_field($_POST['ad_position_name'] ?? '');
    if (empty($name)) {
        wp_send_json_error('Position name is required');
        return;
    }
    $is_edit = !empty($_POST['edit_ad_position_name']);
    ob_start();
    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
        $is_administrator = current_user_can('manage_options');
        $is_advertiser = current_user_can('amp_advertiser_access');
        if ($is_edit) {
            $original_name = sanitize_text_field($_POST['edit_ad_position_name']);
            
            $existing_position = $position_manager->get_position($original_name);
            if (!$existing_position) {
                ob_end_clean();
                wp_send_json_error('Position "' . $original_name . '" not found. It may have been deleted.');
                return;
            }
            
            $new_name = sanitize_text_field($_POST['ad_position_name'] ?? '');
            if (!empty($new_name) && $new_name !== $original_name) {
                $name_conflict = $position_manager->get_position($new_name);
                if ($name_conflict) {
                    ob_end_clean();
                    wp_send_json_error('Position name "' . $new_name . '" already exists. Please choose a different name.');
                    return;
                }
                
                global $wpdb;
                $table_name = $wpdb->prefix . 'ad_positions';
                $wpdb->update(
                    $table_name,
                    ['ad_position' => $new_name],
                    ['ad_position' => $original_name],
                    ['%s'],
                    ['%s']
                );
                $original_name = $new_name;
            }
            
            $user_id = intval($_POST['ad_user_id'] ?? 0);
            $expiration_date = sanitize_text_field($_POST['expiration_date'] ?? '');

            if ($is_administrator && (isset($_POST['ad_user_id']) || isset($_POST['expiration_date']))) {
                $result = $position_manager->update_position_ownership($original_name, $user_id, $expiration_date);
                if (is_wp_error($result)) {
                    ob_end_clean();
                    wp_send_json_error('Failed to update position ownership: ' . $result->get_error_message());
                    return;
                }
            }
            if ($is_administrator || $is_advertiser) {
                $content_data = [
                    'image' => $_POST['ad_image_url'] ?? '',
                    'link' => $_POST['ad_link'] ?? '',
                    'website_name' => $_POST['ad_website_name'] ?? ''
                ];
                
                $result = $position_manager->update_position_content($original_name, $content_data);
                if (is_wp_error($result)) {
                    ob_end_clean();
                    wp_send_json_error('Failed to update position content: ' . $result->get_error_message());
                    return;
                }

                do_action('amp_ad_updated', $original_name, $content_data);
            }
            if ($is_administrator) {
                $ad_width = intval($_POST['ad_width'] ?? 0);
                $ad_height = intval($_POST['ad_height'] ?? 0);
                if ($ad_width > 0 && $ad_height > 0) {
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'ad_positions';
                    $wpdb->update(
                        $table_name,
                        ['width' => $ad_width, 'height' => $ad_height],
                        ['ad_position' => $original_name],
                        ['%d', '%d'],
                        ['%s']
                    );
                }
            }
        } else {
            if (!$is_administrator) {
                ob_end_clean();
                wp_send_json_error('Only administrators can create new positions.');
                return;
            }

            $position_data = [
                'name' => $name,
                'type' => 'banner',
                'width' => intval($_POST['ad_width'] ?? 300),
                'height' => intval($_POST['ad_height'] ?? 250),
                'status' => 'active'
            ];
            $result = $position_manager->create_position($position_data);
            if (is_wp_error($result)) {
                error_log("AMP: Position creation failed: " . $result->get_error_message());
                ob_end_clean();
                wp_send_json_error('Failed to create position: ' . $result->get_error_message());
                return;
            } 
            error_log("AMP: Position '{$name}' created successfully in AJAX handler");
            $content_data = [
                'image' => isset($_POST['ad_image_url']) ? esc_url_raw($_POST['ad_image_url']) : '',
                'link' => isset($_POST['ad_link']) ? esc_url_raw($_POST['ad_link']) : '',
                'website_name' => isset($_POST['ad_website_name']) ? sanitize_text_field($_POST['ad_website_name']) : ''
            ];
            $position_manager->update_position_content($name, $content_data);
            $sel_user = isset($_POST['ad_user_id']) ? intval($_POST['ad_user_id']) : 0;
            if ($sel_user > 0) {
                $position_manager->assign_positions_to_user($sel_user, [$name]);
            }
        }

    } catch (Exception $e) {
        error_log("AMP: Error in position creation/update: " . $e->getMessage());
        ob_end_clean();
        wp_send_json_error('Failed to process position: ' . $e->getMessage());
        return;
    }

    require_once AMP_PLUGIN_DIR . 'admin/handlers/admin-ajax.php';
    $html = get_ad_positions_table_html();

    ob_end_clean();
    wp_send_json_success([
        'message' => $is_edit ? 'Position updated successfully' : 'Position created successfully',
        'is_edit' => $is_edit,
        'html' => $html
    ]);
}
add_action('wp_ajax_add_ad_position', 'handle_add_ad_position');

function handle_bulk_create_positions() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-position')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Only administrators can create positions', 403);
        return;
    }

    $positions = isset($_POST['positions']) ? $_POST['positions'] : [];
    $width = isset($_POST['width']) ? intval($_POST['width']) : 0;
    $height = isset($_POST['height']) ? intval($_POST['height']) : 0;

    if (empty($positions) || !is_array($positions)) {
        wp_send_json_error('No positions provided');
        return;
    }

    if ($width <= 0 || $height <= 0) {
        wp_send_json_error('Invalid width or height');
        return;
    }

    if (count($positions) > 100) {
        wp_send_json_error('Too many positions. Maximum 100 positions allowed');
        return;
    }

    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');

        $positions_data = [];
        foreach ($positions as $position_name) {
            $positions_data[] = [
                'name' => $position_name,
                'width' => $width,
                'height' => $height
            ];
        }

        $result = $position_manager->bulk_create_positions($positions_data);

        if (is_wp_error($result)) {
            wp_send_json_error('Failed to create positions: ' . $result->get_error_message());
            return;
        }

        require_once AMP_PLUGIN_DIR . 'admin/handlers/admin-ajax.php';
        $html = get_ad_positions_table_html();

        wp_send_json_success([
            'message' => 'Positions created successfully',
            'html' => $html,
            'created_count' => $result['created_count'],
            'skipped_count' => $result['skipped_count'],
            'total_processed' => $result['total_processed']
        ]);

    } catch (Exception $e) {
        wp_send_json_error('Failed to create positions: ' . $e->getMessage());
    }
}
add_action('wp_ajax_bulk_create_positions', 'handle_bulk_create_positions');

function handle_delete_ad() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-delete-position')) {
        wp_send_json_error('Security check failed');
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Only administrators can delete positions', 403);
        return;
    }
    $position = isset($_POST['position']) ? sanitize_text_field($_POST['position']) : '';

    if (empty($position)) {
        wp_send_json_error('Position or index is required');
        return;
    }

    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');

        $result = $position_manager->delete_position($position);

        if (is_wp_error($result)) {
            wp_send_json_error('Failed to delete position: ' . $result->get_error_message());
            return;
        }

        require_once AMP_PLUGIN_DIR . 'admin/handlers/admin-ajax.php';
        $html = get_ad_positions_table_html();

        wp_send_json_success([
            'message' => 'Position deleted successfully',
            'html' => $html
        ]);
    } catch (Exception $e) {
        error_log('AMP Delete Position Error: ' . $e->getMessage());
        wp_send_json_error('Failed to delete position: ' . $e->getMessage());
    }
}
add_action('wp_ajax_delete_ad', 'handle_delete_ad');

function handle_bulk_delete_positions() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-delete-position')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Only administrators can delete positions', 403);
        return;
    }

    $positions = isset($_POST['positions']) ? $_POST['positions'] : [];

    if (empty($positions) || !is_array($positions)) {
        wp_send_json_error('No positions selected for deletion');
        return;
    }

    if (count($positions) > 50) {
        wp_send_json_error('Too many positions selected. Maximum 50 positions allowed per bulk operation');
        return;
    }

    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');

        $deleted_count = 0;
        $failed_positions = [];

        foreach ($positions as $position_name) {
            $position_name = sanitize_text_field($position_name);
            if (empty($position_name)) {
                continue;
            }

            $result = $position_manager->delete_position($position_name);

            if (is_wp_error($result)) {
                $failed_positions[] = $position_name . ' (' . $result->get_error_message() . ')';
            } else {
                $deleted_count++;
            }
        }

        require_once AMP_PLUGIN_DIR . 'admin/handlers/admin-ajax.php';
        $html = get_ad_positions_table_html();

        $message = "Successfully deleted {$deleted_count} position(s)";
        if (!empty($failed_positions)) {
            $message .= ". Failed to delete: " . implode(', ', $failed_positions);
        }

        wp_send_json_success([
            'message' => $message,
            'html' => $html,
            'deleted_count' => $deleted_count,
            'failed_count' => count($failed_positions),
            'failed_positions' => $failed_positions
        ]);

    } catch (Exception $e) {
        error_log('AMP Bulk Delete Error: ' . $e->getMessage());
        wp_send_json_error('Failed to delete positions: ' . $e->getMessage());
    }
}
add_action('wp_ajax_bulk_delete_positions', 'handle_bulk_delete_positions');

function handle_reset_ad_position() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-reset-position')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Only administrators can reset positions'], 403);
        return;
    }

    $position = isset($_POST['position']) ? sanitize_text_field($_POST['position']) : '';

    if (empty($position)) {
        wp_send_json_error(['message' => 'Position is required']);
        return;
    }

    try {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');

        $result = $position_manager->reset_position_ownership($position);
        if (is_wp_error($result)) {
            wp_send_json_error(['message' => 'Failed to reset position: ' . $result->get_error_message()]);
            return;
        }

        require_once AMP_PLUGIN_DIR . 'admin/handlers/admin-ajax.php';
        $updated_html = get_ad_positions_table_html();

        wp_send_json_success([
            'message' => 'Position reset successfully',
            'html' => $updated_html,
            'position' => $position
        ]);

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'Failed to reset position: ' . $e->getMessage()]);
    }
}
add_action('wp_ajax_reset_ad_position', 'handle_reset_ad_position');

?>