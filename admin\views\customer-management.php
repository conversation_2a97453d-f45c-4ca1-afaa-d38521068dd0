<?php
if (!defined('WPINC')) {
    die;
}

function display_customer_management_page() {
    global $wpdb;
    $notice = '';

    $customers = get_users(array('role' => 'advertiser'));
    $special_users = array('demo');
    foreach ($special_users as $special_user) {
        $user = get_user_by('login', $special_user);
        if ($user && !in_array($user, $customers)) {
            $customers[] = $user;
        }
    }
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $ad_positions_objects = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
    $unique_positions = [];
    $seen_names = [];
    foreach ($ad_positions_objects as $position) {
        if (!in_array($position->name, $seen_names)) {
            $unique_positions[] = $position;
            $seen_names[] = $position->name;
        }
    }
    $ad_positions_objects = $unique_positions;

    usort($ad_positions_objects, function($a, $b) {
        return strnatcasecmp($a->name, $b->name);
    });
    
    $ad_positions = wp_list_pluck($ad_positions_objects, 'name');
    ?>
    <link rel="stylesheet" href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-unified.css?v=' . filemtime(plugin_dir_path(dirname(__FILE__)) . 'assets/css/admin-unified.css'); ?>" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
    .position-item.highlight {
        background-color: #fff3cd !important;
        border: 2px solid #ffc107 !important;
        border-radius: 4px;
        animation: highlightPulse 0.5s ease-in-out 3;
    }

    @keyframes highlightPulse {
        0% { background-color: #fff3cd; }
        50% { background-color: #ffeaa7; }
        100% { background-color: #fff3cd; }
    }
    </style>

    <div class="wrap user-management-wrap">
        <div class="user-management-header">
            <h1><i class="fas fa-users"></i> User Management</h1>
            <div>
                <button id="add-new-user-btn" class="ad-btn ad-btn-primary">
                    <i class="fas fa-user-plus"></i> Add New User
                </button>
            </div>
        </div>
        <?php echo $notice; ?>
        <div class="user-management-content">
            <div class="users-table-container">
                <div class="users-table-header">
                    <h2><i class="fas fa-list"></i> Users List</h2>
                    <div class="users-table-stats">
                        <span class="stat-item">
                            <i class="fas fa-users"></i>
                            Total: <strong><?php echo count($customers); ?></strong>
                        </span>
                    </div>
                </div>
                <div class="users-table-wrapper">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th class="col-username">Username</th>
                                <th class="col-name">Full Name</th>
                                <th class="col-email">Email</th>
                                <th class="col-positions">Ad Positions</th>
                                <th class="col-telegram">Telegram</th>
                                <th class="col-stats">Show Stats</th>
                                <th class="col-bypass">Bypass Checkout</th>
                                <th class="col-actions">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                <?php if (empty($customers)) : ?>
                    <tr>
                        <td colspan="8">No users found</td>
                    </tr>
                <?php else : ?>
                    <?php foreach ($customers as $customer) : ?>
                        <?php
                        $customer_positions = get_user_meta($customer->ID, 'ad_positions', true);
                        if (!is_array($customer_positions)) {
                            $customer_positions = array();
                        }
                        $telegram_contact = get_user_meta($customer->ID, 'telegram_contact', true);
                        $show_stats = get_user_meta($customer->ID, 'show_stats', true);
                        $bypass_checkout = get_user_meta($customer->ID, 'bypass_checkout', true);
                        ?>
                        <tr>
                            <td><?php echo esc_html($customer->user_login); ?></td>
                            <td><?php echo esc_html($customer->display_name); ?></td>
                            <td><?php echo esc_html($customer->user_email); ?></td>
                            <td>
                                <?php if (!empty($customer_positions)) : ?>
                                    <span class="positions-count"><?php echo count($customer_positions); ?> positions</span>
                                    <div class="positions-preview">
                                        <?php echo esc_html(implode(', ', array_slice($customer_positions, 0, 3))); ?>
                                        <?php if (count($customer_positions) > 3) : ?>
                                            <span class="more-positions">+<?php echo count($customer_positions) - 3; ?> more</span>
                                        <?php endif; ?>
                                    </div>
                                <?php else : ?>
                                    <span class="no-positions">No positions</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($telegram_contact) : ?>
                                    <span class="telegram-contact"><i class="fab fa-telegram"></i> <?php echo esc_html($telegram_contact); ?></span>
                                <?php else : ?>
                                    <span class="no-telegram">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $show_stats ? 'status-enabled' : 'status-disabled'; ?>">
                                    <?php echo $show_stats ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $bypass_checkout ? 'status-enabled' : 'status-disabled'; ?>">
                                    <?php echo $bypass_checkout ? 'Enabled' : 'Disabled'; ?>
                                </span>
                            </td>
                            <td class="actions-cell">
                                <div class="action-buttons">
                                    <button class="action-btn edit-customer" data-customer-id="<?php echo $customer->ID; ?>" title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="action-btn delete-customer" data-customer-id="<?php echo $customer->ID; ?>" data-username="<?php echo esc_attr($customer->user_login); ?>" title="Delete User">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="edit-user-modal" class="ad-modal">
        <div class="ad-modal-content">
            <div class="ad-modal-header">
                <h2 id="edit-modal-title">
                    <i class="fas fa-user-edit"></i> Edit User Settings
                </h2>
                <button class="ad-modal-close" type="button">&times;</button>
            </div>
            <div class="ad-modal-body">
                <form method="post" action="" id="edit-user-form">
                    <input type="hidden" name="customer_id" id="edit-user-id" value="">
                    <input type="hidden" name="user_edit_nonce" value="<?php echo wp_create_nonce('edit_user_settings'); ?>">

                    <div class="user-edit-tabs">
                        <button type="button" class="tab-button active" data-tab="positions">
                            <i class="fas fa-map-marker-alt"></i> Ad Positions
                        </button>
                        <button type="button" class="tab-button" data-tab="contact">
                            <i class="fas fa-address-card"></i> Contact Info
                        </button>
                        <button type="button" class="tab-button" data-tab="settings">
                            <i class="fas fa-cog"></i> Settings
                        </button>
                    </div>

            <div class="user-edit-content">
                <div class="tab-content active" id="positions-tab">
                    <h3>Ad Positions</h3>
                    <p>Select ad positions for this user:</p>
                    <div class="positions-container">
                        <div class="positions-list">
                            <?php
                            foreach ($ad_positions as $position) :
                                $position_data = $position_manager->get_position($position);
                                $owner_id = $position_manager->get_position_owner_from_usermeta($position);
                                $has_owner = $owner_id > 0;
                                $owner = '';
                                if ($has_owner) {
                                    $owner_user = get_userdata($owner_id);
                                    $owner = $owner_user ? $owner_user->user_login : '';
                                }
                                $is_disabled = $has_owner && $owner !== '{{username}}';
                            ?>
                                <label class="position-item <?php echo $is_disabled ? 'disabled' : ''; ?>">
                                    <input type="checkbox" name="ad_positions[]" value="<?php echo esc_attr($position); ?>" class="position-checkbox" <?php echo $is_disabled ? 'disabled' : ''; ?> data-owner="<?php echo esc_attr($owner); ?>">
                                    <span class="position-name"><?php echo esc_html($position); ?></span>
                                    <?php if ($is_disabled) : ?>
                                        <span class="position-owner">(Owner: <?php echo esc_html($owner); ?>)</span>
                                    <?php endif; ?>
                                </label>
                            <?php endforeach; ?>
                        </div>
                        <div class="positions-actions">
                            <p class="positions-note">
                                <i class="fas fa-info-circle"></i>
                                To add new positions, please use the <a href="<?php echo admin_url('admin.php?page=ads-management'); ?>">Position Management</a> page.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="tab-content" id="contact-tab">
                    <h3>Contact Information</h3>
                    <div class="form-field">
                        <label for="user-display-name">Display Name:</label>
                        <input type="text" name="display_name" id="user-display-name" value="">
                    </div>
                    <div class="form-field">
                        <label for="user-email">Email:</label>
                        <input type="email" name="email" id="user-email" value="">
                    </div>
                    <div class="form-field">
                        <label for="telegram-contact">Telegram Contact:</label>
                        <input type="text" name="telegram_contact" id="telegram-contact" value="">
                        <p class="field-description">Used for expiration notifications</p>
                    </div>
                </div>
                <div class="tab-content" id="settings-tab">
                    <h3>User Settings</h3>
                    <div class="form-field checkbox-field">
                        <label>
                            <input type="checkbox" name="show_stats" id="show-stats">
                            Show statistics to this user
                        </label>
                        <p class="field-description">When enabled, the user will be able to see click statistics for their ad positions</p>
                    </div>
                    <div class="form-field checkbox-field">
                        <label>
                            <input type="checkbox" name="bypass_checkout" id="bypass-checkout">
                            Allow bypass checkout
                        </label>
                        <p class="field-description">When enabled, the user can purchase ad positions without payment verification (like demo and admins users)</p>
                    </div>

                    <div class="form-field">
                        <label for="user-password">New Password:</label>
                        <input type="password" name="password" id="user-password" placeholder="Leave blank to keep current password">
                    </div>
                </div>
            </div>
                    <div class="ultra-modal-footer">
                        <button type="submit" name="update_user_settings" class="ultra-footer-btn ultra-btn-primary">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <button type="button" class="ultra-footer-btn ultra-btn-secondary cancel-modal">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div id="add-user-modal" class="ad-modal">
        <div class="ad-modal-content user-modal-content compact">
            <div class="ad-modal-header">
                <h2><i class="fas fa-user-plus"></i> Add New User</h2>
                <button class="ad-modal-close" type="button"><i class="fas fa-times"></i></button>
            </div>
            <div class="ad-modal-body">
                <form method="post" action="" id="add-user-form" class="user-form">
                    <input type="hidden" name="add_user_nonce" value="<?php echo wp_create_nonce('add_new_user'); ?>">
                    <div class="form-row">
                        <div class="form-field">
                            <label for="new-username">Username <span class="required">*</span></label>
                            <input type="text" name="username" id="new-username" required>
                        </div>
                        <div class="form-field">
                            <label for="new-email">Email <span class="required">*</span></label>
                            <input type="email" name="email" id="new-email" required>
                        </div>
                    </div>
                    <div class="form-field">
                        <label for="new-display-name">Display Name</label>
                        <input type="text" name="display_name" id="new-display-name">
                    </div>
                    <div class="form-field">
                        <label for="new-password">Password <span class="required">*</span></label>
                        <div class="password-wrapper">
                            <input type="password" name="password" id="new-password" required>
                            <button type="button" id="generate-password" class="ad-btn ad-btn-secondary">Generate</button>
                        </div>
                    </div>
                    <div class="form-field">
                        <label for="new-telegram">Telegram Contact</label>
                        <input type="text" name="telegram_contact" id="new-telegram">
                    </div>
                    <div class="form-row">
                        <div class="form-field checkbox-field">
                            <label><input type="checkbox" name="show_stats" id="new-show-stats"> Show Statistics</label>
                        </div>
                        <div class="form-field checkbox-field">
                            <label><input type="checkbox" name="bypass_checkout" id="new-bypass-checkout"> Bypass Checkout</label>
                        </div>
                    </div>
                    <div class="form-field">
                        <label>Ad Positions</label>
                        <div class="positions-list-container">
                            <?php
                            foreach ($ad_positions as $position) : ?>
                                <label class="position-item">
                                    <input type="checkbox" name="ad_positions[]" value="<?php echo esc_attr($position); ?>">
                                    <span class="position-name"><?php echo esc_html($position); ?></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </form>
            </div>
            <div class="ad-modal-footer">
                <button type="button" class="ad-btn ad-btn-secondary cancel-add-user"><i class="fas fa-times"></i> Cancel</button>
                <button type="submit" form="add-user-form" id="add-user-submit" class="ad-btn ad-btn-primary"><i class="fas fa-user-plus"></i> Add User</button>
            </div>
        </div>
    </div>
    <div id="delete-user-modal" class="ad-modal">
        <div class="ad-modal-content">
            <div class="ad-modal-header">
                <h2>
                    <i class="fas fa-exclamation-triangle"></i> Delete User
                </h2>
                <button class="ad-modal-close" type="button">&times;</button>
            </div>
            <div class="ad-modal-body">
                <div class="delete-user-content">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Are you sure you want to delete the user <strong id="delete-username"></strong>?</p>
                        <p class="warning-text">This action cannot be undone and will remove all user data.</p>
                    </div>
                    <div class="ultra-modal-footer">
                        <button type="button" id="confirm-delete-user" class="ultra-footer-btn ultra-btn-danger">
                            <i class="fas fa-trash"></i> Delete User
                        </button>
                        <button type="button" class="ultra-footer-btn ultra-btn-secondary cancel-delete-user">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
    jQuery(document).ready(function($) {
        $('.user-management-wrap').css('visibility', 'visible');
        $('.tab-button').on('click', function() {
            var tab = $(this).data('tab');
            $('.tab-button').removeClass('active');
            $(this).addClass('active');
            $('.tab-content').removeClass('active');
            $('#' + tab + '-tab').addClass('active');
        });
        $('#add-new-user-btn').on('click', function() {
            $('#add-user-modal').addClass('show').css('display', 'flex');
        });
        $('.ad-modal-close, .cancel-add-user, .cancel-modal, .cancel-delete-user').on('click', function() {
            $(this).closest('.ad-modal').removeClass('show').css('display', 'none');
        });

        $('.ad-modal').on('click', function(e) {
            if (e.target === this) {
                $(this).removeClass('show').css('display', 'none');
            }
        });
        $('#generate-password').on('click', function(e) {
            e.preventDefault();
            var length = Math.floor(Math.random() * 5) + 8;
            var charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
            var password = '';

            for (var i = 0; i < length; i++) {
                var randomIndex = Math.floor(Math.random() * charset.length);
                password += charset.charAt(randomIndex);
            }

            $('#new-password').val(password);
        });

        $('#add-user-submit').on('click', function() {
            var $form = $('#add-user-form');
            var $button = $(this);
            if (!$form[0].checkValidity()) {
                $form[0].reportValidity();
                return;
            }
            $button.prop('disabled', true).html('<span class="loading-spinner"></span> Adding...');
            var formData = new FormData($form[0]);
            formData.append('action', 'add_new_user');
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        $('#add-user-modal').removeClass('show').css('display', 'none');
                        Swal.fire({
                            title: 'Success!',
                            text: response.data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(function() {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: response.data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        $button.prop('disabled', false).html('<i class="fas fa-user-plus"></i> Add User');
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred while adding the user.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    $button.prop('disabled', false).html('<i class="fas fa-user-plus"></i> Add User');
                }
            });
        });
        $('.delete-customer').on('click', function() {
            var customerId = $(this).data('customer-id');
            var username = $(this).data('username');
            $('#delete-username').text(username);
            $('#confirm-delete-user').data('customer-id', customerId);
            $('#delete-user-modal').addClass('show').css('display', 'flex');
        });
        $('#confirm-delete-user').on('click', function() {
            var customerId = $(this).data('customer-id');
            var $button = $(this);          
            $button.prop('disabled', true).html('<span class="loading-spinner"></span> Deleting...');         
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'delete_user',
                    customer_id: customerId,
                    nonce: '<?php echo wp_create_nonce('delete_user'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        $('#delete-user-modal').removeClass('show').css('display', 'none');                      
                        Swal.fire({
                            title: 'Success!',
                            text: response.data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(function() {

                            location.reload();
                        });
                    } else {

                        Swal.fire({
                            title: 'Error!',
                            text: response.data.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        $button.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> Delete User');
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred while deleting the user.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    $button.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> Delete User');
                }
            });
        });
        $('.edit-customer').on('click', function() {
            var customerId = $(this).data('customer-id');
            $('#edit-user-id').val(customerId);
            $('#edit-user-form')[0].reset();
            $('.position-checkbox').prop('checked', false);
            $.post(ajaxurl, {
                action: 'get_user_data',
                customer_id: customerId,
                nonce: '<?php echo wp_create_nonce("get_user_data"); ?>'
            }, function(response) {
                if (response.success) {
                    var userData = response.data;
                    $('#user-display-name').val(userData.display_name);
                    $('#user-email').val(userData.email);
                    $('#telegram-contact').val(userData.telegram || '');
                    $('#show-stats').prop('checked', userData.show_stats);
                    $('#bypass-checkout').prop('checked', userData.bypass_checkout);
                    $('.position-checkbox').each(function() {
                        var owner = $(this).data('owner');
                        if (owner === '{{username}}') {
                            $(this).data('owner', userData.username);
                            $(this).attr('data-owner', userData.username);
                        }
                    });
                    $('.position-checkbox').each(function() {
                        var owner = $(this).data('owner');
                        if (owner && owner !== userData.username) {
                            $(this).prop('disabled', true);
                            $(this).closest('.position-item').addClass('disabled');
                            if ($(this).closest('.position-item').find('.position-owner').length === 0) {
                                $(this).closest('.position-item').append('<span class="position-owner">(Owner: ' + owner + ')</span>');
                            }
                        } else {
                            $(this).prop('disabled', false);
                            $(this).closest('.position-item').removeClass('disabled');
                            $(this).closest('.position-item').find('.position-owner').remove();
                        }
                    });

                    $('input[name="ad_positions[]"]').prop('checked', false);

                    if (userData.positions) {
                        var positions = userData.positions;
                        console.log('Processing positions:', positions);
                        if (Array.isArray(positions)) {
                            $.each(positions, function(index, position) {
                                console.log('Checking array position:', position);
                                var checkbox = $('input[name="ad_positions[]"][value="' + position + '"]');
                                console.log('Found checkbox:', checkbox.length);
                                checkbox.prop('checked', true);
                            });
                        } else if (typeof positions === 'object') {
                            $.each(positions, function(index, position) {
                                console.log('Checking object position:', position);
                                var checkbox = $('input[name="ad_positions[]"][value="' + position + '"]');
                                console.log('Found checkbox:', checkbox.length);
                                checkbox.prop('checked', true);
                            });
                        }
                    }
                    $('#edit-modal-title').html('<i class="fas fa-user-edit"></i> Edit User: ' + userData.username);
                    $('#edit-user-modal').addClass('show').css('display', 'flex');
                } else {
                    alert('Error loading user data. Please try again.');
                }
            });
        });     
        $('#edit-user-form').on('submit', function(e) {
            e.preventDefault();
            var formData = $(this).serialize();
            $.post(ajaxurl, {
                action: 'update_user_settings',
                form_data: formData
            }, function(response) {
                if (response.success) {
                    $('#edit-user-modal').removeClass('show').css('display', 'none');     
                    Swal.fire({
                        title: 'Success!',
                        text: response.data.message,
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then(function() {

                        location.reload();
                    });
                } else {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Error updating user: ' + response.data.message,
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });
    });
    </script>
    <?php
}
