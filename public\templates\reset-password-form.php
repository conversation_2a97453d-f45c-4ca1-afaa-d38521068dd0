<?php
if (!defined('AMP_PLUGIN_DIR')) exit;
$reset_token = isset($_GET['token']) ? sanitize_text_field($_GET['token']) : '';
$reset_user = isset($_GET['user']) ? sanitize_text_field($_GET['user']) : '';
?>
<form id="reset-password-form" class="ad-login-form" method="post">
    <input type="hidden" name="token" value="<?php echo esc_attr($reset_token); ?>">
    <input type="hidden" name="user" value="<?php echo esc_attr($reset_user); ?>">
    <div class="ad-login-field">
        <label for="reset-password">New Password</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon password-icon"></i>
            <input type="password" id="reset-password" name="password" required autocomplete="new-password">
            <i class="ad-login-icon toggle-password"></i>
        </div>
        <div id="password-strength-container">
            <div id="password-strength-text"></div>
            <div class="password-strength-meter">
                <div id="password-strength-progress"></div>
            </div>
            <ul id="password-requirements">
                <li id="length-requirement">อย่างน้อย 8 ตัวอักษร</li>
                <li id="uppercase-requirement">ตัวพิมพ์ใหญ่ (A-Z)</li>
                <li id="lowercase-requirement">ตัวพิมพ์เล็ก (a-z)</li>
                <li id="number-requirement">ตัวเลข (0-9)</li>
                <li id="special-requirement">อักขระพิเศษ (!@#$%)</li>
            </ul>
        </div>
    </div>
    <div class="ad-login-field">
        <label for="reset-confirm-password">Confirm New Password</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon password-icon"></i>
            <input type="password" id="reset-confirm-password" name="confirm_password" required autocomplete="new-password">
            <i class="ad-login-icon toggle-password"></i>
        </div>
    </div>
    <?php if (defined('ABSPATH') && get_option('amp_turnstile_enabled', false)): ?>
    <div class="ad-login-captcha" id="reset-password-turnstile-container"></div>
    <?php endif; ?>
    <button type="submit" class="ad-login-button">Reset Password</button>
    <input type="hidden" name="amp_reset_password_nonce" value="<?php echo esc_attr($unified_nonce ?? ''); ?>">
    <input type="hidden" name="cf-turnstile-response" value="">
</form>