:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #7209b7;
    --accent-color: #00b4d8;

    --text-color: #333333;
    --light-text: #666666;
    --muted-text: #888888;

    --error-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;

    --card-bg: #ffffff;
    --body-bg: #f8f9fa;
    --input-bg: #f8f9fa;
    --sidebar-bg: #ffffff;
    --header-bg: #ffffff;
    --main-bg: #f8f9fa;

    --border-color: #dddddd;
    --divider-color: #eeeeee;

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 15px 40px rgba(0, 0, 0, 0.15);

    --sidebar-width: 260px;
    --header-height: 70px;
    --footer-height: 60px;

    --diagonal-stripe: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    --diagonal-stripe-size: 20px 20px;
    --theme-gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --theme-gradient-light: linear-gradient(135deg, rgba(67, 97, 238, 0.08), rgba(114, 9, 183, 0.08));
}

.dark-mode {
    --text-color: #f8f9fa;
    --light-text: #cccccc;
    --muted-text: #999999;

    --card-bg: #2d3748;
    --body-bg: #1a202c;
    --input-bg: #1a202c;
    --sidebar-bg: #1a202c;
    --header-bg: #2d3748;
    --main-bg: #121212;

    --border-color: #444444;
    --divider-color: #333333;

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 15px 40px rgba(0, 0, 0, 0.4);

    --theme-gradient-light: linear-gradient(135deg, rgba(67, 97, 238, 0.15), rgba(114, 9, 183, 0.15));
}

@keyframes slideInUp {
    from {
        transform: translateY(100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(100px);
        opacity: 0;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideOutUp {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

.mini-popup-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: 12px;
    color: white;
    font-size: 15px;
    font-weight: 500;
    z-index: 10001;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(100px);
    opacity: 0;
    animation: slideInUp 0.4s 0.1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.mini-popup-notification i {
    margin-right: 12px;
    font-size: 20px;
}

.mini-popup-notification.success {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.85), rgba(39, 174, 96, 0.9));
}

.mini-popup-notification.error {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.85), rgba(192, 57, 43, 0.9));
}

.mini-popup-notification.info {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.85), rgba(41, 128, 185, 0.9));
}

.timer-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.95), rgba(114, 9, 183, 0.95));
    color: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 10000;
    min-width: 250px;
    animation: slideInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.timer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.timer-header i {
    margin-right: 8px;
    font-size: 18px;
}

.timer-title {
    font-weight: 600;
    font-size: 16px;
}

.timer-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
}

.timer-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.timer-body {
    text-align: center;
}

.timer-display {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    font-family: 'Courier New', monospace;
}

.timer-actions button {
    background: rgba(231, 76, 60, 0.8);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.timer-actions button:hover {
    background: rgba(231, 76, 60, 1);
}

.timer-widget.timer-warning {
    animation: pulse 1s infinite;
}

.timer-widget.timer-caution {
    border-color: rgba(255, 193, 7, 0.5);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.ad-dashboard-wrapper {
    display: flex;
    min-height: 100vh;
    font-family: 'Prompt', sans-serif;
}

.ad-dashboard-sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    box-shadow: 6px 3px 10px rgb(0 0 0 / 29%);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 100;
    transition: all 0.3s ease;
    transform: translateX(-100%);
}

.sidebar-open .ad-dashboard-sidebar {
    transform: translateX(0);
}

.sidebar-animated {
    animation: sidebarPulse 0.3s ease;
}

@keyframes sidebarPulse {
    0% { box-shadow: var(--shadow); }
    50% { box-shadow: 0 0 15px rgba(67, 97, 238, 0.3); }
    100% { box-shadow: var(--shadow); }
}

.ad-dashboard-sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    background: var(--theme-gradient);
    position: relative;
    overflow: hidden;
}

.ad-dashboard-sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.ad-dashboard-logo {
    width: 100%;
    height: auto;
    max-width: 150px;
    margin-bottom: 10px;
    max-height: 150px;
}

.ad-dashboard-sidebar-header h2 {
    margin: 0;
    font-size: 18px;
    color: white;
    position: relative;
    z-index: 1;
    font-weight: 700;
}

.ad-dashboard-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.ad-dashboard-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ad-dashboard-nav li {
    margin-bottom: 5px;
}

.ad-dashboard-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    font-size: 15px;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.ad-dashboard-nav a i {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.menu-emoji {
    margin-right: 10px;
    font-size: 20px;
    width: 24px;
    text-align: center;
    display: inline-block;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.ad-dashboard-nav a:hover .menu-emoji {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.ad-dashboard-nav li.active a .menu-emoji {
    transform: scale(1.05);
    filter: drop-shadow(0 3px 6px rgba(67, 97, 238, 0.3));
}

.ad-dashboard-nav li.active a {
    background: var(--theme-gradient-light);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.ad-dashboard-nav li.active a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.2;
}

.ad-dashboard-nav a:hover {
    background: var(--theme-gradient-light);
}

.ad-dashboard-sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.ad-dashboard-logout {
    display: flex;
    align-items: center;
    color: var(--text-color);
    text-decoration: none;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.ad-dashboard-logout:hover {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
}

.ad-dashboard-logout i {
    margin-right: 8px;
}

.ad-dashboard-logout .menu-emoji {
    margin-right: 8px;
    font-size: 16px;
    width: 18px;
    text-align: center;
    display: inline-block;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.ad-dashboard-logout:hover .menu-emoji {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(231, 76, 60, 0.3));
}

.theme-toggle {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

.theme-toggle-checkbox {
    opacity: 0;
    position: absolute;
}

.theme-toggle-label {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 60px;
    height: 30px;
    background-color: #1a202c;
    border-radius: 30px;
    padding: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle-label .fa-sun {
    color: #f39c12;
    font-size: 14px;
}

.theme-toggle-label .fa-moon {
    color: #f8f9fa;
    font-size: 14px;
}

.theme-toggle-ball {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 26px;
    height: 26px;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.theme-toggle-checkbox:checked + .theme-toggle-label .theme-toggle-ball {
    transform: translateX(30px);
}

.theme-toggle-checkbox:checked + .theme-toggle-label {
    background-color: #4361ee;
}

.ad-dashboard-content {
    flex: 1;
    margin-left: 0;
    background-color: var(--main-bg);
    transition: all 0.3s ease;
    width: 100%;
}

.sidebar-open .ad-dashboard-content {
    margin-left: var(--sidebar-width);
}

.ad-dashboard-header {
    height: var(--header-height);
    background: var(--theme-gradient);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30px;
    position: sticky;
    top: 0;
    z-index: 99;
    position: relative;
}

.ad-dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.ad-dashboard-header-left {
    display: flex;
    align-items: center;
}

.ad-dashboard-menu-toggle {
    display: block;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    font-size: 20px;
    color: white;
    cursor: pointer;
    margin-right: 15px;
    transition: all 0.2s ease;
    padding: 8px 15px;
    border-radius: 5px;
    position: relative;
    z-index: 1;
}

.ad-dashboard-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.ad-dashboard-menu-toggle:active {
    transform: scale(0.95);
}

.ad-dashboard-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
    position: relative;
    z-index: 1;
}

.ad-dashboard-header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.exchange-rate-display-container {
    display: flex;
    align-items: center;
    background-color: var(--input-bg);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    min-width: 140px;
}

@media (max-width: 768px) {
    .exchange-rate-display-container {
        display: none;
    }
}

.exchange-rate-display-container.updated {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
    border-color: var(--success-color);
    transform: scale(1.02);
}

.exchange-rate-icon {
    margin-right: 8px;
    font-size: 16px;
    color: var(--success-color);
}

.exchange-rate-label {
    font-size: 14px;
    color: var(--light-text);
    font-weight: 500;
}

.exchange-rate-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--success-color);
    margin-left: 5px;
    min-width: 40px;
    text-align: center;
}

.ad-dashboard-cart {
    position: relative;
}

.ad-dashboard-cart-icon {
    font-size: 20px;
    color: white;
    text-decoration: none;
    position: relative;
    z-index: 1;
}

.ad-dashboard-cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--error-color);
    color: white;
    font-size: 12px;
    min-width: 18px;
    height: 18px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.cart-count-update {
    transform: scale(1.5);
    background-color: var(--primary-color);
}

.cart-count-new {
    animation: popIn 0.5s ease;
}

@keyframes popIn {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    70% {
        transform: scale(1.2);
        opacity: 1;
    }
    100% {
        transform: scale(1);
    }
}

.flying-item {
    position: fixed;
    z-index: 9999;
    width: 30px;
    height: 30px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.flying-item i {
    font-size: 14px;
}

.flying {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

.cart-bounce {
    animation: cartBounce 0.6s ease;
}

@keyframes cartBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.swal2-popup {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.swal2-title {
    font-weight: 700 !important;
    font-size: 1.6rem !important;
}

.swal2-content {
    font-size: 1rem !important;
    line-height: 1.5 !important;
}

.swal2-confirm {
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 10px 25px !important;
}

.swal2-cancel {
    font-weight: 600 !important;
    border-radius: 8px !important;
    padding: 10px 25px !important;
}

.swal2-popup.swal2-icon-error {
    border-left: 5px solid #f44336 !important;
}

.swal2-popup.swal2-icon-success {
    border-left: 5px solid #4CAF50 !important;
}

.swal2-popup.swal2-icon-warning {
    border-left: 5px solid #ff9800 !important;
}

.modern-session-popup {
    background: linear-gradient(145deg, #ffffff, #f8faff) !important;
    border-radius: 24px !important;
    border: none !important;
    box-shadow:
        0 32px 64px rgba(67, 97, 238, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
    padding: 0 !important;
    width: 480px !important;
    max-width: 90vw !important;
    position: relative;
    overflow: hidden;
}

.modern-session-popup::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #ffa726, #ffeb3b);
    border-radius: 24px 24px 0 0;
}

.modern-session-warning {
    padding: 40px 30px 30px;
    text-align: center;
}

.warning-icon-container {
    margin-bottom: 24px;
    display: flex;
    justify-content: center;
}

.warning-icon-bg {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b6b, #ffa726);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3);
    animation: pulse-warning 2s infinite;
}

.warning-icon {
    font-size: 32px;
    color: white;
    animation: rotate-warning 3s ease-in-out infinite;
}

.warning-title {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 16px 0;
    background: linear-gradient(135deg, #ff6b6b, #ffa726);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.warning-message {
    font-size: 16px;
    color: #5a6c7d;
    margin: 0 0 24px 0;
    line-height: 1.5;
}

.warning-message strong {
    color: #ff6b6b;
    font-weight: 700;
}

.warning-info-box {
    background: linear-gradient(135deg, #fff3e0, #ffecb3);
    border: 1px solid #ffa726;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 8px;
}

.warning-info-box i {
    color: #ff8f00;
    font-size: 18px;
}

.warning-info-box span {
    color: #e65100;
    font-weight: 500;
    font-size: 14px;
}

.modern-actions {
    padding: 0 30px 30px !important;
    gap: 16px !important;
    justify-content: center !important;
}

.modern-btn {
    padding: 14px 28px !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
    font-size: 15px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    min-width: 160px !important;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #4361ee, #7209b7) !important;
    color: white !important;
    box-shadow: 0 8px 24px rgba(67, 97, 238, 0.3) !important;
}

.modern-btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 32px rgba(67, 97, 238, 0.4) !important;
}

.modern-btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
    color: white !important;
    box-shadow: 0 8px 24px rgba(108, 117, 125, 0.3) !important;
}

.modern-btn-secondary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 32px rgba(108, 117, 125, 0.4) !important;
}

.modern-timer-bar {
    background: linear-gradient(90deg, #ff6b6b, #ffa726, #ffeb3b) !important;
    height: 4px !important;
    border-radius: 0 !important;
}

@keyframes pulse-warning {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 12px 32px rgba(255, 107, 107, 0.5);
    }
}

@keyframes rotate-warning {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(-10deg);
    }
    75% {
        transform: rotate(10deg);
    }
}

.swal2-popup.swal2-icon-info {
    border-left: 5px solid #2196F3 !important;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.ad-dashboard-user {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ad-dashboard-user:hover {
    opacity: 0.9;
}

.ad-dashboard-username {
    font-size: 14px;
    font-weight: 500;
    color: white;
    position: relative;
    z-index: 1;
}

.ad-dashboard-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.ad-dashboard-avatar:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.ad-dashboard-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 10px 0;
    z-index: 1000;
    margin-top: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.profile-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-animated {
    animation: dropdownPulse 0.3s ease;
}

@keyframes dropdownPulse {
    0% { box-shadow: var(--shadow); }
    50% { box-shadow: 0 0 15px rgba(67, 97, 238, 0.3); }
    100% { box-shadow: var(--shadow); }
}

.profile-dropdown:before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 16px;
    height: 16px;
    background-color: var(--card-bg);
    transform: rotate(45deg);
    border-top: 1px solid var(--border-color);
    border-left: 1px solid var(--border-color);
}

.profile-dropdown-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s ease;
}

.profile-dropdown-item:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

.profile-dropdown-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 16px;
}

.profile-dropdown-item .menu-emoji {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
    display: inline-block;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.profile-dropdown-item:hover .menu-emoji {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(67, 97, 238, 0.3));
}

.profile-dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 5px 0;
}

.ad-dashboard-main {
    padding: 30px;
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    width: 100%;
    background: var(--main-bg);
}

.ad-dashboard-footer {
    height: var(--footer-height);
    background-color: var(--header-bg);
    border-top: 1px solid var(--border-color);
    padding: 0 30px;
    display: flex;
    align-items: center;
}

.ad-dashboard-footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.ad-dashboard-footer-logo {
    display: flex;
    align-items: center;
}

.ad-dashboard-footer-logo-img {
    height: 30px;
    width: auto;
}

.ad-dashboard-footer-copyright {
    color: var(--light-text);
    font-size: 14px;
}

@media (max-width: 991px) {
    .sidebar-open .ad-dashboard-content {
        margin-left: 0;
    }
    .ad-dashboard-sidebar {
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    }
    .sidebar-open:before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99;
        animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
}

@media (max-width: 576px) {
    .ad-dashboard-header {
        padding: 0 15px;
    }

    .ad-dashboard-main {
        padding: 20px 15px;
    }

    .ad-dashboard-username {
        display: none;
    }

    .profile-dropdown {
        width: 180px;
        right: -10px;
    }
}



.swal2-loading {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 10000 !important;
}

.swal2-loader {
    margin: 0 auto !important;
    display: block !important;
}

.swal2-popup .swal2-loader {
    margin: 20px auto !important;
}



@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #4361ee;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}


.amp-popup-container {
    background: linear-gradient(145deg, rgba(90, 56, 173, 0.9), rgba(82, 106, 224, 0.9)) !important;
    backdrop-filter: blur(15px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(15px) saturate(180%) !important;
    border-radius: 24px !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.3) !important;
    color: white !important;
    padding: 30px !important;
    max-width: 594px !important;
    width: 100% !important;
}

.amp-popup-container .swal2-title {
    color: white !important;
    font-size: 26px !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.25) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 12px !important;
    margin-bottom: 20px !important;
    padding: 0 !important;
}

.amp-popup-container .swal2-html-container {
    margin: 20px 0 0 0 !important;
    color: rgba(255, 255, 255, 0.85) !important;
    font-size: 16px !important;
    line-height: 1.6 !important;
}

.amp-popup-container .swal2-icon {
    margin: 0 auto 20px auto !important;
    border-width: 3px !important;
    width: 80px !important;
    height: 80px !important;
}

.amp-popup-success {
    background: linear-gradient(145deg, rgba(29, 135, 71, 0.9), rgba(39, 174, 96, 0.95)) !important;
}
.amp-popup-success .swal2-icon.swal2-success {
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: white !important;
}
.amp-popup-success .swal2-success-line-tip,
.amp-popup-success .swal2-success-line-long {
    background-color: white !important;
}

.amp-popup-success .swal2-success-circular-line-left,
.amp-popup-success .swal2-success-circular-line-right,
.amp-popup-success .swal2-success-fix {
    background: none !important;
}

.amp-popup-success .swal2-success-ring {
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.amp-popup-error {
    background: linear-gradient(145deg, rgba(192, 28, 40, 0.9), rgba(231, 76, 60, 0.95)) !important;
}
.amp-popup-error .swal2-icon.swal2-error {
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: white !important;
}

.amp-popup-warning {
     background: linear-gradient(145deg, rgba(230, 126, 34, 0.9), rgba(243, 156, 18, 0.95)) !important;
}
.amp-popup-warning .swal2-icon.swal2-warning {
    border-color: rgba(255, 255, 255, 0.4) !important;
    color: white !important;
}

.amp-duration-grid {
    display: grid !important;
    gap: 20px !important;
    margin: 0 auto !important;
    max-height: 60vh !important;
    overflow-y: auto !important;
    padding: 10px !important;
}

.amp-duration-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
    max-width: 500px !important;
}

.amp-duration-grid-4 {
    grid-template-columns: repeat(2, 1fr) !important;
    grid-template-rows: repeat(2, 1fr) !important;
}

.amp-duration-grid-scrollable {
    grid-template-columns: repeat(2, 1fr) !important;
    grid-template-rows: auto !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
    max-width: 600px !important;
}

.amp-duration-grid::-webkit-scrollbar {
    width: 8px !important;
}

.amp-duration-grid::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 4px !important;
}

.amp-duration-grid::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3) !important;
    border-radius: 4px !important;
}

.amp-duration-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5) !important;
}

.amp-duration-option {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 18px !important;
    padding: 25px 20px !important;
    text-align: center !important;
    cursor: pointer !important;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
    position: relative !important;
    overflow: hidden !important;
    aspect-ratio: 1.2 / 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 180px !important;
    width: 100% !important;
    height: 100% !important;
    min-width: 200px !important;
}

.amp-duration-option:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2) !important;
}

.amp-duration-option.selected {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.6) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25) !important;
}

.amp-duration-option .selected-checkmark {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 24px;
    height: 24px;
    background-color: #39e981;
    color: #1a5934;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    transform: scale(0);
    transition: transform 0.3s ease;
    z-index: 2;
}

.amp-duration-option.selected .selected-checkmark {
    transform: scale(1);
}

.amp-option-icon {
    font-size: 30px !important;
    line-height: 1 !important;
    margin: 8px 0 !important;
    text-shadow: 0 0 15px rgba(0, 0, 0, 0.3) !important;
    animation: iconFloat 3s ease-in-out infinite !important;
    transform-origin: center !important;
}

@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) !important;
    }
    25% {
        transform: translateY(-3px) scale(1.05) !important;
    }
    50% {
        transform: translateY(-6px) scale(1.1) !important;
    }
    75% {
        transform: translateY(-3px) scale(1.05) !important;
    }
}

.amp-duration-option:nth-child(1) .amp-option-icon {
    animation-delay: 0s !important;
}

.amp-duration-option:nth-child(2) .amp-option-icon {
    animation-delay: 0.5s !important;
}

.amp-duration-option:nth-child(3) .amp-option-icon {
    animation-delay: 1s !important;
}

.amp-duration-option:nth-child(4) .amp-option-icon {
    animation-delay: 1.5s !important;
}

.amp-duration-option:nth-child(5) .amp-option-icon {
    animation-delay: 2s !important;
}

.amp-duration-option:nth-child(6) .amp-option-icon {
    animation-delay: 2.5s !important;
}

.amp-duration-option:hover .amp-option-icon {
    animation-duration: 1.5s !important;
    transform: translateY(-8px) scale(1.15) !important;
}

.amp-duration-option.selected .amp-option-icon {
    animation-duration: 2s !important;
    transform: translateY(-5px) scale(1.2) !important;
}

.amp-option-label {
    font-size: 17px !important;
    font-weight: 600 !important;
    margin-bottom: 0px !important;
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
}

.amp-option-price {
    font-size: 25px !important;
    font-weight: 700 !important;
    color: #F9E2AF !important;
    margin-bottom: 5px !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

.amp-option-tag {
    display: inline-block !important;
    padding: 6px 18px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
}

.tag-discount {
    background-color: #39e981 !important;
    color: #1a5934 !important;
    box-shadow: 0 2px 5px rgba(57, 233, 129, 0.3) !important;
}

.tag-normal {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: rgba(255, 255, 255, 0.8) !important;
}

.tag-trial {
    background-color: #e67e22 !important;
    color: white !important;
    box-shadow: 0 2px 5px rgba(230, 126, 34, 0.3) !important;
}

.amp-popup-container .swal2-actions {
    margin-top: 35px !important;
    gap: 15px !important;
}

.amp-btn {
    border-radius: 18px !important;
    padding: 15px 30px !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    border: none !important;
    transition: all 0.25s ease !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
    letter-spacing: 0.5px !important;
    text-transform: uppercase;
}

.amp-btn:hover {
    transform: translateY(-3px) scale(1.03) !important;
}

.amp-btn-primary {
    background: linear-gradient(135deg, #39e981, #28a745) !important;
    color: #ffffff !important;
    text-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.amp-btn-primary:hover {
    box-shadow: 0 8px 25px rgba(57, 233, 129, 0.4) !important;
}

.amp-btn-secondary {
    background-color: rgba(255, 255, 255, 0.15) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(255, 255, 255, 0.25) !important;
}

.amp-btn-secondary:hover {
    background-color: var(--border-color);
    color: var(--text-color);
}

.amp-btn-warning {
    background-color: var(--warning-color);
    color: white;
}
.amp-btn-warning:hover {
    background-color: #d68910;
}

.amp-btn-danger {
    background-color: var(--error-color);
    color: white;
}
.amp-btn-danger:hover {
    background-color: #c0392b;
}

.amp-loading-spinner {
    display: none;
    width: 20px;
    height: 20px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
    display: block;
}



@media (max-width: 768px) {
    .amp-duration-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        grid-template-rows: auto !important;
        gap: 15px !important;
        max-width: 100% !important;
    }
    .amp-popup-container {
        max-width: 95% !important;
        padding: 20px !important;
    }
}

@media (max-width: 480px) {
    .amp-duration-grid {
        grid-template-columns: 1fr !important;
        grid-template-rows: auto !important;
        gap: 12px !important;
    }

    .amp-duration-option {
        aspect-ratio: auto !important;
        min-height: 120px !important;
        padding: 15px !important;
    }

    .amp-option-icon {
        font-size: 28px !important;
        margin-bottom: 10px !important;
    }

    .amp-option-label {
        font-size: 15px !important;
    }

    .amp-option-price {
        font-size: 16px !important;
    }
}

.swal2-container .swal2-loader {
    display: none !important;
}

.dark-mode .swal-force-light {
    background: #fff !important;
    color: #545454 !important;
}

.dark-mode .swal-force-light .swal2-title {
    color: #545454 !important;
}

.dark-mode .swal-force-light .swal2-html-container {
    color: #716add !important;
}

.dark-mode .swal-force-light .swal2-icon.swal2-success .swal2-success-ring {
    border-color: #a5dc86 !important;
}

.dark-mode .swal-force-light .swal2-icon.swal2-success .swal2-success-line-tip,
.dark-mode .swal-force-light .swal2-icon.swal2-success .swal2-success-line-long {
    background-color: #a5dc86 !important;
}



.cancel-info-card {
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.08), rgba(238, 90, 36, 0.08));
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    border-left: 5px solid #ff6b6b;
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.1);
}

.cancel-position-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 18px;
    font-size: 17px;
    font-weight: 600;
    color: #2c3e50;
}

.cancel-position-info i {
    color: #ff6b6b;
    font-size: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cancel-warning-text p {
    margin: 0 0 12px 0;
    font-weight: 600;
    color: #2c3e50;
    font-size: 16px;
}

.cancel-warning-text ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.cancel-warning-text li {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    font-size: 15px;
    color: #34495e;
    font-weight: 500;
}

.cancel-warning-text li i {
    color: #e74c3c;
    font-size: 16px;
    width: 18px;
    text-align: center;
}

.cancel-confirmation {
    text-align: center;
    padding: 20px 0 10px;
}

.cancel-question {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}



.success-popup-header {
    text-align: center;
    margin-bottom: 25px;
    padding-top: 15px;
}

.success-icon-wrapper {
    width: 90px;
    height: 90px;
    margin: 0 auto 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 32px rgba(46, 204, 113, 0.4);
    animation: pulseSuccess 2s infinite;
}

@keyframes pulseSuccess {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.success-icon-wrapper i {
    font-size: 36px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.success-popup-header h3 {
    margin: 0;
    font-size: 26px;
    font-weight: 700;
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.success-popup-content {
    text-align: center;
    padding: 0 10px;
}

.success-popup-content p {
    font-size: 17px;
    margin: 0 0 18px 0;
    line-height: 1.5;
    color: #2c3e50;
}

.success-note {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 15px;
    color: #27ae60;
    background: rgba(46, 204, 113, 0.1);
    padding: 12px 20px;
    border-radius: 12px;
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.success-note i {
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}



.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 3px;
    animation: progressAnimation 2s infinite;
}

@keyframes progressAnimation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.modern-btn {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 15px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.modern-btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.3);
}

.modern-btn-light {
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
    color: #2c3e50;
}

.modern-btn-light:hover {
    background: linear-gradient(135deg, #bdc3c7, #95a5a6);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(189, 195, 199, 0.3);
}

.dark-mode .modern-cancel-popup {
    background: linear-gradient(145deg, #2d3748, #1a202c) !important;
    color: #f8f9fa !important;
}

.dark-mode .cancel-popup-header h3 {
    color: #f8f9fa !important;
}

.dark-mode .cancel-position-info {
    color: #f8f9fa !important;
}

.dark-mode .cancel-warning-text p {
    color: #f8f9fa !important;
}

.dark-mode .cancel-warning-text li {
    color: #cccccc !important;
}

.dark-mode .cancel-question {
    color: #cccccc;
}

.amp-status-popup {
    border-radius: 24px !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    border: none !important;
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15), 0 8px 30px rgba(0, 0, 0, 0.1) !important;
    width: 550px !important;
    max-width: 95% !important;
    padding: 0 !important;
    overflow: hidden !important;
}

.dark-mode .amp-status-popup {
    background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%) !important;
}

.amp-status-popup-container {
    text-align: center;
    padding: 50px 40px 45px;
    position: relative;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
}

.dark-mode .amp-status-popup-container {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.amp-status-popup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.amp-status-icon {
    margin: 0 auto 35px;
    width: 120px;
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: white;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    animation: iconPulse 2s ease-in-out infinite;
}

.dark-mode .amp-status-icon {
    background: #2d3748;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.status-icon-svg {
    width: 60px;
    height: 60px;
}

.status-circle {
    stroke-width: 4;
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    animation: status-circle-draw 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    fill: none;
}

.status-icon-path {
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 50;
    stroke-dashoffset: 50;
    animation: status-path-draw 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
    fill: none;
}

.status-icon-additional {
    stroke-width: 5;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 10;
    stroke-dashoffset: 10;
    animation: status-path-draw 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.5s forwards;
    fill: none;
}

@keyframes status-circle-draw {
    to { stroke-dashoffset: 0; }
}

@keyframes status-path-draw {
    to { stroke-dashoffset: 0; }
}

.success-icon {
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%) !important;
}

.success-icon .status-circle,
.success-icon .status-icon-path {
    stroke: #28a745;
}

.dark-mode .success-icon {
    background: linear-gradient(135deg, #1d4b25 0%, #155724 100%) !important;
}

.cancelled-icon {
    background: linear-gradient(135deg, #fdf2f2 0%, #f8d7da 100%) !important;
}

.cancelled-icon .status-circle,
.cancelled-icon .status-icon-path {
    stroke: #dc3545;
}

.dark-mode .cancelled-icon {
    background: linear-gradient(135deg, #4a1e1e 0%, #721c24 100%) !important;
}

.expired-icon {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%) !important;
}

.expired-icon .status-circle,
.expired-icon .status-icon-path,
.expired-icon .status-icon-additional {
    stroke: #ffc107;
}

.dark-mode .expired-icon {
    background: linear-gradient(135deg, #4a3d1a 0%, #856404 100%) !important;
}

.error-icon {
    background: linear-gradient(135deg, #fdf2f2 0%, #f8d7da 100%) !important;
}

.error-icon .status-circle,
.error-icon .status-icon-path {
    stroke: #dc3545;
}

.dark-mode .error-icon {
    background: linear-gradient(135deg, #4a1e1e 0%, #721c24 100%) !important;
}

.unknown-icon {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.unknown-icon .status-circle,
.unknown-icon .status-icon-path {
    stroke: #6c757d;
}

.dark-mode .unknown-icon {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

.amp-status-header h2 {
    font-size: 36px;
    font-weight: 800;
    margin: 0 0 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.5px;
    animation: titleSlide 1s ease-out 0.5s both;
}

@keyframes titleSlide {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.amp-status-message p {
    font-size: 20px;
    color: var(--text-secondary);
    margin: 0 0 25px;
    line-height: 1.6;
    font-weight: 500;
    animation: messageSlide 1s ease-out 0.7s both;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.amp-status-details {
    margin: 30px 0 40px;
    animation: detailsSlide 1s ease-out 0.9s both;
}

@keyframes detailsSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.amp-status-details p {
    font-size: 16px;
    color: var(--text-muted);
    margin: 0;
    background: rgba(102, 126, 234, 0.08);
    padding: 20px 25px;
    border-radius: 16px;
    border-left: 5px solid #667eea;
    line-height: 1.6;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
}

.dark-mode .amp-status-details p {
    background: rgba(102, 126, 234, 0.15);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.amp-status-details i {
    color: #667eea;
    font-size: 18px;
    flex-shrink: 0;
}

.amp-status-actions {
    margin-top: 40px;
    animation: buttonSlide 1s ease-out 1.1s both;
}

@keyframes buttonSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.amp-status-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 18px 45px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: inline-flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    justify-content: center;
}

.amp-status-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s;
}

.amp-status-btn:hover::before {
    left: 100%;
}

.amp-status-btn:hover {
    transform: translateY(-4px) scale(1.08);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.5);
}

.amp-status-btn:active {
    transform: translateY(-2px) scale(1.05);
}

.payment-cancelled-popup .amp-status-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.3);
}

.payment-cancelled-popup .amp-status-btn:hover {
    box-shadow: 0 20px 50px rgba(231, 76, 60, 0.5);
}

.payment-cancelled-popup .amp-status-details p {
    background: rgba(231, 76, 60, 0.08);
    border-left-color: #e74c3c;
}

.dark-mode .payment-cancelled-popup .amp-status-details p {
    background: rgba(231, 76, 60, 0.15);
}

.payment-expired-popup .amp-status-btn {
    background: linear-gradient(135deg, #f39c12 0%, #d68910 100%);
    box-shadow: 0 12px 35px rgba(243, 156, 18, 0.3);
}

.payment-expired-popup .amp-status-btn:hover {
    box-shadow: 0 20px 50px rgba(243, 156, 18, 0.5);
}

.payment-expired-popup .amp-status-details p {
    background: rgba(243, 156, 18, 0.08);
    border-left-color: #f39c12;
}

.dark-mode .payment-expired-popup .amp-status-details p {
    background: rgba(243, 156, 18, 0.15);
}

.payment-error-popup .amp-status-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    box-shadow: 0 12px 35px rgba(220, 53, 69, 0.3);
}

.payment-error-popup .amp-status-btn:hover {
    box-shadow: 0 20px 50px rgba(220, 53, 69, 0.5);
}

.payment-error-popup .amp-status-details p {
    background: rgba(220, 53, 69, 0.08);
    border-left-color: #dc3545;
}

.dark-mode .payment-error-popup .amp-status-details p {
    background: rgba(220, 53, 69, 0.15);
}

.payment-success-popup .amp-status-btn {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    box-shadow: 0 12px 35px rgba(40, 167, 69, 0.3);
}

.payment-success-popup .amp-status-btn:hover {
    box-shadow: 0 20px 50px rgba(40, 167, 69, 0.5);
}

.payment-success-popup .amp-status-details p {
    background: rgba(40, 167, 69, 0.08);
    border-left-color: #28a745;
}

.dark-mode .payment-success-popup .amp-status-details p {
    background: rgba(40, 167, 69, 0.15);
}

.amp-success-popup-container {
    text-align: center;
    padding: 50px 40px 45px;
    position: relative;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.02) 0%, rgba(39, 174, 96, 0.02) 100%);
    border-radius: 24px;
    animation: popupSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
    max-height: 90vh;
}

.dark-mode .amp-success-popup-container {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.05) 0%, rgba(39, 174, 96, 0.05) 100%);
}

.amp-success-popup-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #2ecc71 0%, #27ae60 50%, #16a085 100%);
    border-radius: 24px 24px 0 0;
    animation: successShimmer 2s ease-in-out infinite;
}

@keyframes popupSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes successShimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.amp-success-icon {
    margin: 0 auto 35px;
    width: 120px;
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
    box-shadow: 0 15px 40px rgba(46, 204, 113, 0.2);
    animation: successIconPulse 2s ease-in-out infinite;
}

.dark-mode .amp-success-icon {
    background: linear-gradient(135deg, #1d4b25 0%, #155724 100%);
    box-shadow: 0 15px 40px rgba(46, 204, 113, 0.3);
}

@keyframes successIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.checkmark {
    width: 70px;
    height: 70px;
    animation: checkmarkScale 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
}

@keyframes checkmarkScale {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.checkmark-circle {
    stroke: #2ecc71;
    stroke-width: 3;
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    animation: checkmarkCircleDraw 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    fill: none;
}

.dark-mode .checkmark-circle {
    stroke: #27ae60;
}

@keyframes checkmarkCircleDraw {
    to {
        stroke-dashoffset: 0;
    }
}

.checkmark-check {
    stroke: #2ecc71;
    stroke-width: 4;
    stroke-linecap: round;
    stroke-linejoin: round;
    stroke-dasharray: 25;
    stroke-dashoffset: 25;
    animation: checkmarkCheckDraw 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s forwards;
    fill: none;
}

.dark-mode .checkmark-check {
    stroke: #27ae60;
}

@keyframes checkmarkCheckDraw {
    to {
        stroke-dashoffset: 0;
    }
}

.amp-success-header {
    margin-bottom: 25px;
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

.amp-success-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: #2ecc71;
    text-shadow: 0 2px 4px rgba(46, 204, 113, 0.2);
}

.dark-mode .amp-success-header h2 {
    color: #27ae60;
    text-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
}

.amp-success-message {
    margin-bottom: 25px;
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both;
}

.amp-success-message p {
    margin: 8px 0;
    font-size: 16px;
    color: #2c3e50;
    line-height: 1.5;
}

.dark-mode .amp-success-message p {
    color: #e2e8f0;
}

.amp-success-timer {
    margin-bottom: 30px;
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s both;
}

.amp-success-timer p {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

.dark-mode .amp-success-timer p {
    color: #a0aec0;
}

.amp-success-actions {
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1s both;
}

@keyframes successTextSlideUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.swal2-popup.amp-success-popup {
    border-radius: 24px !important;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    border: none !important;
    box-shadow: 0 30px 80px rgba(46, 204, 113, 0.15), 0 8px 30px rgba(46, 204, 113, 0.1) !important;
    width: 550px !important;
    max-width: 95% !important;
    padding: 0 !important;
    overflow: hidden !important;
}

.swal2-popup.amp-success-popup .swal2-html-container {
    overflow: hidden !important;
    max-height: none !important;
}

.dark-mode .swal2-popup.amp-success-popup {
    background: linear-gradient(145deg, #1e293b 0%, #0f172a 100%) !important;
    box-shadow: 0 30px 80px rgba(46, 204, 113, 0.2), 0 8px 30px rgba(46, 204, 113, 0.15) !important;
}

.swal2-popup.amp-success-popup .swal2-title {
    display: none !important;
}

.swal2-popup.amp-success-popup .swal2-html-container {
    margin: 0 !important;
    padding: 0 !important;
}

.mini-popup-notification {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    display: flex !important;
    align-items: center !important;
    padding: 15px 20px !important;
    border-radius: 12px !important;
    color: white !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    z-index: 10001 !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    transform: translateY(100px) !important;
    opacity: 0 !important;
    min-width: 250px !important;
    max-width: 400px !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    animation: miniPopupSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.mini-popup-notification.success {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.9), rgba(39, 174, 96, 0.95)) !important;
}

.mini-popup-notification.error {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.95)) !important;
}

.mini-popup-notification.info {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.9), rgba(41, 128, 185, 0.95)) !important;
}

.mini-popup-notification.warning {
    background: linear-gradient(135deg, rgba(243, 156, 18, 0.9), rgba(230, 126, 34, 0.95)) !important;
}

.mini-popup-notification i {
    margin-right: 12px !important;
    font-size: 20px !important;
    animation: miniIconPulse 2s ease-in-out infinite;
}

@keyframes miniPopupSlideIn {
    0% {
        transform: translateY(100px) !important;
        opacity: 0 !important;
    }
    100% {
        transform: translateY(0) !important;
        opacity: 1 !important;
    }
}

@keyframes miniIconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.mini-popup-notification.show {
    transform: translateY(0) !important;
    opacity: 1 !important;
}

.amp-success-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 8px;
    min-width: 120px;
    position: relative;
    overflow: hidden;
}

.amp-success-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.amp-success-btn:hover::before {
    left: 100%;
}

.amp-success-btn-primary {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(46, 204, 113, 0.3);
}

.amp-success-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(46, 204, 113, 0.4);
}

.amp-success-btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(108, 117, 125, 0.3);
}

.amp-success-btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(108, 117, 125, 0.4);
}

.dark-mode .amp-success-btn-primary {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    box-shadow: 0 8px 20px rgba(39, 174, 96, 0.4);
}

.dark-mode .amp-success-btn-secondary {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    box-shadow: 0 8px 20px rgba(74, 85, 104, 0.4);
}

.amp-success-btn i {
    font-size: 16px;
}

@media (max-width: 480px) {
    .amp-success-actions {
        flex-direction: column;
        gap: 20px;
    }

    .amp-success-btn {
        width: 100%;
        margin: 0 0 10px 0;
    }
}

.amp-status-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0 8px;
    min-width: 140px;
    position: relative;
    overflow: hidden;
    color: white;
}

.amp-status-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.amp-status-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.dark-mode .amp-status-btn-primary {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 8px 20px rgba(90, 103, 216, 0.4);
}

.amp-status-header {
    margin-bottom: 20px;
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s both;
}

.amp-status-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
}

.dark-mode .amp-status-header h2 {
    color: #e2e8f0;
}

.amp-status-message {
    margin-bottom: 30px;
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s both;
}

.amp-status-message p {
    margin: 0;
    font-size: 16px;
    color: #6c757d;
    line-height: 1.5;
}

.dark-mode .amp-status-message p {
    color: #a0aec0;
}

.amp-status-actions {
    animation: successTextSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.8s both;
}

.modern-session-warning {
    text-align: center;
    padding: 30px;
}

.warning-icon-container {
    margin-bottom: 25px;
}

.warning-icon-bg {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    border-radius: 50%;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: warningPulse 2s ease-in-out infinite;
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.warning-icon {
    font-size: 32px;
    color: white;
}

.warning-title {
    font-size: 22px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 15px 0;
}

.dark-mode .warning-title {
    color: #e2e8f0;
}

.warning-message {
    font-size: 16px;
    color: #6c757d;
    margin: 0 0 20px 0;
}

.dark-mode .warning-message {
    color: #a0aec0;
}

.warning-info-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #856404;
}

.dark-mode .warning-info-box {
    background: #4a3d1a;
    border-color: #856404;
    color: #ffeaa7;
}

.modern-session-popup {
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(243, 156, 18, 0.2) !important;
}


