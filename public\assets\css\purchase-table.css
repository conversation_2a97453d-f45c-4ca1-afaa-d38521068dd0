.purchase-table-container {
    width: 100%;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    background: var(--card-bg);
    margin-bottom: 30px;
    border: 1px solid var(--divider-color);
}

.purchase-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 15px;
}

.purchase-table thead {
    background: var(--theme-gradient);
    position: relative;
    overflow: hidden;
}

.purchase-table thead::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.purchase-table th {
    color: white;
    font-weight: 700;
    text-align: center;
    padding: 18px 15px;
    position: relative;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
    z-index: 1;
}

.purchase-table th i {
    margin-right: 8px;
    font-size: 16px;
}

.purchase-table th:first-child {
    border-top-left-radius: 20px;
}

.purchase-table th:last-child {
    border-top-right-radius: 20px;
}

.purchase-table tbody tr {
    transition: all 0.3s ease;
}

.purchase-table tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

.purchase-table tbody tr:hover {
    background: var(--theme-gradient-light);
    transform: translateY(-3px);
    box-shadow: var(--shadow);
    position: relative;
    z-index: 1;
}

.purchase-table td {
    padding: 16px 15px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    color: var(--text-color, #333);
    font-weight: 500;
}

.purchase-table .date {
    white-space: nowrap;
    font-weight: 600;
    color: #4361ee;
}

.purchase-table .amount {
    font-weight: 700;
    color: #2ecc71;
    font-size: 16px;
}

.purchase-status {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 700;
    letter-spacing: 0.5px;
    min-width: 120px;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.purchase-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0.1) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.purchase-table tr:hover .purchase-status::before {
    transform: translateX(100%);
}

.status-active {
    background-color: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.status-expired {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.status-expiring {
    background-color: rgba(243, 156, 18, 0.15);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.purchase-action {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 15px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
}

.purchase-action i {
    margin-right: 6px;
}

.action-view {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.action-view:hover {
    background-color: #3498db;
    color: white;
}

.action-renew {
    background-color: rgba(243, 156, 18, 0.1);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.action-renew:hover {
    background-color: #f39c12;
    color: white;
}

.action-unavailable {
    background-color: rgba(189, 195, 199, 0.1);
    color: #95a5a6;
    border: 1px solid rgba(189, 195, 199, 0.3);
    cursor: not-allowed;
}

.purchase-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.purchase-empty i {
    font-size: 80px;
    color: rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.purchase-empty p {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-muted, #6c757d);
    margin: 0;
}

.dark-mode .purchase-table-container {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

.dark-mode .purchase-table tbody tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.dark-mode .purchase-table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.dark-mode .purchase-table td {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .purchase-empty i {
    color: rgba(255, 255, 255, 0.1);
}

@media (max-width: 992px) {
    .purchase-table {
        min-width: 800px;
    }

    .purchase-table-container {
        overflow-x: auto;
    }
}

@media (max-width: 576px) {
    .purchase-table th,
    .purchase-table td {
        padding: 12px 10px;
        font-size: 13px;
    }

    .purchase-status {
        padding: 6px 10px;
        min-width: 100px;
        font-size: 11px;
    }
}
