/**
 * CSRF Protection JavaScript
 *
 * This script adds CSRF protection to all AJAX requests by adding the nonce
 * from the ampAdmin.nonces object to each request.
 */
(function() {
    'use strict';
    
    function initializeCSRFProtection() {
        if (typeof jQuery === 'undefined') {
            setTimeout(initializeCSRFProtection, 100);
            return;
        }
        
        var $ = jQuery;

        $(document).ready(function() {
            $(document).on('click', '.status-toggle input[type="checkbox"]', function() {
                const position = $(this).data('position');
                const status = $(this).is(':checked') ? 1 : 0;

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'toggle_ad_status',
                        position: position,
                        status: status,
                        security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.toggle_status_nonce : ''
                    },
                    success: function(response) {
                        if (response.success) {
                            console.log('Status updated successfully');
                        } else {
                            console.error('Failed to update status:', response.data);
                            alert('Failed to update status: ' + response.data);
                        }
                    }
                });
            });

            $(document).on('click', '.edit-ad', function() {
                const position = $(this).data('position');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_ad_data',
                        position: position,
                        security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.get_ad_data_nonce : ''
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#edit-ad-position-name').val(response.data.position);
                            $('#ad_image_url').val(response.data.image);
                            $('#ad_link').val(response.data.link);
                            $('#ad_website_name').val(response.data.seo_keyword);
                            $('#ad_user_id').val(response.data.user_id);
                            $('#expiration_date').val(response.data.expiration_date);
                            $('#ad_status').prop('checked', response.data.active == 1);

                            $('#edit-ad-modal').show();
                        } else {
                            console.error('Failed to get ad data:', response.data);
                            alert('Failed to get ad data: ' + response.data);
                        }
                    }
                });
            });

            $('#add-ad-form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('action', 'add_ad_position');
                formData.append('security', (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.position_nonce : '');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#ad-positions-table-container').html(response.data);
                            $('#add-ad-modal').hide();
                            $('#add-ad-form')[0].reset();
                        } else {
                            console.error('Failed to add ad position:', response.data);
                            alert('Failed to add ad position: ' + response.data);
                        }
                    }
                });
            });

            $('#edit-ad-form').on('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                formData.append('action', 'edit_ad_position');
                formData.append('security', (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.edit_position_nonce : '');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            $('#ad-positions-table-container').html(response.data);
                            $('#edit-ad-modal').hide();
                        } else {
                            console.error('Failed to edit ad position:', response.data);
                            alert('Failed to edit ad position: ' + response.data);
                        }
                    }
                });
            });



            $(document).on('click', '.reset-ad', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const position = $(this).data('position');

                Swal.fire({
                    title: 'Are you sure?',
                    text: "All data for this position will be reset!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, reset it!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'reset_ad_position',
                                position: position,
                                security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.reset_position_nonce : ''
                            },
                            success: function(response) {
                                if (response.success) {
                                    if (response.data && response.data.html) {
                                        $('#ad-positions-table').html(response.data.html);
                                        var totalPositions = $('#ad-positions-table tr').length;
                                        $('#total-positions-count').text(totalPositions);
                                    }
                                    Swal.fire({
                                        title: 'Reset Complete!',
                                        text: 'This position has been cleared.',
                                        icon: 'success',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                } else {
                                    Swal.fire('Error!', 'Failed to reset ad position: ' + response.data, 'error');
                                }
                            }
                        });
                    }
                });

                return false;
            });
        });
    }
    
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCSRFProtection);
    } else {
        initializeCSRFProtection();
    }
})();
