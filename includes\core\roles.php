<?php


if (!defined('WPINC')) {
    die;
}

function amp_setup_user_roles() {
    remove_role('subscriber');

    $role_exists = wp_roles()->is_role('advertiser');
    if (!$role_exists) {
        add_role('advertiser', 'Advertiser', [
            'read' => true,
            'edit_posts' => false,
            'delete_posts' => false,
            'publish_posts' => false,
            'upload_files' => true,
        ]);
    }

    $admin_role = get_role('administrator');
    if ($admin_role) {
        $admin_role->add_cap('manage_options');
    }

    $advertiser_role = get_role('advertiser');
    if ($advertiser_role) {
        $advertiser_role->add_cap('amp_advertiser_access');
    }
}
add_action('init', 'amp_setup_user_roles', 1);


function amp_user_has_bypass_permission($user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }
    if (user_can($user_id, 'manage_options')) {
        return true;
    }
    $bypass_permission = get_user_meta($user_id, 'bypass_checkout', true);
    if ($bypass_permission) {
        $user = get_user_by('ID', $user_id);
        $username = $user ? $user->user_login : 'unknown';
        $user_hash = hash('sha256', $username . SECURE_AUTH_SALT);
        error_log("AMP Bypass: User hash {$user_hash} (ID: {$user_id}) used bypass checkout permission");

        if (!user_can($user_id, 'amp_advertiser_access')) {
            error_log("AMP Security Warning: User hash {$user_hash} (ID: {$user_id}) has bypass permission but no advertiser role");
            return false;
        }
        $user_status = get_user_meta($user_id, 'amp_account_status', true);
        if ($user_status === 'suspended' || $user_status === 'banned') {
            error_log("AMP Security Warning: Suspended/banned user hash {$user_hash} (ID: {$user_id}) attempted to use bypass permission");
            return false;
        }
        $bypass_usage_count = get_user_meta($user_id, 'amp_bypass_usage_count', true) ?: 0;
        $bypass_usage_count++;
        update_user_meta($user_id, 'amp_bypass_usage_count', $bypass_usage_count);
        update_user_meta($user_id, 'amp_last_bypass_usage', time());
    }

    return (bool)$bypass_permission;
}

function amp_restrict_media_library_for_advertisers($query) {
    if (!is_admin() || !function_exists('get_current_screen')) {
        return;
    }

    $screen = get_current_screen();
    if (!$screen || $screen->base !== 'upload') {
        return;
    }

    if (current_user_can('manage_options')) {
        return;
    }

    if (current_user_can('amp_advertiser_access')) {
        $query['author'] = get_current_user_id();
    }
}
add_action('pre_get_posts', 'amp_restrict_media_library_for_advertisers');

function amp_filter_media_library_ajax($query) {
    if (!wp_doing_ajax() || !isset($_REQUEST['action']) || $_REQUEST['action'] !== 'query-attachments') {
        return $query;
    }

    if (current_user_can('manage_options')) {
        return $query;
    }

    if (current_user_can('amp_advertiser_access')) {
        $query['author'] = get_current_user_id();
    }

    return $query;
}
add_filter('ajax_query_attachments_args', 'amp_filter_media_library_ajax');