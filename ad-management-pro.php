<?php
/**
 * Plugin Name: Ad Management Pro
 * Description: A unified platform for managing ad positions, expiration dates, and ad clicks.
 * Version: 1.0
 * Author: AEK SEO
 * Text Domain: ad-management-pro
 */

if (!defined('ABSPATH')) {
    exit;
}

define('AMP_VERSION', '2.3');
define('AMP_PLUGIN_FILE', __FILE__);
define('AMP_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('AMP_PLUGIN_URL', plugin_dir_url(__FILE__));

class AdManagementPro {
    
    private static $instance = null;
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array(__CLASS__, 'uninstall'));
    }
    
    public function init() {
        $this->load_core_files();
        $this->init_hooks();
    }
    
    private function load_core_files() {
        $files = array(
            'includes/bootstrap.php',
            'admin/admin-init.php',
            'admin/admin-notices.php'
        );
        
        foreach ($files as $file) {
            $file_path = AMP_PLUGIN_DIR . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                $this->log_error("Missing file: {$file}");
            }
        }
    }
    
    private function init_hooks() {
        if (!has_action('init', array($this, 'load_textdomain'))) {
            add_action('init', array($this, 'load_textdomain'));
        }
        if (!has_action('init', array($this, 'setup_uninstall_handler'))) {
            add_action('init', array($this, 'setup_uninstall_handler'), 1);
        }
        if (!shortcode_exists('display_ad_position')) {
            add_shortcode('display_ad_position', array($this, 'display_ad_position_shortcode'));
        }
        add_action('login_init', array($this, 'wp_login_redirect_protection'));
        add_filter('wp_redirect', array($this, 'intercept_wp_login_redirect'), 10, 2);
    }
    
    public function wp_login_redirect_protection() {
        $protection_enabled = get_option('amp_wp_admin_protection', 'enabled');
        if ($protection_enabled === 'disabled') {
            return;
        }
        if ($this->is_litespeed_cache_request()) {
            return;
        }
        if ($this->is_ip_allowed()) {
            return;
        }
        if ($this->is_whm_cpanel_request()) {
            return;
        }
        if ($this->is_emergency_access()) {
            return;
        }
        wp_redirect(home_url(), 302);
        exit;
    }
    
    public function intercept_wp_login_redirect($location, $status) {
        if (strpos($location, 'wp-login.php') !== false) {
            $protection_enabled = get_option('amp_wp_admin_protection', 'enabled');
            if ($protection_enabled === 'enabled') {
                if (!$this->is_litespeed_cache_request() && 
                    !$this->is_ip_allowed() && 
                    !$this->is_whm_cpanel_request() && 
                    !$this->is_emergency_access()) {
                    return home_url();
                }
            }
        }
        return $location;
    }
    
    public function activate() {
        try {
            require_once AMP_PLUGIN_DIR . 'includes/core/setup/class-activator.php';
            \AdManagementPro\Core\Setup\Activator::activate();

            $this->setup_google_api();
            $this->setup_uninstall_handler();
            $this->setup_initial_wp_admin_protection();
            $this->setup_htaccess_security();

            do_action('amp_plugin_activated');

            $page_results = $this->create_plugin_pages();
            $this->setup_cron_jobs();

            flush_rewrite_rules();

            wp_schedule_single_event(time() + 5, 'amp_verify_installation');
            add_action('amp_verify_installation', array($this, 'verify_installation_complete'));

            error_log('AMP: Plugin activation completed successfully');

        } catch (Exception $e) {
            error_log('AMP Plugin Activation Error: ' . $e->getMessage());
            wp_die('Plugin activation failed: ' . $e->getMessage());
        }
    }

    public function deactivate() {
        try {
            require_once AMP_PLUGIN_DIR . 'includes/core/setup/class-deactivator.php';
            \AdManagementPro\Core\Setup\Deactivator::deactivate();

            $this->remove_htaccess_security();
            do_action('amp_plugin_deactivated');

            $this->clear_cron_jobs();
            flush_rewrite_rules();
        } catch (Exception $e) {
            error_log('AMP Plugin Deactivation Error: ' . $e->getMessage());
        }
    }

    public static function uninstall() {
        try {
            if (class_exists('AdManagementPro\Core\Setup\Uninstaller')) {
                \AdManagementPro\Core\Setup\Uninstaller::uninstall();
            }
        } catch (Exception $e) {
            error_log('AMP Plugin Uninstall Error: ' . $e->getMessage());
        }
    }
    
    public function load_textdomain() {
        load_plugin_textdomain('ad-management-pro', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }
    
    public function display_ad_position_shortcode($atts) {
        $atts = shortcode_atts(array(
            'position' => '',
        ), $atts, 'display_ad_position');
        
        if (empty($atts['position'])) {
            return '';
        }
        
        $position_name = sanitize_text_field($atts['position']);
       
        try {
            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
            $position = $position_manager->get_position($position_name);
            
            if ($position && !empty($position->image_url) && !empty($position->target_url)) {
                $output = sprintf(
                    '<div class="ad-position ad-position-%s">
                        <a href="%s" target="_blank" rel="nofollow">
                            <img src="%s" alt="%s" style="max-width: 100%%; height: auto;" />
                        </a>
                    </div>',
                    esc_attr($position_name),
                    esc_url($position->target_url),
                    esc_url($position->image_url),
                    esc_attr($position->website_name ?: $position_name)
                );
                return $output;
            }
        } catch (Exception $e) {
            error_log('AMP Shortcode Error: ' . $e->getMessage());
        }
        
        return '';
    }
    
    private function log_error($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Ad Management Pro: ' . $message);
        }
    }
    
    public function setup_google_api() {
        $google_api_dir = AMP_PLUGIN_DIR . 'admin/google-api-client';
        $autoload_file = $google_api_dir . '/vendor/autoload.php';
        
        if (!file_exists($autoload_file)) {
            update_option('amp_google_api_setup_needed', true);
            $this->install_google_api_client();
        } else {
            update_option('amp_google_api_setup_needed', false);
            update_option('amp_google_api_installed', true);
        }
    }
    
    public function install_google_api_client() {
        try {
            $google_api_dir = AMP_PLUGIN_DIR . 'admin/google-api-client';
            $install_script = $google_api_dir . '/install-google-api.php';
            
            if (!file_exists($install_script)) {
                throw new Exception('Installation script not found');
            }
            
            if (!is_writable($google_api_dir)) {
                throw new Exception('Directory not writable: ' . $google_api_dir);
            }
            
            ob_start();
            $old_cwd = getcwd();
            chdir($google_api_dir);
            
            include $install_script;
            
            chdir($old_cwd);
            $output = ob_get_clean();
            
            $autoload_file = $google_api_dir . '/vendor/autoload.php';
            if (file_exists($autoload_file)) {
                delete_option('amp_google_api_error');
                update_option('amp_google_api_setup_needed', false);
                update_option('amp_google_api_installed', true);
                return true;
            } else {
                throw new Exception('Installation completed but autoload.php not found');
            }
            
        } catch (Exception $e) {
            $error_message = 'Google API Client installation failed: ' . $e->getMessage();
            update_option('amp_google_api_error', $error_message);
            update_option('amp_google_api_setup_needed', true);
            $this->log_error($error_message);
            return false;
        }
    }

    public function setup_uninstall_handler() {
        $mu_plugins_dir = WP_CONTENT_DIR . '/mu-plugins';
        $source_file = AMP_PLUGIN_DIR . 'admin/amp-uninstall-handler.php';
        $destination_file = $mu_plugins_dir . '/amp-uninstall-handler.php';

        if (!is_dir($mu_plugins_dir)) {
            if (!wp_mkdir_p($mu_plugins_dir)) {
                return false;
            }
        }

        if (!file_exists($source_file)) {
            return false;
        }

        if (!is_writable($mu_plugins_dir)) {
            return false;
        }

        if (file_exists($destination_file)) {
            @unlink($destination_file);
        }

        if (copy($source_file, $destination_file)) {
            if (file_exists($destination_file)) {
                $content = @file_get_contents($destination_file);
                if ($content !== false) {
                    $open_brackets = substr_count($content, '{');
                    $close_brackets = substr_count($content, '}');
                }
            }
            return true;
        } else {
            return false;
        }
    }

    public function setup_htaccess_security() {
        add_action('init', array($this, 'wp_admin_protection_check'), 1);
        add_action('init', array($this, 'wp_login_protection_check'), 1);
        return true;
    }
    
    public function wp_login_protection_check() {
        global $pagenow;
        if ($pagenow !== 'wp-login.php') {
            return;
        }
        $protection_enabled = get_option('amp_wp_admin_protection', 'enabled');
        if ($protection_enabled === 'disabled') {
            return;
        }
        if ($this->is_litespeed_cache_request()) {
            return;
        }
        if ($this->is_ip_allowed()) {
            return;
        }
        if ($this->is_whm_cpanel_request()) {
            return;
        }
        if ($this->is_emergency_access()) {
            return;
        }
        wp_redirect(home_url(), 302);
        exit;
    }
    
    public function create_wp_login_htaccess_protection() {
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file) || !is_writable($htaccess_file)) {
            return ['success' => false, 'message' => 'ไม่สามารถเขียนไฟล์ .htaccess ได้'];
        }
        $htaccess_content = file_get_contents($htaccess_file);
        $pattern = '/# AMP WP-Login Protection - START.*?# AMP WP-Login Protection - END\s*/s';
        $htaccess_content = preg_replace($pattern, '', $htaccess_content);
        $allowed_ips = get_option('amp_allowed_ip_addresses', '');
        $whm_detection = get_option('amp_whm_cpanel_detection', 'enabled');
        $emergency_code = get_option('amp_emergency_access_code', '');
        $home_url = home_url();
        if (empty($allowed_ips)) {
            return ['success' => false, 'message' => 'กรุณาระบุ IP ที่อนุญาตก่อนสร้างการป้องกัน'];
        }
        $ip_list = array_filter(array_map('trim', explode(',', $allowed_ips)));
        $protection_rules = "\n# AMP WP-Login Protection - START\n";
        $protection_rules .= "<Files \"wp-login.php\">\n";
        $protection_rules .= "    # Allow specific IPs\n";
        foreach ($ip_list as $ip) {
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                $protection_rules .= "    SetEnvIf Remote_Addr \"^{$ip}$\" allowed_ip\n";
            }
        }
        if ($whm_detection === 'enabled') {
            $protection_rules .= "    # Allow localhost for WHM/cPanel\n";
            $protection_rules .= "    SetEnvIf Remote_Addr \"^127\\.0\\.0\\.1$\" allowed_ip\n";
            $protection_rules .= "    SetEnvIf Remote_Addr \"^::1$\" allowed_ip\n";
        }
        if (!empty($emergency_code)) {
            $protection_rules .= "    # Emergency access\n";
            $protection_rules .= "    SetEnvIf Query_String \"emergency={$emergency_code}\" allowed_ip\n";
        }
        $protection_rules .= "    # Allow LiteSpeed Cache\n";
        $protection_rules .= "    SetEnvIf User-Agent \"LiteSpeed\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf User-Agent \"lscache\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Query_String \"action=async_litespeed\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Query_String \"action=litespeed\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Query_String \"lscache\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Query_String \"noabort=1\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Request_URI \"litespeed\" allowed_ip\n";
        $protection_rules .= "    SetEnvIf Request_URI \"lscache\" allowed_ip\n";
        $protection_rules .= "    # Allow WordPress REST API with Authorization headers\n";
        $protection_rules .= "    SetEnvIf Request_Method \"POST\" post_request\n";
        $protection_rules .= "    SetEnvIf HTTP:Authorization \"(.+)\" has_auth_header\n";
        $protection_rules .= "    SetEnvIf post_request:has_auth_header \"1:1\" allowed_ip\n";
        $protection_rules .= "    # Redirect unauthorized access to home page\n";
        $protection_rules .= "    RewriteEngine On\n";
        $protection_rules .= "    RewriteCond %{ENV:allowed_ip} !^1$\n";
        $protection_rules .= "    RewriteRule ^.*$ {$home_url} [R=302,L]\n";
        $protection_rules .= "</Files>\n";
        $protection_rules .= "# AMP WP-Login Protection - END\n\n";
        $new_content = $protection_rules . $htaccess_content;
        if (file_put_contents($htaccess_file, $new_content) !== false) {
            return ['success' => true, 'message' => 'สร้างการป้องกัน wp-login.php ใน .htaccess เรียบร้อยแล้ว (redirect ไปหน้าแรก)'];
        } else {
            return ['success' => false, 'message' => 'ไม่สามารถเขียนไฟล์ .htaccess ได้'];
        }
    }
    
    public function remove_wp_login_htaccess_protection() {
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file) || !is_writable($htaccess_file)) {
            return ['success' => false, 'message' => 'ไม่สามารถเขียนไฟล์ .htaccess ได้'];
        }
        $htaccess_content = file_get_contents($htaccess_file);
        $pattern = '/# AMP WP-Login Protection - START.*?# AMP WP-Login Protection - END\s*/s';
        if (preg_match($pattern, $htaccess_content)) {
            $new_content = preg_replace($pattern, '', $htaccess_content);
            if (file_put_contents($htaccess_file, $new_content) !== false) {
                return ['success' => true, 'message' => 'ลบการป้องกัน wp-login.php จาก .htaccess เรียบร้อยแล้ว'];
            } else {
                return ['success' => false, 'message' => 'ไม่สามารถเขียนไฟล์ .htaccess ได้'];
            }
        }
        return ['success' => true, 'message' => 'ไม่พบการป้องกัน wp-login.php ใน .htaccess'];
    }
    
    public function wp_admin_protection_check() {
        if (!is_admin() || (defined('DOING_AJAX') && DOING_AJAX)) {
            return;
        }
        $protection_enabled = get_option('amp_wp_admin_protection', 'enabled');
        if ($protection_enabled === 'disabled') {
            return;
        }

        if (current_user_can('manage_options')) {
            return;
        }

        if (is_user_logged_in() && !current_user_can('manage_options')) {
            error_log("AMP: Non-admin user attempted to access wp-admin - User ID: " . get_current_user_id());
            wp_redirect(home_url());
            exit;
        }

        if ($this->is_litespeed_cache_request()) {
            return;
        }
        if ($this->is_ip_allowed()) {
            return;
        }
        if ($this->is_whm_cpanel_request()) {
            return;
        }
        if ($this->is_emergency_access()) {
            return;
        }
        wp_redirect(home_url());
        exit;
    }
    
    public function is_litespeed_cache_request() {
        $request_uri = $_SERVER['REQUEST_URI'] ?? '';
        $query_string = $_SERVER['QUERY_STRING'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (stripos($user_agent, 'LiteSpeed') !== false || 
            stripos($user_agent, 'lscache') !== false) {
            return true;
        }
        if (strpos($request_uri, 'litespeed') !== false || 
            strpos($request_uri, 'lscache') !== false) {
            return true;
        }
        $litespeed_patterns = [
            'action=async_litespeed',
            'action=litespeed',
            'lscache',
            'noabort=1',
            'litespeed_purge',
            'litespeed_cache',
            'lscache_purge',
            'lscache_refresh'
        ];
        foreach ($litespeed_patterns as $pattern) {
            if (strpos($query_string, $pattern) !== false) {
                return true;
        }
        }
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_SERVER['HTTP_AUTHORIZATION'])) {
            return true;
        }
        return false;
    }
    
    public function is_ip_allowed() {
        $allowed_ips = get_option('amp_allowed_ip_addresses', '');
        if (empty($allowed_ips)) {
            return false;
        }
        $ip_list = array_filter(array_map('trim', explode(',', $allowed_ips)));
        $user_ip = $this->get_user_ip();
        return in_array($user_ip, $ip_list);
    }
    
    public function is_whm_cpanel_request() {
        $whm_detection = get_option('amp_whm_cpanel_detection', 'enabled');
        if ($whm_detection === 'disabled') {
            return false;
        }
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $user_ip = $this->get_user_ip();
        if (stripos($user_agent, 'cPanel') !== false || 
            stripos($user_agent, 'WHM') !== false || 
            stripos($user_agent, 'Webmail') !== false) {
            return true;
        }
        if (in_array($user_ip, ['127.0.0.1', '::1'])) {
            return true;
        }
        return false;
    }
    
    public function is_emergency_access() {
        $emergency_code = get_option('amp_emergency_access_code', '');
        if (empty($emergency_code)) {
            return false;
        }
        $provided_code = isset($_GET['emergency']) ? sanitize_text_field($_GET['emergency']) : '';
        return $provided_code === $emergency_code;
    }
    
    public function get_user_ip() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        foreach ($ip_keys as $key) {
            if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '';
    }

    public function remove_htaccess_security() {
        $htaccess_file = ABSPATH . '.htaccess';
        if (!file_exists($htaccess_file) || !is_writable($htaccess_file)) {
            return true;
        }
        $htaccess_content = file_get_contents($htaccess_file);
        $pattern = '/# AMP Security Protection - START.*?# AMP Security Protection - END\s*/s';
        if (preg_match($pattern, $htaccess_content)) {
            $new_content = preg_replace($pattern, '', $htaccess_content);
            $result = file_put_contents($htaccess_file, $new_content) !== false;
            if ($result) {
                error_log('AMP: Legacy .htaccess security rules removed');
            }
            return $result;
        }
        return true; 
    }

    private function create_plugin_pages() {
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';
        if (function_exists('ad_management_create_pages')) {
            $results = ad_management_create_pages();
            ad_management_create_directories();
            error_log('AMP: Page creation results: ' . json_encode($results));
            return $results;
        }
        return false;
    }

    public function verify_installation_complete() {
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';
        $verification_results = ad_management_verify_pages_accessibility();
        $all_accessible = true;
        foreach ($verification_results as $page_name => $result) {
            if (!$result['accessible']) {
                $all_accessible = false;
                error_log("AMP: Page '{$page_name}' is not accessible");
            }
        }

        if ($all_accessible) {
            error_log('AMP: All pages are accessible - installation verification complete');
            update_option('amp_installation_verified', true);
        } else {
            error_log('AMP: Some pages are not accessible - attempting to fix');
            flush_rewrite_rules();
            wp_schedule_single_event(time() + 10, 'amp_verify_installation_retry');
            add_action('amp_verify_installation_retry', array($this, 'retry_installation_verification'));
        }
    }

    public function retry_installation_verification() {
        require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';
        $verification_results = ad_management_verify_pages_accessibility();
        $all_accessible = true;
        foreach ($verification_results as $page_name => $result) {
            if (!$result['accessible']) {
                $all_accessible = false;
            }
        }
        if ($all_accessible) {
            error_log('AMP: Installation verification successful after retry');
            update_option('amp_installation_verified', true);
        } else {
            error_log('AMP: Installation verification failed after retry - manual permalink refresh may be needed');
            update_option('amp_installation_verified', false);
            update_option('amp_manual_permalink_refresh_needed', true);
        }
    }
    
    public function force_create_tables() {
        \AdManagementPro\Core\Database::create_tables();
        return true;
    }

    private function setup_cron_jobs() {
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));
        if (!wp_next_scheduled('amp_daily_cleanup')) {
            wp_schedule_event(time(), 'daily', 'amp_daily_cleanup');
        }
        add_action('amp_daily_cleanup', array($this, 'run_daily_cleanup'));
    }

    private function clear_cron_jobs() {
        wp_clear_scheduled_hook('amp_daily_cleanup');
    }

    public function add_cron_schedules($schedules) {
        $schedules['amp_daily'] = array(
            'interval' => 24 * 60 * 60,
            'display' => 'Daily (AMP Plugin)'
        );
        return $schedules;
    }

    public function run_daily_cleanup() {
        require_once AMP_PLUGIN_DIR . 'includes/core/class-ajax-handlers.php';
        $deleted_users = \AdManagementPro\Core\AjaxHandlers::cleanup_unverified_users();
        $deleted_logs = \AdManagementPro\Core\AjaxHandlers::cleanup_old_login_logs();
        error_log("AMP Daily Cleanup: Deleted {$deleted_users} unverified users and {$deleted_logs} old login logs");
    }

    private function setup_initial_wp_admin_protection() {
        if (!get_option('amp_wp_admin_protection')) {
            update_option('amp_wp_admin_protection', 'enabled');
        }
        
        if (!get_option('amp_whm_cpanel_detection')) {
            update_option('amp_whm_cpanel_detection', 'enabled');
        }
        
        update_option('amp_wp_admin_setup_completed', true);
    }
}

$amp_instance = AdManagementPro::instance();
$amp_instance->setup_htaccess_security();

