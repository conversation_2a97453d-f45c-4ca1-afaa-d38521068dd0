<?php
namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

class AMP_Options {
    private static $defaults = [
        'site_logo_url' => '',
        'turnstile_enabled' => false,
        'turnstile_site_key' => '',
        'turnstile_secret_key' => '',
        'turnstile_login_required' => false,
        'google_login_enabled' => false,
        'google_client_id' => '',
        'google_client_secret' => '',
    ];
    public static function get($key, $default = null) {
        $default_value = isset(self::$defaults[$key]) ? self::$defaults[$key] : $default;

        $encrypted_keys = ['turnstile_site_key', 'turnstile_secret_key', 'google_client_id', 'google_client_secret'];

        if (in_array($key, $encrypted_keys)) {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            return $encryption_manager->get_secret('amp_' . $key);
        }

        $value = get_option('amp_' . $key, $default_value);
        return $value;
    }

    public static function set($key, $value) {
        $encrypted_keys = ['turnstile_site_key', 'turnstile_secret_key', 'google_client_id', 'google_client_secret'];

        if (in_array($key, $encrypted_keys)) {
            $encryption_manager = \AMP_Encryption_Manager::instance();
            if (empty($value)) {
                return $encryption_manager->delete_secret('amp_' . $key);
            }
            return $encryption_manager->set_secret('amp_' . $key, $value);
        }

        return update_option('amp_' . $key, $value);
    }

    public static function all() {
        $options = [];
        foreach (array_keys(self::$defaults) as $key) {
            $options[$key] = self::get($key);
        }
        return $options;
    }
} 