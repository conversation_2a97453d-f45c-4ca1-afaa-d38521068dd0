<?php
if (!defined('WPINC')) {
    die;
}

function get_user_data() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Administrator access required'));
    }


    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_user_data')) {
        wp_send_json_error(array('message' => 'Security check failed'));
    }

    if (!isset($_POST['customer_id'])) {
        wp_send_json_error(array('message' => 'User ID is required'));
    }
    $customer_id = intval($_POST['customer_id']);
    $user = get_user_by('id', $customer_id);

    if (!$user) {
        wp_send_json_error(array('message' => 'User not found'));
    }
    $customer_positions = get_user_meta($customer_id, 'ad_positions', true);
    if (!is_array($customer_positions)) {
        $customer_positions = array();
    }

    $telegram_contact = get_user_meta($customer_id, 'telegram_contact', true);
    $show_stats = get_user_meta($customer_id, 'show_stats', true);
    $bypass_checkout = get_user_meta($customer_id, 'bypass_checkout', true);

    $user_data = array(
        'username' => $user->user_login,
        'display_name' => $user->display_name,
        'email' => $user->user_email,
        'positions' => $customer_positions,
        'telegram' => $telegram_contact,
        'show_stats' => (bool)$show_stats,
        'bypass_checkout' => (bool)$bypass_checkout
    );

    wp_send_json_success($user_data);
}
add_action('wp_ajax_get_user_data', 'get_user_data');

function update_user_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'Only administrators can modify user position assignments'));
    }

    if (!isset($_POST['form_data'])) {
        wp_send_json_error(array('message' => 'No data received.'));
        return;
    }

    parse_str($_POST['form_data'], $form_data);

    if (!isset($form_data['user_edit_nonce']) || !wp_verify_nonce($form_data['user_edit_nonce'], 'edit_user_settings')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
        return;
    }

    $user_id = intval($form_data['customer_id']);
    if (!current_user_can('edit_user', $user_id)) {
        wp_send_json_error(array('message' => 'You do not have permission to edit this user.'));
        return;
    }

    $user_data = array('ID' => $user_id);
    if (isset($form_data['display_name'])) {
        $user_data['display_name'] = sanitize_text_field($form_data['display_name']);
    }
    if (isset($form_data['email'])) {
        $user_data['user_email'] = sanitize_email($form_data['email']);
    }
    if (!empty($form_data['password'])) {
        $user_data['user_pass'] = sanitize_text_field($form_data['password']);
    }
    wp_update_user($user_data);

    if (isset($form_data['telegram_contact'])) {
        update_user_meta($user_id, 'telegram_contact', sanitize_text_field($form_data['telegram_contact']));
    }

    $show_stats = isset($form_data['show_stats']) ? 1 : 0;
    update_user_meta($user_id, 'show_stats', $show_stats);

    $bypass_checkout = isset($form_data['bypass_checkout']) ? 1 : 0;
    update_user_meta($user_id, 'bypass_checkout', $bypass_checkout);

    $positions = isset($form_data['ad_positions']) ? (array) $form_data['ad_positions'] : array();
    
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $position_manager->assign_positions_to_user($user_id, $positions);

    wp_send_json_success(array('message' => 'User settings updated successfully!'));
}
add_action('wp_ajax_update_user_settings', 'update_user_settings');

function get_customer_positions() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-ajax')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }
    if (!current_user_can('manage_options') && !current_user_can('amp_advertiser_access')) {
        wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        return;
    }
    if (!isset($_POST['customer_id'])) {
        wp_send_json_error(array('message' => 'User ID is required'));
        return;
    }
    $customer_id = intval($_POST['customer_id']);
    if ($customer_id <= 0) {
        wp_send_json_error(array('message' => 'Invalid customer ID'));
        return;
    }

    if (current_user_can('amp_advertiser_access') && !current_user_can('manage_options')) {
        $current_user_id = get_current_user_id();
        if ($customer_id !== $current_user_id) {
            wp_send_json_error(array('message' => 'You can only access your own data'));
            return;
        }
    }

    $customer_positions = get_user_meta($customer_id, 'ad_positions', true);
    if (!is_array($customer_positions)) {
        $customer_positions = array();
    }

    wp_send_json_success(array('positions' => $customer_positions));
}
add_action('wp_ajax_get_customer_positions', 'get_customer_positions');

function get_customer_telegram() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-ajax')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }

    if (!current_user_can('manage_options') && !current_user_can('amp_advertiser_access')) {
        wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        return;
    }

    if (!isset($_POST['customer_id'])) {
        wp_send_json_error(array('message' => 'User ID is required'));
        return;
    }

    $customer_id = intval($_POST['customer_id']);

    if ($customer_id <= 0) {
        wp_send_json_error(array('message' => 'Invalid customer ID'));
        return;
    }

    if (current_user_can('amp_advertiser_access') && !current_user_can('manage_options')) {
        $current_user_id = get_current_user_id();
        if ($customer_id !== $current_user_id) {
            wp_send_json_error(array('message' => 'You can only access your own data'));
            return;
        }
    }

    $telegram = get_user_meta($customer_id, 'telegram_contact', true);

    wp_send_json_success(array('telegram' => sanitize_text_field($telegram)));
}
add_action('wp_ajax_get_customer_telegram', 'get_customer_telegram');

function get_customer_stats() {
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp-admin-ajax')) {
        wp_send_json_error(array('message' => 'Security check failed'));
        return;
    }

    if (!current_user_can('manage_options') && !current_user_can('amp_advertiser_access')) {
        wp_send_json_error(array('message' => 'You do not have permission to access this data'));
        return;
    }

    if (!isset($_POST['customer_id'])) {
        wp_send_json_error(array('message' => 'User ID is required'));
        return;
    }

    $customer_id = intval($_POST['customer_id']);

    if ($customer_id <= 0) {
        wp_send_json_error(array('message' => 'Invalid customer ID'));
        return;
    }

    if (current_user_can('amp_advertiser_access') && !current_user_can('manage_options')) {
        $current_user_id = get_current_user_id();
        if ($customer_id !== $current_user_id) {
            wp_send_json_error(array('message' => 'You can only access your own data'));
            return;
        }
    }

    $show_stats = get_user_meta($customer_id, 'show_stats', true);

    wp_send_json_success(array('show_stats' => (bool)$show_stats));
}
add_action('wp_ajax_get_customer_stats', 'get_customer_stats');
