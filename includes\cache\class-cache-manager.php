<?php

namespace AdManagementPro\Core {
    if (!defined('WPINC')) {
        die;
    }

    class Cache {
        public static function instance() {
            return \AMP_Cache_Manager::instance();
        }
    }
}

namespace {
    if (!defined('WPINC')) {
        die;
    }

    class AMP_Cache_Manager {

    private static $instance = null;
    private $cache_enabled = true;
    private $cache_prefix = 'amp_';
    private $default_expiration = 86400;
    private $cache_groups = array();
    private $disabled_groups = array();
    private $cache_stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
        'clears' => 0
    ];
    private $realtime_data = [
        'click_statistics',
        'reservation_data',
        'active_timers',
        'cart_data',
        'pending_payments',
        'ga_analytics_realtime',
        'payment_status',
        'session_data'
    ];

    private function __construct() {
        $this->cache_enabled = get_option('amp_cache_enabled', true);
        $this->default_expiration = get_option('amp_cache_default_expiration', 86400);
        $this->disabled_groups = get_option('amp_disabled_cache_groups', array());
        $this->init_cache_groups();
        $this->init_hooks();
        $this->init_auto_clear_hooks();
    }

    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function init_cache_groups() {
        $this->cache_groups = array(
            'position_data' => $this->default_expiration,
            'exchange_rates' => 1800,
            'pricing_data' => 900,
            'ga_analytics' => $this->default_expiration,
            'cloudflare_zones' => $this->default_expiration,
            'user_profiles' => 3600,
            'payment_settings' => 7200,
            'static_content' => $this->default_expiration,
            'rate_limits' => 3600,
            'test_group' => 3600,
        );
    }

    private function init_hooks() {
        add_action('amp_position_updated', array($this, 'clear_positions_cache'));
        add_action('amp_ad_updated', array($this, 'clear_ads_cache'));
    }
    private function init_auto_clear_hooks() {
        add_action('amp_payment_success', [$this, 'clear_cache_on_payment_success']);
        add_action('amp_ad_updated', [$this, 'clear_cache_on_ad_update']);
        add_action('amp_ad_status_changed', [$this, 'clear_cache_on_status_change']);
        add_action('amp_file_uploaded', [$this, 'clear_cache_on_file_upload']);
        add_action('amp_position_created', [$this, 'clear_cache_on_position_change']);
        add_action('amp_position_deleted', [$this, 'clear_cache_on_position_change']);
        add_action('amp_position_updated', [$this, 'clear_cache_on_position_change']);
        add_action('wp_ajax_toggle_ad_status', [$this, 'clear_cache_on_status_change'], 5);
        add_action('wp_ajax_upload_ad_image', [$this, 'clear_cache_on_file_upload'], 5);
        add_action('wp_ajax_save_ad_data', [$this, 'clear_cache_on_ad_update'], 5);
    }

    public function get($key, $group = 'default') {
        if (!$this->cache_enabled || $this->is_realtime_data($group) || $this->is_group_disabled($group)) {
            return false;
        }

        $cache_key = $this->get_cache_key($key, $group);

        $cached_value = wp_cache_get($cache_key, $this->cache_prefix . $group);
        
        if (false !== $cached_value) {
            $this->cache_stats['hits']++;
            return $cached_value;
        }
        
        $this->cache_stats['misses']++;
        return false;
    }

    public function set($key, $data, $expiration = 0, $group = 'default') {
        if (!$this->cache_enabled || $this->is_realtime_data($group) || $this->is_group_disabled($group)) {
            return false;
        }

        $cache_key = $this->get_cache_key($key, $group);

        if ($expiration === 0) {
            $expiration = $this->get_cache_expiration($group);
        }

        $this->add_key_to_group_list($key, $group);
        $this->cache_stats['sets']++;
        
        return wp_cache_set($cache_key, $data, $this->cache_prefix . $group, $expiration);
    }

    public function delete($key, $group = 'default') {
        $cache_key = $this->get_cache_key($key, $group);
        
        $this->remove_key_from_group_list($key, $group);
        $this->cache_stats['deletes']++;

        return wp_cache_delete($cache_key, $this->cache_prefix . $group);
    }

    private function get_group_list_key($group) {
        return 'group_keys_' . $group;
    }

    private function add_key_to_group_list($key, $group) {
        $list_key = $this->get_group_list_key($group);
        $keys = wp_cache_get($list_key, 'amp_cache_groups');
        if (!is_array($keys)) {
            $keys = [];
        }
        if (!in_array($key, $keys)) {
            $keys[] = $key;
            wp_cache_set($list_key, $keys, 'amp_cache_groups', 0);
        }
    }

    private function remove_key_from_group_list($key, $group) {
        $list_key = $this->get_group_list_key($group);
        $keys = wp_cache_get($list_key, 'amp_cache_groups');
        if (is_array($keys)) {
            if (($index = array_search($key, $keys)) !== false) {
                unset($keys[$index]);
                wp_cache_set($list_key, array_values($keys), 'amp_cache_groups', 0);
            }
        }
    }

    private function is_realtime_data($group) {
        return in_array($group, $this->realtime_data);
    }

    private function is_group_disabled($group) {
        return in_array($group, $this->disabled_groups);
    }

    public function enable_group($group) {
        if (!isset($this->cache_groups[$group])) return false;
        $key = array_search($group, $this->disabled_groups);
        if ($key !== false) {
            unset($this->disabled_groups[$key]);
            $this->disabled_groups = array_values($this->disabled_groups);
            update_option('amp_disabled_cache_groups', $this->disabled_groups);
        }
        return true;
    }

    public function disable_group($group) {
        if (!isset($this->cache_groups[$group])) return false;
        if (!in_array($group, $this->disabled_groups)) {
            $this->disabled_groups[] = $group;
            update_option('amp_disabled_cache_groups', $this->disabled_groups);
            $this->clear_group($group);
        }
        return true;
    }

    public function is_group_enabled($group) {
        return !$this->is_group_disabled($group);
    }

    public function clear_group($group) {
        $list_key = $this->get_group_list_key($group);
        $keys_to_delete = wp_cache_get($list_key, 'amp_cache_groups');

        if (is_array($keys_to_delete)) {
            foreach ($keys_to_delete as $key) {
                $this->delete($key, $group);
            }
        }
        
        wp_cache_delete($list_key, 'amp_cache_groups');
        wp_cache_flush_group($this->cache_prefix . $group);

        $this->cache_stats['clears']++;
        do_action('amp_cache_group_cleared', $group);
        return true;
    }

    public function clear_all() {
        wp_cache_flush();

        $this->cache_stats['clears']++;
        do_action('amp_cache_cleared');
        return true;
    }

    public function clear_cache_on_payment_success() {
        $this->clear_group('position_data');
        $this->clear_group('user_dashboard');
        $this->clear_group('pricing_data');
        $this->clear_group('dashboard');
        $this->clear_cloudflare_cache();
    }

    public function clear_cache_on_ad_update() {
        $this->clear_group('position_data');
        $this->clear_group('user_dashboard');
        $this->clear_cloudflare_cache();
    }

    public function clear_cache_on_status_change() {
        $this->clear_group('position_data');
        $this->clear_group('user_dashboard');
        $this->clear_cloudflare_cache();
    }

    public function clear_cache_on_file_upload() {
        $this->clear_group('position_data');
        $this->clear_group('user_dashboard');
        $this->clear_cloudflare_cache();
    }

    public function clear_cache_on_position_change() {
        $this->clear_group('position_data');
        $this->clear_group('user_dashboard');
        $this->clear_group('pricing_data');
        $this->clear_cloudflare_cache();
    }

    private function clear_cloudflare_cache() {
        $email = get_option('amp_cloudflare_email', '');
        $domain = get_option('amp_cloudflare_domain', '');
        $api_token = get_option('amp_cloudflare_api_token', '');

        if (empty($email) || empty($domain) || empty($api_token)) {
            return true;
        }

        $zone_id = $this->get_cloudflare_zone_id($domain, $api_token);
        if (!$zone_id) {
            return false;
        }

        $response = wp_remote_post("https://api.cloudflare.com/client/v4/zones/{$zone_id}/purge_cache", [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_token,
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode(['purge_everything' => true]),
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $success = isset($body['success']) && $body['success'] === true;

        do_action('amp_cache_purged');

        return $success;
    }

    public function get_cloudflare_zone_id($domain, $api_token) {
        $cache_key = 'cloudflare_zone_id_' . md5($domain);
        $cached_zone_id = $this->get($cache_key, 'cloudflare_zones');

        if ($cached_zone_id) {
            return $cached_zone_id;
        }

        $response = wp_remote_get("https://api.cloudflare.com/client/v4/zones?name={$domain}", [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_token,
                'Content-Type' => 'application/json',
            ],
            'timeout' => 30
        ]);

        if (is_wp_error($response)) {
            return false;
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        if (isset($body['success']) && $body['success'] === true && !empty($body['result'])) {
            $zone_id = $body['result'][0]['id'];
            $this->set($cache_key, $zone_id, 86400, 'cloudflare_zones');
            return $zone_id;
        }

        return false;
    }

    public function clear_positions_cache() {
        $this->clear_group('position_data');
    }

    public function clear_ads_cache() {
        $this->clear_group('position_data');
    }

    public function get_cache_stats() {
        $stats = $this->cache_stats;
        $this->reset_stats_if_needed();
        return $stats;
    }

    private function reset_stats_if_needed() {
        $total_operations = $this->cache_stats['hits'] + $this->cache_stats['misses'];
        if ($total_operations > 10000) {
            $this->cache_stats = [
                'hits' => 0,
                'misses' => 0,
                'sets' => 0,
                'deletes' => 0,
                'clears' => 0
            ];
        }
    }

    public function get_cache_info() {
        global $wp_object_cache;
        
        $info = [
            'enabled' => $this->cache_enabled,
            'default_expiration' => $this->default_expiration,
            'stats' => $this->get_cache_stats(),
            'active_backend' => 'Database',
            'dropin_present' => file_exists(WP_CONTENT_DIR . '/object-cache.php'),
            'disabled_dropin_present' => file_exists(WP_CONTENT_DIR . '/object-cache.php.disabled'),
            'available_backends' => [
                'memcached' => extension_loaded('memcached'),
                'redis' => extension_loaded('redis'),
            ],
            'realtime_data' => $this->realtime_data,
        ];
        
        if ($info['dropin_present']) {
            $cache_class = is_object($wp_object_cache) ? get_class($wp_object_cache) : '';

            if (stripos($cache_class, 'Memcached') !== false || stripos($cache_class, 'memcached') !== false) {
                $info['active_backend'] = 'Memcached (Persistent Cache)';
            } elseif (stripos($cache_class, 'Redis') !== false || stripos($cache_class, 'redis') !== false) {
                $info['active_backend'] = 'Redis (Persistent Cache)';
            } elseif (class_exists('AMP_Redis_Object_Cache')) {
                $info['active_backend'] = 'Redis (AMP Custom)';
            } else {
                $dropin_content = file_get_contents(WP_CONTENT_DIR . '/object-cache.php');
                if (stripos($dropin_content, 'redis') !== false) {
                    $info['active_backend'] = 'Redis (Persistent Cache)';
                } elseif (stripos($dropin_content, 'memcached') !== false) {
                    $info['active_backend'] = 'Memcached (Persistent Cache)';
                } else {
                    $info['active_backend'] = 'Unknown Persistent Cache';
                }
            }
        }

        $groups_with_status = array();
        foreach ($this->cache_groups as $group => $expiration) {
            $groups_with_status[$group] = [
                'expiration' => $expiration,
                'enabled' => $this->is_group_enabled($group)
            ];
        }
        $info['groups_with_status'] = $groups_with_status;

        return $info;
    }



    private function get_cache_key($key, $group) {
        return preg_replace('/[^a-zA-Z0-9_\-]/', '', $key);
    }

    private function get_cache_expiration($group) {
        return $this->cache_groups[$group] ?? $this->default_expiration;
    }

    public function is_enabled() {
        return $this->cache_enabled;
    }

    public function enable_cache() {
        update_option('amp_cache_enabled', true);
        $this->cache_enabled = true;
        return true;
    }

    public function disable_cache() {
        update_option('amp_cache_enabled', false);
        $this->cache_enabled = false;
        $this->clear_all();
        return true;
    }

    public function set_default_expiration($seconds) {
        $seconds = intval($seconds);
        if ($seconds <= 0) {
            return false;
        }

        update_option('amp_cache_default_expiration', $seconds);
        $this->default_expiration = $seconds;

        $this->init_cache_groups();
        $this->clear_all();

        return true;
    }
}

}