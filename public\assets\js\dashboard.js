(function () {
  "use strict";

  const $ = jQuery;
  if (typeof jQuery.migrateMute === "undefined") {
    jQuery.migrateMute = true;
  }

  const MAX_RETRIES = 3;
  let sessionCheckInterval = null;
  let warningShown = false;
  let sessionCheckInProgress = false;
  let sessionExpired = false;
  let timers = new Set();
  let intervals = new Set();

  const addTimer = (timer) => timers.add(timer);
  const addInterval = (interval) => intervals.add(interval);
  const clearAllTimers = () => {
    timers.forEach(timer => clearTimeout(timer));
    intervals.forEach(interval => clearInterval(interval));
    timers.clear();
    intervals.clear();
  };

  class EventManager {
    static init() {
      if (typeof window.TimerSystem !== 'undefined' && window.TimerSystem.init) {
        window.TimerSystem.init();
      }
    }

    static cleanup() {
      if (typeof clearAllTimers === 'function') {
        clearAllTimers();
      }
    }

    static restart() {
      this.cleanup();
      this.init();
    }
  }

  window.EventManager = EventManager;

  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  function initThemeToggle() {
    const themeToggle = $("#theme-toggle-checkbox");
    const body = $("body");
    const savedTheme = localStorage.getItem("adDashboardTheme");
    
    if (!savedTheme) {
      const currentHour = new Date().getHours();
      const isNightTime = currentHour >= 18 || currentHour < 6;
      const theme = isNightTime ? "dark" : "light";
      body.toggleClass("dark-mode", isNightTime);
      themeToggle.prop("checked", isNightTime);
      localStorage.setItem("adDashboardTheme", theme);
    } else if (savedTheme === "dark") {
      body.addClass("dark-mode");
      themeToggle.prop("checked", true);
    }
    
    themeToggle.on("change", function () {
      const isDark = $(this).is(":checked");
      body.toggleClass("dark-mode", isDark);
      localStorage.setItem("adDashboardTheme", isDark ? "dark" : "light");
    });
  }

  function initSidebarToggle() {
    const menuToggle = $(".ad-dashboard-menu-toggle");
    const wrapper = $(".ad-dashboard-wrapper");
    const sidebar = $(".ad-dashboard-sidebar");
    let sidebarHoverTimer = null;
    
    if (window.innerWidth > 991) {
      wrapper.addClass("sidebar-open");
    }
    
    const animateSidebar = () => {
      sidebar.addClass("sidebar-animated");
      const timer = setTimeout(() => sidebar.removeClass("sidebar-animated"), 300);
      addTimer(timer);
    };
    
    menuToggle.on("click", function (e) {
      e.stopPropagation();
      wrapper.toggleClass("sidebar-open");
      animateSidebar();
    });
    
    const handleMouseMove = debounce((e) => {
      if (window.innerWidth <= 991) return;
      
      if (e.clientX <= 20 && !wrapper.hasClass("sidebar-open")) {
        if (!sidebarHoverTimer) {
          sidebarHoverTimer = setTimeout(() => {
            wrapper.addClass("sidebar-open");
            animateSidebar();
          }, 200);
          addTimer(sidebarHoverTimer);
        }
      } else {
        if (sidebarHoverTimer) {
          clearTimeout(sidebarHoverTimer);
          sidebarHoverTimer = null;
        }
      }
    }, 50);
    
    $(document).on("mousemove", handleMouseMove);
    
    $(document).on("click", function (e) {
      if (wrapper.hasClass("sidebar-open") && 
          !$(e.target).closest(".ad-dashboard-sidebar, .ad-dashboard-menu-toggle").length) {
        wrapper.removeClass("sidebar-open");
      }
    });
    
    $(".ad-dashboard-nav a").on("click", function () {
      if (window.innerWidth <= 991) {
        wrapper.removeClass("sidebar-open");
      }
    });
    
    $(window).on("resize", debounce(() => {
      if (window.innerWidth > 991 && !wrapper.hasClass("sidebar-open")) {
        wrapper.addClass("sidebar-open");
      }
    }, 250));
  }

  function showMiniPopup(message, type = 'success') {
    $('.mini-popup-notification').remove();
    const typeConfig = {
      'success': { icon: 'fa-check-circle', class: 'success' },
      'error': { icon: 'fa-times-circle', class: 'error' },
      'info': { icon: 'fa-info-circle', class: 'info' },
      'warning': { icon: 'fa-exclamation-triangle', class: 'warning' }
    };
    const config = typeConfig[type] || typeConfig['success'];

    const notification = $(`
      <div class="mini-popup-notification ${config.class}">
        <i class="fas ${config.icon}"></i>
        <span>${message}</span>
      </div>
    `);

    $('body').append(notification);
    
    const showTimer = setTimeout(() => notification.addClass('show'), 100);
    const hideTimer = setTimeout(() => {
      notification.removeClass('show');
      const removeTimer = setTimeout(() => notification.remove(), 400);
      addTimer(removeTimer);
    }, 3500);
    
    addTimer(showTimer);
    addTimer(hideTimer);
  }

  function showSuccessBar(message) {
    $('.success-bar').remove();
    const successBar = $(`
      <div class="success-bar" style="
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #2ecc71, #27ae60);
        color: white;
        padding: 12px 20px;
        text-align: center;
        font-weight: 500;
        z-index: 10000;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        animation: slideInDown 0.3s ease-out;
      ">
        <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
        ${message}
      </div>
    `);
    
    $('body').prepend(successBar);
    const timer = setTimeout(() => {
      successBar.css('animation', 'slideOutUp 0.3s ease-in');
      const removeTimer = setTimeout(() => successBar.remove(), 300);
      addTimer(removeTimer);
    }, 4000);
    addTimer(timer);
  }

  const CartManager = {
    async performCartAction(position, duration, btn, showFullPopup = true) {
      const actionData = {
        url: adDashboardData.ajaxurl,
        type: "POST",
        data: {
          action: "add_to_cart",
          security: adDashboardData.nonce,
          position: position,
          duration: duration,
        }
      };

      if (btn && btn.prop) {
        btn.prop("disabled", true).text("กำลังดำเนินการ...");
      }

      try {
        const response = await $.ajax(actionData);
        
        if (response.success) {
          const cartCount = response.data ? response.data.cart_count : response.cart_count;
          
          this.updateCartCallbacks(cartCount, position);
          createFlyingAnimation(btn);
          this.replaceButton(btn, position);
          
          if (showFullPopup) {
            this.showFullSuccessPopup(position, duration);
          } else {
            this.showSimpleSuccess(position);
          }
        } else {
          this.handleError(response.data?.message || 'ไม่สามารถเพิ่มสินค้าลงในตะกร้าได้', btn);
        }
      } catch (error) {
        this.handleNetworkError(btn);
      }
    },

    updateCartCallbacks(cartCount, position) {
      if (typeof window.updateCartCount === "function") {
        window.updateCartCount(cartCount);
      }
      if (typeof window.refreshCartData === "function") {
        window.refreshCartData();
      }
      if (typeof window.onCartItemAdded === "function") {
        window.onCartItemAdded(position);
      }
    },

    replaceButton(btn, position) {
      if (btn && btn.replaceWith) {
        const inCartBtn = $(`<button class="action-btn btn-success in-cart-btn" onclick="removeFromCart('${position}', this)" data-position="${position}">
          <i class="fas fa-check-circle"></i> อยู่ในตะกร้า
        </button>`);
        btn.replaceWith(inCartBtn);
      }
    },

    showFullSuccessPopup(position, duration) {
      const cartSuccessHtml = `
        <div class="amp-success-popup-container">
          <div class="amp-success-icon">
            <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
              <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
              <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
            </svg>
          </div>
          <div class="amp-success-header"><h2>เพิ่มลงตะกร้าสำเร็จ!</h2></div>
          <div class="amp-success-message">
            <p>เพิ่มป้ายโฆษณา <strong>${position}</strong></p>
            <p>ระยะเวลา <strong>${duration} วัน</strong> ลงในตะกร้าแล้ว</p>
          </div>
          <div class="amp-success-timer">
            <p>กำลังนำทางไปยังตะกร้าใน <strong id="cart-timer-span">5</strong> วินาที...</p>
          </div>
          <div class="amp-success-actions">
            <button class="amp-success-btn amp-success-btn-primary" id="go-to-cart-btn">
              <i class="fas fa-shopping-cart"></i> ไปที่ตะกร้า
            </button>
            <button class="amp-success-btn amp-success-btn-secondary" id="continue-shopping-btn">
              <i class="fas fa-store"></i> ซื้อต่อ
            </button>
          </div>
        </div>
      `;

      Swal.fire({
        html: cartSuccessHtml,
        showConfirmButton: false,
        showCancelButton: false,
        customClass: { popup: 'amp-success-popup' },
        allowOutsideClick: false,
        buttonsStyling: false,
        timer: 5000,
        timerProgressBar: true,
        didOpen: () => {
          const timerSpan = document.getElementById('cart-timer-span');
          const goToCartBtn = document.getElementById('go-to-cart-btn');
          const continueShoppingBtn = document.getElementById('continue-shopping-btn');

          if (timerSpan) {
            const timer = setInterval(() => {
              const secondsLeft = Math.ceil(Swal.getTimerLeft() / 1000);
              timerSpan.textContent = secondsLeft > 0 ? secondsLeft : 0;
            }, 100);
            addInterval(timer);

            Swal.getPopup().addEventListener('close', () => clearInterval(timer));
          }

          goToCartBtn?.addEventListener('click', () => {
            Swal.close();
            if (typeof loadTabContent === 'function') {
              loadTabContent('cart');
            }
          });

          continueShoppingBtn?.addEventListener('click', () => Swal.close());
        }
      }).then((result) => {
        if (result.dismiss === Swal.DismissReason.timer && typeof loadTabContent === 'function') {
          loadTabContent('cart');
        }
      });
    },

    showSimpleSuccess(position) {
      Swal.fire({
        title: '<i class="fas fa-check-circle"></i> เพิ่มลงตะกร้าสำเร็จ!',
        html: `<p>เพิ่มป้ายโฆษณา <strong>${position}</strong> (1 เดือน) ลงในตะกร้าแล้ว</p>`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: { popup: 'amp-popup-container' }
      });
    },

    handleError(message, btn) {
      Swal.fire({
        title: 'เกิดข้อผิดพลาด',
        html: message,
        icon: 'error',
        confirmButtonText: '<i class="fas fa-redo"></i> ลองใหม่อีกครั้ง',
        customClass: {
          popup: 'amp-popup-container amp-popup-error',
          confirmButton: 'amp-btn amp-btn-primary'
        },
        buttonsStyling: false
      });
      if (btn && btn.prop) {
        btn.prop('disabled', false).text('เพิ่มลงตะกร้า');
      }
    },

    handleNetworkError(btn) {
      Swal.fire({
        title: 'การเชื่อมต่อล้มเหลว',
        html: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้<br>กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ตของคุณ',
        icon: 'error',
        confirmButtonText: '<i class="fas fa-redo"></i> ลองใหม่',
        customClass: {
          popup: 'amp-popup-container amp-popup-error',
          confirmButton: 'amp-btn amp-btn-primary'
        },
        buttonsStyling: false
      });
      if (btn && btn.prop) {
        btn.prop('disabled', false).text('เพิ่มลงตะกร้า');
      }
    }
  };

  function showDurationPopup(position, basePrice, btn) {
    let selectedDuration = 30;
    let selectedPrice = basePrice;
    
    Swal.fire({
      title: '<i class="fas fa-spinner fa-spin"></i> กำลังโหลดข้อมูลราคา...',
      allowOutsideClick: false,
      showConfirmButton: false,
      didOpen: () => Swal.showLoading(Swal.getConfirmButton()),
      customClass: { popup: "amp-popup-container" }
    });
    
    $.ajax({
      url: adDashboardData.ajaxurl,
      type: 'POST',
      data: {
        action: 'get_discount_rates',
        security: adDashboardData.nonce,
        position: position
      },
      success: function(response) {
        if (response.success && response.data) {
          if (response.data.popup_type === 'no_popup') {
            CartManager.performCartAction(position, 30, btn, false);
            return;
          }

          const packages = response.data.html ? 
            parsePackagesFromHTML(response.data.html) : 
            createFallbackPackages(basePrice);

          if (packages.length > 0) {
            showPackageSelection(packages, position, btn);
          }
        } else {
          showErrorPopup('ไม่สามารถโหลดข้อมูลราคาได้');
        }
      },
      error: () => showErrorPopup('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้')
    });
  }

  function parsePackagesFromHTML(html) {
    const tempDiv = $('<div>').html(html);
    const packages = [];

    tempDiv.find('.duration-option').each(function() {
      const $option = $(this);
      packages.push({
        duration: parseInt($option.data('days')) || parseInt($option.data('duration')) || 30,
        price: parseFloat($option.data('price')) || 0,
        discount: parseFloat($option.data('discount')) || 0,
        icon: $option.find('.duration-icon').text().trim() || '💎',
        label: $option.find('.duration-label').text().trim() || ($option.data('duration') + ' วัน')
      });
    });

    return packages;
  }

  function createFallbackPackages(basePrice) {
    return [
      { duration: 7, price: basePrice * 1.8, discount: 0, icon: '⚡️', label: '7 วัน' },
      { duration: 30, price: basePrice, discount: 0, icon: '📅', label: '1 เดือน' },
      { duration: 90, price: basePrice * 2.7, discount: 10, icon: '🚀', label: '3 เดือน' },
      { duration: 180, price: basePrice * 5.1, discount: 15, icon: '💎', label: '6 เดือน' }
    ];
  }

  function showPackageSelection(packages, position, btn) {
    const optionsHTML = packages.map(pkg => {
      const price = Math.ceil(pkg.price);
      let tagHTML = '';
      
      if (pkg.duration === 7) {
        tagHTML = `<div class="amp-option-tag tag-trial">ทดลอง</div>`;
      } else if (pkg.discount > 0) {
        tagHTML = `<div class="amp-option-tag tag-discount">ส่วนลด ${pkg.discount}%</div>`;
      } else {
        tagHTML = `<div class="amp-option-tag tag-normal">ราคาปกติ</div>`;
      }

      return `
        <div class="amp-duration-option" data-duration="${pkg.duration}" data-price="${price}">
          <div class="selected-checkmark"><i class="fas fa-check"></i></div>
          <div class="amp-option-icon">${pkg.icon}</div>
          <div class="amp-option-label">${pkg.label}</div>
          <div class="amp-option-price">$${price}</div>
          ${tagHTML}
        </div>
      `;
    }).join('');

    const gridClass = packages.length === 2 ? 'amp-duration-grid amp-duration-grid-2' :
                     packages.length > 4 ? 'amp-duration-grid amp-duration-grid-scrollable' :
                     'amp-duration-grid amp-duration-grid-4';

    Swal.fire({
      title: '<i class="fas fa-calendar-alt"></i> เลือกระยะเวลาโฆษณา',
      html: `<div class="${gridClass}">${optionsHTML}</div>`,
      showCancelButton: true,
      confirmButtonText: '<i class="fas fa-shopping-cart"></i> เพิ่มลงตะกร้า',
      cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
      customClass: {
        popup: 'amp-popup-container',
        confirmButton: 'amp-btn amp-btn-primary',
        cancelButton: 'amp-btn amp-btn-secondary'
      },
      buttonsStyling: false,
      didOpen: () => {
        $('.amp-duration-option').on('click', function() {
          $('.amp-duration-option').removeClass('selected');
          $(this).addClass('selected');
        });
        
        const defaultOption = $('.amp-duration-option[data-duration="30"]').length ? 
                            $('.amp-duration-option[data-duration="30"]') : 
                            $('.amp-duration-option').first();
        defaultOption.addClass('selected');
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const selectedOption = $(".amp-duration-option.selected");
        const duration = selectedOption.data("duration");
        
        if (duration) {
          CartManager.performCartAction(position, duration, btn, true);
        } else {
          showMiniPopup('กรุณาเลือกแพ็กเกจ', 'warning');
        }
      }
    });
  }

  function showErrorPopup(message) {
    Swal.fire({
      title: '<i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาด',
      html: `<div style="text-align: center; padding: 10px 0;">
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 12px;">
          ❌ ${message}
        </div>
        <div style="font-size: 15px; opacity: 0.9;">
          กรุณาลองใหม่อีกครั้งในภายหลัง
        </div>
      </div>`,
      icon: "error",
      confirmButtonText: '<i class="fas fa-redo"></i> ลองใหม่',
      customClass: {
        popup: "theme-error-popup",
        confirmButton: "theme-btn-primary"
      },
      buttonsStyling: false
    });
  }

  function createFlyingAnimation(btn) {
    if (!btn || !btn.length) return;

    const cartIcon = $(".ad-dashboard-cart-icon");
    if (!cartIcon.length) return;

    const btnOffset = btn.offset();
    const cartOffset = cartIcon.offset();

    const flyingItem = $('<div class="flying-item"><i class="fas fa-shopping-cart"></i></div>');
    flyingItem.css({
      position: 'fixed',
      left: btnOffset.left + btn.width() / 2 - 15,
      top: btnOffset.top + btn.height() / 2 - 15,
      zIndex: 9999
    });

    $('body').append(flyingItem);

    const moveTimer = setTimeout(() => {
      flyingItem.css({
        left: cartOffset.left + cartIcon.width() / 2 - 15,
        top: cartOffset.top + cartIcon.height() / 2 - 15,
        transform: 'scale(0.5)',
        opacity: 0.8
      });

      const removeTimer = setTimeout(() => {
        flyingItem.remove();
        cartIcon.addClass('cart-bounce');
        const bounceTimer = setTimeout(() => cartIcon.removeClass('cart-bounce'), 600);
        addTimer(bounceTimer);
      }, 500);
      addTimer(removeTimer);
    }, 100);
    addTimer(moveTimer);
  }

  const CartAPI = {
    async getCart() {
      if (typeof adDashboardData === "undefined") return;
      
      try {
        const response = await $.ajax({
          url: adDashboardData.ajaxurl,
          type: "POST",
          data: {
            action: "get_cart",
            security: adDashboardData.nonce,
          }
        });
        
        if (response && response.success && response.data) {
          if (typeof window.updateCartCount === "function") {
            window.updateCartCount(response.data.cart_count);
          }
          
          if (window.adDashboardData) {
            window.adDashboardData.cart_count = response.data.cart_count;
            window.adDashboardData.cart_items = response.data.cart_items || [];
          }
        }
      } catch (e) {}
    }
  };

  window.refreshCartCount = () => CartAPI.getCart();
  window.refreshCartData = () => CartAPI.getCart();

  function addToCartDirectly(position, duration, btn) {
    CartManager.performCartAction(position, duration, btn, false);
  }

  window.showMiniPopup = showMiniPopup;
  window.showSuccessBar = showSuccessBar;
  window.showDurationPopup = showDurationPopup;
  window.createFlyingAnimation = createFlyingAnimation;
  window.addToCartDirectly = addToCartDirectly;

  function updateCartCount(newCount) {
    const cartCount = $(".ad-dashboard-cart-count");
    const cartIcon = $(".ad-dashboard-cart-icon");

    if (!cartIcon.length) return;

    if (newCount > 0) {
      if (cartCount.length) {
        cartCount.text(newCount).show().addClass("cart-count-update");
        const timer = setTimeout(() => cartCount.removeClass("cart-count-update"), 300);
        addTimer(timer);
      } else {
        const newCountElement = $(`<span class="ad-dashboard-cart-count cart-count-new">${newCount}</span>`);
        cartIcon.append(newCountElement);
        const timer = setTimeout(() => newCountElement.removeClass("cart-count-new"), 10);
        addTimer(timer);
      }
    } else {
      cartCount.text('0').hide();
    }
  }

  function initProfileDropdown() {
    const profileToggle = $("#profile-menu-toggle");
    const profileDropdown = $("#profile-dropdown");

    const showDropdown = () => {
      profileDropdown.addClass("show dropdown-animated");
      const timer = setTimeout(() => profileDropdown.removeClass("dropdown-animated"), 300);
      addTimer(timer);
    };

    profileToggle.on("mouseenter", (e) => {
      e.stopPropagation();
      showDropdown();
    });

    profileDropdown.on("mouseenter", (e) => {
      e.stopPropagation();
      profileDropdown.addClass("show");
    });

    $(document).on("click", function (e) {
      if (!profileToggle.is(e.target) && profileToggle.has(e.target).length === 0 &&
          !profileDropdown.is(e.target) && profileDropdown.has(e.target).length === 0) {
        profileDropdown.removeClass("show");
      }
    });

    $(".ad-dashboard-avatar").on("click", function (e) {
      e.stopPropagation();
      profileDropdown.toggleClass("show");
    });
  }

  function initAjaxNavigation() {
    const navigationSelectors = ".ad-dashboard-nav a, .profile-dropdown-item:not(.ad-dashboard-logout), .ad-dashboard-cart-icon, .overview-manage-btn, .overview-cta-btn, .overview-renew-btn, .ajax-nav-btn";

    $(document).on("click", ".ad-dashboard-logout", function (e) {
      e.preventDefault();
      handleLogout();
    });

    $(document).on("click", navigationSelectors, function (e) {
      if ($(this).attr("target") === "_blank" || $(this).hasClass("ad-dashboard-logout")) {
        return true;
      }
      const href = $(this).attr("href");
      if (href && (href.includes('/wp-admin/') || href.includes('admin.php'))) {
        window.location.href = href;
        return false;
      }
      e.preventDefault();
      let tab;
      if ($(this).hasClass("overview-manage-btn")) {
        tab = $(this).data("tab") || "my-ads";
      } else if ($(this).hasClass("overview-cta-btn")) {
        tab = "buy";
      } else if ($(this).hasClass("overview-renew-btn")) {
        const position = $(this).data('position');
        if (position && typeof window.showDurationPopup === 'function') {
          window.showDurationPopup(position, 0, $(this), true);
        }
        return false;
      } else if ($(this).hasClass("ajax-nav-btn")) {
        tab = $(this).data("tab");
      } else {
        const tabMatch = href.match(/[?&]tab=([^&]*)/);
        tab = tabMatch ? tabMatch[1] : "overview";
      }

      if (history.pushState) {
        const newUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?tab=${tab}`;
        window.history.pushState({ path: newUrl, tab: tab }, "", newUrl);
      }

      loadTabContent(tab);
      $(".ad-dashboard-nav li").removeClass("active");
      $(`.ad-dashboard-nav li a[href*="tab=${tab}"]`).parent().addClass("active");

      if (window.innerWidth <= 991) {
        $(".ad-dashboard-wrapper").removeClass("sidebar-open");
      }
      $("#profile-dropdown").removeClass("show");
    });

    window.addEventListener("popstate", function (event) {
      if (event.state && event.state.tab) {
        loadTabContent(event.state.tab);
        $(".ad-dashboard-nav li").removeClass("active");
        $(`.ad-dashboard-nav li a[href*="tab=${event.state.tab}"]`).parent().addClass("active");
      }
    });

    const currentUrl = window.location.href;
    const tabMatch = currentUrl.match(/[?&]tab=([^&]*)/);
    const currentTab = tabMatch ? tabMatch[1] : "overview";

    if (history.replaceState) {
      history.replaceState({ path: currentUrl, tab: currentTab }, "", currentUrl);
    }
  }

  window.loadTab = function(tab) {
    if (window.TimerSystem && typeof window.TimerSystem.markAjaxNavigation === 'function') {
      window.TimerSystem.markAjaxNavigation();
    }

    if (typeof loadTabContent === 'function') {
      loadTabContent(tab);

      if (history.pushState) {
        const newUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}?tab=${tab}`;
        window.history.pushState({ path: newUrl, tab: tab }, "", newUrl);
      }

      $(".ad-dashboard-nav li").removeClass("active");
      $(`.ad-dashboard-nav li a[href*="tab=${tab}"]`).parent().addClass("active");
    }
  };

  window.goToMyAds = function() {
    if (window.Swal && typeof window.Swal.close === 'function') {
      window.Swal.close();
    }
    if (typeof window.loadTab === 'function') {
      window.loadTab('my-ads');
    } else {
      window.location.href = `${window.location.pathname}?tab=my-ads`;
    }
  };

  function loadTabContent(tab) {
    window.cleanupCurrentTab?.();
    
    if (tab === 'overview') {
      window.overviewChartsInitialized = false;
    }

    const mainContent = $(".ad-dashboard-main");
    mainContent.addClass("loading");

    const tabNames = {
      'overview': 'ภาพรวม',
      'buy': 'ซื้อป้ายโฆษณา',
      'my-ads': 'ป้ายโฆษณาของฉัน',
      'profile': 'โปรไฟล์',
      'cart': 'ตะกร้าสินค้า',
      'purchase-history': 'ประวัติการซื้อ',
      'analytics': 'สถิติเว็บไซต์',
      'admin-contact': 'ติดต่อผู้ดูแล'
    };

    const tabName = tabNames[tab] || tab;

    mainContent.html(`
      <div class="tab-loading-progress">
        <div class="progress-container">
          <div class="progress-header">
            <i class="fas fa-download"></i>
            <span class="progress-title">กำลังโหลด${tabName}...</span>
          </div>
          <div class="progress-bar-container">
            <div class="progress-bar">
              <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text">
              <span id="progress-percentage">0%</span>
              <span id="progress-status">เริ่มต้นการโหลด...</span>
            </div>
          </div>
        </div>
      </div>
    `);

    $(".ad-dashboard-title").html('<i class="fas fa-download"></i> กำลังโหลด...');

    let progress = 0;
    const progressFill = $("#progress-fill");
    const progressPercentage = $("#progress-percentage");
    const progressStatus = $("#progress-status");

    const progressSteps = [
      { percent: 10, status: "เชื่อมต่อเซิร์ฟเวอร์..." },
      { percent: 25, status: "ส่งคำขอข้อมูล..." },
      { percent: 40, status: "รอการตอบกลับ..." },
      { percent: 60, status: "ประมวลผลข้อมูล..." },
      { percent: 80, status: "เตรียมการแสดงผล..." },
      { percent: 95, status: "เกือบเสร็จแล้ว..." }
    ];

    let stepIndex = 0;
    const progressInterval = setInterval(() => {
      if (stepIndex < progressSteps.length) {
        const step = progressSteps[stepIndex];
        progress = step.percent;
        progressFill.css('width', progress + '%');
        progressPercentage.text(progress + '%');
        progressStatus.text(step.status);
        stepIndex++;
      }
    }, 200);
    addInterval(progressInterval);

    $.ajax({
      url: adDashboardData.ajaxurl,
      type: "POST",
      data: {
        action: "load_tab_content",
        tab: tab,
        security: window.adDashboardData?.nonce || adDashboardData.nonce,
      },
      xhr: function() {
        const xhr = new window.XMLHttpRequest();
        xhr.addEventListener("progress", function(evt) {
          if (evt.lengthComputable) {
            const percentComplete = Math.round((evt.loaded / evt.total) * 100);
            if (percentComplete > progress) {
              progress = Math.min(percentComplete, 95);
              progressFill.css('width', progress + '%');
              progressPercentage.text(progress + '%');
              if (percentComplete > 50) {
                progressStatus.text("กำลังรับข้อมูล...");
              }
            }
          }
        }, false);
        return xhr;
      },
      success: function (response) {
        clearInterval(progressInterval);

        progress = 100;
        progressFill.css('width', '100%');
        progressPercentage.text('100%');
        progressStatus.text('โหลดเสร็จสิ้น!');

        const timer = setTimeout(() => {
          if (response.success) {
            if (response.data.redirect) {
              window.open(response.data.redirect, '_blank');
              mainContent.removeClass("loading");
              $(".ad-dashboard-title").text(response.data.title);
              return;
            }

            if (response.data.content && typeof response.data.content === 'string') {
              mainContent.html(response.data.content);
            } else {
              console.error('Invalid content received:', response.data.content);
              mainContent.html('<div class="error-message">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>');
            }
            mainContent.removeClass("loading");
            if (response.data.title && typeof response.data.title === 'string') {
              $(".ad-dashboard-title").text(response.data.title);
            }

            if (response.data.cart_count !== undefined) {
              updateCartCount(response.data.cart_count);
            }

            initTabSpecificFunctions(tab);
            const exchangeTimer = setTimeout(() => updateLiveExchangeRate(), 500);
            addTimer(exchangeTimer);
            window.scrollTo(0, 0);
          } else {
            mainContent.html(`
              <div class="tab-error">
                <i class="fas fa-exclamation-circle"></i>
                <span>${response.data.message || "เกิดข้อผิดพลาดในการโหลดข้อมูล"}</span>
              </div>
            `);
            mainContent.removeClass("loading");
          }
        }, 300);
        addTimer(timer);
      },
      error: function(xhr, status, error) {
        clearInterval(progressInterval);
        progressFill.css('width', '100%').addClass('progress-error');
        progressPercentage.text('Error');
        progressStatus.text('เกิดข้อผิดพลาดในการโหลด');

        const timer = setTimeout(() => {
          mainContent.html(`
            <div class="tab-error">
              <i class="fas fa-exclamation-circle"></i>
              <span>ไม่สามารถโหลดข้อมูลได้ กรุณาลองใหม่อีกครั้ง</span>
              <button onclick="loadTabContent('${tab}')" class="retry-btn">
                <i class="fas fa-redo"></i> ลองใหม่
              </button>
            </div>
          `);
          mainContent.removeClass("loading");
        }, 1000);
        addTimer(timer);
      }
    });
  }

  function initGA4Dashboard() {
    const chartCanvas = document.getElementById("dailyUsersChart");
    if (!chartCanvas) return;

    if (typeof Chart === "undefined") {
      const script = document.createElement("script");
      script.src = "https://cdn.jsdelivr.net/npm/chart.js";
      script.onload = () => initGA4Chart();
      document.head.appendChild(script);
    } else {
      initGA4Chart();
    }
  }

  function initGA4Chart() {
    const chartCanvas = document.getElementById("dailyUsersChart");
    if (!chartCanvas) return;

    if (window.ga4ChartInstance) {
      window.ga4ChartInstance.destroy();
      window.ga4ChartInstance = null;
    }
    
    const ctx = chartCanvas.getContext("2d");
    const datesElement = document.getElementById("ga4-chart-dates");
    const valuesElement = document.getElementById("ga4-chart-values");
    
    if (!datesElement || !valuesElement) return;

    try {
      const dates = JSON.parse(datesElement.getAttribute("data-dates"));
      const values = JSON.parse(valuesElement.getAttribute("data-values"));

      window.ga4ChartInstance = new Chart(ctx, {
        type: "line",
        data: {
          labels: dates,
          datasets: [{
            label: "จำนวนผู้ใช้งานรายวัน",
            data: values,
            backgroundColor: "rgba(54, 162, 235, 0.2)",
            borderColor: "rgba(54, 162, 235, 1)",
            borderWidth: 2,
            pointBackgroundColor: "rgba(54, 162, 235, 1)",
            pointBorderColor: "#fff",
            pointHoverBackgroundColor: "#fff",
            pointHoverBorderColor: "rgba(54, 162, 235, 1)",
            pointRadius: 4,
            pointHoverRadius: 6,
            tension: 0.3,
            fill: true,
          }],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
              labels: { font: { size: 14 } }
            },
            tooltip: {
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              titleFont: { size: 16 },
              bodyFont: { size: 14 },
              padding: 12,
              displayColors: false,
              callbacks: {
                label: function (context) {
                  return `จำนวนผู้ใช้งาน: ${context.parsed.y} คน`;
                }
              }
            }
          },
          scales: {
            x: {
              grid: { display: false },
              ticks: { maxRotation: 45, minRotation: 45 }
            },
            y: {
              beginAtZero: true,
              grid: { color: "rgba(0, 0, 0, 0.05)" },
              ticks: { precision: 0 }
            }
          }
        }
      });
    } catch (error) {}
  }

  function initProfileFormHandlers() {
    if (typeof jQuery === 'undefined') return;
    
    $('.clickable-avatar, .profile-avatar').off('click.avatar').on('click.avatar', function(e) {
      e.preventDefault();
      e.stopPropagation();

      const fileInput = $('<input type="file" accept="image/*" style="display: none;">');
      $('body').append(fileInput);

      fileInput.on('change', function() {
        const file = this.files[0];
        if (file) uploadAvatar(file);
        $(this).remove();
      });

      fileInput.click();
    });
    
    const handleFormSubmit = (formSelector, action, successMessage) => {
      $(formSelector).off('submit.profile').on('submit.profile', function(e) {
        e.preventDefault();
        const formData = $(this).serialize() + `&action=${action}`;
        const $button = $(this).find('button[type="submit"]');
        const originalText = $button.html();
        const $messageContainer = $(formSelector.replace('-form', '-message'));

        $button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังดำเนินการ...');
        $messageContainer.hide();
        
        $.ajax({
          url: adDashboardData.ajaxurl,
          type: 'POST',
          data: formData,
          success: function(response) {
            if (response.success) {
              $messageContainer.removeClass('error').addClass('success')
                .html(`<i class="fas fa-check-circle"></i> ${successMessage}`)
                .slideDown();

              if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup(successMessage, 'success');
              }

              if (action === 'update_password') {
                $(formSelector)[0].reset();
              }

              const timer = setTimeout(() => $messageContainer.slideUp(), 5000);
              addTimer(timer);
            } else {
              const errorMessage = (response.data && typeof response.data.message === 'string')
                ? response.data.message
                : 'เกิดข้อผิดพลาด';
              $messageContainer.removeClass('success').addClass('error')
                .html(`<i class="fas fa-exclamation-circle"></i> ${errorMessage}`)
                .slideDown();

              if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup(response.data.message || 'เกิดข้อผิดพลาด', 'error');
              }
            }
          },
          error: function() {
            const errorMsg = 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้';
            $messageContainer.removeClass('success').addClass('error')
              .html(`<i class="fas fa-exclamation-circle"></i> ${errorMsg}`)
              .slideDown();

            if (typeof window.showMiniPopup === 'function') {
              window.showMiniPopup(errorMsg, 'error');
            }
          },
          complete: function() {
            $button.prop('disabled', false).html(originalText);
          }
        });
      });
    };

    handleFormSubmit('#profile-form', 'update_profile', 'บันทึกข้อมูลเรียบร้อยแล้ว');
    handleFormSubmit('#password-form', 'update_password', 'เปลี่ยนรหัสผ่านเรียบร้อยแล้ว');
  }

  window.initProfileFormHandlers = initProfileFormHandlers;

  function uploadAvatar(file) {
    if (!file.type.match('image.*')) {
      showMiniPopup('กรุณาเลือกไฟล์รูปภาพเท่านั้น', 'error');
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      showMiniPopup('ขนาดไฟล์ต้องไม่เกิน 2MB', 'error');
      return;
    }
    
    const formData = new FormData();
    formData.append('avatar', file);
    formData.append('action', 'upload_avatar');
    formData.append('security', adDashboardData.nonce);
    
    showMiniPopup('กำลังอัพโหลดรูปภาพ...', 'info');
    
    $.ajax({
      url: adDashboardData.ajaxurl,
      type: 'POST',
      data: formData,
      processData: false,
      contentType: false,
      success: function(response) {
        if (response.success) {
          showMiniPopup('อัพโหลดรูปภาพเรียบร้อยแล้ว', 'success');
          if (response.data && response.data.avatar_url) {
            $('.profile-avatar img, .ad-dashboard-avatar img').attr('src', response.data.avatar_url + '?v=' + Date.now());
            $('.avatar-placeholder').replaceWith(`<img src="${response.data.avatar_url}?v=${Date.now()}" alt="Profile Avatar">`);

            const headerAvatar = $('.ad-dashboard-avatar img');
            if (headerAvatar.length) {
              headerAvatar.attr('src', response.data.avatar_url + '?v=' + Date.now());
            }
          }
        } else {
          showMiniPopup(response.data?.message || 'เกิดข้อผิดพลาดในการอัพโหลด', 'error');
        }
      },
      error: function() {
        showMiniPopup('ไม่สามารถอัพโหลดรูปภาพได้', 'error');
      }
    });
  }

  function initTabSpecificFunctions(tab) {
    const tabFunctions = {
      "buy": () => typeof window.initBuyPageFunctions === "function" && window.initBuyPageFunctions(),
      "profile": () => {
        typeof window.initLocalProfileHandlers === 'function' && window.initLocalProfileHandlers();
        typeof initProfileFormHandlers === 'function' && initProfileFormHandlers();
      },
      "cart": () => typeof window.initGlobalCartHandlers === 'function' && window.initGlobalCartHandlers(),
      "analytics": () => typeof initGA4Dashboard === "function" && initGA4Dashboard()
    };

    if (tabFunctions[tab]) {
      tabFunctions[tab]();
    }
  }

  function initGlobalEventBindings() {
    $(document).off('click.global', '.add-to-cart-btn').on('click.global', '.add-to-cart-btn', function(e) {
      e.preventDefault();
      const btn = $(this);
      const position = btn.data('position');
      const basePrice = parseFloat(btn.closest('.buy-card').data('price')) || 0;

      if (typeof window.showDurationPopup === 'function') {
        window.showDurationPopup(position, basePrice, btn);
      }
    });

    $(document).off('click.global', '.action-renew').on('click.global', '.action-renew', function(e) {
      e.preventDefault();
      const position = $(this).data('position');

      if (position && typeof window.showDurationPopup === 'function') {
        window.showDurationPopup(position, 0, $(this));
      }
    });
  }

  $(document).ready(function () {
    try {
      initThemeToggle();
      initSidebarToggle();
      initProfileDropdown();
      initAjaxNavigation();
      initGlobalEventBindings();
      
      if (typeof refreshCartCount === "function") {
        refreshCartCount();
      }
      
      const initialTab = new URLSearchParams(window.location.search).get('tab') || 'overview';
      initTabSpecificFunctions(initialTab);
      loadTabContent(initialTab);
      document.body.classList.toggle('sidebar-collapsed');
      
      const exchangeTimer = setTimeout(updateLiveExchangeRate, 1000);
      addTimer(exchangeTimer);

      const syncInterval = setInterval(syncSecurityDataPeriodically, 10 * 60 * 1000);
      addInterval(syncInterval);

      if (typeof EventManager !== 'undefined' && EventManager.init) {
        EventManager.init();
      }
    } catch (e) {}
  });

  function updateLiveExchangeRate() {
    const rateContainer = document.querySelector('.exchange-rate-display-container');
    const rateValueEl = document.getElementById('live-exchange-rate');

    if (!rateValueEl || !window.adDashboardData || !window.adDashboardData.ajaxurl || !window.adDashboardData.nonce) {
      return;
    }

    fetch(window.adDashboardData.ajaxurl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        'action': 'amp_get_live_exchange_rate',
        'security': window.adDashboardData.nonce
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success && data.data && data.data.rate) {
        rateValueEl.textContent = data.data.rate;
        if (rateContainer) {
          rateContainer.classList.add('updated');
          const timer = setTimeout(() => rateContainer.classList.remove('updated'), 2000);
          addTimer(timer);
        }
      } else {
        rateValueEl.textContent = 'N/A';
      }
    })
    .catch(error => {
      rateValueEl.textContent = 'Error';
    });
  }

  const SessionManager = {
    isAdmin: () => window.adDashboardData?.is_admin,
    
    init() {
      if (this.isAdmin()) {
        return;
      }
      
      if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
      }

      sessionCheckInterval = setInterval(() => this.unifiedSessionCheck(), 90000);
      addInterval(sessionCheckInterval);
    },

    async unifiedSessionCheck() {
      if (!window.adDashboardData || !window.adDashboardData.ajaxurl || 
          sessionCheckInProgress || sessionExpired || this.isAdmin()) {
        return;
      }

      sessionCheckInProgress = true;

      try {
        const response = await fetch(window.adDashboardData.ajaxurl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
          },
          body: 'action=amp_check_session',
          credentials: 'same-origin'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        sessionCheckInProgress = false;

        if (!data.success) {
          sessionExpired = true;
          clearInterval(sessionCheckInterval);

          if (data.data && data.data.force_reload) {
            window.location.href = window.adDashboardData.loginUrl || '/login/?error=session_expired';
            return;
          }

          if (typeof window.showFullScreenLoginOverlay === 'function') {
            window.showFullScreenLoginOverlay();
          } else {
            window.location.href = '/login/?error=session_expired';
          }
          return;
        }

        if (data.data.is_admin) {
          clearInterval(sessionCheckInterval);
          sessionCheckInterval = null;
          return;
        }

        const remainingTime = data.data.remaining_time;

        if (remainingTime <= 300 && !warningShown) {
          this.showSessionWarning(remainingTime);
          warningShown = true;
        }

        if (remainingTime <= 0) {
          sessionExpired = true;
          clearInterval(sessionCheckInterval);
          if (typeof window.showFullScreenLoginOverlay === 'function') {
            window.showFullScreenLoginOverlay();
          } else {
            window.location.href = '/login/?error=session_expired';
          }
        }
      } catch (error) {
        sessionCheckInProgress = false;

        if (error.message.includes('HTTP 403') || error.message.includes('HTTP 401') ||
            error.name === 'TypeError' && error.message.includes('fetch')) {
          sessionExpired = true;
          clearInterval(sessionCheckInterval);
          if (typeof window.showFullScreenLoginOverlay === 'function') {
            window.showFullScreenLoginOverlay();
          } else {
            window.location.href = '/login/?error=session_expired';
          }
        }
      }
    },

    restart() {
      if (this.isAdmin()) return;
      
      this.init();
      const timer = setTimeout(() => this.unifiedSessionCheck(), 5000);
      addTimer(timer);
    },

    showSessionWarning(remainingTime) {
      const minutes = Math.ceil(remainingTime / 60);
      const message = `เซสชันของคุณจะหมดอายุใน ${minutes} นาที กรุณาบันทึกงานของคุณ`;

      if (typeof Swal !== 'undefined') {
        Swal.fire({
          title: '',
          html: `
            <div class="modern-session-warning">
              <div class="warning-icon-container">
                <div class="warning-icon-bg">
                  <i class="fas fa-hourglass-half warning-icon"></i>
                </div>
              </div>
              <h2 class="warning-title">แจ้งเตือนเซสชัน</h2>
              <p class="warning-message">เซสชันของคุณจะหมดอายุใน <strong>${minutes} นาที</strong></p>
              <div class="warning-info-box">
                <i class="fas fa-info-circle"></i>
                <span>กรุณาบันทึกงานของคุณและต่ออายุเซสชัน</span>
              </div>
            </div>
          `,
          showCancelButton: true,
          confirmButtonText: '<i class="fas fa-refresh"></i> ต่ออายุเซสชัน',
          cancelButtonText: '<i class="fas fa-sign-out-alt"></i> ออกจากระบบ',
          timer: 15000,
          timerProgressBar: true,
          customClass: {
            popup: 'modern-session-popup',
            confirmButton: 'modern-btn modern-btn-primary',
            cancelButton: 'modern-btn modern-btn-secondary',
            actions: 'modern-actions',
            timerProgressBar: 'modern-timer-bar'
          },
          buttonsStyling: false,
          allowOutsideClick: false,
          backdrop: 'rgba(0, 0, 0, 0.8)',
          showClass: { popup: 'animate__animated animate__zoomIn animate__faster' },
          hideClass: { popup: 'animate__animated animate__zoomOut animate__faster' }
        }).then((result) => {
          if (result.isConfirmed) {
            this.extendSession();
          } else {
            sessionExpired = true;
            clearInterval(sessionCheckInterval);
            const timer = setTimeout(() => {
              if (typeof window.showFullScreenLoginOverlay === 'function') {
                window.showFullScreenLoginOverlay();
              } else {
                window.location.href = '/login/?error=session_expired';
              }
            }, 500);
            addTimer(timer);
          }
        });
      } else {
        if (confirm(message + '\n\nคลิก OK เพื่อต่ออายุเซสชัน หรือ Cancel เพื่อปิด')) {
          this.extendSession();
        } else {
          const timer = setTimeout(() => {
            if (typeof window.showFullScreenLoginOverlay === 'function') {
              window.showFullScreenLoginOverlay();
            } else {
              window.location.href = '/login/?error=session_expired';
            }
          }, 500);
          addTimer(timer);
        }
      }
    },

    async extendSession() {
      try {
        const response = await fetch(window.adDashboardData.ajaxurl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
          },
          body: 'action=amp_extend_session&security=' + encodeURIComponent(adDashboardData.nonce),
          credentials: 'same-origin'
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
          warningShown = false;
          sessionExpired = false;

          if (!sessionCheckInterval) {
            sessionCheckInterval = setInterval(() => this.unifiedSessionCheck(), 90000);
            addInterval(sessionCheckInterval);
          }

          const extensionMinutes = data.data?.extension_minutes || 3;

          showMiniPopup(
            `เซสชันถูกขยายเวลา ${extensionMinutes} นาที`,
            'success'
          );
          return true;
        } else {
          sessionExpired = true;
          clearInterval(sessionCheckInterval);
          if (typeof window.showFullScreenLoginOverlay === 'function') {
            window.showFullScreenLoginOverlay();
          } else {
            window.location.href = '/login/?error=session_expired';
          }
          return false;
        }
      } catch (error) {
        sessionExpired = true;
        clearInterval(sessionCheckInterval);
        if (typeof window.showFullScreenLoginOverlay === 'function') {
          window.showFullScreenLoginOverlay();
        } else {
          window.location.href = '/login/?error=session_expired';
        }
        return false;
      }
    }
  };

  document.addEventListener('DOMContentLoaded', function() {
    if (SessionManager.isAdmin()) {
      return;
    }

    SessionManager.init();

    const urlParams = new URLSearchParams(window.location.search);
    const isGoogleCallback = urlParams.has('action') && urlParams.get('action') === 'google_callback';

    if (isGoogleCallback) {
      const timer = setTimeout(() => checkAndRefreshNoncesAfterGoogleLogin(), 1000);
      addTimer(timer);
    }
  });

  window.showFullScreenLoginOverlay = async function showFullScreenLoginOverlay() {
    const existingOverlay = document.getElementById('amp-login-overlay');
    if (existingOverlay) {
      const forms = existingOverlay.querySelectorAll('form');
      forms.forEach(form => {
        const newForm = form.cloneNode(true);
        form.parentNode.replaceChild(newForm, form);
      });
      document.body.removeChild(existingOverlay);
    }

    await refreshSecurityDataForOverlay();

    const overlay = document.createElement('div');
    overlay.id = 'amp-login-overlay';
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 999999;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(5px);
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    createLoginForm(overlay);
    document.body.appendChild(overlay);
  }

  function createLoginForm(overlay) {
    const dashboardData = window.adDashboardData || {};
    const authData = window.ampAuthData || {};

    const googleEnabled = dashboardData.google_login_enabled || authData.googleLoginEnabled || false;
    const turnstileEnabled = dashboardData.turnstile_enabled || authData.turnstileEnabled || false;
    const turnstileSiteKey = dashboardData.turnstile_site_key || authData.turnstileSiteKey || '';
    const googleClientId = dashboardData.google_client_id || authData.googleClientId || '';

    window.overlayTurnstileToken = turnstileEnabled ? null : 'not_required';

    if (turnstileEnabled && !turnstileSiteKey) {
      window.overlayTurnstileToken = 'bypass_missing_key';
    }

    if (googleEnabled && !googleClientId) {
      console.warn('AMP: Google login enabled but client ID missing');
    }

    const loginContainer = document.createElement('div');
    loginContainer.style.cssText = `
      background: white;
      border-radius: 16px;
      padding: 40px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      max-width: 450px;
      width: 90%;
    `;

    let googleLoginHTML = '';
    if (googleEnabled && googleClientId) {
      googleLoginHTML = `
        <div class="google-login-section" style="margin-bottom: 25px;">
          <button type="button" id="overlay-google-login" class="google-login-button"
                  style="width: 100%; padding: 12px 20px; background: #fff; border: 1px solid #dadce0; border-radius: 8px; font-size: 14px; font-weight: 500; color: #3c4043; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 10px;"
                  onmouseover="this.style.background='#f8f9fa'; this.style.borderColor='#c6c6c6'"
                  onmouseout="this.style.background='#fff'; this.style.borderColor='#dadce0'">
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            เข้าสู่ระบบด้วย Google
          </button>
        </div>
        <div class="social-login-divider" style="margin: 20px 0; position: relative; text-align: center;">
          <span style="background: #fff; padding: 0 15px; color: #666; font-size: 12px; font-weight: 500; position: relative; z-index: 1;">หรือ</span>
          <div style="position: absolute; top: 50%; left: 0; right: 0; height: 1px; background: #e0e0e0; z-index: 0;"></div>
        </div>
      `;
    }

    let turnstileHTML = '';
    if (turnstileEnabled && turnstileSiteKey) {
      turnstileHTML = `
        <div class="ad-login-captcha" id="overlay-turnstile-container" style="margin: 20px 0; text-align: center;">
          <div id="overlay-turnstile-widget"></div>
        </div>
      `;
    }

    const formHTML = `
      <div class="ad-login-header" style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #333; margin: 0; font-size: 1.8rem; font-weight: 600;">เข้าสู่ระบบ</h1>
        <p style="color: #666; margin: 10px 0 0 0; font-size: 14px;">เซสชันหมดอายุ กรุณาเข้าสู่ระบบใหม่</p>
      </div>
      ${googleLoginHTML}
      <form id="overlay-login-form" style="margin: 0;">
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">ชื่อผู้ใช้หรืออีเมล</label>
          <div style="position: relative;">
            <input type="text" name="username" required
                   style="width: 100%; padding: 12px 15px 12px 45px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;"
                   onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 2px rgba(0, 123, 255, 0.25)'"
                   onblur="this.style.borderColor='#ddd'; this.style.boxShadow='none'">
            <i class="fas fa-user" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">รหัสผ่าน</label>
          <div style="position: relative;">
            <input type="password" name="password" required
                   style="width: 100%; padding: 12px 15px 12px 45px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; transition: border-color 0.3s ease; box-sizing: border-box;"
                   onfocus="this.style.borderColor='#007bff'; this.style.boxShadow='0 0 0 2px rgba(0, 123, 255, 0.25)'"
                   onblur="this.style.borderColor='#ddd'; this.style.boxShadow='none'">
            <i class="fas fa-lock" style="position: absolute; left: 15px; top: 50%; transform: translateY(-50%); color: #666; font-size: 16px;"></i>
          </div>
        </div>

        <div style="margin-bottom: 20px; display: flex; align-items: center; font-size: 14px; color: #666;">
          <input type="checkbox" name="remember_me" style="margin-right: 8px; width: auto; padding: 0;">
          <label style="margin: 0; font-weight: normal; cursor: pointer;">จดจำการเข้าสู่ระบบ</label>
        </div>
        ${turnstileHTML}
        <button type="submit" id="overlay-login-btn"
                style="width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);"
                onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(102, 126, 234, 0.3)'"
                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.2)'">
          เข้าสู่ระบบ
        </button>

        <div style="text-align: center; margin-top: 20px;">
          <a href="/login/?tab=forgot-password" style="color: #007bff; text-decoration: none; font-size: 14px;"
             onmouseover="this.style.textDecoration='underline'"
             onmouseout="this.style.textDecoration='none'">
            ลืมรหัสผ่าน?
          </a>
        </div>

        <input type="hidden" name="amp_login_nonce" value="${window.adDashboardData?.nonce || ''}">
      </form>
    `;

    loginContainer.innerHTML = formHTML;
    overlay.appendChild(loginContainer);
    setupLoginFormHandler(overlay, loginContainer);

    if (turnstileEnabled) {
      loadTurnstileForOverlay(turnstileSiteKey);
    } else {
      window.overlayTurnstileToken = 'not_required';
    }

    if (googleEnabled && googleClientId) {
      setupGoogleLoginForOverlay(googleClientId);
    }

    const timer = setTimeout(() => updateOverlayButtonStates(), 200);
    addTimer(timer);
  }

  function setupLoginFormHandler(overlay, loginContainer) {
    const form = loginContainer.querySelector('#overlay-login-form');
    const submitBtn = loginContainer.querySelector('#overlay-login-btn');
    if (!form) return;
    
    form.addEventListener('submit', function(e) {
      e.preventDefault();
      handleOverlayLogin(this, submitBtn, overlay);
    });
  }

  async function handleOverlayLogin(form, submitBtn, overlay, retryCount = 0) {
    const readiness = checkOverlaySecurityReadiness();

    if (!readiness.nonces) {
      if (typeof showMiniPopup === 'function') {
        showMiniPopup('ข้อมูล security ไม่พร้อม กรุณารอสักครู่', 'error');
      }
      return;
    }

    if (!readiness.turnstile) {
      if (typeof showMiniPopup === 'function') {
        showMiniPopup('กรุณายืนยันตัวตนด้วย Turnstile ก่อน', 'error');
      }
      return;
    }

    const maxRetries = MAX_RETRIES;
    const formData = new FormData(form);
    formData.append('action', 'amp_login');

    const loginNonce = window.adDashboardData?.nonce ||
                      window.ampAuthData?.loginNonce;

    if (!formData.has('amp_login_nonce') && loginNonce) {
      formData.append('amp_login_nonce', loginNonce);
    }

    if (window.overlayTurnstileToken &&
        window.overlayTurnstileToken !== 'not_required' &&
        window.overlayTurnstileToken !== 'bypass_missing_key') {
      formData.append('cf-turnstile-response', window.overlayTurnstileToken);
    }
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังเข้าสู่ระบบ...';
    
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        body: formData,
        credentials: 'same-origin',
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        submitBtn.innerHTML = '<i class="fas fa-check"></i> สำเร็จ!';
        submitBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
        showMiniPopup('เข้าสู่ระบบสำเร็จ! กำลังรีเฟรชข้อมูล...', 'success');
        
        try {
          const syncSuccess = await syncSessionStateAfterLogin(data);

          if (syncSuccess) {
            showMiniPopup('เข้าสู่ระบบเรียบร้อย! ข้อมูลได้รับการอัพเดทแล้ว', 'success');
            const timer = setTimeout(() => document.body.removeChild(overlay), 800);
            addTimer(timer);
          } else {
            throw new Error('Session sync failed');
          }
        } catch (error) {
          showMiniPopup('เข้าสู่ระบบสำเร็จ กำลังอัพเดทข้อมูล...', 'success');
          const timer = setTimeout(() => document.body.removeChild(overlay), 1000);
          addTimer(timer);
        }
        return;
      }

      const errorMessage = data.data?.message || data.message || 'เข้าสู่ระบบไม่สำเร็จ';
      const errorCode = data.data?.error_code || data.error_code;
      const retryAllowed = data.data?.retry_allowed || data.retry_allowed;
      const freshNonces = data.data?.nonces || data.nonces;
      const requiresManualAction = data.data?.requires_manual_action || data.requires_manual_action;

      if (requiresManualAction) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'เข้าสู่ระบบ';
        showMiniPopup(errorMessage, 'error');
        return;
      }

      if ((retryAllowed || isDashboardRecoverableError(errorMessage) || errorCode === 'NONCE_VERIFICATION_FAILED') && retryCount < maxRetries) {
        if (errorCode === 'NONCE_VERIFICATION_FAILED') {
          showMiniPopup(`กำลังขอ security token ใหม่... (ครั้งที่ ${retryCount + 1}/${maxRetries})`, 'info');
        } else {
          showMiniPopup(`กำลังรีเฟรชข้อมูลความปลอดภัย... (ครั้งที่ ${retryCount + 1}/${maxRetries})`, 'info');
        }

        if (freshNonces) {
          updateDashboardSecurityData(freshNonces);
        }

        const recoverySuccess = await attemptDashboardRecovery(errorMessage, retryCount, errorCode);
        if (recoverySuccess) {
          const timer = setTimeout(() => handleOverlayLogin(form, submitBtn, overlay, retryCount + 1), 1000);
          addTimer(timer);
          return;
        }
      }

      submitBtn.disabled = false;
      submitBtn.innerHTML = 'เข้าสู่ระบบ';
      showMiniPopup(errorMessage, 'error');

    } catch (error) {
      if (retryCount < maxRetries) {
        submitBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ลองใหม่... (${retryCount + 1}/${maxRetries})`;
        showMiniPopup(`กำลังลองใหม่... (${retryCount + 1}/${maxRetries})`, 'info');
        const timer = setTimeout(() => handleOverlayLogin(form, submitBtn, overlay, retryCount + 1), 1500);
        addTimer(timer);
        return;
      }

      submitBtn.disabled = false;
      submitBtn.innerHTML = 'เข้าสู่ระบบ';
      submitBtn.style.background = 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)';
      showMiniPopup('ไม่สามารถเชื่อมต่อเซิร์ฟเวอร์ได้ กรุณาลองใหม่', 'error');
    }
  }

  function loadTurnstileForOverlay(siteKey) {
    if (!siteKey) {
      window.overlayTurnstileToken = 'bypass_missing_key';
      enableOverlaySubmitButton();
      return;
    }

    if (typeof window.turnstile === 'undefined' && !window.turnstileLoading) {
      window.turnstileLoading = true;
      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        window.turnstileLoading = false;
        const timer = setTimeout(() => renderOverlayTurnstile(siteKey), 200);
        addTimer(timer);
      };
      script.onerror = () => {
        window.turnstileLoading = false;
        window.overlayTurnstileToken = 'bypass_csp_restriction';
        enableOverlaySubmitButton();
      };
      document.head.appendChild(script);
    } else if (typeof window.turnstile !== 'undefined') {
      const timer = setTimeout(() => renderOverlayTurnstile(siteKey), 100);
      addTimer(timer);
    } else if (window.turnstileLoading) {
      const checkInterval = setInterval(() => {
        if (!window.turnstileLoading) {
          clearInterval(checkInterval);
          if (typeof window.turnstile !== 'undefined') {
            renderOverlayTurnstile(siteKey);
          }
        }
      }, 100);
      const timer = setTimeout(() => clearInterval(checkInterval), 5000);
      addTimer(timer);
    }
  }

  function renderOverlayTurnstile(siteKey) {
    const container = document.getElementById('overlay-turnstile-widget');
    if (!container || typeof window.turnstile === 'undefined') {
      window.overlayTurnstileToken = 'bypass_render_failed';
      enableOverlaySubmitButton();
      return;
    }

    try {
      if (container.innerHTML.trim()) {
        container.innerHTML = '';
      }

      window.turnstile.render(container, {
        sitekey: siteKey,
        theme: document.body.classList.contains('dark-mode') ? 'dark' : 'light',
        callback: function(token) {
          window.overlayTurnstileToken = token;
          enableOverlaySubmitButton();
        },
        'expired-callback': function() {
          window.overlayTurnstileToken = null;
          disableOverlaySubmitButton();
        },
        'error-callback': function() {
          window.overlayTurnstileToken = 'bypass_turnstile_error';
          enableOverlaySubmitButton();
        }
      });

      disableOverlaySubmitButton();
    } catch (error) {
      window.overlayTurnstileToken = 'bypass_render_exception';
      enableOverlaySubmitButton();
    }
  }

  function enableOverlaySubmitButton() {
    const timer = setTimeout(() => updateOverlayButtonStates(), 50);
    addTimer(timer);
  }

  function disableOverlaySubmitButton() {
    const timer = setTimeout(() => updateOverlayButtonStates(), 50);
    addTimer(timer);
  }

  function checkOverlaySecurityReadiness() {
    const dashboardData = window.adDashboardData || {};
    const authData = window.ampAuthData || {};

    const readinessChecks = {
      nonces: false,
      turnstile: false,
      google: false,
      overall: false
    };

    const loginNonce = dashboardData.login_nonce || authData.loginNonce || dashboardData.nonce;
    readinessChecks.nonces = !!loginNonce;

    if (dashboardData.turnstile_enabled || authData.turnstileEnabled) {
      const turnstileSiteKey = dashboardData.turnstile_site_key || authData.turnstileSiteKey;
      if (turnstileSiteKey) {
        readinessChecks.turnstile = (window.overlayTurnstileToken !== null);
      } else {
        readinessChecks.turnstile = (window.overlayTurnstileToken === 'bypass_missing_key');
      }
    } else {
      readinessChecks.turnstile = (window.overlayTurnstileToken === 'not_required');
    }

    if (dashboardData.google_login_enabled || authData.googleLoginEnabled) {
      const googleClientId = dashboardData.google_client_id || authData.googleClientId;
      readinessChecks.google = !!googleClientId;
    } else {
      readinessChecks.google = true;
    }

    readinessChecks.overall = readinessChecks.nonces && readinessChecks.turnstile && readinessChecks.google;

    return readinessChecks;
  }

  function updateOverlayButtonStates() {
    const readiness = checkOverlaySecurityReadiness();

    const submitBtn = document.getElementById('overlay-login-btn');
    const googleBtn = document.getElementById('overlay-google-login');

    if (submitBtn) {
      if (readiness.nonces && readiness.turnstile) {
        submitBtn.disabled = false;
        submitBtn.style.opacity = '1';
        submitBtn.title = '';
      } else {
        submitBtn.disabled = true;
        submitBtn.style.opacity = '0.6';

        let reasons = [];
        if (!readiness.nonces) reasons.push('ข้อมูล security ไม่พร้อม');
        if (!readiness.turnstile) reasons.push('รอการยืนยัน Turnstile');
        submitBtn.title = reasons.join(', ');
      }
    }

    if (googleBtn) {
      if (readiness.nonces && readiness.google && readiness.turnstile) {
        googleBtn.disabled = false;
        googleBtn.style.opacity = '1';
        googleBtn.title = '';
      } else {
        googleBtn.disabled = true;
        googleBtn.style.opacity = '0.6';

        let reasons = [];
        if (!readiness.nonces) reasons.push('ข้อมูล security ไม่พร้อม');
        if (!readiness.google) reasons.push('Google Client ID ไม่ได้ตั้งค่า');
        if (!readiness.turnstile) reasons.push('รอการยืนยัน Turnstile');
        googleBtn.title = reasons.join(', ');
      }
    }

    return readiness.overall;
  }

  function setupGoogleLoginForOverlay(clientId) {
    const timer = setTimeout(() => {
      const googleBtn = document.getElementById('overlay-google-login');
      if (!googleBtn) return;

      if (!clientId) {
        googleBtn.disabled = true;
        googleBtn.style.opacity = '0.6';
        googleBtn.title = 'Google Client ID ไม่ได้ตั้งค่า';
        return;
      }

      googleBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const dashboardData = window.adDashboardData || {};

        if (dashboardData.turnstile_enabled &&
            window.overlayTurnstileToken === null) {
          showMiniPopup('กรุณายืนยันตัวตนด้วย Cloudflare Turnstile ก่อน', 'error');
          return;
        }

        if (!clientId) {
          showMiniPopup('การตั้งค่า Google Login ไม่สมบูรณ์', 'error');
          return;
        }

        googleBtn.disabled = true;
        googleBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังเชื่อมต่อ Google...';

        initiateGoogleLoginForOverlay(clientId, googleBtn);
      });
    }, 100);
    addTimer(timer);
  }

  function initiateGoogleLoginForOverlay(clientId, googleBtn) {
    try {
      const dashboardData = window.adDashboardData || {};

      let currentUrl = window.location.protocol + '//' + window.location.host + window.location.pathname;
      if (!currentUrl.endsWith('/')) {
        currentUrl += '/';
      }
      const redirectUri = currentUrl + '?action=google_callback';
      const nonce = dashboardData.login_nonce || dashboardData.nonce || '';

      const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${encodeURIComponent(clientId)}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=${encodeURIComponent('https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile')}&` +
        `response_type=code&` +
        `state=${encodeURIComponent(nonce)}&` +
        `access_type=online&` +
        `prompt=select_account`;

      window.location.href = authUrl;
    } catch (error) {
      googleBtn.disabled = false;
      googleBtn.innerHTML = '<svg width="18" height="18" viewBox="0 0 24 24">...</svg> เข้าสู่ระบบด้วย Google';
      showMiniPopup('เกิดข้อผิดพลาดในการเชื่อมต่อ Google', 'error');
    }
  }

  window.showLoginPopup = () => window.showFullScreenLoginOverlay();



  function updateDashboardSecurityData(nonces) {
    if (!nonces) return;

    if (window.adDashboardData) {
      window.adDashboardData.nonce = nonces.dashboardNonce;
    }

    if (window.ampAuthData) {
      window.ampAuthData.loginNonce = nonces.dashboardNonce;
    }
    updateFormNonces(nonces);
  }

  function updateFormNonces(nonces) {
    if (typeof window.updateAllFormNonces === 'function') {
      window.updateAllFormNonces(nonces);
    } else {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        const nonceInputs = form.querySelectorAll('input[name*="_nonce"]');
        nonceInputs.forEach(input => {
          input.value = nonces.dashboardNonce;
        });
      });
    }
  }

  async function syncSessionStateAfterLogin(loginData) {
    try {
      sessionExpired = false;
      warningShown = false;

      if (loginData.session_id) {
        window.adDashboardData.session_id = loginData.session_id;
      }

      if (loginData.timestamp) {
        window.adDashboardData.last_login_timestamp = loginData.timestamp;
      }

      const nonces = loginData.data?.nonces || loginData.nonces;
      if (nonces) {
        updateDashboardSecurityData(nonces);
      }

      const securityData = loginData.data?.security_data;
      if (securityData) {
        if (window.adDashboardData) {
          window.adDashboardData.google_login_enabled = securityData.google_login_enabled;
          window.adDashboardData.google_client_id = securityData.google_client_id;
          window.adDashboardData.turnstile_enabled = securityData.turnstile_enabled;
          window.adDashboardData.turnstile_site_key = securityData.turnstile_site_key;
        }

        if (window.ampAuthData) {
          window.ampAuthData.googleLoginEnabled = securityData.google_login_enabled;
          window.ampAuthData.googleClientId = securityData.google_client_id;
          window.ampAuthData.turnstileEnabled = securityData.turnstile_enabled;
          window.ampAuthData.turnstileSiteKey = securityData.turnstile_site_key;
        }
      }

      const timer1 = setTimeout(() => {
        if (typeof updateOverlayButtonStates === 'function') {
          updateOverlayButtonStates();
        }
      }, 100);
      addTimer(timer1);

      clearInterval(sessionCheckInterval);
      const timer2 = setTimeout(() => SessionManager.restart(), 1000);
      addTimer(timer2);

      return true;
    } catch (error) {
      return false;
    }
  }

  async function refreshDashboardData() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_refresh_dashboard_data&security=' + encodeURIComponent(window.adDashboardData.nonce),
        credentials: 'same-origin'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          if (data.data.nonces) {
            updateDashboardSecurityData(data.data.nonces);
          }
          if (data.data.user_data) {
            Object.assign(window.adDashboardData, data.data.user_data);
          }
        }
      }
    } catch (error) {}
  }

  function refreshCurrentTabContent() {
    try {
      const activeTab = document.querySelector('.ad-dashboard-tab.active');
      const tabName = activeTab?.getAttribute('data-tab') || 
                    new URL(window.location).searchParams.get('tab') || 
                    'overview';
      
      if (typeof window.loadTabContent === 'function') {
        window.loadTabContent(tabName, true);
      }
    } catch (error) {
      if (typeof window.loadTabContent === 'function') {
        window.loadTabContent('overview', true);
      }
    }
  }

  async function attemptSessionRecovery() {
    if (sessionExpired) return false;

    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_check_session',
        credentials: 'same-origin'
      });

      if (!response.ok) return false;

      const data = await response.json();

      if (data.success && data.data) {
        sessionExpired = false;
        warningShown = false;

        if (!sessionCheckInterval) {
          sessionCheckInterval = setInterval(() => SessionManager.unifiedSessionCheck(), 90000);
          addInterval(sessionCheckInterval);
        }
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  window.addEventListener('focus', async function() {
    if (sessionExpired) {
      const recovered = await attemptSessionRecovery();
      if (!recovered) {
        if (typeof window.showFullScreenLoginOverlay === 'function') {
          window.showFullScreenLoginOverlay();
        } else {
          window.location.href = '/login/?error=session_expired';
        }
      }
    }
  });

  window.addEventListener('beforeunload', clearAllTimers);

  function setupGlobalAjaxErrorHandling() {
    if (typeof $ !== 'undefined' && $.ajaxSetup) {
      $(document).ajaxError(function(event, xhr, settings, thrownError) {
        if (xhr.status === 403 || xhr.status === 401) {
          handleSessionExpiredError(xhr, settings);
        }
      });

      $(document).ajaxComplete(function(event, xhr, settings) {
        if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.session_expired) {
          handleSessionExpiredError(xhr, settings);
        }
      });
    }

    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      try {
        const response = await originalFetch.apply(this, args);

        if (response.status === 403 || response.status === 401) {
          const clonedResponse = response.clone();
          try {
            const data = await clonedResponse.json();
            if (data.data && data.data.session_expired) {
              handleSessionExpiredError(response, args[1]);
            }
          } catch (e) {
            handleSessionExpiredError(response, args[1]);
          }
        }
        return response;
      } catch (error) {
        throw error;
      }
    };
  }

  async function handleSessionExpiredError(response, settings) {
    if (sessionExpired) return;

    sessionExpired = true;
    clearInterval(sessionCheckInterval);

    const isLoginRequest = settings && (
      (typeof settings === 'object' && settings.body && settings.body.includes('amp_login')) ||
      (settings.data && settings.data.includes && settings.data.includes('amp_login')) ||
      (settings.url && settings.url.includes('amp_login'))
    );

    if (isLoginRequest) return;

    const recoveryAttempted = await attemptSessionRecovery();
    if (!recoveryAttempted) {
      if (typeof window.showFullScreenLoginOverlay === 'function') {
        window.showFullScreenLoginOverlay();
      } else {
        window.location.href = '/login/?error=session_expired';
      }
    }
  }

  setupGlobalAjaxErrorHandling();

  function syncSecurityDataPeriodically() {
    if (!window.adDashboardData?.ajaxurl) return;

    fetch(window.adDashboardData.ajaxurl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({ action: 'amp_sync_security_data' })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success && data.data.security_data && data.data.security_data.nonces) {
        updateDashboardSecurityData(data.data.security_data.nonces);
      }
    })
    .catch(error => {});
  }

  async function checkAndRefreshNoncesAfterGoogleLogin() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const isGoogleCallback = urlParams.has('action') && urlParams.get('action') === 'google_callback';

      if (!isGoogleCallback) {
        return;
      }

      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_check_google_oauth_login',
        credentials: 'same-origin'
      });

      if (!response.ok) return;

      const data = await response.json();

      if (data.success && data.data.google_oauth_login) {
        const refreshSuccess = await refreshAllSecurityData();
        if (refreshSuccess) {
          showMiniPopup('ข้อมูลการเข้าสู่ระบบได้รับการอัพเดทแล้ว', 'success');

          const cleanUrl = window.location.protocol + '//' + window.location.host + window.location.pathname;
          if (window.history && window.history.replaceState) {
            window.history.replaceState({}, document.title, cleanUrl);
          }
        }
      }
    } catch (error) {}
  }

  async function refreshAllSecurityData() {
    try {
      const nonceResponse = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_refresh_nonces',
        credentials: 'same-origin'
      });

      if (nonceResponse.ok) {
        const nonceData = await nonceResponse.json();
        if (nonceData.success && nonceData.data) {
          updateDashboardSecurityData(nonceData.data);
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  function isDashboardRecoverableError(errorMessage) {
    const recoverableErrors = [
      'Invalid Nonce', 'การตรวจสอบความปลอดภัยล้มเหลว', 'session', 'expired',
      'Security check failed', 'Nonce verification failed', 'Session timeout',
      'Authentication failed', 'CSRF token', 'Token expired', 'Session invalid',
      'Login session', 'Security token', 'Verification failed', 'Access denied',
      'Unauthorized', 'Forbidden'
    ];

    return recoverableErrors.some(err => errorMessage.toLowerCase().includes(err.toLowerCase()));
  }

  function updateDashboardSecurityData(nonces) {
    if (!nonces) return;

    if (window.adDashboardData) {
      window.adDashboardData.nonce = nonces.dashboardNonce;
    }

    if (window.ampAuthData) {
      window.ampAuthData.loginNonce = nonces.dashboardNonce;
    }

    const loginForm = document.querySelector('#overlay-login-form');
    if (loginForm) {
      const nonceInput = loginForm.querySelector('input[name="amp_login_nonce"]');
      if (nonceInput && nonces.dashboardNonce) {
        nonceInput.value = nonces.dashboardNonce;
      }
    }
  }

  async function attemptDashboardRecovery(errorMessage, retryCount, errorCode = null) {
    if (errorCode === 'NONCE_VERIFICATION_FAILED') {
      const nonceRefreshSuccess = await refreshOverlayNonces();
      if (nonceRefreshSuccess) {
        return true;
      }
    }

    const recoverySteps = [];
    const lowerMsg = errorMessage.toLowerCase();

    if (lowerMsg.includes('nonce') || lowerMsg.includes('token') || lowerMsg.includes('csrf')) {
      recoverySteps.push('refreshOverlayNonces');
    }

    if (lowerMsg.includes('session') || lowerMsg.includes('expired') || lowerMsg.includes('timeout')) {
      recoverySteps.push('refreshSessionData');
    }

    if (lowerMsg.includes('auth') || lowerMsg.includes('login') || lowerMsg.includes('access')) {
      recoverySteps.push('fullDashboardRecovery');
    }

    if (recoverySteps.length === 0) {
      recoverySteps.push('refreshOverlayNonces');
    }

    for (let step of recoverySteps) {
      const success = await executeDashboardRecoveryStep(step);
      if (success) return true;
    }
    return false;
  }

  async function executeDashboardRecoveryStep(step) {
    switch (step) {
      case 'refreshOverlayNonces':
        return await refreshOverlayNonces();
      case 'refreshSessionData':
        return await refreshDashboardSessionData();
      case 'fullDashboardRecovery':
        return await fullDashboardRecovery();
      default:
        return await refreshOverlayNonces();
    }
  }

  async function refreshDashboardSessionData() {
    try {
      if (typeof syncSecurityDataPeriodically === 'function') {
        await syncSecurityDataPeriodically();
        return true;
      }
      return await refreshOverlayNonces();
    } catch (error) {
      return false;
    }
  }

  async function silentSessionRefresh() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_extend_session&security=' + encodeURIComponent(window.adDashboardData.nonce),
        credentials: 'same-origin'
      });

      if (!response.ok) return false;

      const data = await response.json();
      return data.success;
    } catch (error) {
      return false;
    }
  }

  async function checkSessionHealth() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_check_session',
        credentials: 'same-origin'
      });

      if (!response.ok) {
        return { healthy: false, reason: 'network_error' };
      }

      const data = await response.json();
      return {
        healthy: data.success,
        reason: data.success ? 'ok' : 'session_invalid',
        data: data.data
      };
    } catch (error) {
      return { healthy: false, reason: 'fetch_error', error };
    }
  }

  async function performHealthCheck() {
    const checks = { session: false, nonces: false, connectivity: false, overall: false };

    try {
      const sessionCheck = await checkSessionHealth();
      checks.session = sessionCheck.healthy;

      const nonceCheck = await testNonceValidity();
      checks.nonces = nonceCheck;

      const connectivityCheck = await testConnectivity();
      checks.connectivity = connectivityCheck;

      checks.overall = checks.session && checks.nonces && checks.connectivity;
      return checks;
    } catch (error) {
      return checks;
    }
  }

  async function testNonceValidity() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_test_nonce&security=' + encodeURIComponent(window.adDashboardData.nonce),
        credentials: 'same-origin'
      });

      if (!response.ok) return false;

      const data = await response.json();
      return data.success;
    } catch (error) {
      return false;
    }
  }

  async function testConnectivity() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'action=amp_ping',
        credentials: 'same-origin'
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async function refreshOverlayNonces() {
    return await refreshAllSecurityData();
  }

  async function fullDashboardRecovery() {
    return await refreshDashboardData() && await refreshAllSecurityData();
  }

  async function refreshSecurityDataForOverlay() {
    try {
      const response = await fetch(window.adDashboardData.ajaxurl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'action=amp_sync_security_data',
        credentials: 'same-origin'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data && data.data.security_data) {
          const securityData = data.data.security_data;

          if (window.adDashboardData) {
            window.adDashboardData.nonce = securityData.nonces?.dashboardNonce || window.adDashboardData.nonce;
            window.adDashboardData.google_login_enabled = securityData.google_login_enabled;
            window.adDashboardData.google_client_id = securityData.google_client_id;
            window.adDashboardData.turnstile_enabled = securityData.turnstile_enabled;
            window.adDashboardData.turnstile_site_key = securityData.turnstile_site_key;
          }

          if (window.ampAuthData) {
            window.ampAuthData.loginNonce = securityData.nonces?.loginNonce || window.ampAuthData.loginNonce;
            window.ampAuthData.googleLoginEnabled = securityData.google_login_enabled;
            window.ampAuthData.googleClientId = securityData.google_client_id;
            window.ampAuthData.turnstileEnabled = securityData.turnstile_enabled;
            window.ampAuthData.turnstileSiteKey = securityData.turnstile_site_key;
          }

          const timer = setTimeout(() => {
            if (typeof updateOverlayButtonStates === 'function') {
              updateOverlayButtonStates();
            }
          }, 100);
          addTimer(timer);

          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => EventManager.init());
  } else {
    EventManager.init();
  }
  function handleLogout() {
    Swal.fire({
      title: 'ออกจากระบบ?',
      text: 'คุณต้องการออกจากระบบหรือไม่?',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'ออกจากระบบ',
      cancelButtonText: 'ยกเลิก'
    }).then((result) => {
      if (result.isConfirmed) {
        $.ajax({
          url: window.adDashboardData.ajaxurl,
          type: 'POST',
          data: {
            action: 'amp_logout',
            security: window.adDashboardData.nonce
          },
          success: function(response) {
            if (response.success) {
              window.location.href = response.data.redirect;
            } else {
              Swal.fire('เกิดข้อผิดพลาด', response.data.message || 'ไม่สามารถออกจากระบบได้', 'error');
            }
          },
          error: function() {
            window.location.href = window.adDashboardData.loginUrl + '?action=logout';
          }
        });
      }
    });
  }

  window.syncSessionStateAfterLogin = syncSessionStateAfterLogin;
  window.restartSessionMonitoring = () => SessionManager.restart();
  window.handleLogout = handleLogout;
  window.syncSecurityDataPeriodically = syncSecurityDataPeriodically;
  window.checkAndRefreshNoncesAfterGoogleLogin = checkAndRefreshNoncesAfterGoogleLogin;
  window.silentSessionRefresh = silentSessionRefresh;

})();