<?php
if (!defined('AMP_PLUGIN_DIR')) exit;
$turnstile_enabled = defined('ABSPATH') ? (bool) get_option('amp_turnstile_enabled', false) : false;
?>
<form id="register-form" class="ad-login-form" method="post">
    <div class="ad-login-field">
        <label for="register-username">Username</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon user-icon"></i>
            <input type="text" id="register-username" name="username" required autocomplete="username">
        </div>
    </div>
    <div class="ad-login-field">
        <label for="register-email">Email</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon email-icon"></i>
            <input type="email" id="register-email" name="email" required autocomplete="email">
        </div>
    </div>
    <div class="ad-login-field">
        <label for="register-password">Password</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon password-icon"></i>
            <input type="password" id="register-password" name="password" required autocomplete="new-password">
            <i class="ad-login-icon toggle-password"></i>
        </div>
        <div id="password-strength-container">
            <div id="password-strength-text"></div>
            <div class="password-strength-meter">
                <div id="password-strength-progress"></div>
            </div>
            <ul id="password-requirements">
                <li id="length-requirement">อย่างน้อย 8 ตัวอักษร</li>
                <li id="uppercase-requirement">ตัวพิมพ์ใหญ่ (A-Z)</li>
                <li id="lowercase-requirement">ตัวพิมพ์เล็ก (a-z)</li>
                <li id="number-requirement">ตัวเลข (0-9)</li>
                <li id="special-requirement">อักขระพิเศษ (!@#$%)</li>
            </ul>
        </div>
    </div>
    <div class="ad-login-field">
        <label for="register-confirm-password">Confirm Password</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon password-icon"></i>
            <input type="password" id="register-confirm-password" name="confirm_password" required autocomplete="new-password">
            <i class="ad-login-icon toggle-password"></i>
        </div>
    </div>

    <div class="ad-login-field">
        <label class="ad-login-checkbox">
            <input type="checkbox" id="register-terms" name="terms" value="1" required>
            <span class="checkmark"></span>
            ฉันยอมรับ <a href="#" class="terms-link">ข้อกำหนดและเงื่อนไขการใช้งาน</a>
        </label>
    </div>
    
    <?php if ($turnstile_enabled): ?>
    <div class="ad-login-captcha" id="register-turnstile-container"></div>
    <?php endif; ?>

    <button type="submit" class="ad-login-button">Register</button>
    <input type="hidden" name="amp_register_nonce" value="<?php echo esc_attr($unified_nonce ?? ''); ?>">
</form> 