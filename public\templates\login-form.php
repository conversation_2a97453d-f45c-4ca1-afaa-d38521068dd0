<?php
if (!defined('AMP_PLUGIN_DIR')) exit;

$google_enabled = $google_enabled ?? (defined('ABSPATH') ? (bool) get_option('amp_google_login_enabled', false) : false);
$google_client_id = $google_client_id ?? (defined('ABSPATH') ? (\AMP_Encryption_Manager::instance()->get_secret('amp_google_client_id')) : '');
$turnstile_enabled = $turnstile_enabled ?? (defined('ABSPATH') ? (bool) get_option('amp_turnstile_enabled', false) : false);
$unified_nonce = $unified_nonce ?? (defined('ABSPATH') && function_exists('wp_create_nonce') ? wp_create_nonce('amp_dashboard_action') : bin2hex(random_bytes(16)));
?>

<form id="amp-login-form" class="ad-login-form" method="post">
    <?php if ($google_enabled && !empty($google_client_id)): ?>
    <div class="google-login-section">
        <button type="button" id="google-login-btn" class="google-login-button">
            <svg width="18" height="18" viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><path d="M17.64 9.205c0-.639-.057-1.252-.164-1.841H9v3.481h4.844a4.14 4.14 0 0 1-1.796 2.716v2.259h2.908c1.702-1.567 2.684-3.875 2.684-6.615z" fill="#4285F4"/><path d="M9 18c2.43 0 4.467-.806 5.956-2.18l-2.908-2.259c-.806.54-1.837.86-3.048.86-2.344 0-4.328-1.584-5.036-3.711H.957v2.332A8.997 8.997 0 0 0 9 18z" fill="#34A853"/><path d="M3.964 10.71A5.41 5.41 0 0 1 3.682 9c0-.593.102-1.17.282-1.71V4.958H.957A8.996 8.996 0 0 0 0 9c0 1.452.348 2.827.957 4.042l3.007-2.332z" fill="#FBBC05"/><path d="M9 3.58c1.321 0 2.508.454 3.44 1.345l2.582-2.58C13.463.891 11.426 0 9 0A8.997 8.997 0 0 0 .957 4.958L3.964 7.29C4.672 5.163 6.656 3.58 9 3.58z" fill="#EA4335"/></g></svg>
            Sign in with Google
        </button>
        <div class="social-login-divider"><span>OR</span></div>
    </div>
    <?php endif; ?>

    <div class="ad-login-field">
        <label for="login-username">Username or Email</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon user-icon"></i>
            <input type="text" id="login-username" name="username" required autocomplete="username">
        </div>
    </div>

    <div class="ad-login-field">
        <label for="login-password">Password</label>
        <div class="ad-login-input-wrapper">
            <i class="ad-login-icon password-icon"></i>
            <input type="password" id="login-password" name="password" required autocomplete="current-password">
            <i class="ad-login-icon toggle-password"></i>
        </div>
    </div>

    <div class="ad-login-field">
        <label class="ad-login-checkbox">
            <input type="checkbox" name="remember_me" value="1">
            <span class="checkmark"></span>
            Remember me
        </label>
    </div>

    <?php if ($turnstile_enabled): ?>
    <div class="ad-login-captcha" id="login-turnstile-container"></div>
    <?php endif; ?>

    <button type="submit" class="ad-login-button">Sign In</button>
    <input type="hidden" name="amp_login_nonce" value="<?php echo esc_attr($unified_nonce); ?>">
</form> 