<?php
if (!defined('WPINC')) {
    die;
}

function display_export_import_positions_page() {
    if (!current_user_can('manage_options') && !current_user_can('amp_advertiser_access')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    if (isset($_POST['export_positions'])) {
        export_positions_to_json();
    }

    $import_message = '';
    if (isset($_POST['import_positions']) && isset($_FILES['import_file'])) {
        $import_message = import_positions_from_json();
    }

    ?>
    <div class="wrap">
        <h1>Export/Import Ad Positions</h1>

        <?php if (!empty($import_message)): ?>
            <div class="notice <?php echo strpos($import_message, 'successfully') !== false ? 'notice-success' : 'notice-error'; ?> is-dismissible">
                <p><?php echo esc_html($import_message); ?></p>
            </div>
        <?php endif; ?>

        <div class="card" style="max-width: 800px; padding: 20px; margin-top: 20px; background: #fff; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h2>Export Ad Positions</h2>
            <p>Export all ad positions and their settings to a JSON file. This includes position names, dimensions, and other metadata.</p>
            <form method="post" action="">
                <input type="hidden" name="export_positions" value="1">
                <?php wp_nonce_field('export_positions_nonce', 'export_positions_nonce'); ?>
                <p><input type="submit" class="button button-primary" value="Export Ad Positions"></p>
            </form>
        </div>

        <div class="card" style="max-width: 800px; padding: 20px; margin-top: 20px; background: #fff; border-radius: 5px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
            <h2>Import Ad Positions</h2>
            <p>Import ad positions from a JSON file. This will add new positions or update existing ones.</p>
            <form method="post" enctype="multipart/form-data" action="">
                <input type="hidden" name="import_positions" value="1">
                <?php wp_nonce_field('import_positions_nonce', 'import_positions_nonce'); ?>
                <p>
                    <input type="file" name="import_file" accept=".json">
                </p>
                <p><input type="submit" class="button button-primary" value="Import Ad Positions"></p>
            </form>
        </div>
    </div>
    <?php
}

function export_positions_to_json() {
    if (!isset($_POST['export_positions_nonce']) || !wp_verify_nonce($_POST['export_positions_nonce'], 'export_positions_nonce')) {
        wp_die('Security check failed');
    }

    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $all_positions = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
    
    $positions_data = [];
    foreach ($all_positions as $position) {
        $positions_data[] = [
            'name' => $position->name,
            'type' => $position->type,
            'width' => $position->width,
            'height' => $position->height,
            'description' => $position->description,
            'status' => $position->status
        ];
    }

    $export_data = array(
        'positions' => $positions_data,
        'export_date' => current_time('mysql'),
        'export_version' => '2.0'
    );

    $json_data = json_encode($export_data, JSON_PRETTY_PRINT);

    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="ad-positions-export-' . date('Y-m-d') . '.json"');
    header('Content-Length: ' . strlen($json_data));

    echo $json_data;
    exit;
}

function import_positions_from_json() {
    if (!isset($_POST['import_positions_nonce']) || !wp_verify_nonce($_POST['import_positions_nonce'], 'import_positions_nonce')) {
        return 'Security check failed';
    }

    if (!isset($_FILES['import_file']) || $_FILES['import_file']['error'] !== UPLOAD_ERR_OK) {
        return 'Error uploading file. Please try again.';
    }

    $file_path = $_FILES['import_file']['tmp_name'];

    if (!is_uploaded_file($file_path)) {
        return 'Invalid file upload.';
    }

    if ($_FILES['import_file']['size'] > 1024 * 1024) {
        return 'File size too large. Maximum 1MB allowed.';
    }

    if ($_FILES['import_file']['type'] !== 'application/json') {
        return 'Only JSON files are allowed.';
    }

    require_once(ABSPATH . 'wp-admin/includes/file.php');
    WP_Filesystem();
    global $wp_filesystem;

    $file_content = $wp_filesystem->get_contents($file_path);
    if (!$file_content) {
        return 'Could not read the uploaded file.';
    }

    $import_data = json_decode($file_content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        return 'Invalid JSON file: ' . json_last_error_msg();
    }

    $positions_data = null;
    if (isset($import_data['positions']) && is_array($import_data['positions'])) {
        $positions_data = $import_data['positions'];
    } elseif (isset($import_data['ad_positions']) && is_array($import_data['ad_positions'])) {
        $old_positions = $import_data['ad_positions'];
        $old_sizes = $import_data['ad_sizes'] ?? [];
        
        $positions_data = [];
        foreach ($old_positions as $position_name) {
            $positions_data[] = [
                'name' => $position_name,
                'type' => 'banner',
                'width' => $old_sizes[$position_name]['width'] ?? 300,
                'height' => $old_sizes[$position_name]['height'] ?? 250,
                'description' => '',
                'status' => 'active'
            ];
        }
    }

    if (!$positions_data || !is_array($positions_data)) {
        return 'Invalid file format: missing positions data.';
    }

    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    
    $imported_count = 0;
    foreach ($positions_data as $position_data) {
        $result = $position_manager->create_position($position_data);
        if (!is_wp_error($result)) {
            $imported_count++;
        }
    }

    return "Successfully imported $imported_count ad positions.";
}
