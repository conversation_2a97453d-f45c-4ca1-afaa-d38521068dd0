<?php
namespace AdManagementPro\Core\Setup;

if (!defined('WPINC')) {
    die;
}

class Activator {

    public static function activate() {
        try {
            self::check_requirements();
            self::forward_to_plugin_activate();
            self::ensure_tables_exist();
            self::create_roles();
            self::setup_rewrite_rules();
            self::create_dynamic_link_file();
            self::create_statistics_directories();
        } catch (\Exception $e) {
            error_log('AMP Activation Error: ' . $e->getMessage());
            throw new \Exception('Plugin activation failed: ' . $e->getMessage());
        }
    }

    private static function check_requirements() {
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            throw new \Exception('PHP 7.4 or higher is required');
        }

        if (!function_exists('wp_get_current_user')) {
            throw new \Exception('WordPress core functions not available');
        }

        global $wpdb;
        if (!$wpdb) {
            throw new \Exception('Database connection not available');
        }
    }
    
    private static function forward_to_plugin_activate() {
        try {
            if (class_exists('\\AdManagementPro\\Core\\Plugin')) {
                $plugin = \AdManagementPro\Core\Plugin::instance();
                if (method_exists($plugin, 'activate')) {
                    $plugin->activate();
                }
            }
        } catch (\Exception $e) {
            error_log('Plugin instance activation error: ' . $e->getMessage());
            throw new \Exception('Failed to activate plugin instance: ' . $e->getMessage());
        }
    }
    
    private static function ensure_tables_exist() {
        try {
            $database_file = dirname(__DIR__) . '/class-database.php';
            if (!file_exists($database_file)) {
                throw new \Exception('Database class file not found');
            }
            require_once $database_file;
            \AdManagementPro\Core\Database::create_tables();
        } catch (\Exception $e) {
            error_log('Database tables creation error: ' . $e->getMessage());
            throw new \Exception('Failed to create database tables: ' . $e->getMessage());
        }
    }

    private static function create_roles() {
        try {
            $roles_file = dirname(__DIR__) . '/roles.php';
            if (!file_exists($roles_file)) {
                throw new \Exception('Roles file not found');
            }
            require_once $roles_file;
        } catch (\Exception $e) {
            error_log('Roles creation error: ' . $e->getMessage());
            throw new \Exception('Failed to create user roles: ' . $e->getMessage());
        }
    }

    private static function setup_rewrite_rules() {
        try {
            require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';

            add_action('init', function() {
                add_rewrite_rule('^dashboard/?$', 'index.php?pagename=dashboard', 'top');
                add_rewrite_rule('^login/?$', 'index.php?pagename=login', 'top');
            }, 10, 0);

            \flush_rewrite_rules(false);

            wp_schedule_single_event(time() + 3, 'amp_delayed_flush_rules');
            add_action('amp_delayed_flush_rules', function() {
                \flush_rewrite_rules(true);
                error_log('AMP: Delayed rewrite rules flush completed');
            });

        } catch (\Exception $e) {
            error_log('Setup rewrite rules error: ' . $e->getMessage());
        }
    }

    private static function create_dynamic_link_file() {
        if (!class_exists('\AdManagementPro\Core\Shortcodes')) {
            require_once AMP_PLUGIN_DIR . 'includes/core/class-shortcodes.php';
        }

        $new_content = \AdManagementPro\Core\Shortcodes::get_dynamic_link_file_content();
        $destination = ABSPATH . 'dynamic-link.php';
        
        $should_write_file = true;

        if (file_exists($destination)) {
            $existing_content = file_get_contents($destination);
            if ($existing_content === $new_content) {
                $should_write_file = false;
            }
        }

        if ($should_write_file) {
            if (file_put_contents($destination, $new_content) === false) {
                error_log('Ad Management Pro: Failed to create or update dynamic-link.php in WordPress root.');
            }
        }
    }

    private static function create_statistics_directories() {
        try {
            $stats_dir = WP_CONTENT_DIR . '/statistics';
            $users_dir = $stats_dir . '/users';
            
            if (!function_exists('wp_mkdir_p')) {
                require_once(ABSPATH . 'wp-admin/includes/file.php');
            }
            
            if (!file_exists($stats_dir)) {
                if (!wp_mkdir_p($stats_dir)) {
                    throw new \Exception('Failed to create statistics directory: ' . $stats_dir);
                }
                if (!file_put_contents($stats_dir . '/.htaccess', 'deny from all')) {
                    error_log('AMP: Failed to create .htaccess in statistics directory');
                }
            }
            
            if (!file_exists($users_dir)) {
                if (!wp_mkdir_p($users_dir)) {
                    throw new \Exception('Failed to create users directory: ' . $users_dir);
                }
            }
            
            if (!is_writable($stats_dir)) {
                throw new \Exception('Statistics directory is not writable: ' . $stats_dir);
            }
            
            if (!is_writable($users_dir)) {
                throw new \Exception('Users directory is not writable: ' . $users_dir);
            }
            
            error_log('AMP: Statistics directories created successfully');
            
        } catch (\Exception $e) {
            error_log('Statistics directories creation error: ' . $e->getMessage());
            throw new \Exception('Failed to create statistics directories: ' . $e->getMessage());
        }
    }
} 