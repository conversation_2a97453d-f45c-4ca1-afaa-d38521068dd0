<?php
if (!defined('WPINC')) {
    die;
}

if (!function_exists('get_user_position_clicks') || !function_exists('get_user_total_clicks')) {
    require_once plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/utils/click-statistics.php';
}

function display_admin_dashboard_page() {
    global $wpdb;
    
    if (!class_exists('Ad_Dashboard_Optimizer')) {
        class Ad_Dashboard_Optimizer {
            public static function get_dashboard_data() {
                global $wpdb;

                if (!class_exists('\AdManagementPro\Modules\Shared\PositionManager')) {
                    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
                }
                $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
                $all_positions = $position_manager->get_positions();

                $total_positions = 0;
                $active_positions = 0;
                $expiring_soon = 0;

                if (is_array($all_positions)) {
                    $total_positions = count($all_positions);
                    $now = time();
                    $seven_days_from_now = strtotime('+7 days');

                    foreach ($all_positions as $position) {
                        $is_owned = !empty($position->user_id);
                        $is_active = $position->status === 'active';

                        if ($is_owned && $is_active) {
                            if (isset($position->expiration_date)) {
                                $expiration_time = strtotime($position->expiration_date);
                                if ($expiration_time > $now) {
                                    $active_positions++;
                                    if ($expiration_time <= $seven_days_from_now) {
                                        $expiring_soon++;
                                    }
                                }
                            } else {
                                $active_positions++;
                            }
                        }
                    }
                }
                
                $vacant_positions = $total_positions - $active_positions;

                $cache_manager = \AMP_Cache_Manager::instance();
                $total_customers = $cache_manager->get('total_customers', 'user_dashboard');
                if ($total_customers === false) {
                    $total_customers = count(get_users(array('role' => 'advertiser')));
                    $cache_manager->set('total_customers', $total_customers, 86400, 'user_dashboard');
                }

                $monthly_revenue = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT SUM(amount) FROM {$wpdb->prefix}ad_payments
                         WHERE payment_date >= DATE_FORMAT(NOW(), '%%Y-%%m-01')
                         AND payment_date < DATE_ADD(DATE_FORMAT(NOW(), '%%Y-%%m-01'), INTERVAL 1 MONTH)
                         AND status = %s
                         AND purchase_type = %s",
                        'completed',
                        'api'
                    )
                );

                $recent_payments = $wpdb->get_results($wpdb->prepare(
                    "SELECT * FROM {$wpdb->prefix}ad_payments
                     ORDER BY payment_date DESC LIMIT %d", 10
                ));

                $data = array(
                    'stats' => array(
                        'total_positions' => intval($total_positions),
                        'active_positions' => intval($active_positions),
                        'vacant_positions' => intval($vacant_positions),
                        'total_customers' => intval($total_customers),
                        'expiring_soon' => intval($expiring_soon),
                        'monthly_revenue' => floatval($monthly_revenue)
                    ),
                    'recent_payments' => $recent_payments
                );

                return $data;
            }
        }
    }

    $dashboard_data = Ad_Dashboard_Optimizer::get_dashboard_data();
    $stats = $dashboard_data['stats'];
    $recent_payments = $dashboard_data['recent_payments'];
    $total_positions = $stats['total_positions'];
    $active_positions = $stats['active_positions'];
    $vacant_positions = $stats['vacant_positions'];
    $total_customers = $stats['total_customers'];
    $expiring_soon = $stats['expiring_soon'];
    $monthly_revenue = $stats['monthly_revenue'];
    require_once plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/utils/google-analytics.php';

    try {
        $ga_realtime_users = amp_get_realtime_users();
        $ga_monthly_users = amp_get_monthly_users(30);
        $ga_configured = amp_is_ga_configured();
    } catch (Exception $e) {
        error_log('Admin Dashboard GA Error: ' . $e->getMessage());
        $ga_realtime_users = 0;
        $ga_monthly_users = 0;
        $ga_configured = false;
    }
    $positions_table = $wpdb->prefix . 'ad_positions';
    $ads = get_option('ads', array());

    $cache_manager = \AMP_Cache_Manager::instance();
    $cache_key = 'admin_dashboard_clicks_data';
    $cached_data = $cache_manager->get($cache_key, 'dashboard');

    $users = get_users(array('role__in' => array('advertiser')));
    if (!is_array($users)) {
        $users = array();
    }

    if ($cached_data !== false) {
        $recent_clicks = $cached_data['recent_clicks'];
        $all_position_clicks = $cached_data['all_position_clicks'];
    } else {
        $recent_clicks = 0;
        $all_position_clicks = array();

        foreach ($users as $user) {
            $user_positions = get_user_position_clicks($user->ID, true);

            if (is_array($user_positions) && !empty($user_positions)) {
                foreach ($user_positions as $position) {
                    if (is_array($position) && isset($position['ad_position']) && isset($position['total_clicks'])) {
                        $position_id = $position['ad_position'];
                        $clicks = intval($position['total_clicks']);

                        $recent_clicks += $clicks;

                        if (!isset($all_position_clicks[$position_id])) {
                            $all_position_clicks[$position_id] = 0;
                        }
                        $all_position_clicks[$position_id] += $clicks;
                    }
                }
            }
        }

        $cache_manager->set($cache_key, array(
            'recent_clicks' => $recent_clicks,
            'all_position_clicks' => $all_position_clicks
        ), 1800, 'dashboard');
    }

    arsort($all_position_clicks);
    $top_5_positions = array_slice($all_position_clicks, 0, 5, true);
    $top_positions = array();
    foreach ($top_5_positions as $position_id => $click_count) {
        $top_positions[] = (object) array(
            'ad_position' => sanitize_text_field($position_id),
            'click_count' => intval($click_count)
        );
    }

    if (!empty($recent_payments)) {
        foreach ($recent_payments as $payment) {
            if (!isset($payment->position) && isset($payment->ad_position)) {
                $payment->position = $payment->ad_position;
            }
        }
    }

    $revenue_data = array(
        'labels' => array(),
        'values' => array()
    );

    try {
        $last_6_months = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT DATE_FORMAT(payment_date, '%%Y-%%m') as month,
                SUM(amount) as total
                FROM {$wpdb->prefix}ad_payments
                WHERE payment_date >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
                AND status = %s
                AND purchase_type = %s
                GROUP BY month
                ORDER BY month ASC",
                'completed',
                'api'
            )
        );

        if (!empty($last_6_months)) {
            foreach ($last_6_months as $month) {
                $date = new DateTime($month->month . '-01');
                $revenue_data['labels'][] = $date->format('M Y');
                $revenue_data['values'][] = floatval($month->total);
            }
        } else {
            for ($i = 5; $i >= 0; $i--) {
                $date = new DateTime("-$i months");
                $revenue_data['labels'][] = $date->format('M Y');
                $revenue_data['values'][] = 0;
            }
        }
    } catch (Exception $e) {
        error_log('Admin Dashboard Revenue Chart Error: ' . $e->getMessage());
        for ($i = 5; $i >= 0; $i--) {
            $date = new DateTime("-$i months");
            $revenue_data['labels'][] = $date->format('M Y');
            $revenue_data['values'][] = 0;
        }
    }

    error_log('AMP: Revenue Chart Data - Labels: ' . json_encode($revenue_data['labels']));
    error_log('AMP: Revenue Chart Data - Values: ' . json_encode($revenue_data['values']));

    $clicks_data = array(
        'labels' => array(),
        'values' => array()
    );

    $last_7_days_dates = array();
    for ($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $last_7_days_dates[$date] = 0;
    }

    if (!isset($users) || !is_array($users)) {
        $users = get_users(array('role__in' => array('advertiser')));
        if (!is_array($users)) {
            $users = array();
        }
    }

    foreach ($users as $user) {
        $user_positions = get_user_position_clicks($user->ID, true);

        if (is_array($user_positions) && !empty($user_positions)) {
            foreach ($user_positions as $position) {
                if (is_array($position) && isset($position['ad_position'])) {
                    $position_id = $position['ad_position'];

                    $daily_stats = get_position_last_30_days_clicks($user->ID, $position_id);

                    if (is_array($daily_stats) && !empty($daily_stats['daily_stats'])) {
                        foreach ($daily_stats['daily_stats'] as $date => $clicks) {
                            if (isset($last_7_days_dates[$date])) {
                                $last_7_days_dates[$date] += $clicks;
                            }
                        }
                    }
                }
            }
        }
    }

    foreach ($last_7_days_dates as $date => $clicks) {
        $formatted_date = date('d M', strtotime($date));
        $clicks_data['labels'][] = $formatted_date;
        $clicks_data['values'][] = $clicks;
    }

    $clear_stats_nonce = wp_create_nonce('amp-admin-clear-stats');
    ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <div class="wrap">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Dashboard</h1>

            <div class="dashboard-actions">
                <button id="clear-all-stats" class="ad-btn ad-btn-danger">
                    <i class="fas fa-eraser"></i> ล้างสถิติทั้งหมด
                </button>

                <a href="<?php echo admin_url('admin.php?page=ad-management'); ?>" class="ad-btn ad-btn-primary">
                    <i class="fas fa-cog"></i> Manage Ads
                </a>
            </div>
        </div>
        <div class="dashboard-summary">
            <div class="dashboard-card-container">
                <div class="dashboard-card card-primary animate-fade-in">
                    <div class="dashboard-card-header">
                        <h3 class="dashboard-card-title">Ad Positions</h3>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-ad"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="dashboard-card-value counter-value"><?php echo $total_positions; ?></div>
                        <div class="dashboard-card-details">
                            <span><?php echo $active_positions; ?> Active</span>
                            <span><?php echo $vacant_positions; ?> Vacant</span>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card card-success animate-fade-in">
                    <div class="dashboard-card-header">
                        <h3 class="dashboard-card-title">Customers</h3>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="dashboard-card-value counter-value"><?php echo $total_customers; ?></div>
                        <div class="dashboard-card-details">
                            <span>Manage Customers</span>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card card-warning animate-fade-in">
                    <div class="dashboard-card-header">
                        <h3 class="dashboard-card-title">Expiring Soon</h3>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="dashboard-card-value counter-value"><?php echo $expiring_soon; ?></div>
                        <div class="dashboard-card-details">
                            <span>Within 7 days</span>
                        </div>
                    </div>
                </div>
                <div class="dashboard-card card-info animate-fade-in">
                    <div class="dashboard-card-header">
                        <h3 class="dashboard-card-title">รายได้เดือนนี้</h3>
                        <div class="dashboard-card-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="dashboard-card-body">
                        <div class="dashboard-card-value counter-value"><?php echo number_format($monthly_revenue ?: 0, 2); ?></div>
                        <div class="dashboard-card-details">
                            <span>USDT - <?php echo date('F Y'); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php
        $revenue_cache_key = 'admin_dashboard_revenue_summary';
        $revenue_summary_data = $cache_manager->get($revenue_cache_key, 'dashboard');

        if ($revenue_summary_data === false) {
            try {
                $total_revenue = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT SUM(amount) FROM {$wpdb->prefix}ad_payments WHERE status = %s AND purchase_type = %s",
                        'completed',
                        'api'
                    )
                );

                $today_revenue = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT SUM(amount) FROM {$wpdb->prefix}ad_payments WHERE DATE(payment_date) = CURDATE() AND status = %s AND purchase_type = %s",
                        'completed',
                        'api'
                    )
                );

                $this_week_revenue = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT SUM(amount) FROM {$wpdb->prefix}ad_payments
                         WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
                         AND payment_date < DATE_ADD(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY)
                         AND status = %s
                         AND purchase_type = %s",
                        'completed',
                        'api'
                    )
                );

                $revenue_summary_data = array(
                    'total' => floatval($total_revenue),
                    'today' => floatval($today_revenue),
                    'week' => floatval($this_week_revenue)
                );

                $cache_manager->set($revenue_cache_key, $revenue_summary_data, 3600, 'dashboard');
            } catch (Exception $e) {
                error_log('Admin Dashboard Revenue Query Error: ' . $e->getMessage());
                $revenue_summary_data = array('total' => 0, 'today' => 0, 'week' => 0);
            }
        }

        $total_revenue = $revenue_summary_data['total'];
        $today_revenue = $revenue_summary_data['today'];
        $this_week_revenue = $revenue_summary_data['week'];
        ?>

        <div class="revenue-summary-section">
            <div class="dashboard-section animate-slide-up">
                <div class="dashboard-section-header">
                    <div class="header-left">
                        <h2 class="dashboard-section-title"><i class="fas fa-chart-pie"></i> สรุปรายได้โดยรวม</h2>
                        <div class="dashboard-section-subtitle">เฉพาะการซื้อจริงผ่าน API เท่านั้น</div>
                    </div>
                </div>
                <div class="dashboard-section-body">
                    <div class="revenue-summary-grid">
                        <div class="revenue-summary-card total">
                            <div class="revenue-summary-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="revenue-summary-content">
                                <h3>รายได้ทั้งหมด</h3>
                                <div class="revenue-summary-value counter-value"><?php echo number_format($total_revenue ?: 0, 2); ?></div>
                                <div class="revenue-summary-unit">USDT</div>
                            </div>
                        </div>

                        <div class="revenue-summary-card today">
                            <div class="revenue-summary-icon">
                                <i class="fas fa-calendar-day"></i>
                            </div>
                            <div class="revenue-summary-content">
                                <h3>รายได้วันนี้</h3>
                                <div class="revenue-summary-value counter-value"><?php echo number_format($today_revenue ?: 0, 2); ?></div>
                                <div class="revenue-summary-unit">USDT</div>
                            </div>
                        </div>

                        <div class="revenue-summary-card week">
                            <div class="revenue-summary-icon">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="revenue-summary-content">
                                <h3>รายได้สัปดาห์นี้</h3>
                                <div class="revenue-summary-value counter-value"><?php echo number_format($this_week_revenue ?: 0, 2); ?></div>
                                <div class="revenue-summary-unit">USDT</div>
                            </div>
                        </div>

                        <div class="revenue-summary-card month">
                            <div class="revenue-summary-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="revenue-summary-content">
                                <h3>รายได้เดือนนี้</h3>
                                <div class="revenue-summary-value counter-value"><?php echo number_format($monthly_revenue ?: 0, 2); ?></div>
                                <div class="revenue-summary-unit">USDT</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-charts-container">
            <div class="dashboard-charts-grid">
                <div class="dashboard-section animate-slide-up">
                    <div class="dashboard-section-header">
                        <div class="header-left">
                            <h2 class="dashboard-section-title"><i class="fas fa-chart-line"></i> รายได้รายเดือน</h2>
                            <div class="dashboard-section-subtitle">เฉพาะการซื้อจริงผ่าน API</div>
                        </div>
                    </div>
                    <div class="dashboard-section-body">
                        <div class="chart-container">
                            <canvas id="revenue-chart"></canvas>
                        </div>
                        <div class="chart-legend">
                            <div class="chart-legend-item">
                                <span class="chart-legend-color" style="background-color: var(--primary-color);"></span>
                                <span class="chart-legend-label">รายได้ (USDT)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-section animate-slide-up">
                    <div class="dashboard-section-header">
                        <div class="header-left">
                            <h2 class="dashboard-section-title"><i class="fas fa-mouse-pointer"></i> คลิกรายวัน</h2>
                            <div class="dashboard-section-subtitle">7 วันที่ผ่านมา</div>
                        </div>
                    </div>
                    <div class="dashboard-section-body">
                        <div class="chart-container">
                            <canvas id="clicks-chart"></canvas>
                        </div>
                        <div class="chart-legend">
                            <div class="chart-legend-item">
                                <span class="chart-legend-color" style="background-color: var(--info-color);"></span>
                                <span class="chart-legend-label">จำนวนคลิก</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-clock"></i> สถานะการจอง Countdown Timer</h2>
                </div>
                <div class="header-right">
                    <button class="ad-btn ad-btn-primary" id="refresh-timers-btn">
                        <i class="fas fa-sync"></i> รีเฟรช
                    </button>
                </div>
            </div>
            <div class="dashboard-section-body">
                <div id="active-timers-container">
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <p>กำลังโหลดข้อมูล...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-shopping-cart"></i> รายการซื้อล่าสุด</h2>
                </div>
            </div>
            <div class="dashboard-section-body">
                <?php if (!empty($recent_payments)): ?>
                <div class="transaction-table-container">
                    <table class="transaction-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-calendar-day"></i> วันที่</th>
                                <th><i class="fas fa-map-marker-alt"></i> ตำแหน่ง</th>
                                <th><i class="fas fa-user"></i> ผู้ซื้อ</th>
                                <th><i class="fas fa-clock"></i> ระยะเวลา</th>
                                <th class="amount"><i class="fas fa-coins"></i> จำนวนเงิน</th>
                                <th class="status"><i class="fas fa-credit-card"></i> ช่องทาง</th>
                                <th class="status"><i class="fas fa-info-circle"></i> สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_payments as $payment):
                                $user_info = get_userdata($payment->user_id);
                                $username = $user_info ? $user_info->user_login : 'Unknown';

                                $duration = '';
                                if (!empty($payment->duration)) {
                                    if ($payment->duration == 7) {
                                        $duration = '7 วัน (ทดลอง)';
                                    } else {
                                        $months = floor($payment->duration / 30);
                                        $duration = $months . ' เดือน';
                                    }
                                }

                                $status_class = 'status-active';
                                $status_text = 'ใช้งาน';

                                $position_value = '';
                                if (isset($payment->ad_position) && !empty($payment->ad_position)) {
                                    $position_value = $payment->ad_position;
                                } elseif (isset($payment->position) && !empty($payment->position)) {
                                    $position_value = $payment->position;
                                } elseif (isset($payment->position_id) && !empty($payment->position_id)) {
                                    $position_value = $payment->position_id;
                                }

                                if (!empty($position_value)) {
                                    $position_entry = $wpdb->get_row($wpdb->prepare(
                                        "SELECT expiration_date FROM {$positions_table} WHERE ad_position = %s",
                                        $position_value
                                    ));
                                } else {
                                    $position_entry = null;
                                }

                                if ($position_entry && $position_entry->expiration_date && $position_entry->expiration_date != '0000-00-00 00:00:00') {
                                    $exp_date = new DateTime($position_entry->expiration_date);
                                    $now = new DateTime();

                                    if ($exp_date < $now) {
                                        $status_class = 'status-expired';
                                        $status_text = 'หมดอายุ';
                                    } elseif ($exp_date->diff($now)->days <= 7) {
                                        $status_class = 'status-expiring';
                                        $status_text = 'ใกล้หมดอายุ';
                                    }
                                }
                            ?>
                            <tr>
                                <td class="date"><?php echo date('d/m/Y H:i', strtotime($payment->payment_date)); ?></td>
                                <td>
                                    <?php if (!empty($position_value)): ?>
                                        <span class="position-name"><?php echo esc_html($position_value); ?></span>
                                    <?php else: ?>
                                        <span class="position-unknown">ไม่ระบุตำแหน่ง</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($username); ?></td>
                                <td><?php echo esc_html($duration); ?></td>
                                <td class="amount"><?php echo number_format($payment->amount, 2); ?> <span class="currency"><?php echo esc_html(strtoupper($payment->currency ?? 'THB')); ?></span></td>
                                <td class="status">
                                    <?php
                                    $method_class = 'method-bypass';
                                    $method_text = 'Bypass';
                                    if ($payment->payment_method !== 'bypass') {
                                        $method_class = 'method-api';
                                        $method_text = 'API';
                                    }
                                    ?>
                                    <span class="status-badge <?php echo $method_class; ?>"><?php echo $method_text; ?></span>
                                </td>
                                <td class="status">
                                    <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <p>ยังไม่มีรายการซื้อ</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-users"></i> สถิติการคลิกของลูกค้า</h2>
                    <div class="dashboard-section-subtitle">ข้อมูลการคลิกของลูกค้าแต่ละคน</div>
                </div>
                <div class="header-right">
                    <button class="ad-btn ad-btn-primary" id="refresh-customer-stats">
                        <i class="fas fa-sync-alt"></i> รีเฟรชข้อมูล
                    </button>
                </div>
            </div>
            <div class="dashboard-section-body">
                <div id="customer-stats-loading" style="display: none;">
                    <div class="loading-container">
                        <div class="loading-bar">
                            <div class="loading-progress"></div>
                        </div>
                        <p>กำลังโหลดสถิติการคลิกของลูกค้า...</p>
                    </div>
                </div>
                <div id="customer-stats-container">
                    <?php
                    $customers = get_users(array('role' => 'advertiser'));
                    if (!empty($customers)): ?>
                    <div class="customer-stats-table-container">
                        <table class="customer-stats-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-user"></i> ลูกค้า</th>
                                    <th><i class="fas fa-map-marker-alt"></i> ตำแหน่งที่มี</th>
                                    <th><i class="fas fa-mouse-pointer"></i> คลิกทั้งหมด</th>
                                    <th><i class="fas fa-calendar-day"></i> คลิก 30 วัน</th>
                                    <th><i class="fas fa-chart-line"></i> อัตราคลิก</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($customers as $customer):
                                    $customer_positions = get_user_meta($customer->ID, 'ad_positions', true);
                                    $customer_positions = is_array($customer_positions) ? $customer_positions : array();

                                    $total_clicks = function_exists('get_user_total_clicks') ? get_user_total_clicks($customer->ID, false) : 0;
                                    $monthly_clicks = function_exists('get_user_total_clicks') ? get_user_total_clicks($customer->ID, true) : 0;

                                    $click_rate = count($customer_positions) > 0 ? ($monthly_clicks / count($customer_positions)) : 0;
                                ?>
                                <tr>
                                    <td>
                                        <div class="customer-info">
                                            <strong><?php echo esc_html($customer->display_name); ?></strong>
                                            <small>@<?php echo esc_html($customer->user_login); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="positions-count"><?php echo count($customer_positions); ?> ตำแหน่ง</span>
                                        <?php if (!empty($customer_positions)): ?>
                                        <div class="positions-preview">
                                            <?php echo esc_html(implode(', ', array_slice($customer_positions, 0, 3))); ?>
                                            <?php if (count($customer_positions) > 3): ?>
                                                <span class="more-positions">+<?php echo count($customer_positions) - 3; ?> อื่นๆ</span>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="click-count total"><?php echo number_format($total_clicks); ?></span>
                                    </td>
                                    <td>
                                        <span class="click-count monthly"><?php echo number_format($monthly_clicks); ?></span>
                                    </td>
                                    <td>
                                        <span class="click-rate"><?php echo number_format($click_rate, 1); ?> คลิก/ตำแหน่ง</span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <p>ยังไม่มีลูกค้าในระบบ</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-chart-bar"></i> จัดการสถานะระบบ</h2>
                </div>
                <div class="header-right">
                    <button class="ad-btn ad-btn-danger" id="clear-all-statistics">
                        <i class="fas fa-eraser"></i> ล้างสถานะทั้งหมด
                    </button>
                </div>
            </div>
            <div class="dashboard-section-body">
                <div class="system-status-grid">
                    <div class="status-card">
                        <div class="status-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="status-content">
                            <h4>ข้อมูลที่จะถูกล้าง</h4>
                            <ul>
                                <li><i class="fas fa-check"></i> ประวัติการซื้อขาย (<?php echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ad_payments"); ?> รายการ)</li>
                                <li><i class="fas fa-check"></i> สถิติการคลิกทั้งหมด</li>
                                <li><i class="fas fa-check"></i> ข้อมูล Cache ระบบ</li>
                                <li><i class="fas fa-check"></i> ข้อมูลสถิติต่างๆ</li>
                            </ul>
                        </div>
                    </div>

                    <div class="status-card">
                        <div class="status-icon preserve">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="status-content">
                            <h4>ข้อมูลที่จะไม่ถูกลบ</h4>
                            <ul>
                                <li><i class="fas fa-times"></i> ตำแหน่งโฆษณา (<?php echo $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}ad_positions"); ?> ตำแหน่ง)</li>
                                <li><i class="fas fa-times"></i> บัญชีลูกค้า (<?php echo count(get_users(array('role' => 'advertiser'))); ?> คน)</li>
                                <li><i class="fas fa-times"></i> การตั้งค่าระบบ</li>
                                <li><i class="fas fa-times"></i> ข้อมูลผู้ใช้</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="stats-management-info">
                    <div class="warning-box">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>คำเตือน:</strong> การดำเนินการนี้ไม่สามารถยกเลิกได้ กรุณาตรวจสอบข้อมูลให้แน่ใจก่อนดำเนินการ
                    </div>
                </div>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-chart-line"></i> ตำแหน่งที่มีคลิกมากที่สุด</h2>
                </div>
            </div>
            <div class="dashboard-section-body">
                <?php if (!empty($top_positions)): ?>
                <div class="transaction-table-container">
                    <table class="transaction-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-map-marker-alt"></i> ตำแหน่ง</th>
                                <th><i class="fas fa-user"></i> เจ้าของ</th>
                                <th><i class="fas fa-mouse-pointer"></i> จำนวนคลิก</th>
                                <th><i class="fas fa-calendar-alt"></i> วันหมดอายุ</th>
                                <th class="status"><i class="fas fa-info-circle"></i> สถานะ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_positions as $position):
                                $owner = 'ไม่มีเจ้าของ';
                                $owner_display = 'ไม่มีเจ้าของ';
                                $owner_id = 0;
                                $click_count = $position->click_count;

                        
                                $owner_user_id = $wpdb->get_var($wpdb->prepare(
                                    "SELECT user_id FROM {$wpdb->usermeta}
                                     WHERE meta_key = 'ad_positions'
                                     AND meta_value LIKE %s",
                                    '%' . $wpdb->esc_like('"' . $position->ad_position . '"') . '%'
                                ));

                                if ($owner_user_id) {
                            
                                    $user_positions = get_user_meta($owner_user_id, 'ad_positions', true);
                                    if (is_array($user_positions) && in_array($position->ad_position, $user_positions)) {
                                        $owner_user = get_userdata($owner_user_id);
                                        if ($owner_user) {
                                            $owner_id = $owner_user->ID;
                                            $owner = $owner_user->user_login;
                                            $owner_display = $owner_user->display_name . ' (@' . $owner_user->user_login . ')';
                                        }
                                    }
                                }

                        
                                if ($owner_id == 0) {
                                    foreach ($ads as $ad) {
                                        if ($ad['position'] === $position->ad_position && !empty($ad['user'])) {
                                            $owner = $ad['user'];
                                            $owner_user = get_user_by('login', $owner);
                                            if ($owner_user) {
                                                $owner_id = $owner_user->ID;
                                                $owner_display = $owner_user->display_name . ' (@' . $owner_user->user_login . ')';
                                            }
                                            break;
                                        }
                                    }
                                }

                                $expiration_date = 'ไม่มีกำหนด';
                                $status_class = 'status-active';
                                $status_text = 'ใช้งาน';

                                $position_entry = $wpdb->get_row($wpdb->prepare(
                                    "SELECT expiration_date FROM {$positions_table} WHERE ad_position = %s",
                                    $position->ad_position
                                ));

                                if ($position_entry && $position_entry->expiration_date && $position_entry->expiration_date != '0000-00-00 00:00:00') {
                                    $exp_date = new DateTime($position_entry->expiration_date);
                                    $expiration_date = $exp_date->format('d/m/Y');
                                    $now = new DateTime();

                                    if ($exp_date < $now) {
                                        $status_class = 'status-expired';
                                        $status_text = 'หมดอายุ';
                                    } elseif ($exp_date->diff($now)->days <= 7) {
                                        $status_class = 'status-expiring';
                                        $status_text = 'ใกล้หมดอายุ';
                                    }
                                }
                            ?>
                            <tr>
                                <td>
                                    <span class="position-name"><?php echo esc_html($position->ad_position); ?></span>
                                </td>
                                <td>
                                    <?php if ($owner_id > 0): ?>
                                        <div class="owner-info">
                                            <strong><?php echo esc_html($owner_display); ?></strong>
                                        </div>
                                    <?php else: ?>
                                        <span class="no-owner">ไม่มีเจ้าของ</span>
                                        <?php
                                
                                        if (current_user_can('manage_options')) {
                                            echo '<br><small style="color: #999;">Debug: Position=' . esc_html($position->ad_position);

                                            // ตรวจสอบว่ามีใครเป็นเจ้าของตำแหน่งนี้บ้าง
                                            $debug_owner = $wpdb->get_var($wpdb->prepare(
                                                "SELECT user_id FROM {$wpdb->usermeta}
                                                 WHERE meta_key = 'ad_positions'
                                                 AND meta_value LIKE %s",
                                                '%' . $wpdb->esc_like('"' . $position->ad_position . '"') . '%'
                                            ));

                                            if ($debug_owner) {
                                                $debug_user = get_userdata($debug_owner);
                                                echo '<br>Found owner: ' . ($debug_user ? $debug_user->user_login : 'Unknown') . ' (ID: ' . $debug_owner . ')';

                                                $debug_positions = get_user_meta($debug_owner, 'ad_positions', true);
                                                echo '<br>User positions: ' . (is_array($debug_positions) ? implode(', ', $debug_positions) : 'Not array');
                                            } else {
                                                echo '<br>No owner found in usermeta';
                                            }
                                            echo '</small>';
                                        }
                                        ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="click-count-badge"><?php echo number_format($click_count); ?> คลิก</span>
                                </td>
                                <td><?php echo $expiration_date; ?></td>
                                <td class="status">
                                    <span class="status-badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-mouse-pointer"></i>
                    <p>ยังไม่มีข้อมูลคลิก</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-chart-line"></i> Google Analytics 4 Insights</h2>
                </div>
            </div>
            <div class="dashboard-section-body">
                <?php
                $realtime_users = $ga_realtime_users;
                $daily_users = amp_get_monthly_users(1);
                $monthly_users = $ga_monthly_users;
                $using_ga4_data = $ga_configured;
                ?>

                <div class="ga4-stats-container">
                    <div class="ga4-stat-card">
                        <div class="ga4-stat-icon realtime-icon">
                            <i class="fas fa-user-clock"></i>
                        </div>
                        <div class="ga4-stat-content">
                            <h3>ผู้ใช้งานขณะนี้</h3>
                            <div class="ga4-stat-value"><?php echo number_format($realtime_users); ?></div>
                            <div class="ga4-stat-details">
                                <span>Real-time</span>
                            </div>
                        </div>
                    </div>

                    <div class="ga4-stat-card">
                        <div class="ga4-stat-icon daily-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="ga4-stat-content">
                            <h3>ผู้ใช้งานวันนี้</h3>
                            <div class="ga4-stat-value"><?php echo number_format($daily_users); ?></div>
                            <div class="ga4-stat-details">
                                <span>วันนี้</span>
                            </div>
                        </div>
                    </div>

                    <div class="ga4-stat-card">
                        <div class="ga4-stat-icon monthly-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="ga4-stat-content">
                            <h3>ผู้ใช้งาน 31 วัน</h3>
                            <div class="ga4-stat-value"><?php echo number_format($monthly_users); ?></div>
                            <div class="ga4-stat-details">
                                <span><?php echo date('d M', strtotime('-31 days')); ?> - <?php echo date('d M'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    .ga4-stats-container {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 20px;
                        margin-top: 10px;
                    }

                    .ga4-stat-card {
                        background: #fff;
                        border-radius: 10px;
                        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
                        padding: 20px;
                        display: flex;
                        align-items: center;
                        transition: all 0.3s ease;
                    }

                    .ga4-stat-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
                    }

                    .ga4-stat-icon {
                        width: 50px;
                        height: 50px;
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 15px;
                    }

                    .ga4-stat-icon i {
                        color: white;
                        font-size: 20px;
                    }

                    .realtime-icon {
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                    }

                    .daily-icon {
                        background: linear-gradient(135deg, #9b59b6, #8e44ad);
                    }

                    .monthly-icon {
                        background: linear-gradient(135deg, #3498db, #2980b9);
                    }

                    .ga4-stat-content {
                        flex-grow: 1;
                    }

                    .ga4-stat-content h3 {
                        font-size: 14px;
                        margin: 0 0 5px;
                        color: #555;
                    }

                    .ga4-stat-value {
                        font-size: 24px;
                        font-weight: 700;
                        color: #333;
                        margin-bottom: 5px;
                    }

                    .ga4-stat-details {
                        font-size: 12px;
                        color: #777;
                    }

                    @media (max-width: 991px) {
                        .ga4-stats-container {
                            grid-template-columns: repeat(2, 1fr);
                        }
                    }

                    @media (max-width: 576px) {
                        .ga4-stats-container {
                            grid-template-columns: 1fr;
                        }
                    }
                </style>
            </div>
        </div>

        <div class="dashboard-section animate-slide-up">
            <div class="dashboard-section-header">
                <div class="header-left">
                    <h2 class="dashboard-section-title"><i class="fas fa-bolt"></i> การดำเนินการด่วน</h2>
                    <div class="dashboard-section-subtitle">เข้าถึงฟีเจอร์หลักได้อย่างรวดเร็ว</div>
                </div>
            </div>
            <div class="dashboard-section-body">
                <div class="dashboard-quick-actions">
                    <a href="<?php echo admin_url('admin.php?page=ad-management'); ?>" class="dashboard-action-button primary">
                        <div class="action-icon">
                            <i class="fas fa-ad"></i>
                        </div>
                        <div class="action-content">
                            <h4>จัดการตำแหน่งโฆษณา</h4>
                            <p>เพิ่ม แก้ไข หรือลบตำแหน่งโฆษณา</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=customer-management'); ?>" class="dashboard-action-button success">
                        <div class="action-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="action-content">
                            <h4>จัดการลูกค้า</h4>
                            <p>ดูข้อมูลและจัดการบัญชีผู้ใช้</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=price-calculation-settings'); ?>" class="dashboard-action-button warning">
                        <div class="action-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="action-content">
                            <h4>คำนวณราคา</h4>
                            <p>ตั้งค่าราคาและส่วนลดสำหรับโฆษณา</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=general-settings&tab=payment'); ?>" class="dashboard-action-button info">
                        <div class="action-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="action-content">
                            <h4>ตั้งค่าการชำระเงิน</h4>
                            <p>กำหนดค่าระบบชำระเงิน Crypto</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <a href="<?php echo admin_url('admin.php?page=general-settings&tab=manual-verification'); ?>" class="dashboard-action-button warning">
                        <div class="action-icon">
                            <i class="fas fa-search-dollar"></i>
                        </div>
                        <div class="action-content">
                            <h4>ตรวจสอบการชำระเงิน</h4>
                            <p>ยืนยันการชำระเงินด้วยตนเอง</p>
                        </div>
                        <div class="action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        const adminDashboardData = {
            clearStatsNonce: '<?php echo $clear_stats_nonce; ?>',
            revenueData: <?php echo json_encode($revenue_data); ?>,
            clicksData: <?php echo json_encode($clicks_data); ?>
        };
        let activeTimersInterval;
        function loadActiveTimers() {
            fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_all_active_timers'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        displayActiveTimers(data.data.timers);
                    } else {
                        console.error('Error loading timers:', data.data);
                    }
                } catch (parseError) {
                    console.error('AJAX Response not JSON:', text);
                    console.error('Parse error:', parseError);
                }
            })
            .catch(error => {
                console.error('AJAX error:', error);
            });
        }

        function displayActiveTimers(timers) {
            const container = document.getElementById('active-timers-container');
            
            if (!timers || timers.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-clock"></i>
                        <p>ไม่มีการจองที่ใช้งานอยู่</p>
                    </div>
                `;
                return;
            }

            let html = `
                <div class="timers-table-container">
                    <table class="timers-table">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user"></i> ผู้ใช้</th>
                                <th><i class="fas fa-clock"></i> เหลือเวลา</th>
                                <th><i class="fas fa-map-marker-alt"></i> ตำแหน่งที่จอง</th>
                                <th><i class="fas fa-signal"></i> สถานะการเชื่อมต่อ</th>
                                <th><i class="fas fa-calendar"></i> เริ่มเมื่อ</th>
                                <th><i class="fas fa-cog"></i> การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            timers.forEach(timer => {
                const remainingTime = formatTime(timer.remaining_seconds);
                const connectionStatus = timer.is_connected ? 
                    '<span class="status-badge status-active">เชื่อมต่อ</span>' : 
                    '<span class="status-badge status-danger">หลุดการเชื่อมต่อ</span>';
                let positionsDisplay;
                if (timer.positions_text && timer.positions_text.trim() !== '') {
                    positionsDisplay = `<div class="dashboard-positions-list"><strong>${timer.positions_text}</strong><br><small>(${timer.cart_items} รายการ)</small></div>`;
                } else if (timer.cart_items > 0) {
                    positionsDisplay = `<span class="positions-unknown"><em>มี ${timer.cart_items} รายการ<br>(ไม่พบชื่อตำแหน่ง)</em></span>`;
                } else {
                    positionsDisplay = `<span class="no-positions"><em>ไม่มีข้อมูลตำแหน่ง</em></span>`;
                }

                html += `
                    <tr class="${!timer.is_connected ? 'timer-disconnected' : ''}">
                        <td>
                            <strong>${timer.display_name}</strong><br>
                            <small>@${timer.user_login}</small>
                        </td>
                        <td class="countdown-cell" data-remaining="${timer.remaining_seconds}">
                            ${remainingTime}
                        </td>
                        <td>${positionsDisplay}</td>
                        <td>${connectionStatus}</td>
                        <td>${timer.created_time}</td>
                        <td>
                            <button class="ad-btn ad-btn-danger cancel-timer-btn" data-user-id="${timer.user_id}">
                                <i class="fas fa-times"></i> ยกเลิก
                            </button>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;
            container.innerHTML = html;
            document.querySelectorAll('.cancel-timer-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const userId = this.dataset.userId;
                    cancelUserTimer(userId);
                });
            });
        }

        function formatTime(seconds) {
            if (seconds <= 0) return '<span class="expired">หมดเวลา</span>';
            
            const minutes = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        function cancelUserTimer(userId) {
            Swal.fire({
                title: 'ยกเลิกการจอง?',
                text: 'คุณแน่ใจหรือไม่ที่จะยกเลิกการจองของผู้ใช้นี้?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ใช่, ยกเลิก!',
                cancelButtonText: 'ไม่ยกเลิก'
            }).then((result) => {
                if (!result.isConfirmed) {
                    return;
                }
                
                performCancelTimer(userId);
            });
        }

        function performCancelTimer(userId) {
            fetch(ajaxurl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=cancel_user_timer_admin&user_id=${userId}`
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        Swal.fire({
                            title: 'สำเร็จ!',
                            text: 'ยกเลิกการจองเรียบร้อยแล้ว',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        loadActiveTimers();
                    } else {
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด!',
                            text: data.data.message || 'ไม่สามารถยกเลิกการจองได้',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                } catch (parseError) {
                    console.error('Cancel Timer Response not JSON:', text);
                    Swal.fire({
                        title: 'เกิดข้อผิดพลาด!',
                        text: 'ระบบตอบกลับไม่ถูกต้อง',
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                }
            })
            .catch(error => {
                console.error('Cancel Timer error:', error);
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด!',
                    text: 'เกิดข้อผิดพลาดในระบบ',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
            });
        }
        function updateCountdownDisplays() {
            document.querySelectorAll('.countdown-cell').forEach(cell => {
                const remaining = parseInt(cell.dataset.remaining);
                if (remaining > 0) {
                    cell.dataset.remaining = remaining - 1;
                    cell.innerHTML = formatTime(remaining - 1);
                }
            });
        }
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelectorAll('.animate-fade-in').forEach(el => el.classList.add('visible'));
                document.querySelectorAll('.animate-slide-up').forEach(el => el.classList.add('visible'));
            }, 100);

            loadActiveTimers();
            activeTimersInterval = setInterval(loadActiveTimers, 2000);
            setInterval(updateCountdownDisplays, 1000);
            document.getElementById('refresh-timers-btn').addEventListener('click', function() {
                const btn = this;
                const icon = btn.querySelector('i');
                const originalIconClass = icon.className;

                btn.disabled = true;
                icon.className = 'fas fa-spinner fa-spin';

                fetch(ajaxurl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=force_cleanup_disconnected_timers'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'success',
                            title: 'ล้างสถานะที่หลุดการเชื่อมต่อแล้ว',
                            showConfirmButton: false,
                            timer: 2000
                        });
                    } else {
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'error',
                            title: 'ไม่สามารถล้างสถานะได้',
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                })
                .catch(error => console.error('Error forcing cleanup:', error))
                .finally(() => {
                    loadActiveTimers();
                    btn.disabled = false;
                    icon.className = originalIconClass;
                });
            });
        });
    </script>
    
    <style>
        .positions-list strong {
            color: #2563eb;
            font-weight: 600;
        }
        
        .positions-unknown {
            color: #d97706;
            font-style: italic;
        }
        
        .no-positions {
            color: #9ca3af;
            font-style: italic;
        }
        
        .timer-disconnected {
            background-color: #fff5f5 !important;
            border-left: 4px solid #ef4444 !important;
        }
        
        .timers-table-container {
            margin-top: 15px;
        }
        
        .timers-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .timers-table th,
        .timers-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .timers-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .countdown-cell {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            font-size: 1.1em;
        }

        .stats-management-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .stats-management-info p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }

        .stats-management-info i {
            color: #17a2b8;
            margin-right: 8px;
        }

        .dashboard-charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        @media (max-width: 1200px) {
            .dashboard-charts-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 15px;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 10px;
        }

        .chart-legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-color);
        }

        .chart-legend-color {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }



        .dashboard-section-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 25px;
            gap: 20px;
        }

        .header-left {
            flex: 1;
        }

        .header-right {
            flex-shrink: 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .dashboard-section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 0 0 5px 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color);
        }

        .dashboard-section-title i {
            color: var(--primary-color);
        }

        .dashboard-section-subtitle {
            font-size: 12px;
            color: var(--text-muted);
            margin: 0;
        }

        /* ปรับปุ่มให้สวยงาม */
        .header-right .ad-btn {
            padding: 8px 16px;
            font-size: 13px;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .header-right .ad-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .header-right .ad-btn i {
            margin-right: 6px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-section-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .header-right {
                width: 100%;
                justify-content: flex-start;
            }
        }

        .revenue-summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .revenue-summary-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .revenue-summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            border-radius: 15px 15px 0 0;
        }

        .revenue-summary-card.total::before {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .revenue-summary-card.today::before {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .revenue-summary-card.week::before {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .revenue-summary-card.month::before {
            background: linear-gradient(135deg, #43e97b, #38f9d7);
        }

        .revenue-summary-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .revenue-summary-card .revenue-summary-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        }

        .revenue-summary-card .revenue-summary-icon i {
            color: white;
            font-size: 20px;
        }

        .revenue-summary-content h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .revenue-summary-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 5px;
        }

        .revenue-summary-unit {
            font-size: 12px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .dashboard-quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .dashboard-action-button {
            display: flex;
            align-items: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            text-decoration: none;
            color: var(--text-color);
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .dashboard-action-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 4px;
            transition: width 0.3s ease;
        }

        .dashboard-action-button.primary::before {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        }

        .dashboard-action-button.success::before {
            background: linear-gradient(135deg, var(--success-color), #27ae60);
        }

        .dashboard-action-button.warning::before {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
        }

        .dashboard-action-button.info::before {
            background: linear-gradient(135deg, var(--info-color), #2980b9);
        }

        .dashboard-action-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            text-decoration: none;
            color: var(--text-color);
        }

        .dashboard-action-button:hover::before {
            width: 100%;
            opacity: 0.05;
        }

        .action-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .dashboard-action-button.primary .action-icon {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        }

        .dashboard-action-button.success .action-icon {
            background: linear-gradient(135deg, var(--success-color), #27ae60);
        }

        .dashboard-action-button.warning .action-icon {
            background: linear-gradient(135deg, var(--warning-color), #e67e22);
        }

        .dashboard-action-button.info .action-icon {
            background: linear-gradient(135deg, var(--info-color), #2980b9);
        }

        .action-icon i {
            color: white;
            font-size: 18px;
        }

        .action-content {
            flex-grow: 1;
        }

        .action-content h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .action-content p {
            margin: 0;
            font-size: 13px;
            color: var(--text-muted);
            line-height: 1.4;
        }

        .action-arrow {
            margin-left: 15px;
            color: var(--text-muted);
            transition: all 0.3s ease;
        }

        .dashboard-action-button:hover .action-arrow {
            transform: translateX(5px);
            color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .revenue-summary-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .dashboard-quick-actions {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .revenue-summary-grid {
                grid-template-columns: 1fr;
            }
        }

        .animate-fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
        }

        .animate-fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .animate-slide-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-slide-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .dashboard-card:nth-child(1) { transition-delay: 0.1s; }
        .dashboard-card:nth-child(2) { transition-delay: 0.2s; }
        .dashboard-card:nth-child(3) { transition-delay: 0.3s; }
        .dashboard-card:nth-child(4) { transition-delay: 0.4s; }

        .revenue-summary-card:nth-child(1) { transition-delay: 0.1s; }
        .revenue-summary-card:nth-child(2) { transition-delay: 0.2s; }
        .revenue-summary-card:nth-child(3) { transition-delay: 0.3s; }
        .revenue-summary-card:nth-child(4) { transition-delay: 0.4s; }

        .dashboard-action-button:nth-child(1) { transition-delay: 0.1s; }
        .dashboard-action-button:nth-child(2) { transition-delay: 0.2s; }
        .dashboard-action-button:nth-child(3) { transition-delay: 0.3s; }
        .dashboard-action-button:nth-child(4) { transition-delay: 0.4s; }

        .dashboard-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .dashboard-title {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .dashboard-actions {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .dashboard-actions .ad-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .dashboard-actions .ad-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(67, 97, 238, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
            }
        }

        .dashboard-section {
            margin-bottom: 35px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .dashboard-section:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            transform: translateY(-2px);
        }

        .dashboard-section-body {
            padding: 25px;
            background: white;
        }

        /* เพิ่ม spacing ระหว่าง sections */
        .revenue-summary-section {
            margin-bottom: 40px;
        }

        .dashboard-charts-container {
            margin-bottom: 40px;
        }

        /* ปรับปรุงการแสดงผลของ empty state */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state p {
            margin: 0;
            font-size: 16px;
        }

        .transaction-table .status-badge.method-api {
            background-color: #1a73e8;
            color: white;
        }
        .transaction-table .status-badge.method-bypass {
            background-color: #6c757d;
            color: white;
        }
    </style>

    <script>
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async function loadRequiredLibraries() {
        const promises = [];

        if (typeof Swal === 'undefined') {
            const swalScript = document.createElement('script');
            swalScript.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11.14.5/dist/sweetalert2.all.min.js';
            swalScript.crossOrigin = 'anonymous';
            promises.push(new Promise((resolve, reject) => {
                swalScript.onload = resolve;
                swalScript.onerror = reject;
                document.head.appendChild(swalScript);
            }));
        }

        if (typeof Chart === 'undefined') {
            const chartScript = document.createElement('script');
            chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
            chartScript.crossOrigin = 'anonymous';
            promises.push(new Promise((resolve, reject) => {
                chartScript.onload = resolve;
                chartScript.onerror = reject;
                document.head.appendChild(chartScript);
            }));
        }

        if (promises.length > 0) {
            try {
                await Promise.all(promises);
                console.log('AMP: Required libraries loaded successfully');
            } catch (error) {
                console.error('AMP: Failed to load required libraries:', error);
                if (typeof console !== 'undefined') {
                    console.warn('AMP: Some dashboard features may not work properly');
                }
            }
        }
    }

    loadRequiredLibraries();
    </script>

    <script src="<?php echo plugin_dir_url(dirname(dirname(__FILE__))) . 'admin/assets/js/modern-dashboard.js'; ?>?v=<?php echo time(); ?>"></script>
    <?php
}
