<?php

if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
$position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
$current_user_id = get_current_user_id();
$purchase_history = $current_user_id ? $position_manager->get_user_purchase_history($current_user_id) : [];

function format_duration($days) {
    if (is_null($days) || !is_numeric($days)) {
        return 'N/A';
    }
    if ($days >= 365) {
        $years = floor($days / 365);
        return $years . ' ' . ($years > 1 ? 'ปี' : 'ปี');
    } elseif ($days >= 30) {
        $months = floor($days / 30);
        return $months . ' ' . ($months > 1 ? 'เดือน' : 'เดือน');
    } else {
        return $days . ' ' . ($days > 1 ? 'วัน' : 'วัน');
    }
}

function get_status_info($status) {
    switch (strtolower($status)) {
        case 'completed':
            return ['text' => 'สำเร็จ', 'class' => 'status-completed', 'icon' => 'fas fa-check-circle'];
        case 'pending':
            return ['text' => 'รอ xử lý', 'class' => 'status-pending', 'icon' => 'fas fa-hourglass-half'];
        case 'failed':
            return ['text' => 'ล้มเหลว', 'class' => 'status-failed', 'icon' => 'fas fa-times-circle'];
        case 'refunded':
            return ['text' => 'คืนเงิน', 'class' => 'status-refunded', 'icon' => 'fas fa-undo-alt'];
        default:
            return ['text' => 'ไม่ทราบ', 'class' => 'status-unknown', 'icon' => 'fas fa-question-circle'];
    }
}
?>

<style>
.purchase-history-page {
    padding: 20px;
    background-color: var(--body-bg);
    position: relative;
    overflow: hidden;
    width: 100%;
    min-height: 100vh;
}

.floating-elements {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    pointer-events: none;
    overflow: hidden;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    color: var(--primary-color);
    opacity: 0.05;
    animation: float 15s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-50px) rotate(180deg); }
}

.floating-icon:nth-child(1) { top: 10%; left: 15%; animation-delay: 0s; animation-duration: 20s; }
.floating-icon:nth-child(2) { top: 25%; right: 20%; animation-delay: -5s; animation-duration: 25s; }
.floating-icon:nth-child(3) { bottom: 30%; left: 25%; animation-delay: -10s; animation-duration: 18s; }
.floating-icon:nth-child(4) { bottom: 15%; right: 10%; animation-delay: -15s; animation-duration: 22s; }

.history-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: var(--theme-gradient-light);
    border-radius: 24px;
    position: relative;
}

.history-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.history-subtitle {
    font-size: 1.2rem;
    color: var(--muted-text);
}

.table-responsive {
    overflow-x: auto;
    background: var(--card-bg);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    padding: 10px;
}

.purchase-history-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    min-width: 900px;
}

.purchase-history-table thead {
    background-color: var(--table-header-bg);
}

.purchase-history-table th {
    padding: 18px 25px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--muted-text);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
}

.purchase-history-table th:first-child { border-top-left-radius: 12px; }
.purchase-history-table th:last-child { border-top-right-radius: 12px; }

.purchase-history-table tbody tr {
    transition: background-color 0.3s, transform 0.3s;
    border-bottom: 1px solid var(--divider-color);
}

.purchase-history-table tbody tr:last-child {
    border-bottom: none;
}

.purchase-history-table tbody tr:hover {
    background-color: var(--hover-bg);
    transform: scale(1.01);
    box-shadow: 0 4px 20px rgba(0,0,0,0.05);
    z-index: 10;
    position: relative;
}

.purchase-history-table td {
    padding: 20px 25px;
    vertical-align: middle;
}

.date-cell { font-weight: 500; color: var(--text-color); }
.position-tag {
    background-color: var(--primary-color-light);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}
.amount-cell { font-weight: 700; color: var(--success-color); font-size: 1.1rem; }

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}
.status-completed { background-color: rgba(40, 167, 69, 0.15); color: #28a745; }
.status-pending { background-color: rgba(255, 193, 7, 0.15); color: #ffc107; }
.status-failed { background-color: rgba(220, 53, 69, 0.15); color: #dc3545; }
.status-refunded { background-color: rgba(23, 162, 184, 0.15); color: #17a2b8; }

.txn-id { font-family: 'Courier New', Courier, monospace; color: var(--muted-text); font-size: 0.9rem; }
.btn-receipt {
    background: var(--theme-gradient);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
}
.btn-receipt:hover { transform: translateY(-2px); box-shadow: var(--shadow); }
.btn-receipt:disabled { background: var(--border-color); color: var(--muted-text); cursor: not-allowed; }

.purchase-type-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}
.indicator-api { background-color: var(--primary-color); }
.indicator-bypass { background-color: var(--warning-color); }

.empty-state-container {
    text-align: center;
    padding: 80px 40px;
    background: var(--card-bg);
    border-radius: 24px;
    margin: 40px auto;
    max-width: 600px;
    border: 2px dashed var(--border-color);
}
.empty-state-icon { font-size: 4rem; color: var(--primary-color); opacity: 0.7; margin-bottom: 25px; }
.empty-state-title { font-size: 1.8rem; font-weight: 700; color: var(--text-color); margin-bottom: 15px; }
.empty-state-text { font-size: 1.1rem; color: var(--muted-text); margin-bottom: 30px; }
.btn-primary-empty {
    background: var(--theme-gradient);
    color: white;
    padding: 15px 30px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    border: none;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}
.btn-primary-empty:hover { transform: translateY(-3px); box-shadow: var(--shadow-lg); }

@media (max-width: 768px) {
    .purchase-history-table { min-width: 0; }
    .purchase-history-table thead { display: none; }
    .purchase-history-table tbody, .purchase-history-table tr, .purchase-history-table td { display: block; width: 100%; }
    .purchase-history-table tr { margin-bottom: 20px; border-radius: 16px; border: 1px solid var(--border-color); overflow: hidden; }
    .purchase-history-table td { text-align: right; padding-left: 50%; position: relative; border-bottom: 1px solid var(--divider-color); }
    .purchase-history-table td:last-child { border-bottom: none; }
    .purchase-history-table td::before {
        content: attr(data-label);
        position: absolute;
        left: 20px;
        width: 45%;
        padding-right: 10px;
        white-space: nowrap;
        text-align: left;
        font-weight: 600;
        color: var(--muted-text);
    }
}
</style>

<div class="purchase-history-page">
    <div class="floating-elements">
        <i class="fas fa-receipt floating-icon"></i>
        <i class="fas fa-file-invoice-dollar floating-icon"></i>
        <i class="fas fa-history floating-icon"></i>
        <i class="fas fa-shopping-bag floating-icon"></i>
    </div>

    <div class="history-header">
        <h2 class="history-title"><i class="fas fa-history"></i> ประวัติการซื้อ</h2>
        <p class="history-subtitle">ภาพรวมธุรกรรมทั้งหมดของคุณ</p>
    </div>

    <?php if (!empty($purchase_history)) : ?>
    <div class="table-responsive">
        <table class="purchase-history-table">
            <thead>
                <tr>
                    <th>ประเภท</th>
                    <th><i class="fas fa-calendar-alt"></i> วันที่</th>
                    <th><i class="fas fa-tag"></i> ตำแหน่ง</th>
                    <th><i class="fas fa-hourglass-half"></i> ระยะเวลา</th>
                    <th><i class="fas fa-wallet"></i> จำนวนเงิน</th>
                    <th><i class="fas fa-receipt"></i> รหัสธุรกรรม</th>
                    <th><i class="fas fa-check-circle"></i> สถานะ</th>
                    <th><i class="fas fa-file-invoice-dollar"></i> ใบเสร็จ</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($purchase_history as $purchase) :
                    $status_info = get_status_info($purchase->status);
                    $purchase_date = $purchase->purchase_date ?? $purchase->payment_date ?? $purchase->created_at;
                    $is_bypass = ($purchase->purchase_type ?? '') === 'bypass';
                ?>
                <tr>
                    <td data-label="ประเภท">
                        <span class="purchase-type-indicator <?php echo $is_bypass ? 'indicator-bypass' : 'indicator-api'; ?>"></span>
                        <?php echo $is_bypass ? 'Bypass' : 'API'; ?>
                    </td>
                    <td data-label="วันที่">
                        <div class="date-cell"><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($purchase_date))); ?></div>
                    </td>
                    <td data-label="ตำแหน่ง">
                        <span class="position-tag"><?php echo esc_html($purchase->ad_position); ?></span>
                    </td>
                    <td data-label="ระยะเวลา"><?php echo esc_html(format_duration($purchase->duration)); ?></td>
                    <td data-label="จำนวนเงิน" class="amount-cell">
                        <?php echo number_format($purchase->amount, 2); ?> USDT
                    </td>
                    <td data-label="รหัสธุรกรรม">
                        <span class="txn-id"><?php echo esc_html($purchase->transaction_id ?: ($is_bypass ? 'N/A' : 'Unknown')); ?></span>
                    </td>
                    <td data-label="สถานะ">
                        <span class="status-badge <?php echo esc_attr($status_info['class']); ?>">
                            <i class="<?php echo esc_attr($status_info['icon']); ?>"></i>
                            <?php echo esc_html($status_info['text']); ?>
                        </span>
                    </td>
                    <td data-label="ใบเสร็จ">
                        <button class="btn-receipt" <?php echo $is_bypass ? 'disabled' : ''; ?>>
                            <i class="fas fa-eye"></i> ดูใบเสร็จ
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php else : ?>
    <div class="empty-state-container">
        <div class="empty-state-icon"><i class="fas fa-file-invoice-dollar"></i></div>
        <h3 class="empty-state-title">ยังไม่มีประวัติการซื้อ</h3>
        <p class="empty-state-text">เมื่อคุณซื้อตำแหน่งโฆษณา ประวัติทั้งหมดจะปรากฏที่นี่</p>
        <button class="btn-primary-empty ajax-nav-btn" data-tab="buy">
            <i class="fas fa-shopping-cart"></i> เลือกซื้อตำแหน่งโฆษณา
        </button>
    </div>
    <?php endif; ?>
</div>
