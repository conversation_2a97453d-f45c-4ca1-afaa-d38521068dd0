﻿:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #7209b7;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --text-muted: #6c757d;
    --border-color: #e9ecef;
    --card-bg: #fff;
    --body-bg: #f5f7fa;
    --shadow-sm: 0 .125rem .25rem rgba(0,0,0,.075);
    --shadow: 0 .5rem 1rem rgba(0,0,0,.15);
    --shadow-lg: 0 1rem 3rem rgba(0,0,0,.175);
    --transition-speed: 0.3s;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-sm: 0.25rem;
}

#wpfooter {
    position: relative;
    float: left;
}

a {
    text-decoration: none !important;
    color: var(--primary-color);
    font-weight: 500;
    transition: color var(--transition-speed) ease;
}

a:hover {
    text-decoration: none !important;
    color: var(--primary-hover);
}

.form-table .description a,
.card a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.form-table .description a:hover,
.card a:hover {
    color: var(--primary-hover);
    text-decoration: none;
}

.settings-card a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.settings-card a:hover {
    color: white;
    text-decoration: none;
}

.callback-urls-container {
    margin-bottom: 15px;
}

.url-input-group {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    margin-bottom: 8px !important;
    width: 100% !important;
    flex-wrap: nowrap !important;
}

.form-table .url-input-group {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    width: 100% !important;
}

.url-input-group input,
.url-input-group input.regular-text {
    flex: 1 !important;
    min-width: 0 !important;
    font-family: monospace !important;
    font-size: 13px !important;
    background: #fff !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    padding: 8px 12px !important;
    width: auto !important;
    max-width: none !important;
}

.form-table .url-input-group input.regular-text {
    flex: 1 !important;
    width: auto !important;
    max-width: none !important;
}

.url-input-group .copy-url,
.url-input-group button.copy-url {
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    padding: 8px 16px !important;
    border-radius: 4px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    white-space: nowrap !important;
    min-width: auto !important;
    width: auto !important;
}

.form-table .url-input-group .copy-url {
    flex-shrink: 0 !important;
    margin-left: 0 !important;
}

.url-input-group .copy-url:hover {
    background: var(--primary-hover) !important;
    transform: translateY(-1px);
}

.ad-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    outline: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-decoration: none !important;
}

.ad-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    text-decoration: none !important;
}

.ad-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

.ad-btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.ad-btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.ad-btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.ad-btn-sm {
    padding: 8px 16px;
    font-size: 14px;
}




.card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed) ease;
}

.card:hover {
    box-shadow: var(--shadow);
}



.ads-management-wrap {
    background: var(--body-bg);
    padding: 20px;
    border-radius: var(--border-radius);
}

.ads-management-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.ads-table-container {
    padding: 0;
}

.ads-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

.ads-table-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: white;
}

.ads-table-header h2 i {
    margin-right: 8px;
}

.ads-table-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    opacity: 0.9;
}

.stat-item i {
    font-size: 16px;
}

.ads-table-wrapper {
    overflow-x: auto;
}

.ads-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.ads-table th {
    background: var(--light-color);
    color: var(--text-color);
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.ads-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.ads-table tr:hover {
    background: rgba(67, 97, 238, 0.05);
}

.ads-table .col-position { width: 8%; }
.ads-table .col-user { width: 10%; }
.ads-table .col-image { width: 12%; }
.ads-table .col-link { width: 18%; }
.ads-table .col-seo { width: 12%; }
.ads-table .col-dimensions { width: 10%; }
.ads-table .col-expiration { width: 10%; }
.ads-table .col-clicks { width: 8%; }
.ads-table .col-status { width: 8%; }
.ads-table .col-actions { width: 14%; }

.status-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.status-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.status-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.status-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .status-slider {
    background-color: #2196F3;
}

input:checked + .status-slider:before {
    transform: translateX(26px);
}

.status-active {
    color: #2cbc63;
    font-weight: bold;
}

.status-inactive {
    color: #cc0000;
    font-weight: bold;
}

.ad-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.1), rgba(0, 0, 0, 0.7));
    backdrop-filter: blur(20px);
    z-index: 99999;
    animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.ad-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.ad-modal-content {
    background: linear-gradient(145deg, #ffffff, #f8faff);
    border-radius: 24px;
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    box-shadow:
        0 32px 64px rgba(67, 97, 238, 0.15),
        0 16px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(67, 97, 238, 0.1);
    animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    transform: translateZ(0);
}

.ad-modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #667eea, #764ba2);
    border-radius: 24px 24px 0 0;
}

.ad-modal-header {
    background: linear-gradient(135deg, #ffffff, #f8faff);
    padding: 32px 40px 24px;
    border-bottom: 1px solid rgba(67, 97, 238, 0.08);
    position: relative;
}

.ad-modal-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    gap: 16px;
    letter-spacing: -0.5px;
}

.ad-modal-header h2 i {
    font-size: 32px;
    background: linear-gradient(135deg, var(--primary-color), #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(67, 97, 238, 0.2));
}

.ad-modal-close {
    position: absolute;
    top: 24px;
    right: 32px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border: none;
    color: white;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 16px rgba(238, 90, 82, 0.3);
}

.ad-modal-close:hover {
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 12px 24px rgba(238, 90, 82, 0.4);
}

.ad-modal-body {
    padding: 10px 40px 0;
    overflow: visible;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-40px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.ultra-modern-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px 32px;
    align-items: stretch;
}

.ultra-modern-form .form-section {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.ultra-modern-form .form-section.full-width {
    grid-column: 1 / -1;
}

.ultra-field {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 6px;
    width: 100%;
    min-height: 80px;
    justify-content: flex-start;
}

.field-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 4px;
    padding: 6px 10px;
    background: rgba(67, 97, 238, 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.field-hint i {
    font-size: 11px;
    color: var(--primary-color);
}

.bulk-progress {
    text-align: center;
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.progress-fill {
    width: 0%;
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #667eea);
    transition: width 0.3s ease;
    border-radius: 10px;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    margin: 0;
}

.ultra-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    font-size: 15px;
    color: #2d3748;
    margin-bottom: 0;
    letter-spacing: -0.1px;
    min-height: 24px;
}

.ultra-label i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), #667eea);
    color: white;
    border-radius: 8px;
    font-size: 14px;
    box-shadow: 0 4px 8px rgba(67, 97, 238, 0.2);
}

.ultra-input,
.ultra-select {
    width: 100%;
    padding: 18px 22px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, #ffffff, #f8faff);
    color: #2d3748;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 58px;
    box-sizing: border-box;
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
}

.ultra-input:focus,
.ultra-select:focus {
    outline: none;
    border-color: var(--primary-color);
    background: #ffffff;
    box-shadow:
        0 0 0 4px rgba(67, 97, 238, 0.1),
        0 8px 24px rgba(67, 97, 238, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 1);
    transform: translateY(-2px) scale(1.02);
}

.ultra-input::placeholder {
    color: #a0aec0;
    font-weight: 400;
    font-style: italic;
}

.file-upload-wrapper {
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--light-color);
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    background: rgba(67, 97, 238, 0.05);
    color: var(--primary-color);
}

.simple-toggle-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 5px;
}

.simple-toggle {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.simple-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.simple-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.simple-toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .simple-toggle-slider {
    background-color: var(--primary-color);
}

input:checked + .simple-toggle-slider:before {
    transform: translateX(30px);
}

.simple-toggle-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 16px;
}



.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: var(--light-color);
    border-radius: 8px;
}

.checkbox-input {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

.checkbox-label {
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.ultra-modal-footer {
    padding: 20px 40px;
    background: linear-gradient(135deg, #f8faff, #ffffff);
    border-top: 1px solid rgba(67, 97, 238, 0.08);
    display: flex;
    justify-content: center;
    gap: 24px;
    position: relative;
}

.ultra-modal-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 40px;
    right: 40px;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(67, 97, 238, 0.1), transparent);
}

.ultra-footer-btn {
    padding: 20px 40px;
    border: none;
    border-radius: 16px;
    font-size: 17px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    text-decoration: none;
    min-height: 64px;
    min-width: 160px;
    justify-content: center;
    box-sizing: border-box;
    letter-spacing: -0.2px;
    position: relative;
    overflow: hidden;
}

.ultra-footer-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.ultra-footer-btn:hover::before {
    left: 100%;
}

.ultra-footer-btn.cancel {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
    box-shadow: 0 8px 16px rgba(113, 128, 150, 0.3);
}

.ultra-footer-btn.cancel:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 32px rgba(113, 128, 150, 0.4);
}

.ultra-footer-btn.save {
    background: linear-gradient(135deg, var(--primary-color), #667eea);
    color: white;
    box-shadow: 0 8px 16px rgba(67, 97, 238, 0.3);
}

.ultra-footer-btn.save:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 32px rgba(67, 97, 238, 0.4);
}

.ultra-image-section {
    background: linear-gradient(135deg, #f8faff, #ffffff);
    border: 2px dashed #cbd5e0;
    border-radius: 20px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    margin-top: 8px;
}

.ultra-image-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.02), rgba(102, 126, 234, 0.02));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ultra-image-section:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(67, 97, 238, 0.1);
}

.ultra-image-section:hover::before {
    opacity: 1;
}

.ultra-image-controls {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.ultra-btn {
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    min-height: 52px;
    box-sizing: border-box;
    letter-spacing: 0.3px;
    position: relative;
    overflow: hidden;
    text-transform: none;
}

.ultra-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.ultra-btn:hover::before {
    left: 100%;
}

.ultra-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), #667eea);
    color: white;
    box-shadow: 0 8px 16px rgba(67, 97, 238, 0.3);
}

.ultra-btn.primary:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 32px rgba(67, 97, 238, 0.4);
}

.ultra-btn.secondary {
    background: linear-gradient(135deg, #718096, #4a5568);
    color: white;
    box-shadow: 0 8px 16px rgba(113, 128, 150, 0.3);
}

.ultra-btn.secondary:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 32px rgba(113, 128, 150, 0.4);
}

.ultra-btn.danger {
    background: linear-gradient(135deg, #f56565, #e53e3e);
    color: white;
    box-shadow: 0 8px 16px rgba(245, 101, 101, 0.3);
}

.ultra-btn.danger:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 32px rgba(245, 101, 101, 0.4);
}




.image-preview-popup {
    border-radius: 16px !important;
    z-index: 99999 !important;
}

.image-preview-popup .swal2-image {
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 90%;
    max-height: 70vh;
    object-fit: contain;
}

.swal2-container {
    z-index: 99999 !important;
}







.form-table input[type="text"],
.form-table input[type="number"],
.form-table input[type="url"],
.form-table input[type="email"],
.form-table input[type="password"],
.form-table select,
.form-table textarea {
    width: 100% !important;
    max-width: 400px !important;
    min-width: 200px !important;
    padding: 8px 12px !important;
    border: 1px solid #ddd !important;
    border-radius: 4px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    box-sizing: border-box !important;
}

.form-table input[type="text"]:focus,
.form-table input[type="number"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="password"]:focus,
.form-table select:focus,
.form-table textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1) !important;
    outline: none !important;
}

.form-table input[readonly],
.form-table input.readonly-field,
.form-table input.readonly-ga-field {
    background-color: #f5f5f5 !important;
    color: #666 !important;
    cursor: not-allowed !important;
}

.exchange-rate-field {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
}

.exchange-rate-auto-tag {
    background: var(--success-color) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 3px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.visitors-input-group {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    flex-wrap: wrap !important;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
    padding: 20px;
    border-radius: 12px;
    border: 2px solid rgba(67, 97, 238, 0.1);
    margin-bottom: 20px;
}

.ga-synced-badge {
    background: linear-gradient(135deg, var(--success-color), #27ae60) !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.3);
    animation: pulse 2s infinite;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.ga-synced-badge::before {
    content: '✓';
    font-size: 16px;
    font-weight: 900;
}

.readonly-ga-field {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
    border: 2px solid var(--success-color) !important;
    color: #495057 !important;
    font-weight: 600 !important;
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1) !important;
}

.use-ga-data {
    display: flex !important;
    align-items: center !important;
    gap: 10px !important;
    font-weight: 600 !important;
    color: var(--text-color) !important;
    background: rgba(255, 255, 255, 0.8);
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.use-ga-data:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
}

.multiplier-input {
    width: 120px !important;
    max-width: 120px !important;
    min-width: 100px !important;
    text-align: center !important;
    font-weight: 700 !important;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05)) !important;
    border: 2px solid rgba(67, 97, 238, 0.2) !important;
}

.ga4-json-dropzone {
    border: 3px dashed rgba(67, 97, 238, 0.3);
    border-radius: 16px;
    padding: 40px 20px;
    text-align: center;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.02), rgba(114, 9, 183, 0.02));
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.ga4-json-dropzone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(67, 97, 238, 0.05) 25%, transparent 25%, transparent 75%, rgba(67, 97, 238, 0.05) 75%);
    background-size: 20px 20px;
    opacity: 0.3;
}

.ga4-json-dropzone:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.08), rgba(114, 9, 183, 0.08));
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.15);
}

.ga4-json-dropzone i {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 15px;
    display: block;
    position: relative;
    z-index: 1;
}

.ga4-json-dropzone p {
    font-size: 16px;
    color: var(--text-color);
    margin: 0;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.ga4-json-dropzone span {
    color: var(--primary-color);
    font-weight: 700;
    text-decoration: underline;
}

.ga4-json-file-info {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
    padding: 20px;
    border-radius: 12px;
    border: 2px solid rgba(46, 204, 113, 0.2);
}

.ga4-json-file-info i {
    font-size: 24px;
    color: var(--success-color);
}

.ga4-json-file-info span {
    font-weight: 700;
    color: var(--text-color);
    flex: 1;
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}



.toggle-switch-container {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
    padding: 15px 20px;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
    border-radius: 12px;
    border: 2px solid rgba(67, 97, 238, 0.1);
    transition: all 0.3s ease;
}

.toggle-switch-container:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.15);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 32px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #ddd, #ccc);
    transition: all 0.4s ease;
    border-radius: 32px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
    background: linear-gradient(135deg, #fff, #f8f9fa);
    transition: all 0.4s ease;
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 15px rgba(67, 97, 238, 0.3);
}

input:checked + .toggle-slider:before {
    transform: translateX(28px);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
}

.toggle-label {
    font-weight: 700;
    color: var(--text-color);
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}



@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .form-grid {
        gap: 20px;
    }

    .ad-modal-content {
        width: 95%;
        max-width: none;
        border-radius: 16px;
        max-height: 95vh;
    }

    .form-table input[type="text"],
    .form-table input[type="number"],
    .form-table input[type="url"],
    .form-table input[type="email"],
    .form-table input[type="password"],
    .form-table select,
    .form-table textarea {
        max-width: 100% !important;
        min-width: 100% !important;
    }

    .visitors-input-group,
    .exchange-rate-field {
        flex-direction: column !important;
        align-items: stretch !important;
    }
}




.settings-tabs {
    margin-bottom: 30px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    flex-wrap: wrap;
    gap: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px 12px 0 0;
    padding: 0 20px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.settings-tabs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    opacity: 0.3;
}

.settings-tabs a {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 24px;
    margin: 0;
    background: transparent;
    border: none;
    text-decoration: none !important;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    font-size: 15px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    position: relative;
    min-height: 60px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.settings-tabs a:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.8);
    transition: width 0.3s ease;
}

.settings-tabs a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none !important;
    transform: translateY(-2px);
}

.settings-tabs a:hover:before {
    width: 80%;
}

.settings-tabs a.active {
    background: var(--card-bg);
    color: var(--primary-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: -2px;
    z-index: 1;
    text-shadow: none;
}

.settings-tabs a.active:before {
    width: 100%;
    background: var(--primary-color);
}

.settings-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0 0 16px 16px;
    box-shadow: var(--shadow-lg);
    margin-bottom: 30px;
    padding: 35px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.settings-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 16px 16px 0 0;
}

.settings-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 2px 4px rgba(67, 97, 238, 0.3);
}

.settings-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-3px);
}

.settings-card h2 {
    margin: -35px -35px 35px -35px;
    padding: 25px 35px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-size: 26px;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 15px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
    border-bottom: 3px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(67, 97, 238, 0.3);
}

.settings-card h2:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    opacity: 0.3;
}

.settings-card h2 i {
    position: relative;
    z-index: 1;
    font-size: 28px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    animation: pulse 2s infinite;
}

.settings-card h3 {
    font-size: 20px;
    margin: 35px 0 25px;
    padding: 18px 25px;
    border-left: 5px solid var(--primary-color);
    background: linear-gradient(135deg, var(--light-color), rgba(67, 97, 238, 0.05));
    border-radius: 0 12px 12px 0;
    color: var(--text-color);
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

.settings-card h3::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
}

.tab-content {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-table th {
    font-weight: 600;
    color: var(--text-color);
    padding: 20px 0;
    width: 200px;
}

.form-table td {
    padding: 20px 0;
}

.form-table input[type="text"],
.form-table input[type="password"],
.form-table input[type="email"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table select,
.form-table textarea {
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 15px;
    transition: all 0.3s ease;
    background: var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    font-weight: 500;
}

.form-table input:focus,
.form-table select:focus,
.form-table textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(67, 97, 238, 0.15), 0 4px 12px rgba(67, 97, 238, 0.2);
    transform: translateY(-2px);
}

.button-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    border: none !important;
    border-radius: 12px !important;
    padding: 15px 30px !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4) !important;
    transition: all 0.3s ease !important;
    font-size: 16px !important;
    position: relative !important;
    overflow: hidden !important;
}

.button-primary::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
    transition: left 0.5s ease !important;
}

.button-primary:hover::before {
    left: 100% !important;
}

.button-primary:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 30px rgba(67, 97, 238, 0.5) !important;
}

.button-small {
    padding: 8px 16px !important;
    font-size: 14px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
}

.plisio-settings-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 30px;
}

.plisio-card,
.google-analytics-card {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 30px;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 25px;
}

.google-analytics-card {
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.02), rgba(114, 9, 183, 0.02));
    border: 2px solid rgba(67, 97, 238, 0.15);
}

.google-analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #4285f4, #34a853, #fbbc05, #ea4335);
    border-radius: 16px 16px 0 0;
}

.plisio-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 16px 16px 0 0;
}

.exchange-rate-card {
    background: linear-gradient(135deg, rgba(42, 157, 143, 0.05), rgba(33, 134, 122, 0.05));
    border: 2px solid rgba(42, 157, 143, 0.2);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.exchange-rate-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #2a9d8f, #21867a);
    border-radius: 16px 16px 0 0;
}

.exchange-rate-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, rgba(42, 157, 143, 0.1), rgba(33, 134, 122, 0.1));
    padding: 20px 25px;
    border-radius: 12px;
    border: 2px solid rgba(42, 157, 143, 0.2);
    margin-bottom: 20px;
}

.exchange-rate-value {
    font-size: 24px;
    font-weight: 800;
    color: #2a9d8f;
    text-shadow: 0 2px 4px rgba(42, 157, 143, 0.2);
}

.exchange-rate-auto-tag {
    background: linear-gradient(135deg, var(--success-color), #27ae60) !important;
    color: white !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    box-shadow: 0 3px 8px rgba(46, 204, 113, 0.3);
    animation: pulse 2s infinite;
}

.rate-info-container {
    background: linear-gradient(135deg, rgba(42, 157, 143, 0.05), rgba(33, 134, 122, 0.05));
    border: 2px solid rgba(42, 157, 143, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
}

.rate-display {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.rate-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.rate-value {
    font-size: 24px;
    font-weight: 800;
    color: #2a9d8f;
    text-shadow: 0 2px 4px rgba(42, 157, 143, 0.2);
    background: linear-gradient(135deg, rgba(42, 157, 143, 0.1), rgba(33, 134, 122, 0.1));
    padding: 10px 20px;
    border-radius: 8px;
    border: 1px solid rgba(42, 157, 143, 0.3);
}

.rate-timestamp {
    text-align: center;
    padding-top: 10px;
    border-top: 1px solid rgba(42, 157, 143, 0.2);
}

.last-updated {
    font-size: 14px;
    color: #666;
    font-style: italic;
}
.switch {
    position: relative;
    display: inline-block;
    width: 70px;
    height: 38px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #ddd, #ccc);
    transition: all 0.4s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider:before {
    position: absolute;
    content: "";
    height: 30px;
    width: 30px;
    left: 4px;
    bottom: 4px;
    background: linear-gradient(135deg, #fff, #f8f9fa);
    transition: all 0.4s ease;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 15px rgba(67, 97, 238, 0.3);
}

input:checked + .slider:before {
    transform: translateX(32px);
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
}

.slider.round {
    border-radius: 38px;
}

.slider.round:before {
    border-radius: 50%;
}

.plisio-card:hover,
.google-analytics-card:hover,
.exchange-rate-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.formula-container {
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.03), rgba(114, 9, 183, 0.03));
    border: 2px solid rgba(67, 97, 238, 0.1);
    border-radius: 16px;
    padding: 25px;
    margin: 20px 0;
}

.formula-box {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    border-left: 5px solid var(--primary-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.formula-box:last-child {
    margin-bottom: 0;
}

.formula-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.formula-title::before {
    content: '📋';
    font-size: 18px;
}

.formula-content,
.formula-example {
    font-family: 'Courier New', monospace;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 15px 20px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    font-size: 15px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    word-break: break-all;
}

.formula-example {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(39, 174, 96, 0.1));
    border-color: var(--success-color);
    color: #27ae60;
}

.plisio-card h3 {
    margin: 0 0 20px 0;
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.plisio-card h3 i {
    color: var(--primary-color);
    font-size: 20px;
}

.api-key-input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.api-key-input-wrapper input {
    padding-left: 45px !important;
}

.api-key-input-wrapper .input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 16px;
}

.currency-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #2a9d8f, #21867a);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(42, 157, 143, 0.3);
}

.currency-badge i {
    font-size: 18px;
}

.currency-desc {
    font-size: 14px;
    opacity: 0.9;
}

.callback-urls-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.callback-url-card {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
}

.callback-url-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.1);
}

.callback-url-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.callback-url-header i {
    font-size: 18px;
    color: var(--primary-color);
}

.callback-url-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.callback-urls-note {
    background: var(--light-color);
    border-left: 4px solid var(--primary-color);
    padding: 15px 20px;
    border-radius: 0 8px 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.callback-urls-note i {
    color: var(--primary-color);
    font-size: 18px;
}

.callback-urls-note p {
    margin: 0;
    color: var(--text-color);
}

.test-connection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.test-connection-card {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.test-connection-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.1);
}

.test-card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.test-card-header i {
    font-size: 24px;
    color: var(--primary-color);
}

.test-card-header h4 {
    margin: 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.test-description {
    color: var(--text-muted);
    margin-bottom: 20px;
    font-size: 14px;
}

.test-invoice-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}







.instructions-grid {
    display: grid;
    gap: 20px;
}

.instruction-step {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: var(--light-color);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.instruction-step:hover {
    background: rgba(67, 97, 238, 0.05);
    transform: translateX(5px);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-radius: 50%;
    font-weight: 700;
    font-size: 18px;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.step-content h4 {
    margin: 0 0 8px 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: var(--text-muted);
    line-height: 1.5;
}

.step-content a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.step-content a:hover {
    text-decoration: underline;
}

.actions-cell {
    text-align: center;
    padding: 15px 10px;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: nowrap;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.edit-ad {
    background: linear-gradient(135deg, #4361ee, #3a56d4);
    color: white;
}

.action-btn.edit-ad:hover {
    background: linear-gradient(135deg, #3a56d4, #2d4bc8);
}

.action-btn.reset-ad {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.action-btn.reset-ad:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
}

.action-btn.delete-ad {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.action-btn.delete-ad:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.ultra-field input , .ultra-field select {
    min-height: 40px;
    max-width: 100% !important;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-wrap: wrap;
        gap: 5px;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .url-input-group {
        flex-direction: column;
        align-items: stretch;
    }

    .url-input-group .copy-url {
        margin-top: 8px;
        align-self: center;
    }

    .ad-btn {
        padding: 10px 20px;
        font-size: 14px;
    }

    .test-invoice-form {
        flex-direction: column;
        align-items: stretch;
    }

    .test-invoice-field input {
        width: 100%;
    }

    .ads-management-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .settings-tabs {
        padding: 0 10px;
        flex-direction: column;
    }

    .settings-tabs a {
        text-align: center;
        min-height: 50px;
        font-size: 14px;
    }

    .plisio-settings-grid {
        grid-template-columns: 1fr;
    }

    .callback-urls-grid {
        grid-template-columns: 1fr;
    }

    .test-connection-grid {
        grid-template-columns: 1fr;
    }

    .plisio-card {
        padding: 20px;
    }

    .callback-url-card,
    .test-connection-card {
        padding: 15px;
    }

    .url-input-group {
        flex-direction: column;
        gap: 10px;
    }

    .url-input-group .copy-url {
        align-self: stretch;
    }

    .ads-table-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .ultra-modern-form {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .ad-modal-content {
        width: 95%;
        max-width: none;
        max-height: 95vh;
    }

    .ad-modal-body {
        padding: 20px 24px;
    }

    .ultra-modal-footer {
        padding: 20px 24px;
        flex-direction: column;
        gap: 16px;
    }

    .ultra-input,
    .ultra-select {
        font-size: 16px;
        min-height: 64px;
        padding: 20px 24px;
    }

    .ultra-image-controls {
        flex-direction: column;
        gap: 12px;
    }

    .ultra-btn {
        justify-content: center;
        min-height: 48px;
        width: 100%;
    }

    .ultra-footer-btn {
        width: 100%;
        min-height: 60px;
        padding: 18px 32px;
    }

    .ultra-image-section {
        padding: 24px;
    }

    .dashboard-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
        padding: 20px;
    }

    .dashboard-title {
        font-size: 24px;
    }

    .dashboard-actions {
        justify-content: center;
    }

    .dashboard-card-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .dashboard-charts-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chart-container {
        height: 250px;
    }

    .dashboard-section-header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .dashboard-section-body {
        padding: 20px;
    }

    .transaction-table-container {
        font-size: 12px;
    }

    .transaction-table th,
    .transaction-table td {
        padding: 8px 6px;
    }
}


.ads-management-header,
.user-management-header,
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 25px 30px;
    background: linear-gradient(135deg, #4361ee, #7209b7);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    color: white;
    position: relative;
    overflow: hidden;
}

.ads-management-header::before,
.user-management-header::before,
.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(0,0,0,0.15) 25%, transparent 25%, transparent 75%, rgba(0,0,0,0.15) 75%);
    background-size: 20px 20px;
    opacity: 0.6;
}

.ads-management-header h1,
.user-management-header h1,
.dashboard-title {
    margin: 0;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 1;
}

.ads-management-header > div,
.ads-header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}
.user-management-header > div,
.dashboard-actions {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;
}

.modern-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button-danger {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.button-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(238, 90, 82, 0.4);
}

.button-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover)) !important;
    color: white !important;
}

.button-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
}



.dashboard-summary {
    margin-bottom: 40px;
}

.dashboard-card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
}

.dashboard-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.card-primary::before {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.card-success::before {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.card-warning::before {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

.card-info::before {
    background: linear-gradient(135deg, var(--info-color), #2980b9);
}

.dashboard-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.dashboard-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.dashboard-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.card-primary .dashboard-card-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
}

.card-success .dashboard-card-icon {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
}

.card-warning .dashboard-card-icon {
    background: linear-gradient(135deg, var(--warning-color), #e67e22);
}

.card-info .dashboard-card-icon {
    background: linear-gradient(135deg, var(--info-color), #2980b9);
}

.dashboard-card-body {
    text-align: left;
}

.dashboard-card-value {
    font-size: 36px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 10px;
    line-height: 1;
}

.dashboard-card-details {
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: var(--text-muted);
}

.dashboard-section {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.dashboard-section-header {
    padding: 25px 30px;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05), rgba(114, 9, 183, 0.05));
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-section-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-section-body {
    padding: 30px;
}

.dashboard-charts-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-color);
}

.chart-legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.transaction-table-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.transaction-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.transaction-table th {
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.1), rgba(114, 9, 183, 0.1));
    color: var(--text-color);
    font-weight: 600;
    padding: 15px 12px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
}

.transaction-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.transaction-table tr:hover {
    background: rgba(67, 97, 238, 0.05);
}

.transaction-table .amount {
    font-weight: 600;
    color: var(--success-color);
}

.transaction-table .date {
    color: var(--text-muted);
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.counter-value {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-management-wrap {
    background: var(--body-bg);
    padding: 20px;
    border-radius: var(--border-radius);
}

.user-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius-lg);
    margin-bottom: 30px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.user-management-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    opacity: 0.3;
}

.user-management-header h1 {
    margin: 0;
    font-size: 32px;
    font-weight: 800;
    color: white;
    display: flex;
    align-items: center;
    gap: 15px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.user-management-header h1 i {
    font-size: 36px;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
    animation: pulse 2s infinite;
}



.user-management-content {
    background: var(--card-bg);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.users-table-container {
    padding: 0;
}

.users-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: var(--light-color);
    border-bottom: 2px solid var(--border-color);
}

.users-table-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.users-table-header h2 i {
    color: var(--primary-color);
}

.users-table-stats {
    display: flex;
    gap: 20px;
}

.users-table-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-muted);
}

.users-table-stats .stat-item i {
    color: var(--primary-color);
    font-size: 18px;
}

.users-table-wrapper {
    overflow-x: auto;
    background: white;
}

.users-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.users-table th {
    background: var(--light-color);
    color: var(--text-color);
    font-weight: 600;
    padding: 18px 15px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    white-space: nowrap;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.users-table td {
    padding: 16px 15px;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
    background: white;
}

.users-table tr:hover td {
    background: rgba(67, 97, 238, 0.05);
}

.users-table .col-username { width: 12%; }
.users-table .col-name { width: 15%; }
.users-table .col-email { width: 20%; }
.users-table .col-positions { width: 18%; }
.users-table .col-telegram { width: 12%; }
.users-table .col-stats { width: 8%; }
.users-table .col-bypass { width: 8%; }
.users-table .col-actions { width: 12%; }

.positions-count {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 5px;
}

.positions-preview {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.3;
}

.more-positions {
    color: var(--primary-color);
    font-weight: 500;
}

.no-positions {
    color: var(--text-muted);
    font-style: italic;
    font-size: 13px;
}

.telegram-contact {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: var(--text-color);
}

.telegram-contact i {
    color: #0088cc;
    font-size: 16px;
}

.no-telegram {
    color: var(--text-muted);
    font-style: italic;
}

.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-enabled {
    background: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.status-disabled {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.actions-cell {
    text-align: center;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-btn.edit-customer {
    background: var(--primary-color);
    color: white;
}

.action-btn.edit-customer:hover {
    background: var(--primary-hover);
}

.action-btn.delete-customer {
    background: var(--danger-color);
    color: white;
}

.action-btn.delete-customer:hover {
    background: #c0392b;
}

.user-edit-tabs {
    display: flex;
    background: var(--light-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    overflow: hidden;
    margin: -10px -40px 20px -40px;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    background: var(--light-color);
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-button:hover {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.tab-button.active {
    background: var(--primary-color);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

.user-edit-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-sm);
}

.form-field {
    margin-bottom: 10px;
}

.form-field label {
    display: block;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    font-size: 14px;
}

.form-field input[type="text"],
.form-field input[type="email"],
.form-field input[type="password"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-field input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.field-description {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 5px;
    line-height: 1.4;
}

.checkbox-field {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.checkbox-field input[type="checkbox"] {
    margin-top: 3px;
    transform: scale(1.2);
}

.checkbox-field label {
    margin-bottom: 0;
    cursor: pointer;
    flex: 1;
}

.positions-container {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 15px;
}

.positions-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
}

.dashboard-positions-list {
    display: inline-block;
    padding: 8px 12px;
    background: linear-gradient(135deg, #4361ee, #3a56d4);
    color: white;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
    border: none;
    margin: 2px 0;
}

.positions-list::-webkit-scrollbar {
    width: 8px;
}

.positions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.positions-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.positions-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.position-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    min-height: 42px;
}

.position-item:hover {
    border-color: var(--primary-color);
    background: rgba(67, 97, 238, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.15);
}

.position-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #e9ecef;
    transform: none !important;
    box-shadow: none !important;
}

.position-checkbox {
    transform: scale(1.1);
    accent-color: var(--primary-color);
}

.position-name {
    font-weight: 500;
    flex: 1;
    color: #495057;
    line-height: 1.4;
}

.position-owner {
    font-size: 11px;
    color: #6c757d;
    font-style: italic;
    background: rgba(108, 117, 125, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.add-position-container {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 15px;
    background: white;
    border-radius: var(--border-radius);
    border: 2px dashed var(--border-color);
}

.positions-note {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background: rgba(67, 97, 238, 0.05);
    border: 1px solid rgba(67, 97, 238, 0.2);
    border-radius: var(--border-radius);
    color: var(--primary-color);
    font-size: 14px;
    margin: 0;
}

.positions-note i {
    color: var(--primary-color);
    font-size: 16px;
}

.positions-note a {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
}

.positions-note a:hover {
    text-decoration: underline;
}

.add-position-container input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: 14px;
}

.required {
    color: var(--danger-color);
    font-weight: 600;
}

.new-user-positions {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    background: var(--light-color);
}

.warning-message {
    text-align: center;
    padding: 30px;
}

.warning-message i {
    font-size: 48px;
    color: var(--warning-color);
    margin-bottom: 15px;
    display: block;
}

.warning-message p {
    font-size: 16px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.warning-text {
    color: var(--text-muted) !important;
    font-size: 14px !important;
}

@media (max-width: 768px) {
    .user-management-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .users-table-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .users-table-stats {
        justify-content: center;
    }

    .positions-list {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .add-position-container {
        flex-direction: column;
        gap: 15px;
    }

    .user-edit-tabs {
        flex-direction: column;
    }

    .tab-button {
        text-align: left;
    }
}

#general-settings-status,
#global-settings-status,
#price-calculation-settings-status,
#discount-settings-status {
    display: inline-block;
    margin-left: 15px;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 14px;
}

#general-settings-status.success,
#global-settings-status.success,
#price-calculation-settings-status.success,
#discount-settings-status.success {
    background: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

#general-settings-status.error,
#global-settings-status.error,
#price-calculation-settings-status.error,
#discount-settings-status.error {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.user-management-content {
    background: var(--card-bg);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    text-align: left;
    vertical-align: top;
    font-weight: 600;
    color: var(--text-primary);
}

.form-table td {
    padding: 15px 0;
    vertical-align: top;
}

.form-table input[type="text"],
.form-table input[type="email"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table textarea,
.form-table select {
    width: 100%;
    max-width: 400px;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-table input[type="text"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="number"]:focus,
.form-table textarea:focus,
.form-table select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.1);
}

.form-table .description {
    margin-top: 5px;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.button.button-primary,
.ad-btn.ad-btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    border: none;
    color: white;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    font-size: 14px;
}

.button.button-primary:hover,
.ad-btn.ad-btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
    color: white;
    text-decoration: none;
}

.ad-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 14px;
}

.ad-btn i {
    font-size: 16px;
}

.ad-btn.ad-btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.ad-btn.ad-btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    color: white;
    text-decoration: none;
}

.ad-btn.ad-btn-warning {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: white;
}

.ad-btn.ad-btn-warning:hover {
    background: linear-gradient(135deg, #f7931e, #e67e22);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    color: white;
    text-decoration: none;
}

.ads-header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.ads-header-actions .ad-btn {
    cursor: pointer !important;
    user-select: none;
    pointer-events: auto !important;
    position: relative;
    z-index: 10;
    border: none;
    outline: none;
}

.ads-header-actions .ad-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.ad-btn.ad-btn-warning:hover {
    background: linear-gradient(135deg, #f7931e, #e67e22);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    color: white;
    text-decoration: none;
}

#general-settings-status,
#payment-settings-status,
#price-settings-status,
#discount-settings-status,
#google-settings-status,
#security-settings-status {
    display: inline-block;
    margin-left: 15px;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 14px;
}

#general-settings-status.success,
#payment-settings-status.success,
#price-settings-status.success,
#discount-settings-status.success,
#google-settings-status.success,
#security-settings-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

#general-settings-status.error,
#payment-settings-status.error,
#price-settings-status.error,
#discount-settings-status.error,
#google-settings-status.error,
#security-settings-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.ad-modal-footer {
    padding: 15px 25px;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-radius: 0 0 10px 10px;
}

/* User Management - Add User Modal Specific Styles */
.user-modal-content .user-form {
    display: flex;
    flex-direction: column;
}

.user-modal-content .form-row {
    display: flex;
    gap: 20px;
}

.user-modal-content .form-row .form-field {
    flex: 1;
}

.user-modal-content .form-field {
    display: flex;
    flex-direction: column;
}

.user-modal-content .form-field label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #495057;
}

.user-modal-content .form-field input[type="text"],
.user-modal-content .form-field input[type="email"],
.user-modal-content .form-field input[type="password"] {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.user-modal-content .form-field input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.user-modal-content .password-wrapper {
    display: flex;
    gap: 10px;
}

.user-modal-content .password-wrapper input {
    flex-grow: 1;
}

.user-modal-content .checkbox-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: normal;
}

.user-modal-content .positions-list-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 12px;
    background-color: #fff;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 8px;
}

.user-modal-content .positions-list-container::-webkit-scrollbar {
    width: 6px;
}

.user-modal-content .positions-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.user-modal-content .positions-list-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.user-modal-content .positions-list-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.user-modal-content .positions-list-container .position-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background: #f8f9fa;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 13px;
    min-height: 36px;
}

.user-modal-content .positions-list-container .position-item:hover {
    background-color: rgba(67, 97, 238, 0.05);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.user-modal-content .positions-list-container .position-item input[type="checkbox"] {
    transform: scale(1.1);
    accent-color: var(--primary-color);
}

.user-modal-content .positions-list-container .position-name {
    flex: 1;
    font-weight: 500;
    color: #495057;
}

.user-modal-content .required {
    color: #dc3545;
    margin-left: 2px;
}

.user-modal-content.compact {
    max-width: 600px;
}

@media (max-width: 768px) {
    .positions-list {
        grid-template-columns: 1fr;
        max-height: 280px;
        padding: 12px;
    }
    
    .user-modal-content .positions-list-container {
        grid-template-columns: 1fr;
        max-height: 180px;
        padding: 10px;
    }
    
    .position-item {
        padding: 8px 10px;
        min-height: 38px;
    }
    
    .user-modal-content .positions-list-container .position-item {
        min-height: 32px;
        padding: 6px 8px;
    }
    
    .ads-header-actions {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }
    
    .ads-header-actions .ad-btn {
        width: 100%;
        justify-content: center;
    }
    
    .ads-management-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
}

/* Timer Management Styles */
.timers-table-container {
    overflow-x: auto;
    margin-top: 10px;
}

.timers-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.timers-table th {
    background: linear-gradient(135deg, var(--primary-color), #3a7bd5);
    color: white;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
}

.timers-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.timers-table tr:last-child td {
    border-bottom: none;
}

.timers-table tr:hover {
    background-color: #f8f9fa;
}

.timer-disconnected {
    background-color: #ffebee !important;
}

.timer-disconnected:hover {
    background-color: #ffcdd2 !important;
}

.countdown-cell {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    font-size: 16px;
    color: var(--primary-color);
}

.countdown-cell .expired {
    color: var(--danger-color);
    font-weight: bold;
}

.status-badge.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.status-danger {
    background-color: #f8d7da;
    color: #721c24;
    animation: pulse-red 2s infinite;
}

@keyframes pulse-red {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.cancel-timer-btn {
    font-size: 12px;
    padding: 6px 12px;
}

.text-center {
    text-align: center;
}

.positions-list {
    max-width: 100%;
    width: 100%;
}

.positions-list strong {
    color: var(--primary-color);
    font-weight: 600;
}

.positions-list small {
    color: #666;
    font-style: italic;
}

.no-positions {
    color: #999;
    font-style: italic;
}

/* Responsive table */
@media (max-width: 768px) {
    .timers-table {
        font-size: 12px;
    }
    
    .timers-table th,
    .timers-table td {
        padding: 8px 10px;
    }
    
    .cancel-timer-btn {
        font-size: 10px;
        padding: 4px 8px;
    }
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(245, 247, 250, 0.5), rgba(239, 242, 247, 0.5));
    border-radius: var(--border-radius);
    border: 1px dashed var(--border-color);
    color: var(--text-muted);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.empty-state i {
    font-size: 48px;
    color: var(--primary-color);
    opacity: 0.3;
    transition: all 0.3s ease;
    animation: pulse-icon 3s infinite ease-in-out;
}

.empty-state p {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
    color: #5a6a85;
}

.dashboard-section-body > .empty-state {
    border: none;
    background: transparent;
    padding: 20px 0;
}

.dashboard-section-body > .empty-state i {
    display: none;
}

.dashboard-section-body > .empty-state p {
    font-style: italic;
    color: #9aa5b5;
    background: #f8f9fc;
    padding: 15px 25px;
    border-radius: 8px;
    width: 100%;
    box-sizing: border-box;
}

@keyframes pulse-icon {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.5;
    }
}

.timers-table th {
    background-color: #f9fafb;
    font-weight: 600;
}

/* GA Threshold Settings Styles */
.threshold-level {
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-icon {
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.threshold-range {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.threshold-input {
    width: 120px !important;
    text-align: center;
}

.threshold-display {
    width: 120px !important;
    text-align: center;
    background-color: #f5f5f5 !important;
    color: #666 !important;
}

.popup-behavior {
    display: flex;
    align-items: center;
    gap: 8px;
}

.behavior-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.threshold-note {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin: 20px 0;
}

.threshold-note p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #495057;
}

.threshold-note ul {
    margin: 0;
    padding-left: 20px;
}

.threshold-note li {
    margin-bottom: 5px;
    color: #6c757d;
    line-height: 1.4;
}

#ga-threshold-settings-status.success {
    color: #28a745;
    font-weight: 600;
    margin-left: 10px;
}

#ga-threshold-settings-status.error {
    color: #dc3545;
    font-weight: 600;
    margin-left: 10px;
}

/* Dynamic Pricing Rules Styles */
.dynamic-pricing-toggle {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.toggle-switch-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin: 0;
}

.toggle-label-text {
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4361ee;
}

input:checked + .toggle-slider:before {
    transform: translateX(30px);
}

.toggle-status {
    font-weight: 600;
    color: #6c757d;
}

.dynamic-pricing-explanation {
    margin: 20px 0;
}

.explanation-box {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.explanation-box h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.explanation-box ul {
    margin: 0;
    padding-left: 20px;
}

.explanation-box li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.disabled-section {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.disabled-section::after {
    content: "ปิดใช้งาน - เปิด Dynamic Pricing เพื่อใช้งาน";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(220, 53, 69, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 600;
    z-index: 10;
}

.behavior-logic {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.logic-conditions {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.condition-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.condition-check {
    color: #28a745;
    font-weight: bold;
}

.condition-cross {
    color: #dc3545;
    font-weight: bold;
}

.behavior-note {
    display: block;
    color: #6c757d;
    font-style: italic;
    margin-top: 5px;
}

.popup-content {
    display: block;
    color: #6c757d;
    font-style: italic;
    margin-top: 5px;
}

.legacy-mode-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.legacy-mode-note h4 {
    margin: 0 0 15px 0;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;
}

.legacy-rules {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.legacy-rule {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: white;
    border-radius: 5px;
    border: 1px solid #ffeaa7;
}

.rule-condition {
    font-weight: 600;
    color: #495057;
    min-width: 150px;
}

.rule-arrow {
    color: #6c757d;
    font-weight: bold;
}

.rule-result {
    color: #28a745;
    font-weight: 600;
}

#dynamic-pricing-settings-status.success {
    color: #28a745;
    font-weight: 600;
    margin-left: 10px;
}

#dynamic-pricing-settings-status.error {
    color: #dc3545;
    font-weight: 600;
    margin-left: 10px;
}

/* Customer Statistics Table */
.customer-stats-table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.customer-stats-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

.customer-stats-table thead {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.customer-stats-table th,
.customer-stats-table td {
    padding: 15px 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.customer-stats-table th {
    font-weight: 600;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.customer-stats-table tbody tr {
    transition: all 0.2s ease;
}

.customer-stats-table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.customer-info strong {
    display: block;
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 2px;
}

.customer-info small {
    color: var(--text-muted);
    font-size: 12px;
}

.positions-count {
    display: inline-block;
    background: var(--info-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 4px;
}

.positions-preview {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.3;
}

.more-positions {
    color: var(--primary-color);
    font-weight: 500;
}

.click-count {
    font-weight: 600;
    font-size: 16px;
}

.click-count.total {
    color: var(--primary-color);
}

.click-count.monthly {
    color: var(--success-color);
}

.click-rate {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Loading Container */
.loading-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-bar {
    width: 100%;
    max-width: 400px;
    height: 6px;
    background-color: #f0f0f0;
    border-radius: 3px;
    margin: 0 auto 20px;
    overflow: hidden;
    position: relative;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
    0% {
        width: 0%;
        margin-left: 0%;
    }
    50% {
        width: 75%;
        margin-left: 25%;
    }
    100% {
        width: 0%;
        margin-left: 100%;
    }
}

.loading-container p {
    color: var(--text-muted);
    font-size: 14px;
    margin: 0;
}

/* Position Display in Transaction Table */
.position-name {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
}

.position-unknown {
    color: var(--text-muted);
    font-style: italic;
    font-size: 12px;
}

/* Owner Info Display */
.owner-info strong {
    color: var(--text-color);
    font-size: 13px;
}

.no-owner {
    color: var(--text-muted);
    font-style: italic;
    font-size: 12px;
}

/* Click Count Badge */
.click-count-badge {
    display: inline-block;
    background: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
}

/* System Status Grid */
.system-status-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.status-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.status-icon {
    width: 50px;
    height: 50px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.status-icon.preserve {
    background: var(--success-color);
}

.status-content h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 16px;
    font-weight: 600;
}

.status-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.status-content li {
    padding: 5px 0;
    font-size: 13px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-content li i.fa-check {
    color: var(--danger-color);
}

.status-content li i.fa-times {
    color: var(--success-color);
}

.warning-box {
    background: rgba(243, 156, 18, 0.1);
    border: 1px solid var(--warning-color);
    border-radius: 6px;
    padding: 15px;
    color: var(--warning-color);
    font-size: 14px;
}

.warning-box i {
    margin-right: 8px;
}

@media (max-width: 768px) {
    .system-status-grid {
        grid-template-columns: 1fr;
    }
}

