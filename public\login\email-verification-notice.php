<?php
if (!defined('AMP_PLUGIN_DIR')) {
    require_once dirname(__DIR__, 2) . '/ad-management-pro.php';
}
?>

<div id="email-verification-notice" class="amp-auth-form-container" style="display: none;">
    <div class="email-verification-content">
        <div class="verification-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z" fill="#667eea"/>
            </svg>
        </div>
        
        <h2>ยืนยันอีเมลของคุณ</h2>
        
        <p class="verification-message">
            คุณยังไม่ได้ยืนยันอีเมลของคุณ กรุณาตรวจสอบกล่องจดหมายและคลิกลิงก์ยืนยันที่เราส่งไป
        </p>
        
        <div class="verification-email-display">
            <strong id="user-email-display"></strong>
        </div>
        
        <div class="verification-actions">
            <button type="button" id="resend-verification-btn" class="ad-login-button resend-btn">
                <span class="btn-text">ส่งอีเมลยืนยันใหม่</span>
                <span class="btn-loading" style="display: none;">กำลังส่ง...</span>
            </button>
            
            <button type="button" id="back-to-login-btn" class="ad-login-button secondary-btn">
                กลับไปหน้าเข้าสู่ระบบ
            </button>
        </div>
        
        <div class="verification-help">
            <p><small>ไม่เห็นอีเมล? ตรวจสอบในโฟลเดอร์ Spam หรือ Junk Mail</small></p>
            <p><small>อีเมลยืนยันจะหมดอายุใน 24 ชั่วโมง หากไม่ยืนยันภายในเวลาที่กำหนด บัญชีจะถูกลบอัตโนมัติ</small></p>
        </div>
    </div>
    
    <form id="resend-verification-form" style="display: none;">
        <input type="hidden" name="action" value="amp_resend_verification">
        <input type="hidden" name="email" id="resend-email-field">
        <?php 
        if (defined('ABSPATH') && function_exists('wp_nonce_field')) {
            wp_nonce_field('amp_resend_verification_action', 'amp_resend_verification_nonce');
        } else {
            echo '<input type="hidden" name="amp_resend_verification_nonce" value="' . md5('amp_resend_verification_action' . time()) . '">';
        }
        ?>
    </form>
</div>

<style>
.email-verification-content {
    text-align: center;
    padding: 2rem;
    max-width: 500px;
    margin: 0 auto;
}

.verification-icon {
    margin-bottom: 1.5rem;
}

.verification-icon svg {
    opacity: 0.8;
}

.email-verification-content h2 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.verification-message {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.verification-email-display {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border-left: 4px solid #667eea;
}

.verification-email-display strong {
    color: #667eea;
    font-size: 1.1rem;
}

.verification-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.resend-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.resend-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.secondary-btn {
    background: #6c757d;
    color: white;
}

.secondary-btn:hover {
    background: #5a6268;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.verification-help {
    border-top: 1px solid #eee;
    padding-top: 1.5rem;
}

.verification-help p {
    margin: 0.5rem 0;
    color: #888;
}

.dark-mode .email-verification-content h2 {
    color: #fff;
}

.dark-mode .verification-message {
    color: #ccc;
}

.dark-mode .verification-email-display {
    background: #2d3748;
    color: #e2e8f0;
}

.dark-mode .verification-help {
    border-top-color: #4a5568;
}

.dark-mode .verification-help p {
    color: #a0aec0;
}

@media (max-width: 768px) {
    .email-verification-content {
        padding: 1.5rem;
    }
    
    .verification-actions {
        gap: 0.75rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resendBtn = document.getElementById('resend-verification-btn');
    const backBtn = document.getElementById('back-to-login-btn');
    const resendForm = document.getElementById('resend-verification-form');
    const emailDisplay = document.getElementById('user-email-display');
    const emailField = document.getElementById('resend-email-field');
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            document.getElementById('email-verification-notice').style.display = 'none';
            document.getElementById('login-form-container').style.display = 'block'; 
            const loginForm = document.getElementById('amp-login-form');
            if (loginForm) {
                loginForm.reset();
            }
        });
    }
    
    if (resendBtn && resendForm) {
        resendBtn.addEventListener('click', function() {
            const email = emailField.value;
            if (!email) {
                alert('ไม่พบข้อมูลอีเมล');
                return;
            }           
            resendBtn.disabled = true;
            document.querySelector('.btn-text').style.display = 'none';
            document.querySelector('.btn-loading').style.display = 'inline';           
            const formData = new FormData(resendForm);            
            fetch(window.ampAuthData?.ajaxurl || '/wp-admin/admin-ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.data.message);           
                    setTimeout(() => {
                        resendBtn.disabled = false;
                        document.querySelector('.btn-text').style.display = 'inline';
                        document.querySelector('.btn-loading').style.display = 'none';
                    }, 5 * 60 * 1000);
                } else {
                    alert(data.data.message || 'เกิดข้อผิดพลาด');
                    resendBtn.disabled = false;
                    document.querySelector('.btn-text').style.display = 'inline';
                    document.querySelector('.btn-loading').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('เกิดข้อผิดพลาดในการส่งอีเมล');
                resendBtn.disabled = false;
                document.querySelector('.btn-text').style.display = 'inline';
                document.querySelector('.btn-loading').style.display = 'none';
            });
        });
    }
    
    window.showEmailVerificationNotice = function(email) {
        if (emailDisplay && emailField) {
            emailDisplay.textContent = email;
            emailField.value = email;
        }      
        const containers = document.querySelectorAll('.amp-auth-form-container');
        containers.forEach(container => {
            container.style.display = 'none';
        });      
        const notice = document.getElementById('email-verification-notice');
        if (notice) {
            notice.style.display = 'block';
        }     
        const title = document.getElementById('auth-title');
        if (title) {
            title.textContent = 'ยืนยันอีเมล';
        }
    };
});
</script>
