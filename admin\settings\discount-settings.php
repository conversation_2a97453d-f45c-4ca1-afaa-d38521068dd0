<?php
if (!defined('WPINC')) {
    die;
}

require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';

function display_discount_settings_content() {
    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_discount_rates');
    $notice = '';
    $rates = get_discount_rates();
    $nonce = wp_create_nonce('save_discount_settings');

    $ga_thresholds = get_ga_threshold_settings();
    $ga_nonce = wp_create_nonce('save_ga_threshold_settings');

    $dynamic_pricing_enabled = get_option('amp_dynamic_pricing_enabled', 'yes');
    $dynamic_nonce = wp_create_nonce('save_dynamic_pricing_settings');
    ?>
        <?php echo $notice; ?>

        <div class="plisio-card">
            <h3><i class="fas fa-brain"></i> Dynamic Pricing Rules (Google Analytics)</h3>
            <p class="description">ระบบลำดับชั้นที่ใช้ Google Analytics เป็นตัวกำหนดจำนวนตัวเลือก และการตั้งค่าส่วนลดเป็นตัวเติมข้อมูล</p>

            <form method="post" action="" id="dynamic-pricing-form">
                <input type="hidden" name="dynamic_pricing_nonce" value="<?php echo $dynamic_nonce; ?>">

                <div class="dynamic-pricing-toggle">
                    <label class="toggle-switch-container">
                        <span class="toggle-label-text"><strong>เปิดใช้งาน Dynamic Pricing Rules</strong></span>
                        <label class="toggle-switch">
                            <input type="checkbox" name="dynamic_pricing_enabled" id="dynamic_pricing_enabled"
                                   value="yes" <?php checked($dynamic_pricing_enabled, 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-status"><?php echo $dynamic_pricing_enabled === 'yes' ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?></span>
                    </label>
                </div>

                <div class="dynamic-pricing-explanation">
                    <div class="explanation-box">
                        <h4><i class="fas fa-lightbulb"></i> หลักการทำงาน</h4>
                        <ul>
                            <li><strong>GA เป็นตัวควบคุมหลัก:</strong> กำหนดว่าจะแสดง popup กี่ตัวเลือก (ไม่แสดง/2/4 ตัวเลือก)</li>
                            <li><strong>การตั้งค่าส่วนลดเป็นผู้ส่งมอบ:</strong> เติมข้อมูลในตัวเลือกที่ GA กำหนด</li>
                            <li><strong>ระบบ Fallback อัจฉริยะ:</strong> ลดระดับอัตโนมัติเมื่อข้อมูลไม่เพียงพอ</li>
                        </ul>
                    </div>
                </div>

                <p class="submit">
                    <button type="submit" name="submit_dynamic_pricing_settings" id="save-dynamic-pricing-settings" class="ad-btn ad-btn-primary">
                        <i class="fas fa-save"></i> บันทึกการตั้งค่า Dynamic Pricing
                    </button>
                    <span id="dynamic-pricing-settings-status"></span>
                </p>
            </form>
        </div>

        <div class="plisio-card <?php echo $dynamic_pricing_enabled === 'no' ? 'disabled-section' : ''; ?>">
            <h3><i class="fas fa-chart-line"></i> เกณฑ์ Google Analytics (GA Tiers)</h3>
            <p class="description">กำหนดเกณฑ์จำนวนผู้เข้าชมรายเดือนเพื่อแบ่งผู้ใช้เป็น 3 ระดับ</p>

            <form method="post" action="" id="ga-threshold-form">
                <input type="hidden" name="ga_threshold_nonce" value="<?php echo $ga_nonce; ?>">

                <table class="users-table">
                    <thead>
                        <tr>
                            <th style="width: 25%;">ระดับ</th>
                            <th style="width: 35%;">เกณฑ์ผู้เข้าชม (คน/เดือน)</th>
                            <th style="width: 40%;">การแสดง Popup</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="threshold-level">
                                    <span class="level-icon">💡</span>
                                    <strong>ระดับ 1 (คนน้อย)</strong>
                                </div>
                            </td>
                            <td>
                                <div class="threshold-range">
                                    <span>น้อยกว่า</span>
                                    <input type="number" name="threshold_low" value="<?php echo esc_attr($ga_thresholds['threshold_low']); ?>"
                                           min="1" max="999999999" class="regular-text threshold-input" required>
                                    <span>คน</span>
                                </div>
                            </td>
                            <td>
                                <div class="popup-behavior">
                                    <span class="behavior-icon">🚫</span>
                                    <span><strong>ไม่แสดง popup</strong> - เพิ่มแพ็กเกจ 30 วัน 0% ลงตะกร้าทันที</span>
                                    <small class="behavior-note">ไม่ขึ้นกับจำนวนโปรโมชั่นที่ตั้งค่า</small>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="threshold-level">
                                    <span class="level-icon">💰</span>
                                    <strong>ระดับ 2 (ปานกลาง)</strong>
                                </div>
                            </td>
                            <td>
                                <div class="threshold-range">
                                    <input type="number" name="threshold_medium_min" value="<?php echo esc_attr($ga_thresholds['threshold_low']); ?>"
                                           class="regular-text threshold-display" readonly>
                                    <span>ถึง</span>
                                    <input type="number" name="threshold_medium" value="<?php echo esc_attr($ga_thresholds['threshold_medium']); ?>"
                                           min="1" max="999999999" class="regular-text threshold-input" required>
                                    <span>คน</span>
                                </div>
                            </td>
                            <td>
                                <div class="popup-behavior">
                                    <span class="behavior-icon">📋</span>
                                    <div class="behavior-logic">
                                        <strong>เป้าหมาย: 2 ตัวเลือก</strong>
                                        <div class="logic-conditions">
                                            <div class="condition-item">
                                                <span class="condition-check">✅</span> โปรโมชั่น ≥ 2 รายการ → แสดง popup 2 ตัวเลือก (7 วัน + 30 วัน 0%)
                                            </div>
                                            <div class="condition-item">
                                                <span class="condition-cross">❌</span> โปรโมชั่น < 2 รายการ → Fallback เป็นระดับ 1
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="threshold-level">
                                    <span class="level-icon">💎</span>
                                    <strong>ระดับ 3 (คนเยอะ)</strong>
                                </div>
                            </td>
                            <td>
                                <div class="threshold-range">
                                    <span>มากกว่า</span>
                                    <input type="number" name="threshold_high_min" value="<?php echo esc_attr($ga_thresholds['threshold_medium']); ?>"
                                           class="regular-text threshold-display" readonly>
                                    <span>คน</span>
                                </div>
                            </td>
                            <td>
                                <div class="popup-behavior">
                                    <span class="behavior-icon">🎯</span>
                                    <div class="behavior-logic">
                                        <strong>เป้าหมาย: 2-4+ ตัวเลือก (ตามจำนวนโปรโมชั่น)</strong>
                                        <div class="logic-conditions">
                                            <div class="condition-item">
                                                <span class="condition-check">✅</span> 1+1 โปรโมชั่น → 2 ตัวเลือก (30 วัน 0% + โปรโมชั่น 1)
                                            </div>
                                            <div class="condition-item">
                                                <span class="condition-check">✅</span> 1+2 โปรโมชั่น → 4 ตัวเลือก (7 วัน + 30 วัน 0% + โปรโมชั่น 2 ตัว)
                                            </div>
                                            <div class="condition-item">
                                                <span class="condition-check">✅</span> 1+3+ โปรโมชั่น → 4+ ตัวเลือก (30 วัน 0% + โปรโมชั่นที่ตั้งค่า, ไม่มี 7 วัน)
                                            </div>
                                            <div class="condition-item">
                                                <span class="condition-cross">❌</span> โปรโมชั่น < 2 รายการ → Fallback เป็นระดับ 1
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div class="threshold-note">
                    <p><strong>หมายเหตุ Hierarchical Logic:</strong></p>
                    <ul>
                        <li><strong>ระบบลำดับชั้น:</strong> GA กำหนดจำนวนช่อง → Admin Settings เติมข้อมูลในช่อง</li>
                        <li><strong>Fallback อัจฉริยะ:</strong> เมื่อข้อมูลไม่เพียงพอ ระบบจะลดระดับตัวเองอัตโนมัติ</li>
                        <li><strong>ไอคอนหมุนเวียน:</strong> 💡, 💰, 💎, 🚀, 🏆, 👑, ⚜️, 🏦, ✨, 🌌</li>
                    </ul>
                </div>

                <div class="legacy-mode-note">
                    <h4><i class="fas fa-history"></i> Legacy Mode (เมื่อปิด Dynamic Pricing)</h4>
                    <p>เมื่อปิดใช้งาน Dynamic Pricing หรือ GA ไม่พร้อมใช้งาน ระบบจะใช้โหมดดั้งเดิม:</p>
                    <div class="legacy-rules">
                        <div class="legacy-rule">
                            <span class="rule-condition">1+0 โปรโมชั่น</span>
                            <span class="rule-arrow">→</span>
                            <span class="rule-result">ไม่แสดง popup (เพิ่ม 30 วัน 0% ลงตะกร้าทันที)</span>
                        </div>
                        <div class="legacy-rule">
                            <span class="rule-condition">1+1 โปรโมชั่น</span>
                            <span class="rule-arrow">→</span>
                            <span class="rule-result">แสดง popup 2 ตัวเลือก (30 วัน 0% + โปรโมชั่น 1)</span>
                        </div>
                        <div class="legacy-rule">
                            <span class="rule-condition">1+2 โปรโมชั่น</span>
                            <span class="rule-arrow">→</span>
                            <span class="rule-result">แสดง popup 3 ตัวเลือก (7 วัน + 30 วัน 0% + โปรโมชั่น 1)</span>
                        </div>
                        <div class="legacy-rule">
                            <span class="rule-condition">1+3+ โปรโมชั่น</span>
                            <span class="rule-arrow">→</span>
                            <span class="rule-result">แสดง popup 4+ ตัวเลือก (30 วัน 0% + โปรโมชั่น 3 ตัวแรก, ไม่มี 7 วัน)</span>
                        </div>
                    </div>
                    <p><strong>หมายเหตุ:</strong> "1+" หมายถึง 30 วัน 0% ที่ล็อคไว้ + จำนวนโปรโมชั่นเพิ่มเติมที่ Admin ตั้งค่า</p>
                </div>

                <p class="submit">
                    <button type="submit" name="submit_ga_threshold_settings" id="save-ga-threshold-settings" class="ad-btn ad-btn-primary">
                        <i class="fas fa-save"></i> บันทึกการตั้งค่าเกณฑ์
                    </button>
                    <span id="ga-threshold-settings-status"></span>
                </p>
            </form>
        </div>

        <div class="plisio-card">
            <h3><i class="fas fa-percentage"></i> อัตราส่วนลดตามระยะเวลา</h3>

            <div class="locked-promotion-notice">
                <div class="notice-box locked-notice">
                    <h4><i class="fas fa-lock"></i> โปรโมชั่นล็อค (ไม่สามารถแก้ไขได้)</h4>
                    <p><strong>30 วัน 0%</strong> - โปรโมชั่นนี้ล็อคไว้และนับเป็น 1 โปรโมชั่นเสมอในระบบ GA Tier</p>
                    <small>ใช้เป็นแพ็กเกจพื้นฐานสำหรับการเพิ่มเข้าตะกร้าอัตโนมัติและเป็นตัวเลือกหลักในทุก popup</small>
                </div>
            </div>

            <form method="post" action="" id="discount-settings-form">
                <input type="hidden" name="discount_nonce" value="<?php echo $nonce; ?>">
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>ระยะเวลา (เดือน)</th>
                            <th>ส่วนลด (%)</th>
                            <th>สถานะ</th>
                            <th>การดำเนินการ</th>
                        </tr>
                    </thead>
                <tbody id="discount-rates-table">
                    <?php if (empty($rates)): ?>
                        <tr class="locked-row">
                            <td>
                                <input type="number" name="duration[]" value="1" min="1" required class="regular-text" readonly>
                                <span class="locked-indicator">🔒</span>
                            </td>
                            <td>
                                <input type="number" name="discount[]" value="0" min="0" max="100" step="0.01" required class="regular-text" readonly>
                            </td>
                            <td><span class="status-locked">ล็อค</span></td>
                            <td><span class="locked-text">ไม่สามารถลบได้</span></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($rates as $rate): ?>
                            <?php $is_locked = is_locked_discount_rate($rate->duration); ?>
                            <tr <?php echo $is_locked ? 'class="locked-row"' : ''; ?>>
                                <td>
                                    <input type="number" name="duration[]" value="<?php echo esc_attr($rate->duration); ?>"
                                           min="1" required class="regular-text" <?php echo $is_locked ? 'readonly' : ''; ?>>
                                    <?php if ($is_locked): ?>
                                        <span class="locked-indicator">🔒</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <input type="number" name="discount[]" value="<?php echo esc_attr($rate->discount_percentage); ?>"
                                           min="0" max="100" step="0.01" required class="regular-text" <?php echo $is_locked ? 'readonly' : ''; ?>>
                                </td>
                                <td>
                                    <?php if ($is_locked): ?>
                                        <span class="status-locked">ล็อค</span>
                                    <?php else: ?>
                                        <span class="status-editable">แก้ไขได้</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($is_locked): ?>
                                        <span class="locked-text">ไม่สามารถลบได้</span>
                                    <?php else: ?>
                                        <button type="button" class="ad-btn ad-btn-danger ad-btn-sm remove-rate">ลบ</button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
            <div style="margin: 20px 0;">
                <button type="button" id="add-rate" class="ad-btn ad-btn-secondary">เพิ่มอัตราส่วนลด</button>
            </div>
            <p class="submit">
                <button type="submit" name="submit_discount_settings" id="save-discount-settings" class="ad-btn ad-btn-primary">บันทึกการตั้งค่า</button>
                <span id="discount-settings-status"></span>
            </p>
            </form>
        </div>
        <script>
            jQuery(document).ready(function($) {
                function updateThresholdDisplays() {
                    const lowValue = $('input[name="threshold_low"]').val();
                    const mediumValue = $('input[name="threshold_medium"]').val();

                    $('input[name="threshold_medium_min"]').val(lowValue);
                    $('input[name="threshold_high_min"]').val(mediumValue);
                }

                $('input[name="threshold_low"], input[name="threshold_medium"]').on('input', updateThresholdDisplays);
                updateThresholdDisplays();

                function updateDynamicPricingUI() {
                    const isEnabled = $('#dynamic_pricing_enabled').is(':checked');
                    const statusText = isEnabled ? 'เปิดใช้งาน' : 'ปิดใช้งาน';
                    $('.toggle-status').text(statusText);
                }

                $('#dynamic_pricing_enabled').on('change', updateDynamicPricingUI);
                updateDynamicPricingUI();

                $('#dynamic-pricing-form').on('submit', function(e) {
                    e.preventDefault();
                    const button = $('#save-dynamic-pricing-settings');
                    const statusEl = $('#dynamic-pricing-settings-status');

                    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');
                    statusEl.removeClass('success error').hide();

                    const formData = {
                        action: 'save_dynamic_pricing_settings',
                        nonce: '<?php echo $dynamic_nonce; ?>',
                        dynamic_pricing_enabled: $('#dynamic_pricing_enabled').is(':checked') ? 'yes' : 'no'
                    };

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: formData,
                        success: function(response) {
                            button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่า Dynamic Pricing');
                            if (response.success) {
                                var successMessage = response.data.message || 'บันทึกสำเร็จ';
                                statusEl.addClass('success').html('<i class="fas fa-check"></i> ' + successMessage).show();
                                setTimeout(function() { statusEl.fadeOut(); }, 3000);
                                updateDynamicPricingUI();
                            } else {
                                var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                                statusEl.addClass('error').html('<i class="fas fa-exclamation-triangle"></i> ' + errorMessage).show();
                            }
                        },
                        error: function() {
                            button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่า Dynamic Pricing');
                            statusEl.addClass('error').html('<i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการบันทึก').show();
                        }
                    });
                });

                $('#ga-threshold-form').on('submit', function(e) {
                    e.preventDefault();
                    const button = $('#save-ga-threshold-settings');
                    const statusEl = $('#ga-threshold-settings-status');

                    button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');
                    statusEl.removeClass('success error').hide();

                    const formData = {
                        action: 'save_ga_threshold_settings',
                        nonce: '<?php echo $ga_nonce; ?>',
                        threshold_low: $('input[name="threshold_low"]').val(),
                        threshold_medium: $('input[name="threshold_medium"]').val()
                    };

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: formData,
                        success: function(response) {
                            button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่าเกณฑ์');
                            if (response.success) {
                                var successMessage = response.data.message || 'บันทึกสำเร็จ';
                                statusEl.addClass('success').html('<i class="fas fa-check"></i> ' + successMessage).show();
                                setTimeout(function() { statusEl.fadeOut(); }, 3000);
                            } else {
                                var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                                statusEl.addClass('error').html('<i class="fas fa-exclamation-triangle"></i> ' + errorMessage).show();
                            }
                        },
                        error: function() {
                            button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่าเกณฑ์');
                            statusEl.addClass('error').html('<i class="fas fa-exclamation-triangle"></i> เกิดข้อผิดพลาดในการบันทึก').show();
                        }
                    });
                });

                $('#add-rate').on('click', function() {
                    var row = `
                        <tr>
                            <td><input type="number" name="duration[]" value="1" min="1" required class="regular-text"></td>
                            <td><input type="number" name="discount[]" value="0" min="0" max="100" step="0.01" required class="regular-text"></td>
                            <td><button type="button" class="ad-btn ad-btn-danger ad-btn-sm remove-rate">ลบ</button></td>
                        </tr>
                    `;
                    $('#discount-rates-table').append(row);
                });
                $(document).on('click', '.remove-rate', function() {
                    const row = $(this).closest('tr');
                    const durationInput = row.find('input[name="duration[]"]');
                    const duration = parseInt(durationInput.val());

                    if (duration === 1) {
                        alert('ไม่สามารถลบโปรโมชั่น 30 วัน 0% ที่ล็อคไว้ได้');
                        return;
                    }

                    const editableRows = $('#discount-rates-table tr').not('.locked-row');
                    if (editableRows.length > 1) {
                        row.remove();
                    } else {
                        alert('ต้องมีอัตราส่วนลดอย่างน้อย 1 รายการ (นอกจากโปรโมชั่นล็อค)');
                    }
                });
                $('#discount-settings-form').on('submit', function(e) {
                    e.preventDefault();
                    const button = $('#save-discount-settings');
                    const statusEl = $('#discount-settings-status');
                    button.prop('disabled', true).html('กำลังบันทึก...');
                    statusEl.removeClass('success error').hide();
                    const formData = {
                        action: 'save_discount_settings',
                        nonce: '<?php echo $nonce; ?>',
                        duration: [],
                        discount: []
                    };
                    $('input[name="duration[]"]').each(function() {
                        formData.duration.push($(this).val());
                    });
                    $('input[name="discount[]"]').each(function() {
                        formData.discount.push($(this).val());
                    });
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: formData,
                        success: function(response) {
                            button.prop('disabled', false).html('บันทึกการตั้งค่า');
                            if (response.success) {
                                var successMessage = response.data.message || 'บันทึกสำเร็จ';
                                statusEl.addClass('success').html(successMessage).show();
                                setTimeout(function() { statusEl.fadeOut(); }, 3000);
                            } else {
                                var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                                statusEl.addClass('error').html(errorMessage).show();
                            }
                        },
                        error: function() {
                            button.prop('disabled', false).html('บันทึกการตั้งค่า');
                            statusEl.addClass('error').html('เกิดข้อผิดพลาดในการบันทึก').show();
                        }
                    });
                });
            });
        </script>

        <style>
            .locked-promotion-notice {
                margin-bottom: 20px;
            }

            .notice-box.locked-notice {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border: 2px solid #6c757d;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }

            .notice-box.locked-notice h4 {
                color: #495057;
                margin: 0 0 8px 0;
                font-size: 14px;
            }

            .notice-box.locked-notice p {
                margin: 5px 0;
                color: #6c757d;
                font-size: 13px;
            }

            .notice-box.locked-notice small {
                color: #868e96;
                font-size: 12px;
            }

            .locked-row {
                background-color: #f8f9fa !important;
                opacity: 0.8;
            }

            .locked-row input[readonly] {
                background-color: #e9ecef !important;
                cursor: not-allowed;
                color: #6c757d;
            }

            .locked-indicator {
                margin-left: 8px;
                font-size: 14px;
            }

            .status-locked {
                color: #6c757d;
                font-weight: bold;
                font-size: 12px;
                background: #e9ecef;
                padding: 2px 6px;
                border-radius: 3px;
            }

            .status-editable {
                color: #28a745;
                font-weight: bold;
                font-size: 12px;
                background: #d4edda;
                padding: 2px 6px;
                border-radius: 3px;
            }

            .locked-text {
                color: #6c757d;
                font-style: italic;
                font-size: 12px;
            }
        </style>
    <?php
}

function get_discount_rates() {
    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_discount_rates');

    if (!$database->get_wpdb()->get_var("SHOW TABLES LIKE '{$table_name}'")) {
        \AdManagementPro\Core\Database::create_tables();
    }

    $rates = $database->get_results("SELECT * FROM {$table_name} ORDER BY duration ASC");

    ensure_locked_30_day_rate();

    return $rates;
}

function ensure_locked_30_day_rate() {
    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_discount_rates');

    $existing_30_day = $database->get_var(
        "SELECT COUNT(*) FROM {$table_name} WHERE duration = 1"
    );

    if (!$existing_30_day) {
        $database->insert(
            'ad_discount_rates',
            array(
                'duration' => 1,
                'discount_percentage' => 0.0
            ),
            array('%d', '%f')
        );
        error_log("AMP: Auto-created locked 30-day 0% discount rate");
    }
}

function is_locked_discount_rate($duration) {
    return intval($duration) === 1;
}

function update_discount_rate($duration, $discount_percentage) {
    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_discount_rates');
    
    $exists = $database->get_var(
        "SELECT COUNT(*) FROM {$table_name} WHERE duration = %d",
        [$duration]
    );

    if ($exists) {
        return $database->update(
            'ad_discount_rates',
            array('discount_percentage' => $discount_percentage),
            array('duration' => $duration),
            array('%f'),
            array('%d')
        );
    } else {
        return $database->insert(
            'ad_discount_rates',
            array(
                'duration' => $duration,
                'discount_percentage' => $discount_percentage
            ),
            array('%d', '%f')
        );
    }
}

function handle_save_discount_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_discount_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_discount_rates');

    $durations = isset($_POST['duration']) ? array_map('intval', $_POST['duration']) : array();
    $discounts = isset($_POST['discount']) ? array_map('floatval', $_POST['discount']) : array();

    $database->get_wpdb()->query($database->get_wpdb()->prepare("DELETE FROM `%1s` WHERE duration != 1", $table_name));

    ensure_locked_30_day_rate();

    for ($i = 0; $i < count($durations); $i++) {
        $duration = intval($durations[$i]);
        $discount = floatval($discounts[$i]);

        if ($duration > 0) {
            if ($duration === 1) {
                continue;
            }
            update_discount_rate($duration, $discount);
        }
    }
    wp_send_json_success(['message' => 'บันทึกการตั้งค่าส่วนลดเรียบร้อยแล้ว']);
}
add_action('wp_ajax_save_discount_settings', 'handle_save_discount_settings');

function get_ga_threshold_settings() {
    $defaults = array(
        'threshold_low' => 100000,
        'threshold_medium' => 500000
    );

    $threshold_low = get_option('amp_ga_threshold_low', $defaults['threshold_low']);
    $threshold_medium = get_option('amp_ga_threshold_medium', $defaults['threshold_medium']);

    return array(
        'threshold_low' => intval($threshold_low),
        'threshold_medium' => intval($threshold_medium)
    );
}

function handle_save_ga_threshold_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_ga_threshold_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $threshold_low = isset($_POST['threshold_low']) ? intval($_POST['threshold_low']) : 100000;
    $threshold_medium = isset($_POST['threshold_medium']) ? intval($_POST['threshold_medium']) : 500000;

    if ($threshold_low <= 0 || $threshold_medium <= 0) {
        wp_send_json_error(['message' => 'ค่าเกณฑ์ต้องมากกว่า 0']);
        return;
    }

    if ($threshold_medium <= $threshold_low) {
        wp_send_json_error(['message' => 'เกณฑ์ระดับปานกลางต้องมากกว่าระดับคนน้อย']);
        return;
    }

    update_option('amp_ga_threshold_low', $threshold_low);
    update_option('amp_ga_threshold_medium', $threshold_medium);

    wp_send_json_success(['message' => 'บันทึกการตั้งค่าเกณฑ์ Google Analytics เรียบร้อยแล้ว']);
}
add_action('wp_ajax_save_ga_threshold_settings', 'handle_save_ga_threshold_settings');

function handle_save_dynamic_pricing_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_dynamic_pricing_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $dynamic_pricing_enabled = isset($_POST['dynamic_pricing_enabled']) && $_POST['dynamic_pricing_enabled'] === 'yes' ? 'yes' : 'no';

    update_option('amp_dynamic_pricing_enabled', $dynamic_pricing_enabled);

    $message = $dynamic_pricing_enabled === 'yes'
        ? 'เปิดใช้งาน Dynamic Pricing Rules เรียบร้อยแล้ว'
        : 'ปิดใช้งาน Dynamic Pricing Rules เรียบร้อยแล้ว (จะใช้ Legacy Mode)';

    wp_send_json_success(['message' => $message]);
}
add_action('wp_ajax_save_dynamic_pricing_settings', 'handle_save_dynamic_pricing_settings');
