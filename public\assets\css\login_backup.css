﻿:root {
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --secondary-color: #7209b7;
    --accent-color: #00b4d8;

    --text-color: #333333;
    --light-text: #666666;
    --muted-text: #888888;

    --error-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;

    --card-bg: #ffffff;
    --body-bg: #f8f9fa;
    --input-bg: #f8f9fa;

    --border-color: #dddddd;
    --divider-color: #eeeeee;

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 15px 40px rgba(0, 0, 0, 0.15);

    --diagonal-stripe: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    --diagonal-stripe-size: 20px 20px;
    --theme-gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --theme-gradient-light: linear-gradient(135deg, rgba(67, 97, 238, 0.08), rgba(114, 9, 183, 0.08));
}

.dark-mode {
    --text-color: #f8f9fa;
    --light-text: #cccccc;
    --muted-text: #999999;

    --card-bg: #2d3748;
    --body-bg: #1a202c;
    --input-bg: #1a202c;

    --border-color: #444444;
    --divider-color: #333333;

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 15px 40px rgba(0, 0, 0, 0.4);

    --theme-gradient-light: linear-gradient(135deg, rgba(67, 97, 238, 0.15), rgba(114, 9, 183, 0.15));
}

body {
    font-family: 'Prompt', sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #667eea 50%, #764ba2 75%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
    z-index: -2;
}

body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

body.dark-mode {
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

body.dark-mode::before {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(80, 79, 138, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(155, 79, 138, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(80, 139, 155, 0.3) 0%, transparent 50%);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.floating-element {
    position: absolute;
    opacity: 0.7;
    font-size: 20px;
    user-select: none;
    pointer-events: none;
}

.floating-element:nth-child(1) {
    left: 2%;
    top: 140vh;
    font-size: 24px;
    transform: rotate(45deg);
    animation: floatDiagonal1 22s infinite linear;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    left: 89%;
    top: 102vh;
    font-size: 18px;
    transform: rotate(-30deg);
    animation: floatZigzag1 28s infinite linear;
    animation-delay: 3s;
}

.floating-element:nth-child(3) {
    left: 45%;
    top: 135vh;
    font-size: 26px;
    transform: rotate(120deg);
    animation: floatCurve1 25s infinite linear;
    animation-delay: 7s;
}

.floating-element:nth-child(4) {
    left: 78%;
    top: 108vh;
    font-size: 20px;
    transform: rotate(-90deg);
    animation: floatStraight1 30s infinite linear;
    animation-delay: 12s;
}

.floating-element:nth-child(5) {
    left: 15%;
    top: 125vh;
    font-size: 22px;
    transform: rotate(75deg);
    animation: floatWave1 26s infinite linear;
    animation-delay: 5s;
}

.floating-element:nth-child(6) {
    left: 67%;
    top: 118vh;
    font-size: 28px;
    transform: rotate(-135deg);
    animation: floatDiagonal2 24s infinite linear;
    animation-delay: 9s;
}

.floating-element:nth-child(7) {
    left: 34%;
    top: 103vh;
    font-size: 16px;
    transform: rotate(180deg);
    animation: floatZigzag2 27s infinite linear;
    animation-delay: 2s;
}

.floating-element:nth-child(8) {
    left: 92%;
    top: 132vh;
    font-size: 30px;
    transform: rotate(-60deg);
    animation: floatCurve2 29s infinite linear;
    animation-delay: 14s;
}

.floating-element:nth-child(9) {
    left: 8%;
    top: 115vh;
    font-size: 19px;
    transform: rotate(225deg);
    animation: floatStraight2 23s infinite linear;
    animation-delay: 6s;
}

.floating-element:nth-child(10) {
    left: 56%;
    top: 107vh;
    font-size: 25px;
    transform: rotate(-15deg);
    animation: floatWave2 31s infinite linear;
    animation-delay: 11s;
}

.floating-element:nth-child(11) {
    left: 83%;
    top: 120vh;
    font-size: 21px;
    transform: rotate(300deg);
    animation: floatSpiral1 33s infinite linear;
    animation-delay: 16s;
}

.floating-element:nth-child(12) {
    left: 29%;
    top: 138vh;
    font-size: 27px;
    transform: rotate(-105deg);
    animation: floatBounce1 21s infinite linear;
    animation-delay: 4s;
}

.floating-element:nth-child(13) {
    left: 71%;
    top: 105vh;
    font-size: 17px;
    transform: rotate(150deg);
    animation: floatLoop1 26s infinite linear;
    animation-delay: 8s;
}

.floating-element:nth-child(14) {
    left: 12%;
    top: 110vh;
    font-size: 23px;
    transform: rotate(-270deg);
    animation: floatSpiral2 29s infinite linear;
    animation-delay: 13s;
}

.floating-element:nth-child(15) {
    left: 95%;
    top: 128vh;
    font-size: 29px;
    transform: rotate(315deg);
    animation: floatBounce2 24s infinite linear;
    animation-delay: 1s;
}

.floating-element:nth-child(16) {
    left: 38%;
    top: 142vh;
    font-size: 15px;
    transform: rotate(-45deg);
    animation: floatLoop2 32s infinite linear;
    animation-delay: 15s;
}

@keyframes floatDiagonal1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(200px) rotate(360deg); opacity: 0; }
}

@keyframes floatDiagonal2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(-200px) rotate(-360deg); opacity: 0; }
}

@keyframes floatZigzag1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    25% { transform: translateY(-25vh) translateX(100px) rotate(90deg); }
    50% { transform: translateY(-50vh) translateX(-50px) rotate(180deg); }
    75% { transform: translateY(-75vh) translateX(150px) rotate(270deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(360deg); opacity: 0; }
}

@keyframes floatZigzag2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    25% { transform: translateY(-25vh) translateX(-100px) rotate(-90deg); }
    50% { transform: translateY(-50vh) translateX(50px) rotate(-180deg); }
    75% { transform: translateY(-75vh) translateX(-150px) rotate(-270deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-360deg); opacity: 0; }
}

@keyframes floatCurve1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    30% { transform: translateY(-30vh) translateX(80px) rotate(108deg); }
    60% { transform: translateY(-60vh) translateX(-80px) rotate(216deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(360deg); opacity: 0; }
}

@keyframes floatCurve2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    30% { transform: translateY(-30vh) translateX(-80px) rotate(-108deg); }
    60% { transform: translateY(-60vh) translateX(80px) rotate(-216deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-360deg); opacity: 0; }
}

@keyframes floatStraight1 {
    0% { transform: translateY(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) rotate(360deg); opacity: 0; }
}

@keyframes floatStraight2 {
    0% { transform: translateY(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) rotate(-360deg); opacity: 0; }
}

@keyframes floatWave1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    20% { transform: translateY(-20vh) translateX(60px) rotate(72deg); }
    40% { transform: translateY(-40vh) translateX(-60px) rotate(144deg); }
    60% { transform: translateY(-60vh) translateX(60px) rotate(216deg); }
    80% { transform: translateY(-80vh) translateX(-60px) rotate(288deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(360deg); opacity: 0; }
}

@keyframes floatWave2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    20% { transform: translateY(-20vh) translateX(-60px) rotate(-72deg); }
    40% { transform: translateY(-40vh) translateX(60px) rotate(-144deg); }
    60% { transform: translateY(-60vh) translateX(-60px) rotate(-216deg); }
    80% { transform: translateY(-80vh) translateX(60px) rotate(-288deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-360deg); opacity: 0; }
}

@keyframes floatSpiral1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg) scale(0.5); opacity: 0; }
    5% { opacity: 0.7; }
    25% { transform: translateY(-25vh) translateX(120px) rotate(180deg) scale(0.7); }
    50% { transform: translateY(-50vh) translateX(0) rotate(360deg) scale(1); }
    75% { transform: translateY(-75vh) translateX(-120px) rotate(540deg) scale(0.7); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(720deg) scale(0.5); opacity: 0; }
}

@keyframes floatSpiral2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg) scale(0.5); opacity: 0; }
    5% { opacity: 0.7; }
    25% { transform: translateY(-25vh) translateX(-120px) rotate(-180deg) scale(0.7); }
    50% { transform: translateY(-50vh) translateX(0) rotate(-360deg) scale(1); }
    75% { transform: translateY(-75vh) translateX(120px) rotate(-540deg) scale(0.7); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-720deg) scale(0.5); opacity: 0; }
}

@keyframes floatBounce1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    15% { transform: translateY(-15vh) translateX(40px) rotate(72deg); }
    25% { transform: translateY(-30vh) translateX(0) rotate(108deg); }
    35% { transform: translateY(-45vh) translateX(-40px) rotate(144deg); }
    45% { transform: translateY(-60vh) translateX(0) rotate(180deg); }
    55% { transform: translateY(-75vh) translateX(40px) rotate(216deg); }
    65% { transform: translateY(-90vh) translateX(0) rotate(252deg); }
    75% { transform: translateY(-105vh) translateX(-40px) rotate(288deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(360deg); opacity: 0; }
}

@keyframes floatBounce2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    15% { transform: translateY(-15vh) translateX(-40px) rotate(-72deg); }
    25% { transform: translateY(-30vh) translateX(0) rotate(-108deg); }
    35% { transform: translateY(-45vh) translateX(40px) rotate(-144deg); }
    45% { transform: translateY(-60vh) translateX(0) rotate(-180deg); }
    55% { transform: translateY(-75vh) translateX(-40px) rotate(-216deg); }
    65% { transform: translateY(-90vh) translateX(0) rotate(-252deg); }
    75% { transform: translateY(-105vh) translateX(40px) rotate(-288deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-360deg); opacity: 0; }
}

@keyframes floatLoop1 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    20% { transform: translateY(-25vh) translateX(100px) rotate(90deg); }
    30% { transform: translateY(-35vh) translateX(100px) rotate(180deg); }
    40% { transform: translateY(-45vh) translateX(0) rotate(270deg); }
    50% { transform: translateY(-55vh) translateX(-100px) rotate(360deg); }
    60% { transform: translateY(-65vh) translateX(-100px) rotate(450deg); }
    70% { transform: translateY(-75vh) translateX(0) rotate(540deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(720deg); opacity: 0; }
}

@keyframes floatLoop2 {
    0% { transform: translateY(0) translateX(0) rotate(0deg); opacity: 0; }
    5% { opacity: 0.7; }
    20% { transform: translateY(-25vh) translateX(-100px) rotate(-90deg); }
    30% { transform: translateY(-35vh) translateX(-100px) rotate(-180deg); }
    40% { transform: translateY(-45vh) translateX(0) rotate(-270deg); }
    50% { transform: translateY(-55vh) translateX(100px) rotate(-360deg); }
    60% { transform: translateY(-65vh) translateX(100px) rotate(-450deg); }
    70% { transform: translateY(-75vh) translateX(0) rotate(-540deg); }
    95% { opacity: 0.7; }
    100% { transform: translateY(-130vh) translateX(0) rotate(-720deg); opacity: 0; }
}

.floating-element.star {
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.floating-element.heart {
    width: 20px;
    height: 18px;
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    position: relative;
    transform: rotate(-45deg);
    filter: drop-shadow(0 0 10px rgba(255, 107, 157, 0.5));
}

.floating-element.heart:before,
.floating-element.heart:after {
    content: '';
    width: 12px;
    height: 18px;
    position: absolute;
    left: 10px;
    top: 0;
    background: linear-gradient(45deg, #ff6b9d, #ff8fab);
    border-radius: 10px 10px 0 0;
    transform: rotate(-45deg);
    transform-origin: 0 100%;
}

.floating-element.heart:after {
    left: 0;
    transform: rotate(45deg);
    transform-origin: 100% 100%;
}

.floating-element.diamond {
    width: 16px;
    height: 16px;
    background: linear-gradient(45deg, #00d4ff, #5b86e5);
    transform: rotate(45deg);
    filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

.floating-element.circle {
    width: 12px;
    height: 12px;
    background: linear-gradient(45deg, #a8edea, #fed6e3);
    border-radius: 50%;
    filter: drop-shadow(0 0 8px rgba(168, 237, 234, 0.5));
}

.floating-element.triangle {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 14px solid #d299c2;
    filter: drop-shadow(0 0 8px rgba(210, 153, 194, 0.5));
}

.floating-element.sparkle {
    width: 16px;
    height: 16px;
    background: radial-gradient(circle, #fff 20%, transparent 20%),
                radial-gradient(circle, #fff 20%, transparent 20%);
    background-size: 4px 4px;
    background-position: 0 0, 8px 8px;
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));
    animation: sparkle 2s infinite ease-in-out;
}

@keyframes sparkle {
    0%, 100% { opacity: 0.3; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1.2); }
}

.floating-element.moon {
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border-radius: 50%;
    position: relative;
    filter: drop-shadow(0 0 10px rgba(255, 234, 167, 0.6));
}

.floating-element.moon::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background: linear-gradient(135deg, #fab1a0, #e17055);
    border-radius: 50%;
    opacity: 0.3;
}

.floating-element.cloud {
    width: 24px;
    height: 12px;
    background: linear-gradient(135deg, #ddd6fe, #c7d2fe);
    border-radius: 12px;
    position: relative;
    filter: drop-shadow(0 0 8px rgba(221, 214, 254, 0.5));
}

.floating-element.cloud::before,
.floating-element.cloud::after {
    content: '';
    position: absolute;
    background: linear-gradient(135deg, #ddd6fe, #c7d2fe);
    border-radius: 50%;
}

.floating-element.cloud::before {
    width: 12px;
    height: 12px;
    top: -6px;
    left: 3px;
}

.floating-element.cloud::after {
    width: 16px;
    height: 16px;
    top: -8px;
    right: 3px;
}

.ad-login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 95vh;
    padding: 20px;
}

.ad-login-card {
    background: var(--theme-gradient);
    border-radius: 24px;
    box-shadow: var(--shadow-lg), 0 0 50px rgba(102, 126, 234, 0.3);
    width: 100%;
    max-width: 520px;
    padding: 40px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    animation: cardGlow 4s ease-in-out infinite alternate;
}

@keyframes cardGlow {
    0% {
        box-shadow: var(--shadow-lg), 0 0 30px rgba(102, 126, 234, 0.3);
        transform: translateY(0px);
    }
    100% {
        box-shadow: var(--shadow-lg), 0 0 60px rgba(102, 126, 234, 0.5);
        transform: translateY(-2px);
    }
}

.ad-login-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-lg), 0 0 80px rgba(102, 126, 234, 0.6);
}

.dark-mode .ad-login-card {
    box-shadow: var(--shadow-lg), 0 0 50px rgba(80, 79, 138, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .ad-login-card:hover {
    box-shadow: var(--shadow-lg), 0 0 80px rgba(80, 79, 138, 0.7);
}

.ad-login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.ad-login-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
}

.ad-login-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
}

.ad-login-header h1 {
    font-size: 24px;
    color: white;
    margin: 0;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.ad-login-error, .amp-auth-error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--error-color);
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.ad-login-success, .amp-auth-success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.ad-login-field {
    margin-bottom: 20px;
}

.ad-login-field label {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
    color: white;
    font-weight: 500;
    position: relative;
    z-index: 1;
    align-items: center;
}

.ad-login-input-wrapper {
    position: relative;
    width: 100%;
    display: block;
    z-index: 1;
}

.ad-login-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.5;
}

.user-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
}

.email-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E");
}

.password-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
}

.toggle-password {
    left: auto;
    right: 12px;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
}

.toggle-password.show {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z'/%3E%3C/svg%3E");
}

.ad-login-form input[type="text"],
.ad-login-form input[type="password"],
.ad-login-form input[type="email"] {
    width: 100%;
    padding: 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 15px;
    background-color: var(--input-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.ad-login-form input[type="text"]:focus,
.ad-login-form input[type="password"]:focus,
.ad-login-form input[type="email"]:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
    outline: none;
}

.ad-login-options {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 25px;
}

.ad-login-remember {
    display: flex;
    align-items: center;
}

.ad-login-remember input[type="checkbox"] {
    margin-right: 8px;
}

.ad-login-remember label {
    font-size: 14px;
    color: var(--light-text);
}

.ad-login-forgot {
    font-size: 14px;
    color: var(--primary-color);
    text-decoration: none;
}

.ad-login-forgot:hover {
    text-decoration: underline;
}

.ad-login-button {
    width: 100%;
    padding: 12px;
    background: var(--theme-gradient);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-sm);
}

.ad-login-button:hover {
    background: var(--theme-gradient);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.ad-login-button:disabled,
.ad-login-button:disabled:hover {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--theme-gradient);
    transform: none;
    box-shadow: var(--shadow-sm);
}

.ad-login-register {
    text-align: center;
    margin-top: 25px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    z-index: 1;
    box-shadow: var(--shadow-sm);
}

.ad-login-register a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.ad-login-register a:hover {
    text-decoration: underline;
}

.ad-login-footer {
    text-align: center;
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 1;
}

.ad-login-footer p {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.amp-auth-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    justify-content: center;
    position: relative;
    z-index: 1;
}

.amp-auth-tab {
    padding: 10px 20px;
    cursor: pointer;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    text-align: center;
    flex: 1;
    max-width: 150px;
    position: relative;
    z-index: 1;
}

.amp-auth-tab.active {
    color: white;
    border-bottom-color: white;
}

.amp-auth-tab:hover {
    color: white;
}

.amp-auth-form-container {
    display: none;
    position: relative;
    z-index: 1;
    min-height: 400px;
    transition: all 0.3s ease;
}

.amp-auth-form-container.active {
    display: block;
}

.amp-auth-illustration {
    display: block;
    max-width: 100%;
    height: auto;
    margin: 0 auto 20px;
}

.amp-auth-loading {
    text-align: center;
    padding: 20px;
    color: var(--light-text);
}

#password-strength-container {
    margin-top: 10px;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

#password-strength-text {
    font-size: 12px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-align: center;
}

.password-strength-meter {
    height: 6px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 12px;
}

#password-strength-progress {
    height: 100%;
    width: 0;
    background-color: #e0e0e0;
    transition: width 0.3s ease, background-color 0.3s ease;
    border-radius: 3px;
}

#password-requirements {
    margin: 0;
    padding: 0;
    list-style: none;
    font-size: 11px;
    color: rgba(255, 255, 255, 0.7);
}

#password-requirements li {
    margin-bottom: 4px;
    padding-left: 18px;
    position: relative;
    transition: color 0.3s ease;
}

#password-requirements li::before {
    content: '✗';
    position: absolute;
    left: 0;
    color: #e74c3c;
    font-weight: bold;
}

#password-requirements li.met {
    color: var(--success-color);
}

#password-requirements li.met::before {
    content: 'âœ“';
    color: var(--success-color);
}

.dark-mode-toggle {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    z-index: 100;
    transition: all 0.3s ease;
}

.dark-mode-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.dark-mode-toggle svg {
    width: 24px;
    height: 24px;
    fill: white;
}

.ad-login-captcha {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.g-recaptcha,
.cf-turnstile {
    margin: 0 auto;
}

.ad-login-captcha-note {
    margin-top: 5px;
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    position: relative;
    z-index: 1;
}

.preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.dark-mode .preloader {
    background-color: rgba(26, 32, 44, 0.9);
}

.preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

.preloader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideInDown {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@media (max-width: 480px) {
    .ad-login-card {
        padding: 30px 20px;
    }

    .amp-auth-tabs {
        flex-wrap: wrap;
    }

    .amp-auth-tab {
        padding: 8px 15px;
        font-size: 14px;
    }

    .dark-mode-toggle {
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
    }

    .ad-login-form input[type="text"],
    .ad-login-form input[type="password"],
    .ad-login-form input[type="email"] {
        font-size: 14px;
        padding: 10px 36px;
    }

    .ad-login-icon {
        left: 10px;
        width: 16px;
        height: 16px;
    }

    .toggle-password {
        right: 10px;
        width: 16px;
        height: 16px;
    }

    .amp-auth-info {
        padding: 16px 20px;
        font-size: 13px;
        margin-bottom: 16px;
    }

    .amp-auth-info::after {
        left: 16px;
        font-size: 16px;
    }
}

@media (min-width: 481px) and (max-width: 768px) {
    .ad-login-card {
        max-width: 520px;
        padding: 35px 25px;
    }
}

@media (min-width: 769px) {
    .ad-login-card {
        max-width: 520px;
        padding: 40px;
    }
}

.ad-login-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.ad-login-checkbox {
    display: flex;
    align-items: flex-start;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    cursor: pointer;
    position: relative;
    z-index: 1;
    margin-bottom: 20px;
}

.ad-login-checkbox input[type="checkbox"] {
    margin-top: 2px;
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: white;
    cursor: pointer;
}

.ad-login-checkbox .checkmark {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 10px;
    margin-top: 2px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    background: transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.ad-login-checkbox input[type="checkbox"]:checked + .checkmark {
    background: white;
    border-color: white;
}

.ad-login-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: 'âœ“';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--primary-color);
    font-weight: bold;
    font-size: 12px;
}

.ad-login-checkbox input[type="checkbox"] {
    display: none;
}

.terms-link {
    color: white;
    font-weight: 600;
    text-decoration: underline;
    transition: all 0.3s ease;
}

.terms-link:hover {
    text-decoration: none;
    opacity: 0.8;
}

.ad-login-field-note {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 5px;
    display: block;
    position: relative;
    z-index: 1;
}

.ad-login-message {
    background: var(--theme-gradient-light);
    border-left: 3px solid var(--primary-color);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 12px;
    position: relative;
    z-index: 1;
}

.ad-login-message p {
    margin: 0;
    font-size: 14px;
    color: white;
    line-height: 1.5;
    position: relative;
    z-index: 1;
}

.ad-login-instructions {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    z-index: 1;
    line-height: 1.5;
}

.google-login-section {
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.google-login-button {
    width: 100%;
    padding: 12px;
    background: white;
    color: #333;
    border: 1px solid #dadce0;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: var(--shadow-sm);
}

.google-login-button:hover {
    background: #f8f9fa;
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

.google-login-button:disabled,
.google-login-button:disabled:hover {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: white;
    box-shadow: var(--shadow-sm);
}

.social-login-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin: 20px 0;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    width: 100%;
}

.social-login-divider::before,
.social-login-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: rgba(255, 255, 255, 0.3);
    margin: 0 15px;
}

.social-login-divider span {
    padding: 0 10px;
    background: var(--theme-gradient);
    font-weight: 500;
    font-size: 12px;
    position: relative;
    z-index: 1;
    white-space: nowrap;
    flex-shrink: 0;
}

.amp-notification {
    margin: 15px 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.3s ease-out;
    position: relative;
    z-index: 1;
    width: 100%;
    box-sizing: border-box;
}

.amp-notification.error {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.amp-notification.success {
    background: linear-gradient(135deg, #51cf66, #40c057);
    color: white;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 12px;
}

.notification-icon {
    font-size: 20px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.notification-message {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    font-weight: bold;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    line-height: 1;
}

.notification-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .amp-notification {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
}

.dark-mode .amp-notification.error {
    background: linear-gradient(135deg, #e53e3e, #c53030);
}

.dark-mode .amp-notification.success {
    background: linear-gradient(135deg, #38a169, #2f855a);
}

.amp-auth-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 18px 24px;
    border-radius: 12px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
    border: none;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideInDown 0.4s ease-out;
    font-weight: 500;
    line-height: 1.5;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.amp-auth-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

.amp-auth-info::after {
    content: 'ðŸ“§';
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.dark-mode .amp-auth-info {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    box-shadow: 0 4px 20px rgba(90, 103, 216, 0.4);
}

/* Progressive Loading Styles */
.auth-loading-progress {
    margin: 20px 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    color: white;
}

.auth-loading-progress .progress-container {
    max-width: 100%;
}

.auth-loading-progress .progress-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.auth-loading-progress .progress-header i {
    font-size: 1.2em;
    margin-right: 10px;
    color: #fff;
}

.auth-loading-progress .progress-title {
    font-size: 1.1em;
    color: #fff;
}

.auth-loading-progress .progress-bar-container {
    margin-bottom: 10px;
}

.auth-loading-progress .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.auth-loading-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.auth-loading-progress .progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.9);
}

.auth-loading-progress #auth-progress-percentage {
    font-weight: 600;
    color: #fff;
}

.auth-loading-progress #auth-progress-status {
    font-style: italic;
    color: rgba(255, 255, 255, 0.8);
}

.google-login-button:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none;
}

.google-login-button:disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}
