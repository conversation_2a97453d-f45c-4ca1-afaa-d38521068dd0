<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';

class Ad_Dashboard_Optimizer {

    private static $instance = null;

    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public static function get_user_dashboard_data($user_id) {
        global $wpdb;
        $positions_data = array();
        try {
            $user_positions = get_user_meta($user_id, 'ad_positions', true);
            if (!is_array($user_positions) || empty($user_positions)) {
                return array(
                    'positions' => array(),
                    'total_positions' => 0
                );
            }

            $table_name = $wpdb->prefix . 'ad_positions';
            if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") !== $table_name) {
                error_log('Table does not exist: ' . $table_name);
                return array(
                    'positions' => array(),
                    'total_positions' => 0
                );
            }

            $position_placeholders = implode(',', array_fill(0, count($user_positions), '%s'));
            $results = $wpdb->get_results($wpdb->prepare(
                "SELECT ad_position, expiration_date,
                 DATEDIFF(expiration_date, NOW()) as days_remaining
                 FROM {$table_name}
                 WHERE ad_position IN ($position_placeholders)
                 ORDER BY expiration_date ASC",
                $user_positions
            ));
            if (is_array($results) && !empty($results)) {
                foreach ($results as $row) {
                    if (is_object($row) && isset($row->ad_position)) {
                        $positions_data[] = array(
                            'position' => $row->ad_position,
                            'expiration_date' => $row->expiration_date,
                            'days_remaining' => max(0, intval($row->days_remaining))
                        );
                    }
                }
            }
        } catch (Exception $e) {
            error_log('Dashboard Optimizer Error: ' . $e->getMessage());
        }
        return array(
            'positions' => $positions_data,
            'total_positions' => count($positions_data)
        );
    }

    public static function get_user_stats($user_id) {
        global $wpdb;
        $stats = array(
            'total_clicks' => 0,
            'total_positions' => 0,
            'expiring_soon' => 0,
            'monthly_clicks' => 0
        );
        try {
            if (function_exists('get_user_total_clicks')) {
                $stats['total_clicks'] = get_user_total_clicks($user_id, false);
                $stats['monthly_clicks'] = get_user_total_clicks($user_id, true);
            }
            $user_positions = get_user_meta($user_id, 'ad_positions', true);
            if (is_array($user_positions)) {
                $stats['total_positions'] = count($user_positions);

                if (!empty($user_positions)) {
                    $positions_table = $wpdb->prefix . 'ad_positions';
                    if ($wpdb->get_var("SHOW TABLES LIKE '{$positions_table}'") === $positions_table) {
                        $position_placeholders = implode(',', array_fill(0, count($user_positions), '%s'));
                        $expiring_soon = $wpdb->get_var($wpdb->prepare(
                            "SELECT COUNT(*) FROM {$positions_table}
                             WHERE ad_position IN ($position_placeholders)
                             AND DATEDIFF(expiration_date, NOW()) <= 7
                             AND DATEDIFF(expiration_date, NOW()) > 0",
                            $user_positions
                        ));
                        $stats['expiring_soon'] = intval($expiring_soon);
                    }
                } else {
                    $stats['expiring_soon'] = 0;
                }
            } else {
                $stats['total_positions'] = 0;
                $stats['expiring_soon'] = 0;
            }
        } catch (Exception $e) {
            error_log('Dashboard Stats Error: ' . $e->getMessage());
        }
        return $stats;
    }

    public static function get_popular_positions($limit = 5) {
        global $wpdb;
        $popular = array();
        try {
            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
            $all_positions = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
            
            $position_clicks = [];
            
            foreach ($all_positions as $position_obj) {
                $position = $position_obj->name;
                if (function_exists('get_position_last_30_days_clicks')) {
                    $all_users = get_users();
                    $total_clicks = 0;
                    foreach ($all_users as $user) {
                        $user_clicks = get_position_last_30_days_clicks($user->ID, $position);
                        $total_clicks += $user_clicks['total_clicks'];
                    }
                    if ($total_clicks > 0) {
                        $position_clicks[] = [
                            'position' => $position,
                            'clicks' => $total_clicks
                        ];
                    }
                }
            }
            
            usort($position_clicks, function($a, $b) {
                return $b['clicks'] - $a['clicks'];
            });
            
            $popular = array_slice($position_clicks, 0, $limit);
        } catch (Exception $e) {
            error_log('Popular Positions Error: ' . $e->getMessage());
        }
        return $popular;
    }

    public static function get_revenue_data($user_id) {
        global $wpdb;
        $revenue = array(
            'total_spent' => 0,
            'monthly_spent' => 0,
            'average_cpc' => 0,
            'average_cpm' => 0
        );
        try {
            $payments_table = $wpdb->prefix . 'ad_payments';
            if ($wpdb->get_var("SHOW TABLES LIKE '{$payments_table}'") === $payments_table) {
                $total_spent = $wpdb->get_var($wpdb->prepare(
                    "SELECT SUM(amount) FROM {$payments_table}
                     WHERE user_id = %d",
                    $user_id
                ));
                $monthly_spent = $wpdb->get_var($wpdb->prepare(
                    "SELECT SUM(amount) FROM {$payments_table}
                     WHERE user_id = %d
                     AND payment_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)",
                    $user_id
                ));
                $revenue['total_spent'] = floatval($total_spent);
                $revenue['monthly_spent'] = floatval($monthly_spent);
            }
        } catch (Exception $e) {
            error_log('Revenue Data Error: ' . $e->getMessage());
        }
        return $revenue;
    }

    public static function get_ad_performance_data($position, $user_id) {
        global $wpdb;

        $performance_data = [
            'cpc' => 0,
            'cpm' => 0,
        ];

        try {
            $clicks = 0;
            if (function_exists('get_position_last_30_days_clicks')) {
                $click_data = get_position_last_30_days_clicks($user_id, $position);
                $clicks = $click_data['total_clicks'];
            }

            $payments_table = $wpdb->prefix . 'ad_payments';
            $ad_price = $wpdb->get_var($wpdb->prepare(
                "SELECT amount FROM {$payments_table} WHERE ad_position = %s AND user_id = %d ORDER BY payment_date DESC LIMIT 1",
                $position, $user_id
            ));
            
            if ($ad_price === null) {
                $price_table = $wpdb->prefix . 'ad_price_calculation';
                $ad_price = $wpdb->get_var($wpdb->prepare("SELECT usdt_price FROM {$price_table} WHERE ad_position = %s", $position)) ?: 0;
            }

            $ad_price = floatval($ad_price);

            if ($clicks > 0) {
                $performance_data['cpc'] = $ad_price / $clicks;
            } else {
                $performance_data['cpc'] = 0;
            }

            $use_ga_for_pricing = get_option('use_ga_for_pricing', 'no');
            $monthly_pageviews = 0;

            if ($use_ga_for_pricing === 'yes') {
                $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
                if (file_exists($ga_file)) {
                    require_once($ga_file);
                    if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                        $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no');
                        if ($exclude_homepage === 'yes' && function_exists('amp_get_monthly_pageviews_exclude_homepage')) {
                            $monthly_pageviews = amp_get_monthly_pageviews_exclude_homepage(31);
                        } else if (function_exists('amp_get_monthly_pageviews')) {
                            $monthly_pageviews = amp_get_monthly_pageviews(31);
                        }
                    }
                }
            }

            if ($monthly_pageviews === 0) {
                $global_settings_table = $wpdb->prefix . 'ad_price_global_settings';
                $monthly_pageviews = $wpdb->get_var($wpdb->prepare("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", 'monthly_pageviews'));

                if ($monthly_pageviews === null) {
                    $monthly_visitors = $wpdb->get_var($wpdb->prepare("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", 'monthly_visitors'));
                    $monthly_pageviews = $monthly_visitors ? intval($monthly_visitors) * 4 : 2400000;
                }

                $monthly_pageviews = intval($monthly_pageviews);
            }

            if ($monthly_pageviews > 0) {
                $performance_data['cpm'] = ($ad_price / $monthly_pageviews) * 1000;
            }

        } catch (Exception $e) {
            error_log('Ad Performance Data Error for position ' . $position . ': ' . $e->getMessage());
        }
        
        return $performance_data;
    }
}