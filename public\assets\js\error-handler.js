(function() {
    'use strict';

    window.AMPErrorHandler = {
        errors: [],
        maxErrors: 50,
        isInitialized: false,

        init: function() {
            if (this.isInitialized) {
                return;
            }

            this.bindErrorHandlers();
            this.isInitialized = true;
    
        },

        bindErrorHandlers: function() {
            window.addEventListener('error', (event) => {
                this.logError({
                    type: 'JavaScript Error',
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    stack: event.error?.stack,
                    timestamp: new Date().toISOString()
                });
            });

            window.addEventListener('unhandledrejection', (event) => {
                this.logError({
                    type: 'Unhandled Promise Rejection',
                    message: event.reason?.message || event.reason,
                    stack: event.reason?.stack,
                    timestamp: new Date().toISOString()
                });
            });

            this.bindJQueryEvents();
        },

        bindJQueryEvents: function() {
            if (typeof jQuery !== 'undefined') {
                jQuery(document).ajaxError((event, xhr, settings, thrownError) => {
                    this.logError({
                        type: 'AJAX Error',
                        message: thrownError || 'AJAX request failed',
                        url: settings.url,
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText?.substring(0, 500),
                        timestamp: new Date().toISOString()
                    });
                });
            } else {
                setTimeout(() => this.bindJQueryEvents(), 100);
            }
        },

        logError: function(errorData) {
            this.errors.push(errorData);
            
            if (this.errors.length > this.maxErrors) {
                this.errors.shift();
            }



            if (errorData.type === 'JavaScript Error' && 
                (errorData.message?.includes('hasTimer') || 
                 errorData.message?.includes('timeoutMinutes'))) {
                this.handleCheckoutError();
            }

            if (errorData.type === 'AJAX Error' && errorData.status >= 500) {
                this.handleServerError();
            }
        },

        handleCheckoutError: function() {
            if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup('เกิดข้อผิดพลาดในระบบชำระเงิน กรุณาลองใหม่', 'error');
            }
        },

        handleServerError: function() {
            if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup('เซิร์ฟเวอร์ไม่ตอบสนอง กรุณาลองใหม่ในภายหลัง', 'error');
            }
        },

        getErrors: function() {
            return this.errors;
        },

        clearErrors: function() {
            this.errors = [];
        },

        safeCall: function(fn, context, ...args) {
            try {
                if (typeof fn === 'function') {
                    return fn.apply(context, args);
                }
            } catch (error) {
                this.logError({
                    type: 'Safe Call Error',
                    message: error.message,
                    stack: error.stack,
                    functionName: fn.name || 'anonymous',
                    timestamp: new Date().toISOString()
                });
            }
        },

        safeAjax: function(options) {
            const defaultOptions = {
                timeout: 30000,
                error: (xhr, status, error) => {
                    this.logError({
                        type: 'AJAX Error',
                        message: error || 'AJAX request failed',
                        url: options.url,
                        status: xhr.status,
                        statusText: xhr.statusText,
                        timestamp: new Date().toISOString()
                    });
                }
            };

            return $.ajax($.extend(defaultOptions, options));
        }
    };

    function initErrorHandler() {
        if (typeof jQuery !== 'undefined') {
            jQuery(document).ready(function() {
                window.AMPErrorHandler.init();
            });
        } else {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    window.AMPErrorHandler.init();
                });
            } else {
                window.AMPErrorHandler.init();
            }
        }
    }

    if (typeof jQuery !== 'undefined') {
        initErrorHandler();
    } else {
        let checkJQuery = setInterval(() => {
            if (typeof jQuery !== 'undefined') {
                clearInterval(checkJQuery);
                initErrorHandler();
            }
        }, 100);

        setTimeout(() => {
            clearInterval(checkJQuery);
            initErrorHandler();
        }, 5000);
    }

})();
