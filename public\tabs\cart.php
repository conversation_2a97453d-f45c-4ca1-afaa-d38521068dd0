<?php

if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-user-manager.php';
require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';
require_once AMP_PLUGIN_DIR . 'includes/core/class-price-calculator.php';

$current_user = wp_get_current_user();
$current_user_id = $current_user->ID;

if (!$current_user_id) {
    echo '<div class="cart-container"><div class="cart-empty-modern">กรุณาเข้าสู่ระบบ</div></div>';
    return;
}

$user_manager = new \AdManagementPro\Modules\Shared\UserManager('public');
$cart = $user_manager->get_user_cart($current_user_id);

$cart_items_with_prices = array();
$subtotal = 0;
$cart_total = 0;

$position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
$database = \AdManagementPro\Core\Database::instance();
$price_calculator = \AMP_Price_Calculator::instance();

if (!empty($cart)) {
    $price_table = $database->get_table('ad_price_calculation');

    foreach ($cart as $key => $item) {
        $position_name = $item['position_name'] ?? $key;
        $duration = (int)($item['duration'] ?? 30);

        $price_details = $price_calculator->calculate_price_details($position_name, $duration);

        $cart_items_with_prices[] = [
            'position' => $position_name,
            'duration' => $duration,
            'base_price' => $price_details['base_price'],
            'original_price' => $price_details['original_price'],
            'discount_rate' => $price_details['discount_rate'],
            'total_price' => $price_details['final_price'],
            'is_trial' => $price_details['is_trial'],
        ];
        
        $subtotal += $price_details['original_price'];
        $cart_total += $price_details['final_price'];
    }
}
?>

<div class="cart-container">
    <div class="cart-header">
        <h2>🛒 ตะกร้าสินค้า</h2>
        <p>รายการป้ายโฆษณาที่คุณเลือกซื้อ พร้อมดำเนินการชำระเงิน</p>
    </div>

    <?php if (!empty($cart_items_with_prices)) : ?>
        <div class="cart-content">
            <div class="cart-items">
                <div class="cart-items-header">
                    <h3><i class="fas fa-list"></i> รายการสินค้า (<?php echo count($cart_items_with_prices); ?> รายการ)</h3>
                </div>
                
                <?php foreach ($cart_items_with_prices as $index => $item) : ?>
                    <div class="cart-item">
                        <div class="cart-item-icon">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        
                        <div class="cart-item-info">
                            <h4><?php echo esc_html($item['position']); ?></h4>
                            <div class="cart-item-details">
                                <span class="cart-item-duration">
                                    <i class="fas fa-calendar-alt"></i>
                                    <?php echo esc_html($item['duration']); ?> วัน
                                    <?php if ($item['is_trial']) : ?>
                                        <span class="cart-item-badge trial">โปรโมชั่น 7 วัน</span>
                                    <?php endif; ?>
                                </span>

                                <?php if ($item['discount_rate'] > 0) : ?>
                                    <span class="cart-item-discount">
                                        <i class="fas fa-tag"></i>
                                        ส่วนลด <?php echo esc_html($item['discount_rate']); ?>%
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="cart-item-price">
                            <?php if ($item['discount_rate'] > 0 || $item['is_trial']) : ?>
                                <span class="cart-item-original-price"><?php echo $item['original_price']; ?> USDT</span>
                            <?php endif; ?>
                            <span class="cart-item-final-price"><?php echo $item['total_price']; ?> USDT</span>
                        </div>

                        <div class="cart-item-actions">
                            <button class="cart-item-remove" data-position="<?php echo esc_attr($item['position']); ?>" title="ลบรายการ">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="cart-summary">
                <div class="cart-summary-header">
                    <h3><i class="fas fa-calculator"></i> สรุปการสั่งซื้อ</h3>
                </div>
                
                <div class="cart-summary-details">
                    <div class="cart-summary-row">
                        <span>จำนวนรายการ:</span>
                        <span><?php echo count($cart_items_with_prices); ?> รายการ</span>
                    </div>
                    
                    <div class="cart-summary-row">
                        <span>ยอดรวมก่อนส่วนลด:</span>
                        <span><?php echo number_format($subtotal, 2); ?> USDT</span>
                    </div>
                    
                    <?php
                        $total_discount_amount = $subtotal - $cart_total;
                    if ($total_discount_amount > 0) : ?>
                    <div class="cart-summary-row discount">
                        <span><i class="fas fa-tag"></i> ส่วนลดรวม:</span>
                        <span>-<?php echo number_format($total_discount_amount, 2); ?> USDT</span>
                    </div>
                    <?php endif; ?>
                    
                    <div class="cart-total">
                        <span class="cart-total-label">ยอดรวมสุทธิ:</span>
                        <span class="cart-total-value"><?php echo number_format($cart_total, 2); ?> USDT</span>
                    </div>
                </div>

                <div class="cart-actions">
                    <div class="cart-actions-left">
                        <button class="cart-continue-shopping ajax-nav-btn" data-tab="buy">
                            <i class="fas fa-arrow-left"></i> ซื้อสินค้าต่อ
                        </button>
                        <button id="clear-cart" class="cart-clear-btn">
                            <i class="fas fa-trash"></i> ล้างตะกร้า
                        </button>
                    </div>
                    <button id="proceed-to-checkout" class="cart-checkout-btn">
                        <i class="fas fa-credit-card"></i> ดำเนินการชำระเงิน
                    </button>
                </div>
            </div>
        </div>
    <?php else : ?>
        <div class="cart-empty-modern">
            <div class="cart-empty-animation">
                <div class="cart-empty-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="cart-empty-content">
                    <h3>ตะกร้าของคุณว่างเปล่า</h3>
                    <p>เริ่มต้นซื้อป้ายโฆษณาเพื่อเพิ่มการมองเห็นของคุณ</p>
                    <div class="cart-empty-actions">
                        <button class="cart-cta-btn ajax-nav-btn" data-tab="buy">
                            <i class="fas fa-plus"></i> ซื้อป้ายโฆษณา
                        </button>
                        <button class="cart-browse-btn ajax-nav-btn" data-tab="overview">
                            <i class="fas fa-chart-line"></i> ดูสถิติ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
    function waitForDependencies(callback) {
        if (typeof jQuery !== 'undefined' && typeof adDashboardData !== 'undefined') {
            callback();
        } else {
            setTimeout(() => waitForDependencies(callback), 100);
        }
    }

    function initCartFunctionsWithDependencies() {
        if (typeof jQuery === 'undefined') {
            console.error('jQuery not available for cart functionality');
            return;
        }

        const $ = jQuery;

        function showFallbackPopup(message, type = 'info') {
            if (typeof window.showMiniPopup === 'function') {
                window.showMiniPopup(message, type);
            } else {
                alert(message);
            }
        }

        function initCartFunctions() {
            $(document).off('click.cart', '.cart-item-remove').on('click.cart', '.cart-item-remove', function() {
                const $item = $(this).closest('.cart-item');
                const position = $(this).data('position');

                if (typeof Swal === 'undefined') {
                    if (confirm(`คุณต้องการลบ "${position}" ออกจากตะกร้าใช่ไหม?`)) {
                        removeCartItem($item, position);
                    }
                    return;
                }

                Swal.fire({
                    title: 'คุณแน่ใจหรือไม่?',
                    html: `คุณต้องการลบ <strong>"${position}"</strong> ออกจากตะกร้าใช่ไหม?`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-trash"></i> ใช่, ลบเลย',
                    cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
                    customClass: {
                        popup: 'amp-popup-container amp-popup-warning',
                        confirmButton: 'amp-btn amp-btn-primary',
                        cancelButton: 'amp-btn amp-btn-secondary'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        removeCartItem($item, position);
                    }
                });
            });

            function removeCartItem($item, position) {
                if (typeof adDashboardData === 'undefined') {
                    showFallbackPopup('ไม่สามารถเชื่อมต่อกับระบบได้', 'error');
                    return;
                }

                $item.addClass('cart-item-loading');
                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'remove_cart_item',
                        security: adDashboardData.nonce,
                        position: position
                    },
                    success: function(response) {
                        if (response.success) {
                            $item.addClass('cart-item-removing');
                            showFallbackPopup('ลบรายการเรียบร้อยแล้ว', 'success');
                            setTimeout(() => {
                                $item.fadeOut(300, function() {
                                    $(this).remove();
                                    if ($('.cart-item').length === 0) {
                                        $('.cart-container').html(`
                                            <div class="cart-empty-modern">
                                                <div class="cart-empty-animation">
                                                    <div class="cart-empty-icon">
                                                        <i class="fas fa-shopping-cart"></i>
                                                    </div>
                                                    <div class="cart-empty-content">
                                                        <h3>ตะกร้าของคุณว่างเปล่า</h3>
                                                        <p>เริ่มต้นซื้อป้ายโฆษณาเพื่อเพิ่มการมองเห็นของคุณ</p>
                                                        <div class="cart-empty-actions">
                                                            <button class="cart-cta-btn ajax-nav-btn" data-tab="buy">
                                                                <i class="fas fa-plus"></i> ซื้อป้ายโฆษณา
                                                            </button>
                                                            <button class="cart-browse-btn ajax-nav-btn" data-tab="overview">
                                                                <i class="fas fa-chart-line"></i> ดูสถิติ
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `);
                                    }
                                    if (typeof window.updateCartCount === 'function') {
                                        window.updateCartCount(response.data.cart_count || 0);
                                    }
                                });
                            }, 500);
                        } else {
                            $item.removeClass('cart-item-loading');
                            showFallbackPopup(response.data.message || 'ไม่สามารถลบรายการได้', 'error');
                        }
                    },
                    error: function() {
                        $item.removeClass('cart-item-loading');
                        showFallbackPopup('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้', 'error');
                    }
                });
            }

            $(document).off('click.cart', '#clear-cart').on('click.cart', '#clear-cart', function() {
                if (typeof Swal === 'undefined') {
                    if (confirm('คุณต้องการล้างสินค้าทั้งหมดในตะกร้าใช่ไหม?')) {
                        clearCart();
                    }
                    return;
                }

                Swal.fire({
                    title: 'คุณแน่ใจหรือไม่?',
                    html: 'คุณต้องการล้างสินค้าทั้งหมดในตะกร้าใช่ไหม?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '<i class="fas fa-trash"></i> ใช่, ล้างทั้งหมด',
                    cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
                    customClass: {
                        popup: 'amp-popup-container amp-popup-warning',
                        confirmButton: 'amp-btn amp-btn-primary',
                        cancelButton: 'amp-btn amp-btn-secondary'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        clearCart();
                    }
                });
            });

            function clearCart() {
                if (typeof adDashboardData === 'undefined') {
                    showFallbackPopup('ไม่สามารถเชื่อมต่อกับระบบได้', 'error');
                    return;
                }

                $('#clear-cart').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังล้าง...');
                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'clear_cart',
                        security: adDashboardData.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            showFallbackPopup('ล้างตะกร้าเรียบร้อยแล้ว', 'success');
                            setTimeout(() => {
                                $('.cart-container').html(`
                                    <div class="cart-empty-modern">
                                        <div class="cart-empty-animation">
                                            <div class="cart-empty-icon">
                                                <i class="fas fa-shopping-cart"></i>
                                            </div>
                                            <div class="cart-empty-content">
                                                <h3>ตะกร้าของคุณว่างเปล่า</h3>
                                                <p>เริ่มต้นซื้อป้ายโฆษณาเพื่อเพิ่มการมองเห็นของคุณ</p>
                                                <div class="cart-empty-actions">
                                                    <button class="cart-cta-btn ajax-nav-btn" data-tab="buy">
                                                        <i class="fas fa-plus"></i> ซื้อป้ายโฆษณา
                                                    </button>
                                                    <button class="cart-browse-btn ajax-nav-btn" data-tab="overview">
                                                        <i class="fas fa-chart-line"></i> ดูสถิติ
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `);
                                if (typeof window.updateCartCount === 'function') {
                                    window.updateCartCount(0);
                                }
                            }, 1000);
                        } else {
                            showFallbackPopup(response.data.message || 'ไม่สามารถล้างตะกร้าได้', 'error');
                            $('#clear-cart').prop('disabled', false).html('<i class="fas fa-trash"></i> ล้างตะกร้า');
                        }
                    },
                    error: function() {
                        showFallbackPopup('ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้', 'error');
                        $('#clear-cart').prop('disabled', false).html('<i class="fas fa-trash"></i> ล้างตะกร้า');
                    }
                });
            }

            $(document).off('click.cart', '#proceed-to-checkout').on('click.cart', '#proceed-to-checkout', function() {
                if (typeof window.openCheckoutPopup === 'function') {
                    window.openCheckoutPopup();
                } else {
                    showFallbackPopup('ระบบชำระเงินยังไม่พร้อมใช้งาน', 'error');
                }
            });
        }

        initCartFunctions();
        window.initCartFunctions = initCartFunctions;
    }

    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function() {
            waitForDependencies(initCartFunctionsWithDependencies);
        });
    } else {
        document.addEventListener('DOMContentLoaded', function() {
            waitForDependencies(initCartFunctionsWithDependencies);
        });
    }
</script>

<style>
.cart-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: 20px;
}

.cart-header {
    margin-bottom: 40px;
    text-align: center;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.03), rgba(114, 9, 183, 0.03));
    border-radius: 24px;
    padding: 40px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(67, 97, 238, 0.1);
}

.cart-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.05) 75%);
    background-size: 30px 30px;
    opacity: 0.3;
    pointer-events: none;
}

.cart-header h2 {
    font-size: 32px;
    margin-bottom: 15px;
    color: var(--text-color);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    z-index: 1;
}

.cart-header p {
    color: var(--light-text);
    font-size: 18px;
    font-weight: 500;
    position: relative;
    z-index: 1;
    margin: 0;
}

.cart-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.cart-items {
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.02));
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(67, 97, 238, 0.1);
    overflow: hidden;
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.cart-items:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(67, 97, 238, 0.12), 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.cart-items-header {
    padding: 25px;
    background: rgba(67, 97, 238, 0.03);
    border-bottom: 1px solid var(--border-color);
}

.cart-items-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-item {
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item:hover {
    background: rgba(67, 97, 238, 0.02);
}

.cart-item-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.cart-item-info {
    flex: 1;
}

.cart-item-info h4 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: var(--text-color);
    font-weight: 600;
}

.cart-item-details {
    display: flex;
    gap: 15px;
    color: var(--light-text);
    font-size: 14px;
    align-items: center;
}

.cart-item-duration,
.cart-item-discount {
    display: flex;
    align-items: center;
    gap: 6px;
}

.cart-item-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
}

.cart-item-badge.trial {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.cart-item-price {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 15px;
}

.cart-item-original-price {
    font-size: 14px;
    color: var(--light-text);
    text-decoration: line-through;
    margin-bottom: 4px;
}

.cart-item-final-price {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
}

.cart-item-actions {
    display: flex;
    align-items: center;
}

.cart-item-remove {
    background: rgba(231, 76, 60, 0.1);
    border: 2px solid rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.cart-item-remove:hover {
    background: #e74c3c;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.cart-summary {
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.02));
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(67, 97, 238, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.cart-summary:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(67, 97, 238, 0.12), 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.cart-summary-header {
    padding: 25px 25px 0 25px;
}

.cart-summary-header h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cart-summary-details {
    padding: 0 25px 25px 25px;
}

.cart-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

.cart-summary-row:last-child {
    border-bottom: none;
}

.cart-summary-row.discount {
    color: #27ae60;
    font-weight: 600;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    margin-top: 15px;
    border-top: 2px solid var(--primary-color);
    background: rgba(67, 97, 238, 0.03);
    margin: 20px -25px 0 -25px;
    padding: 20px 25px;
}

.cart-total-label {
    font-size: 18px;
    font-weight: 700;
    color: var(--text-color);
}

.cart-total-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.cart-actions {
    padding: 25px;
    border-top: 1px solid var(--border-color);
    background: rgba(67, 97, 238, 0.02);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cart-actions-left {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.cart-clear-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.cart-clear-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.cart-continue-shopping {
    color: var(--text-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 10px 16px;
    border-radius: 12px;
    border: 2px solid var(--border-color);
    background: var(--card-bg);
    cursor: pointer;
}

.cart-continue-shopping:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
}

.cart-checkout-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.cart-checkout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
    background: linear-gradient(135deg, var(--primary-hover), var(--secondary-color));
}

.cart-empty-modern {
    text-align: center;
    padding: 80px 40px;
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.02));
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(67, 97, 238, 0.1);
    position: relative;
    overflow: hidden;
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.cart-empty-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4361ee, #7209b7, #f72585);
    border-radius: 24px 24px 0 0;
}

.cart-empty-animation {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.cart-empty-icon {
    font-size: 120px;
    color: var(--light-text);
    margin-bottom: 30px;
    position: relative;
    display: inline-block;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.cart-empty-content h3 {
    font-size: 28px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cart-empty-content p {
    color: var(--light-text);
    margin-bottom: 40px;
    font-size: 18px;
    line-height: 1.6;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.cart-empty-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.cart-cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
    position: relative;
    overflow: hidden;
}

.cart-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cart-cta-btn:hover::before {
    left: 100%;
}

.cart-cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(67, 97, 238, 0.4);
}

.cart-browse-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 16px 32px;
    background: linear-gradient(135deg, var(--light-text), var(--border-color));
    color: var(--card-bg);
    border: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(100, 116, 139, 0.3);
    position: relative;
    overflow: hidden;
}

.cart-browse-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cart-browse-btn:hover::before {
    left: 100%;
}

.cart-browse-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(100, 116, 139, 0.4);
    background: linear-gradient(135deg, var(--text-color), var(--light-text));
}

.cart-item-loading {
    opacity: 0.6;
    pointer-events: none;
}

.cart-item-removing {
    animation: slideOut 0.5s ease-out forwards;
}

@keyframes slideOut {
    to {
        opacity: 0;
        transform: translateX(-100%);
        height: 0;
        padding: 0;
        margin: 0;
    }
}

@media (max-width: 1024px) {
    .cart-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }
    
    .cart-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .cart-container {
        padding: 15px;
    }

    .cart-header {
        padding: 25px;
    }

    .cart-header h2 {
        font-size: 24px;
    }

    .cart-item {
        padding: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .cart-item-price {
        align-items: flex-start;
        margin-right: 0;
    }

    .cart-actions {
        flex-direction: column;
    }

    .cart-actions-left {
        flex-direction: column;
        gap: 10px;
    }

    .cart-empty-modern {
        padding: 60px 20px;
    }

    .cart-empty-icon {
        font-size: 80px;
    }

    .cart-empty-content h3 {
        font-size: 24px;
    }

    .cart-empty-content p {
        font-size: 16px;
    }

    .cart-empty-actions {
        flex-direction: column;
        align-items: center;
    }

    .cart-cta-btn,
    .cart-browse-btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }
}

.dark-mode .cart-empty-modern {
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.05));
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(67, 97, 238, 0.2);
}

.dark-mode .cart-empty-modern::before {
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), #f72585);
}

.dark-mode .cart-empty-content h3 {
    color: var(--text-color);
}

.dark-mode .cart-empty-content p {
    color: var(--light-text);
}

.dark-mode .cart-empty-icon {
    color: var(--light-text);
}

.dark-mode .cart-browse-btn {
    background: linear-gradient(135deg, var(--light-text), var(--border-color));
    color: var(--card-bg);
}

.dark-mode .cart-browse-btn:hover {
    background: linear-gradient(135deg, var(--text-color), var(--light-text));
}
</style>
