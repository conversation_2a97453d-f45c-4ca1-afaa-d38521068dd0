<?php
if (!defined('AMP_PLUGIN_DIR')) {
    require_once dirname(__DIR__, 2) . '/ad-management-pro.php';
}

require_once AMP_PLUGIN_DIR . 'includes/bootstrap.php';

if (defined('ABSPATH') && function_exists('is_user_logged_in') && is_user_logged_in()) {
    if (!wp_doing_ajax()) {
        $user_id = get_current_user_id();



        $last_login = get_user_meta($user_id, 'amp_last_login', true);
        $grace_period = 30;
        $is_recent_login = $last_login && (time() - $last_login) < $grace_period;

        $security_manager = \AdManagementPro\Core\UnifiedSecurityManager::instance();
        $session_valid = !$security_manager->is_session_expired();

        if (isset($_GET['action']) && $_GET['action'] === 'logout') {
            wp_logout();
            wp_safe_redirect(home_url('/login/?logged_out=true'));
            exit;
        }

        if (($is_recent_login || current_user_can('manage_options') || current_user_can('amp_advertiser_access')) && $session_valid) {
            $redirect_to = $_GET['redirect_to'] ?? '';
            $from = $_GET['from'] ?? '';
            $is_ajax_request = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
            $is_overlay_request = isset($_GET['overlay']) || isset($_POST['overlay']);
            $is_session_check = isset($_GET['check_session']) || isset($_POST['check_session']);

            if (!$is_ajax_request && !isset($_GET['action']) && !$is_overlay_request && !$is_session_check) {
                if (!empty($redirect_to) && filter_var($redirect_to, FILTER_VALIDATE_URL)) {
                    $parsed_url = parse_url($redirect_to);
                    if ($parsed_url['host'] === $_SERVER['HTTP_HOST']) {
                        wp_redirect($redirect_to);
                        exit;
                    }
                }

                if (!empty($from) && strpos($from, '/dashboard') !== false) {
                    wp_redirect(home_url($from));
                    exit;
                }

                wp_redirect(home_url('/dashboard/'));
                exit;
            }
        } elseif (!$session_valid) {
            wp_logout();
            error_log('AMP: Session expired, user logged out automatically');
        }
    }
}

if (isset($_GET['action'])) {
    $action = sanitize_text_field($_GET['action']);

    if ($action === 'google_callback') {
        try {
            $ajax_handlers = \AdManagementPro\Core\AjaxHandlers::instance();
            if ($ajax_handlers) {
                $ajax_handlers->handle_google_callback();
            } else {
                wp_redirect(home_url('/login/?error=handler_failed'));
            }
        } catch (Exception $e) {
            error_log('AMP Google Callback Exception: ' . $e->getMessage());
            wp_redirect(home_url('/login/?error=google_system_error'));
        }
        exit;
    } elseif ($action === 'verify_email') {
        try {
            $ajax_handlers = \AdManagementPro\Core\AjaxHandlers::instance();
            if ($ajax_handlers) {
                $ajax_handlers->handle_verify_email();
            } else {
                wp_redirect(home_url('/login/?error=handler_failed'));
            }
        } catch (Exception $e) {
            error_log('AMP Email Verification Exception: ' . $e->getMessage());
            wp_redirect(home_url('/login/?error=verification_failed'));
        }
        exit;
    }
}

$logo_url = defined('ABSPATH') ? get_option('site_logo_url', '') : '';
if (empty($logo_url)) {
    $logo_url = AMP_PLUGIN_URL . 'admin/assets/images/logo.svg';
}

$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'login';
$reset_mode = (isset($_GET['action']) && $_GET['action'] === 'reset_password' && isset($_GET['token']) && isset($_GET['user']));

if ($reset_mode) {
    $active_tab = 'reset-password';
}

$dark_mode = isset($_COOKIE['amp_dark_mode']) ? $_COOKIE['amp_dark_mode'] === 'true' : false;

$unified_nonce = wp_create_nonce('amp_dashboard_action');
$site_name = defined('ABSPATH') && function_exists('get_bloginfo') ? get_bloginfo('name') : 'Ad Management Pro';
$site_charset = defined('ABSPATH') && function_exists('get_bloginfo') ? get_bloginfo('charset') : 'UTF-8';
$turnstile_enabled = defined('ABSPATH') ? (bool) get_option('amp_turnstile_enabled', false) : false;
$turnstile_site_key = defined('ABSPATH') ? (\AMP_Encryption_Manager::instance()->get_secret('amp_turnstile_site_key')) : '';
$google_login_enabled = defined('ABSPATH') ? (bool) get_option('amp_google_login_enabled', false) : false;
$google_client_id = defined('ABSPATH') ? (\AMP_Encryption_Manager::instance()->get_secret('amp_google_client_id')) : '';

if (!headers_sent()) {
    header('Content-Type: text/html; charset=UTF-8');
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication &mdash; <?php echo esc_html($site_name); ?></title>
    
    <link rel="icon" href="<?php echo esc_url($logo_url); ?>">

    <style nonce="<?php echo esc_attr($unified_nonce); ?>">
        .dark-mode-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .dark-mode-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        
        .dark-mode-toggle svg {
            width: 24px;
            height: 24px;
            fill: #333;
            transition: all 0.3s ease;
        }
        
        .dark-mode .dark-mode-toggle {
            background: rgba(51, 51, 51, 0.9);
        }
        
        .dark-mode .dark-mode-toggle svg {
            fill: #fff;
        }
        
        .sun-icon { display: block; }
        .moon-icon { display: none; }
        
        .dark-mode .sun-icon { display: none; }
        .dark-mode .moon-icon { display: block; }

        .ad-login-captcha {
            margin: 20px 0;
            text-align: center;
        }

        .ad-login-captcha .cf-turnstile {
            margin: 0 auto;
            display: inline-block;
        }
    </style>
    
    <link rel="stylesheet" href="<?php echo esc_url(AMP_PLUGIN_URL . 'public/assets/css/login.css?v=' . AMP_VERSION); ?>">

<?php if ($turnstile_enabled && !empty($turnstile_site_key)): ?>
<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
<?php endif; ?>

<?php if ($google_login_enabled && !empty($google_client_id)): ?>
<script src="https://apis.google.com/js/platform.js" async defer></script>
<?php endif; ?>

</head>
<body class="amp-auth-body <?php echo $dark_mode ? 'dark-mode' : ''; ?>">
    <div class="floating-elements">
        <div class="floating-element">💎</div>
        <div class="floating-element">⭐</div>
        <div class="floating-element">🔷</div>
        <div class="floating-element">✨</div>
        <div class="floating-element">🌟</div>
        <div class="floating-element">💫</div>
        <div class="floating-element">🔸</div>
        <div class="floating-element">🌠</div>
        <div class="floating-element">💠</div>
        <div class="floating-element">🔹</div>
        <div class="floating-element">⚡</div>
        <div class="floating-element">🎯</div>
        <div class="floating-element">🎪</div>
        <div class="floating-element">🎨</div>
        <div class="floating-element">🎭</div>
        <div class="floating-element">🌈</div>
    </div>

    <div class="dark-mode-toggle" id="dark-mode-toggle" title="Toggle Dark/Light Mode">
        <svg class="sun-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41.39.39 1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41.39.39 1.03.39 1.41 0l1.06-1.06z"/></svg>
        <svg class="moon-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"/></svg>
    </div>

    <div class="ad-login-container">
        <div class="ad-login-card">
            <div class="ad-login-header">
                <img src="<?php echo esc_url($logo_url); ?>" alt="<?php echo esc_attr($site_name); ?> Logo" class="ad-login-logo">
                <h1 id="auth-title"><?php
                    $titles = [
                        'login' => 'เข้าสู่ระบบ',
                        'register' => 'สมัครสมาชิก',
                        'forgot-password' => 'ลืมรหัสผ่าน',
                        'reset-password' => 'รีเซ็ตรหัสผ่าน'
                    ];
                    echo esc_html($titles[$active_tab] ?? 'เข้าสู่ระบบ');
                ?></h1>
            </div>

            <div id="amp-auth-messages">
                <?php
                if (isset($_GET['error'])) {
                    $error_messages = [
                        'google_auth_failed' => 'การเข้าสู่ระบบด้วย Google ล้มเหลว',
                        'security_check_failed' => 'การตรวจสอบความปลอดภัยล้มเหลว',
                        'google_not_configured' => 'การตั้งค่า Google Login ไม่สมบูรณ์',
                        'google_token_failed' => 'ไม่สามารถรับ token จาก Google ได้',
                        'google_token_invalid' => 'Token จาก Google ไม่ถูกต้อง',
                        'google_user_failed' => 'ไม่สามารถดึงข้อมูลผู้ใช้จาก Google ได้',
                        'google_email_missing' => 'ไม่พบอีเมลจากบัญชี Google',
                        'user_creation_failed' => 'ไม่สามารถสร้างบัญชีผู้ใช้ได้',
                        'google_system_error' => 'เกิดข้อผิดพลาดของระบบ',
                        'invalid_verification_link' => 'ลิงก์ยืนยันอีเมลไม่ถูกต้อง',
                        'user_not_found' => 'ไม่พบบัญชีผู้ใช้',
                        'invalid_token' => 'รหัสยืนยันไม่ถูกต้อง',
                        'token_expired' => 'รหัสยืนยันหมดอายุแล้ว',
                        'token_expired_user_deleted' => 'รหัสยืนยันหมดอายุและบัญชีถูกลบแล้ว กรุณาสมัครใหม่',
                        'verification_failed' => 'การยืนยันอีเมลล้มเหลว',
                        'session_expired' => 'เซสชันของคุณหมดอายุแล้ว กรุณาเข้าสู่ระบบใหม่',
                        'session_hijacking' => 'ตรวจพบการเข้าถึงที่ผิดปกติ กรุณาเข้าสู่ระบบใหม่'
                    ];
                    $error_key = sanitize_text_field($_GET['error']);
                    if (isset($error_messages[$error_key])) {
                        $error_message = $error_messages[$error_key];
                        echo '<div class="amp-auth-error">' . esc_html($error_message) . '</div>';
                    }
                }

                if (isset($_GET['success'])) {
                    $success_messages = [
                        'email_verified' => 'ยืนยันอีเมลสำเร็จ! ตอนนี้คุณสามารถเข้าสู่ระบบได้แล้ว'
                    ];
                    $success_key = sanitize_text_field($_GET['success']);
                    if (isset($success_messages[$success_key])) {
                        echo '<div class="amp-auth-success">' . esc_html($success_messages[$success_key]) . '</div>';
                    }
                }

                if (isset($_GET['action']) && $_GET['action'] === 'verification_sent') {
                    echo '<div class="amp-auth-info">อีเมลยืนยันถูกส่งแล้ว กรุณาตรวจสอบกล่องจดหมายของคุณ</div>';
                }
                ?>
            </div>

            <?php if (!$reset_mode): ?>
            <div class="amp-auth-tabs">
                <div class="amp-auth-tab <?php if($active_tab === 'login') echo 'active'; ?>" data-tab="login">Login</div>
                <div class="amp-auth-tab <?php if($active_tab === 'register') echo 'active'; ?>" data-tab="register">Register</div>
                <div class="amp-auth-tab <?php if($active_tab === 'forgot-password') echo 'active'; ?>" data-tab="forgot-password">Forgot Password</div>
            </div>
            <?php endif; ?>

            <div id="login-form-container" class="amp-auth-form-container <?php if($active_tab === 'login') echo 'active'; ?>">
                <?php require_once AMP_PLUGIN_DIR . 'public/templates/login-form.php'; ?>
            </div>
            <div id="register-form-container" class="amp-auth-form-container <?php if($active_tab === 'register') echo 'active'; ?>">
                <?php require_once AMP_PLUGIN_DIR . 'public/templates/register-form.php'; ?>
            </div>
            <div id="forgot-password-form-container" class="amp-auth-form-container <?php if($active_tab === 'forgot-password') echo 'active'; ?>">
                <?php require_once AMP_PLUGIN_DIR . 'public/templates/forgot-password-form.php'; ?>
            </div>
            <div id="reset-password-form-container" class="amp-auth-form-container <?php if($active_tab === 'reset-password') echo 'active'; ?>">
                <?php require_once AMP_PLUGIN_DIR . 'public/templates/reset-password-form.php'; ?>
            </div>

            <?php
            if (file_exists(__DIR__ . '/email-verification-notice.php')) {
                include __DIR__ . '/email-verification-notice.php';
            }
            ?>
            <div class="ad-login-footer">
                <p>&copy; <?php echo date('Y'); ?> <?php echo esc_html($site_name); ?>. All rights reserved.</p>
            </div>
        </div>
    </div>

    <script nonce="<?php echo esc_attr($unified_nonce); ?>">
        window.ampAuthData = {
            ajaxurl: '<?php echo admin_url('admin-ajax.php'); ?>',
            dashboardUrl: '<?php echo esc_url(home_url('/dashboard/')); ?>',
            loginNonce: '<?php echo esc_js($unified_nonce); ?>',
            turnstileEnabled: <?php echo json_encode($turnstile_enabled); ?>,
            turnstileSiteKey: '<?php echo esc_js($turnstile_site_key); ?>',
            activeTab: '<?php echo esc_js($active_tab); ?>',
            googleLoginEnabled: <?php echo json_encode($google_login_enabled); ?>,
            googleClientId: '<?php echo esc_js($google_client_id); ?>',
            nonce: '<?php echo esc_js($unified_nonce); ?>'
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            const darkToggle = document.getElementById('dark-mode-toggle');
            if (darkToggle) {
                darkToggle.addEventListener('click', function() {
                    document.body.classList.toggle('dark-mode');
                    const isDark = document.body.classList.contains('dark-mode');
                    document.cookie = 'amp_dark_mode=' + isDark + '; path=/; max-age=31536000';
                });
            }
        });
    </script>
    <script src="<?php echo esc_url(AMP_PLUGIN_URL . 'public/assets/js/auth.js?v=' . AMP_VERSION); ?>" defer></script>
    <?php if ($turnstile_enabled && !empty($turnstile_site_key)): ?>
        <script src="<?php echo esc_url(AMP_PLUGIN_URL . 'public/assets/js/turnstile-handler.js?v=' . AMP_VERSION); ?>" defer></script>
    <?php endif; ?>

</body>
</html>
