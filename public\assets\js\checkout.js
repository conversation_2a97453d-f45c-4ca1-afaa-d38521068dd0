(function($) {
    'use strict';

    window.checkoutPopupOpen = false;

    function openCheckoutPopup(fromTimer = false) {
        if (typeof adDashboardData === 'undefined') {
            showFallbackMessage('ไม่สามารถเชื่อมต่อกับระบบได้', 'error');
            return;
        }

        if (!fromTimer) {
            extendUserSession();
        }

        let swalInstance = Swal.fire({
            title: 'กำลังเตรียมข้อมูล...',
            html: '<div class="loading-progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>กำลังตรวจสอบสถานะระบบ</p></div>',
            allowOutsideClick: false,
            showConfirmButton: false,
            customClass: {
                popup: 'modern-loading-popup'
            },
            willOpen: () => {
                Swal.showLoading();
            }
        });

        $.ajax({
            url: adDashboardData.ajaxurl,
            type: 'POST',
            data: {
                action: 'check_development_mode',
                security: adDashboardData.nonce
            },
            success: function(response) {
                if (response.success && response.data.is_enabled) {
                    Swal.fire({
                        title: '🚧 โหมดพัฒนา',
                        text: 'ระบบอยู่ในโหมดพัฒนา ไม่สามารถทำการซื้อสินค้าได้ในขณะนี้',
                        icon: 'warning',
                        confirmButtonText: 'ตกลง',
                        customClass: {
                            popup: 'modern-checkout-popup',
                            confirmButton: 'checkout-btn checkout-btn-primary'
                        },
                        buttonsStyling: false
                    });
                    return;
                }

                $('.swal2-html-container .loading-progress p').text('กำลังตรวจสอบข้อมูลผู้ใช้');
                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_user_bypass',
                        security: adDashboardData.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.has_bypass) {
                            $('.swal2-html-container .loading-progress p').text('กำลังดำเนินการชำระเงินแบบ Bypass...');
                            processBypassCheckout();
                        } else {
                            $('.swal2-html-container .loading-progress p').text('กำลังตรวจสอบการจอง...');
                            checkReservationSettings();
                        }
                    },
                    error: function() {
                        $('.swal2-html-container .loading-progress p').text('ดำเนินการต่อสู่หน้าชำระเงิน...');
                        checkReservationSettings();
                    }
                });
            },
            error: function() {
                $('.swal2-html-container .loading-progress p').text('กำลังตรวจสอบข้อมูลผู้ใช้');
                $.ajax({
                    url: adDashboardData.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_user_bypass',
                        security: adDashboardData.nonce
                    },
                    success: function(response) {
                        if (response.success && response.data.has_bypass) {
                            $('.swal2-html-container .loading-progress p').text('กำลังดำเนินการชำระเงินแบบ Bypass...');
                            processBypassCheckout();
                        } else {
                            $('.swal2-html-container .loading-progress p').text('กำลังตรวจสอบการจอง...');
                            checkReservationSettings();
                        }
                    },
                    error: function() {
                        $('.swal2-html-container .loading-progress p').text('ดำเนินการต่อสู่หน้าชำระเงิน...');
                        checkReservationSettings();
                    }
                });
            }
        });
    }

    function processBypassCheckout() {
        $.ajax({
            url: adDashboardData.ajaxurl,
            type: 'POST',
            data: {
                action: 'process_bypass_checkout',
                security: adDashboardData.nonce
            },
            success: function(response) {
                if (response.success) {
                    const successHtml = `
                        <div class="amp-success-popup-container">
                            <div class="amp-success-icon">
                                <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                                    <circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>
                                    <path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
                                </svg>
                            </div>
                            <div class="amp-success-header">
                                <h2>สำเร็จ!</h2>
                            </div>
                            <div class="amp-success-message">
                                <p>การซื้อเสร็จสมบูรณ์แล้ว คุณเป็นเจ้าของป้ายโฆษณาแล้ว</p>
                            </div>
                            <div class="amp-success-timer">
                                <p>กำลังนำทางไปยังหน้า 'ป้ายโฆษณาของฉัน' ใน <strong id="swal-timer-span">3</strong> วินาที...</p>
                            </div>
                            <div class="amp-success-actions">
                                <button class="amp-success-btn amp-success-btn-primary" onclick="goToMyAds()">
                                    <i class="fas fa-ad"></i>
                                    ไปทันที
                                </button>
                                <button class="amp-success-btn amp-success-btn-secondary" onclick="Swal.close()">
                                    <i class="fas fa-times"></i>
                                    ปิด
                                </button>
                            </div>
                        </div>
                    `;

                    Swal.fire({
                        html: successHtml,
                        showConfirmButton: false,
                        showCancelButton: false,
                        customClass: {
                            popup: 'amp-success-popup'
                        },
                        allowOutsideClick: false,
                        buttonsStyling: false,
                        timer: 3000,
                        timerProgressBar: true,
                        didOpen: () => {
                            const timerSpan = document.getElementById('swal-timer-span');
                            if (timerSpan) {
                                const timer = setInterval(() => {
                                    const secondsLeft = Math.ceil(Swal.getTimerLeft() / 1000);
                                    timerSpan.textContent = secondsLeft > 0 ? secondsLeft : 0;
                                }, 100);
                                Swal.getPopup().addEventListener('close', () => {
                                    clearInterval(timer);
                                });
                            }
                        },
                        willClose: () => {
                            if (typeof window.goToMyAds === 'function') {
                                window.goToMyAds();
                            }
                        }
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.timer) {
                            if (typeof window.goToMyAds === 'function') {
                                window.goToMyAds();
                            }
                        }
                    });
                     if (typeof window.updateCartCount === 'function') {
                        window.updateCartCount(0);
                    }
                     if (typeof window.refreshCartData === 'function') {
                        window.refreshCartData(true); 
                    }
                } else {
                    Swal.fire({
                        html: `
                            <div class="bypass-popup-container">
                                <button class="bypass-close-btn" onclick="Swal.close()">
                                    <i class="fas fa-times"></i>
                                </button>

                                <div class="bypass-icon-container">
                                    <div class="bypass-icon-wrapper">
                                        <i class="fas fa-exclamation-triangle bypass-error-icon"></i>
                                    </div>
                                </div>

                                <div class="bypass-header">
                                    <h2>⚠️ เกิดข้อผิดพลาด</h2>
                                </div>

                                <div class="bypass-message">
                                    <p>${response.data.message || 'ไม่สามารถดำเนินการได้'}</p>
                                </div>

                                <div class="bypass-actions">
                                    <button class="bypass-btn bypass-btn-secondary" onclick="Swal.close()">
                                        <i class="fas fa-check"></i>
                                        ตกลง
                                    </button>
                                </div>
                            </div>
                        `,
                        showConfirmButton: false,
                        showCancelButton: false,
                        customClass: {
                            popup: 'modern-bypass-popup'
                        },
                        buttonsStyling: false,
                        allowOutsideClick: true,
                        width: '450px'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    title: 'เกิดข้อผิดพลาด',
                    html: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                    icon: 'error',
                    confirmButtonText: '<i class="fas fa-check"></i> ตกลง',
                    customClass: {
                        popup: 'modern-checkout-popup',
                        confirmButton: 'checkout-btn checkout-btn-primary'
                    },
                    buttonsStyling: false
                });
            }
        });
    }

    function checkReservationSettings() {
        if (window.TimerSystem && !window.TimerSystem.isInitialized) {
            window.TimerSystem.init();
        }

        let hasExistingTimer = !!(window.TimerSystem && window.TimerSystem.userTimer);

        if (!hasExistingTimer && window.TimerSystem && typeof window.TimerSystem.startUserTimer === 'function') {
            if (!window.adDashboardData || !window.adDashboardData.cart_items || window.adDashboardData.cart_items.length === 0) {


                if (typeof window.refreshCartData === 'function') {
                    window.refreshCartData();

                    setTimeout(() => {
                        if (!window.adDashboardData || !window.adDashboardData.cart_items || window.adDashboardData.cart_items.length === 0) {

                            if (typeof window.showMiniPopup === 'function') {
                                window.showMiniPopup('ตะกร้าสินค้าว่าง ไม่สามารถสร้าง timer ได้', 'error');
                            }
                        } else {

                            const timeoutMinutes = (window.adDashboardData && window.adDashboardData.reservation_minutes)
                                ? window.adDashboardData.reservation_minutes
                                : 3;

                            try {
                                window.TimerSystem.startUserTimer(timeoutMinutes * 60);
                            } catch (error) {
                                console.error('Error starting timer system after cart refresh:', error);
                            }
                        }
                    }, 500);
                    return;
                } else {

                    if (typeof window.showMiniPopup === 'function') {
                        window.showMiniPopup('ตะกร้าสินค้าว่าง ไม่สามารถสร้าง timer ได้', 'error');
                    }
                    return;
                }
            }

            const timeoutMinutes = (window.adDashboardData && window.adDashboardData.reservation_minutes)
                ? window.adDashboardData.reservation_minutes
                : 3;

            try {
                window.TimerSystem.startUserTimer(timeoutMinutes * 60);
            } catch (error) {
                console.error('Error starting timer system:', error);
                if (window.AMPErrorHandler) {
                    window.AMPErrorHandler.logError({
                        type: 'Timer System Error',
                        message: error.message,
                        stack: error.stack,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        } else if (hasExistingTimer) {
        }

        loadCheckoutData(null, null);
    }

    function loadCheckoutData(reservationSettings, reservationData) {
        let totalAmount = 0;
        if (window.currentCheckoutData && window.currentCheckoutData.total_amount) {
            totalAmount = parseFloat(window.currentCheckoutData.total_amount);
        }

        $('.swal2-html-container .loading-progress p').text('กำลังสร้างลิงก์ชำระเงิน...');

        $.ajax({
            url: adDashboardData.ajaxurl,
            type: 'POST',
            data: {
                action: 'create_payment_link',
                security: adDashboardData.nonce,
                method: 'api',
                total_amount: totalAmount,
                reservation_data: reservationData
            }
        }).then(function(paymentResponse) {
            if (!paymentResponse.success || !paymentResponse.data || !paymentResponse.data.payment_url) {
                throw new Error(paymentResponse.data?.message || 'ไม่สามารถสร้างลิงค์การชำระเงินได้');
            }

            window.currentPaymentData = paymentResponse.data;
            window.currentReservationData = reservationData;
            
            $('.swal2-html-container .loading-progress p').text('กำลังประมวลผลการจอง...');

            return $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'process_checkout',
                    security: adDashboardData.nonce,
                    total_amount: paymentResponse.data.total_amount || 0
                }
            });

        }).then(function(checkoutResponse) {
            if (!checkoutResponse.success) {
                throw new Error(checkoutResponse.data?.message || 'ไม่สามารถโหลดข้อมูลการชำระเงินได้');
            }
            if (checkoutResponse.data.reservation_conflict) {
                Swal.fire({
                    title: 'ตำแหน่งถูกจองแล้ว!',
                    html: '<div style="text-align: center; padding: 20px;"><i class="fas fa-exclamation-triangle" style="color: #ff6b6b; font-size: 48px; margin-bottom: 15px;"></i><p>ขออภัย ตำแหน่งโฆษณานี้ถูกผู้ใช้คนอื่นจองไปแล้ว</p><p style="color: #6c757d; font-size: 14px;">กรุณาเลือกตำแหน่งอื่นหรือลองใหม่ภายหลัง</p></div>',
                    icon: 'error',
                    confirmButtonText: '<i class="fas fa-check"></i> ตกลง',
                    customClass: {
                        popup: 'modern-checkout-popup',
                        confirmButton: 'checkout-btn checkout-btn-primary'
                    },
                    buttonsStyling: false
                }).then(() => {
                    if (typeof window.loadTab === 'function') {
                        window.loadTab('cart');
                    } else {
                        window.location.href = '?tab=cart';
                    }
                });
                return;
            }

            window.currentReservationData = checkoutResponse.data.reservation_data || {
                cart_items: checkoutResponse.data.cart_items || [],
                total_amount: checkoutResponse.data.total_amount || '0 USDT'
            };
            window.currentReservationSettings = reservationSettings;
            window.currentCheckoutData = checkoutResponse.data;
            window.currentCheckoutData.total_amount = window.currentPaymentData.total_amount || checkoutResponse.data.total_amount;

            showCheckoutInterface(checkoutResponse.data, reservationSettings, window.currentReservationData, window.currentPaymentData);
        }).catch(function(error) {
            Swal.fire({
                title: 'เกิดข้อผิดพลาด',
                html: error.message || 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                icon: 'error',
                confirmButtonText: '<i class="fas fa-check"></i> ตกลง',
                customClass: {
                    popup: 'modern-checkout-popup',
                    confirmButton: 'checkout-btn checkout-btn-primary'
                },
                buttonsStyling: false
            });
        });
    }

    function showCheckoutInterface(data, reservationSettings, reservationData, paymentData) {
        window.lastCheckoutData = {
            data: data,
            reservationSettings: reservationSettings,
            reservationData: reservationData,
            paymentData: paymentData
        };

        const hasTimer = !!(window.TimerSystem && window.TimerSystem.userTimer);
        const timeoutMinutes = (reservationSettings && reservationSettings.timeout_minutes)
            ? reservationSettings.timeout_minutes
            : ((window.adDashboardData && window.adDashboardData.reservation_minutes)
                ? window.adDashboardData.reservation_minutes
                : 3);

        const warningBanner = hasTimer ? `
            <div class="warning-banner" style="text-align:center;justify-content:center;align-items:center;display:flex;flex-direction:column;gap:8px;">
                <div style="display:flex;align-items:center;justify-content:center;gap:8px;">
                    <i class="fas fa-lock"></i>
                    <span>ระบบการจองตำแหน่งโฆษณาจะล็อคนี้ ${timeoutMinutes} นาที</span>
                </div>
                <div class="warning-text" style="margin-top:4px;font-size:15px;color:#fff;text-align:center;max-width:600px;">
                    กรุณาชำระเงินให้เสร็จสิ้นภายในเวลาที่กำหนด มิฉะนั้นระบบจะยกเลิกการจองโดยอัตโนมัติ และตำแหน่งโฆษณาจะกลับไปว่างให้ผู้อื่นซื้อได้ทันที
                </div>
            </div>
        ` : '';

        // ตรวจสอบข้อมูล cart_items จากหลายแหล่ง
        let cartItems = null;
        if (data && data.cart_items && Array.isArray(data.cart_items) && data.cart_items.length > 0) {
            cartItems = data.cart_items;
        } else if (reservationData && reservationData.cart_items && Array.isArray(reservationData.cart_items) && reservationData.cart_items.length > 0) {
            cartItems = reservationData.cart_items;
        } else if (window.currentReservationData && window.currentReservationData.cart_items && Array.isArray(window.currentReservationData.cart_items) && window.currentReservationData.cart_items.length > 0) {
            cartItems = window.currentReservationData.cart_items;
        }

        const checkoutHtml = `
            <div class="new-checkout-container">
                <div class="checkout-header-modern">
                    <div class="header-content">
                        <div class="header-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <h2>ชำระเงิน</h2>
                        <button class="close-btn" onclick="Swal.close()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    ${warningBanner}
                </div>

                <div class="checkout-body-horizontal">
                    <div class="left-panel">
                        <div class="summary-card">
                            <h3>สรุปรายการ</h3>
                            <div class="cart-items-horizontal">
                                ${cartItems && cartItems.length > 0 ? cartItems.map(item => `
                                    <div class="position-item-horizontal">
                                        <div class="position-info">
                                            <div class="position-name">${item.position_name || 'ไม่ระบุตำแหน่ง'}</div>
                                            <div class="position-duration">${item.duration_text || 'ไม่ระบุระยะเวลา'}</div>
                                        </div>
                                        <div class="position-price">${item.price || '0 USDT'}</div>
                                    </div>
                                `).join('') : `
                                    <div class="position-item-horizontal">
                                        <div class="position-info">
                                            <div class="position-name">ไม่มีรายการ</div>
                                            <div class="position-duration">กรุณาเพิ่มสินค้าลงตะกร้า</div>
                                        </div>
                                        <div class="position-price">0 USDT</div>
                                    </div>
                                `}
                            </div>
                            <div class="total-section">
                                <span class="total-label">ยอดรวมทั้งสิ้น:</span>
                                <span class="total-amount">${data.total_amount || data.total_price || data.total || '0'} ${(data.total_amount || data.total_price || data.total || '0').toString().includes('USDT') ? '' : 'USDT'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="right-panel">
                        <div class="payment-card">
                            <div class="payment-header">
                                <i class="fas fa-credit-card"></i>
                                <span>ใบแจ้งหนี้ Plisio</span>
                            </div>

                            <div class="payment-info">
                                <div class="info-row">
                                    <span class="label">รหัสใบแจ้งหนี้:</span>
                                    <span class="value" id="invoice-id">${paymentData && (paymentData.txn_id || paymentData.invoice_id) ? (paymentData.txn_id || paymentData.invoice_id) : 'ไม่พบรหัส'}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">จำนวนเงิน:</span>
                                    <span class="value" id="invoice-amount">${paymentData && (paymentData.total_amount || paymentData.amount) ? (paymentData.total_amount || paymentData.amount) : (data.total_amount || data.total_price || data.total || '0')} ${((paymentData && (paymentData.total_amount || paymentData.amount)) || (data.total_amount || data.total_price || data.total || '0')).toString().includes('USDT') ? '' : 'USDT'}</span>
                                </div>
                                <div class="info-row">
                                    <span class="label">สถานะ:</span>
                                    <span class="status pending">รอการชำระเงิน</span>
                                </div>
                            </div>

                            ${hasTimer ? `
                                <div class="timer-section-centered">
                                    <div class="timer-header-with-icon">
                                        <i class="fas fa-hourglass-half timer-icon"></i>
                                        <span class="timer-label">เวลาที่เหลือในการชำระเงิน</span>
                                    </div>
                                    <div class="timer-display-centered" id="reservation-timer">
                                        <div class="time-unit">
                                            <span class="timer-minutes">00</span>
                                            <span class="unit-label">นาที</span>
                                        </div>
                                        <div class="separator">:</div>
                                        <div class="time-unit">
                                            <span class="timer-seconds">00</span>
                                            <span class="unit-label">วินาที</span>
                                        </div>
                                    </div>
                                </div>
                            ` : ''}

                            <div class="payment-buttons-horizontal">
                                <a href="${paymentData && paymentData.payment_url ? paymentData.payment_url : '#'}" class="btn-primary-wide payment-link" target="_blank" onclick="handlePaymentLinkClick(event)">
                                    <i class="fas fa-credit-card"></i>
                                    ชำระด้วย Plisio
                                </a>
                                <button type="button" class="btn-cancel-wide" onclick="showCancelConfirmation()">
                                    <i class="fas fa-times"></i>
                                    ยกเลิก
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        Swal.fire({
            html: checkoutHtml,
            showConfirmButton: false,
            showCancelButton: false,
            customClass: {
                popup: 'new-checkout-popup'
            },
            buttonsStyling: false,
            width: '1200px',
            allowOutsideClick: false,
            didOpen: () => {
                console.log('Main checkout popup opened - hiding timer widget');
                window.checkoutPopupOpen = true;

                if (window.TimerSystem) {
                    window.TimerSystem.hideTimerWidget();
                    window.TimerSystem.removeTimerElement();

                    if (window.TimerSystem.userTimer) {
                        setTimeout(() => {
                            window.TimerSystem.updateTimerDisplay();

                            if (window.checkoutTimerInterval) {
                                clearInterval(window.checkoutTimerInterval);
                            }
                            window.checkoutTimerInterval = setInterval(() => {
                                if ($('#reservation-timer').length > 0 && window.TimerSystem.userTimer) {
                                    window.TimerSystem.updateTimerDisplay();
                                } else {
                                    clearInterval(window.checkoutTimerInterval);
                                }
                            }, 1000);
                        }, 100);
                    }
                }

                $('.timer-widget').remove();
            },
            willClose: () => {
                window.checkoutPopupOpen = false;

                if (window.checkoutTimerInterval) {
                    clearInterval(window.checkoutTimerInterval);
                    window.checkoutTimerInterval = null;
                }

                if (window.TimerSystem && window.TimerSystem.userTimer) {
                    window.TimerSystem.lastClosedWasMainCheckout = true;

                    setTimeout(() => {
                        window.TimerSystem.showTimerWidget();
                        window.TimerSystem.updateTimerDisplay();
                    }, 200);
                }
            }
        });
    }

    function showFallbackMessage(message, type) {
        if (typeof window.showMiniPopup === 'function') {
            window.showMiniPopup(message, type);
        } else {
            alert(message);
        }
    }

    function clearCartAfterPurchase() {
        if (typeof window.updateCartCount === 'function') {
            window.updateCartCount(0);
        }

        if (typeof adDashboardData !== 'undefined') {
            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'clear_cart',
                    security: adDashboardData.nonce
                },
                success: function(response) {
                    if (response.success && typeof window.refreshCartCount === 'function') {
                        window.refreshCartCount();
                    }
                },
                error: function() {
                    if (typeof window.refreshCartCount === 'function') {
                        window.refreshCartCount();
                    }
                }
            });
        }
    }

    function showCancelConfirmation() {
        if (typeof Swal === 'undefined') {
            if (confirm('คุณต้องการยกเลิกการจองและล้างตะกร้าสินค้าใช่ไหม?')) {
                cancelReservationAndClearCart();
            }
            return;
        }
        Swal.fire({
            title: '<i class="fas fa-exclamation-triangle" style="color: #ff6b6b; margin-right: 10px;"></i>ยกเลิกการจอง?',
            html: `
                <div style="text-align: center; padding: 20px 10px;">
                    <p style="font-size: 16px; color: #333; margin-bottom: 15px;">
                        คุณต้องการยกเลิกการจองและล้างตะกร้าสินค้าใช่ไหม?
                    </p>
                    <p style="font-size: 14px; color: #666; margin: 0;">
                        <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                        การดำเนินการนี้ไม่สามารถย้อนกลับได้
                    </p>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '<i class="fas fa-check-circle" style="margin-right: 8px;"></i>ใช่, ยกเลิก',
            cancelButtonText: '<i class="fas fa-arrow-left" style="margin-right: 8px;"></i>กลับไป',
            customClass: {
                popup: 'modern-cancel-confirmation-popup',
                confirmButton: 'cancel-confirm-btn cancel-confirm-btn-danger',
                cancelButton: 'cancel-confirm-btn cancel-confirm-btn-secondary'
            },
            buttonsStyling: false,
            reverseButtons: true,
            focusCancel: true
        }).then((result) => {
            if (result.isConfirmed) {
                cancelReservationAndClearCart();
            } else if (result.isDismissed || result.dismiss === Swal.DismissReason.cancel) {

                setTimeout(() => {
                    if (window.lastCheckoutData) {
                        showCheckoutInterface(
                            window.lastCheckoutData.data,
                            window.lastCheckoutData.reservationSettings,
                            window.lastCheckoutData.reservationData,
                            window.lastCheckoutData.paymentData
                        );
                    } else {
                        openCheckoutPopup();
                    }
                }, 100);
            }
        });
    }

    function cancelReservationAndClearCart() {


        if (window.TimerSystem && typeof window.TimerSystem.cancelUserTimer === 'function') {

            window.TimerSystem.cancelUserTimer();
        }

        clearCartAndRedirect();
    }

    function clearCartAndRedirect() {
            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'clear_cart',
                    security: adDashboardData.nonce
                },
            success: function(response) {
                if (typeof window.updateCartCount === 'function') {
                    window.updateCartCount(0);
                }

                if (typeof window.refreshBuyTabAfterPurchase === 'function') {
                    window.refreshBuyTabAfterPurchase();
                }

                Swal.close();
                if (typeof window.loadTabContent === 'function') {
                    window.loadTabContent('buy');
                } else {
                    window.location.reload();
                }
                },
                error: function() {
                Swal.close();
                if (typeof window.loadTabContent === 'function') {
                    window.loadTabContent('buy');
        } else {
                    window.location.reload();
        }
            }
        });
    }

    function handlePaymentLinkClick(event) {
        extendUserSession();

    }

    function showPaymentSuccessPopup(paymentData) {
        extendUserSession();

        Swal.fire({
            title: '<i class="fas fa-check-circle" style="color: #28a745; font-size: 3rem; margin-bottom: 20px;"></i>',
            html: `
                <div class="payment-success-container">
                    <h2 style="color: #28a745; margin-bottom: 20px; font-size: 1.8rem;">
                        🎉 ชำระเงินสำเร็จ!
                    </h2>
                    <p style="font-size: 1.1rem; color: #333; margin-bottom: 25px;">
                        การชำระเงินของคุณได้รับการยืนยันแล้ว
                    </p>

                    <div class="ownership-processing-section" style="margin: 30px 0;">
                        <div class="processing-header" style="margin-bottom: 15px;">
                            <i class="fas fa-cog fa-spin" style="color: #007bff; margin-right: 10px;"></i>
                            <span style="font-size: 1rem; color: #666;">กำลังจัดการความเป็นเจ้าของ...</span>
                        </div>

                        <div class="loading-bar-container" style="width: 100%; height: 8px; background: #e9ecef; border-radius: 4px; overflow: hidden; margin-bottom: 15px;">
                            <div class="loading-bar-fill" style="height: 100%; background: linear-gradient(90deg, #007bff, #28a745); width: 0%; transition: width 0.3s ease; border-radius: 4px;"></div>
                        </div>

                        <div class="processing-steps" style="text-align: left; font-size: 0.9rem; color: #666;">
                            <div class="step" data-step="1">
                                <i class="fas fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
                                ตรวจสอบการชำระเงิน
                            </div>
                            <div class="step" data-step="2">
                                <i class="fas fa-spinner fa-spin" style="color: #007bff; margin-right: 8px;"></i>
                                อัพเดตความเป็นเจ้าของ
                            </div>
                            <div class="step" data-step="3">
                                <i class="fas fa-clock" style="color: #6c757d; margin-right: 8px;"></i>
                                ส่งอีเมลยืนยัน
                            </div>
                        </div>
                    </div>
                </div>
            `,
            showConfirmButton: false,
            allowOutsideClick: false,
            customClass: {
                popup: 'payment-success-popup'
            },
            didOpen: () => {
                animateOwnershipProcessing();
            }
        });
    }

    function animateOwnershipProcessing() {
        const loadingBar = document.querySelector('.loading-bar-fill');
        const steps = document.querySelectorAll('.step');

        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 2;
            if (loadingBar) {
                loadingBar.style.width = progress + '%';
            }

            if (progress >= 33 && progress < 35) {
                updateStepStatus(steps[1], 'completed');
                updateStepStatus(steps[2], 'processing');
            }

            if (progress >= 66 && progress < 68) {
                updateStepStatus(steps[2], 'completed');
            }

            if (progress >= 100) {
                clearInterval(progressInterval);
                setTimeout(() => {
                    completeOwnershipProcessing();
                }, 1000);
            }
        }, 100);
    }

    function updateStepStatus(stepElement, status) {
        if (!stepElement) return;

        const icon = stepElement.querySelector('i');
        if (status === 'completed') {
            icon.className = 'fas fa-check-circle';
            icon.style.color = '#28a745';
        } else if (status === 'processing') {
            icon.className = 'fas fa-spinner fa-spin';
            icon.style.color = '#007bff';
        }
    }

    function completeOwnershipProcessing() {
        Swal.update({
            html: `
                <div class="payment-success-container">
                    <h2 style="color: #28a745; margin-bottom: 20px; font-size: 1.8rem;">
                        ✅ ดำเนินการเสร็จสิ้น!
                    </h2>
                    <p style="font-size: 1.1rem; color: #333; margin-bottom: 25px;">
                        ความเป็นเจ้าของได้รับการอัพเดตเรียบร้อยแล้ว
                    </p>
                    <p style="font-size: 1rem; color: #666; margin-bottom: 30px;">
                        กำลังนำคุณไปยังหน้าโฆษณาของฉัน...
                    </p>
                    <div style="margin: 20px 0;">
                        <i class="fas fa-arrow-right" style="color: #007bff; font-size: 1.5rem; animation: bounce 1s infinite;"></i>
                    </div>
                </div>
            `,
            showConfirmButton: true,
            confirmButtonText: '<i class="fas fa-eye"></i> ดูโฆษณาของฉัน',
            customClass: {
                confirmButton: 'btn btn-success btn-lg'
            }
        });

        setTimeout(() => {
            navigateToMyAds();
        }, 3000);
    }

    function showPaymentFailurePopup(paymentData) {
        Swal.fire({
            title: '<i class="fas fa-times-circle" style="color: #dc3545; font-size: 3rem; margin-bottom: 20px;"></i>',
            html: `
                <div class="payment-failure-container">
                    <h2 style="color: #dc3545; margin-bottom: 20px; font-size: 1.8rem;">
                        ❌ การชำระเงินล้มเหลว
                    </h2>
                    <p style="font-size: 1.1rem; color: #333; margin-bottom: 25px;">
                        เกิดข้อผิดพลาดในการชำระเงิน
                    </p>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                        <p style="font-size: 0.95rem; color: #666; margin: 0;">
                            <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                            กรุณาตรวจสอบข้อมูลการชำระเงินและลองใหม่อีกครั้ง
                        </p>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '<i class="fas fa-redo"></i> ลองใหม่',
            cancelButtonText: '<i class="fas fa-shopping-cart"></i> กลับไปซื้อสินค้า',
            customClass: {
                popup: 'payment-failure-popup',
                confirmButton: 'btn btn-warning',
                cancelButton: 'btn btn-primary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                openCheckoutPopup();
            } else {
                navigateToBuyTab();
            }
        });
    }

    function showPaymentTimeoutPopup() {
        Swal.fire({
            title: '<i class="fas fa-clock" style="color: #ffc107; font-size: 3rem; margin-bottom: 20px;"></i>',
            html: `
                <div class="payment-timeout-container">
                    <h2 style="color: #ffc107; margin-bottom: 20px; font-size: 1.8rem;">
                        ⏰ หมดเวลาตรวจสอบ
                    </h2>
                    <p style="font-size: 1.1rem; color: #333; margin-bottom: 25px;">
                        ระบบไม่สามารถตรวจสอบสถานะการชำระเงินได้ภายในเวลาที่กำหนด
                    </p>
                    <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                        <p style="font-size: 0.95rem; color: #856404; margin: 0;">
                            <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i>
                            หากคุณได้ชำระเงินแล้ว กรุณาติดต่อผู้ดูแลระบบเพื่อตรวจสอบ
                        </p>
                    </div>
                </div>
            `,
            confirmButtonText: '<i class="fas fa-shopping-cart"></i> กลับไปซื้อสินค้า',
            customClass: {
                popup: 'payment-timeout-popup',
                confirmButton: 'btn btn-primary'
            },
            buttonsStyling: false
        }).then(() => {
            navigateToBuyTab();
        });
    }

    function navigateToMyAds() {
        clearCartAfterPurchase();
        Swal.close();

        if (typeof window.loadTabContent === 'function') {
            window.loadTabContent('my-ads');
        } else if (typeof window.goToMyAds === 'function') {
            window.goToMyAds();
        } else {
            window.location.href = window.location.pathname + '?tab=my-ads';
        }
    }

    function navigateToBuyTab() {
        Swal.close();

        if (typeof window.loadTabContent === 'function') {
            window.loadTabContent('buy');
        } else {
            window.location.href = window.location.pathname + '?tab=buy';
        }
    }

    function extendUserSession() {
        if (typeof adDashboardData !== 'undefined' && adDashboardData.ajaxurl) {
            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'amp_extend_session',
                    security: adDashboardData.nonce
                },
                success: function(response) {
                    if (response.success) {
                        if (typeof window.showMiniPopup === 'function') {
                            const extensionMinutes = response.data?.extension_minutes ||
                                (window.adDashboardData && window.adDashboardData.reservation_minutes) || 3;

                            window.showMiniPopup(
                                `เซสชันถูกขยายเวลา ${extensionMinutes} นาที`,
                                'success'
                            );
                        }
                    }
                },
                error: function() {
                    if (typeof window.showMiniPopup === 'function') {
                        window.showMiniPopup('ไม่สามารถขยายเวลาเซสชันได้', 'error');
                    }
                }
            });
        } else {
            console.warn('Cannot extend session: adDashboardData not available');
        }
    }

    window.openCheckoutPopup = openCheckoutPopup;
    window.clearCartAfterPurchase = clearCartAfterPurchase;
    window.showCancelConfirmation = showCancelConfirmation;
    window.showPaymentSuccessPopup = showPaymentSuccessPopup;
    window.showPaymentFailurePopup = showPaymentFailurePopup;
    window.handlePaymentLinkClick = handlePaymentLinkClick;
})(jQuery);
