<?php
namespace AdManagementPro\Modules\Shared;

use AdManagementPro\Core\Database;

require_once AMP_PLUGIN_DIR . 'includes/core/roles.php';

if (!defined('WPINC')) {
    die;
}

class PositionManager {
    private static $instances = [];
    private $db;
    private $cache;
    private $security;
    private $context;
    private $database;

    protected function __construct($context = 'public') {
        $this->context = $context;
        $this->database = \AdManagementPro\Core\Database::instance();
        $this->db = $this->database;
        $this->cache = \AMP_Cache_Manager::instance();
        $this->security = null;

        if (!function_exists('clear_position_clicks')) {
            require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';
        }
    }
    
    public static function instance($context = 'public') {
        if (!isset(self::$instances[$context])) {
            self::$instances[$context] = new self($context);
        }
        return self::$instances[$context];
    }
    
    public function get_positions($args = []) {
        $cache_key = 'positions_' . md5(json_encode($args));
        $cached_positions = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_positions) {
            return $cached_positions;
        }

        $defaults = [
            'limit' => 50,
            'offset' => 0,
            'status' => 'active',
            'include_ownership' => false,
            'user_id' => null,
            'include_reserved' => false
        ];
        
        $args = array_merge($defaults, $args);
        
        global $wpdb;
        $ad_positions_table = $this->database->get_table('ad_positions');
        
        $base_sql = "SELECT * FROM `{$ad_positions_table}`";
        $sql = $base_sql;
        $where_conditions = [];
        $values = [];
        
        if ($args['status'] !== 'any') {
            $where_conditions[] = "status = %s";
            $values[] = $args['status'];
        }
        

        
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        $sql .= " ORDER BY ad_position ASC";
        
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['limit']);
            if ($args['offset'] > 0) {
                $sql .= " OFFSET " . intval($args['offset']);
            }
        }
        
        if (!empty($values)) {
            $prepared_sql = $wpdb->prepare($sql, $values);
        } else {
            $prepared_sql = $sql;
        }
        
        $results = $wpdb->get_results($prepared_sql);
        
        $positions = [];
        foreach ($results as $row) {
            $position = new \stdClass();
            $position->id = $row->id;
            $position->name = $row->ad_position;
            $position->type = $row->type ?? 'banner';
            $position->width = $row->width ?? 300;
            $position->height = $row->height ?? 250;
            $position->description = $row->description ?? '';
            $position->status = $row->status ?? 'active';
            $position->created_at = $row->created_at;
            
            if ($args['include_ownership']) {
                $position->ownership_status = $this->get_ownership_status($row);
                $position->ownership_expires = $row->expiration_date;
                $position->start_date = $row->start_date;
                $position->image_url = $row->image_url ?? '';
                $position->target_url = $row->target_url ?? '';
                $position->website_name = $row->website_name ?? '';
                $position->alt_text = $row->alt_text ?? '';
            }
            
            if ($args['include_reserved']) {
                $position->reserved_by = $row->reserved_by;
                $position->reserved_until = $row->reserved_until;
            }
            
            $positions[] = $position;
        }
        
        $this->cache->set($cache_key, $positions, 0, 'position_data');
        return $positions;
    }
    
    public function count_positions($args = []) {
        $defaults = [
            'status' => 'active',
            'search' => ''
        ];
        $args = \wp_parse_args($args, $defaults);

        $cache_key = 'positions_count_' . md5(json_encode($args));
        $cached_count = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_count) {
            return (int) $cached_count;
        }

        global $wpdb;
        $ad_positions_table = $this->database->get_table('ad_positions');
        
        $base_sql = "SELECT COUNT(DISTINCT ad_position) FROM `{$ad_positions_table}`";
        $sql = $base_sql;
        $where_conditions = [];
        $values = [];
        
        if ($args['status'] !== 'any') {
            $where_conditions[] = "status = %s";
            $values[] = $args['status'];
        }
        
        if (!empty($args['search'])) {
            $where_conditions[] = "ad_position LIKE %s";
            $values[] = '%' . $wpdb->esc_like($args['search']) . '%';
        }
        
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }
        
        if (!empty($values)) {
            $prepared_sql = $wpdb->prepare($sql, $values);
        } else {
            $prepared_sql = $sql;
        }
        
        $count = intval($wpdb->get_var($prepared_sql));
        $this->cache->set($cache_key, $count, 0, 'position_data');
        return $count;
    }
        
    private function filter_by_context($positions) {
        if (!is_array($positions) || empty($positions)) {
            return [];
        }
        
        $allowed_fields = $this->get_allowed_fields();
        
        return \array_map(function($position) use ($allowed_fields) {
            $filtered = new \stdClass();
            foreach ($allowed_fields as $field) {
                if (isset($position->$field)) {
                    $filtered->$field = $position->$field;
                }
            }
            return $filtered;
        }, $positions);
    }
    
    private function get_allowed_fields() {
        $base_fields = ['id', 'name', 'type', 'width', 'height', 'status'];

        if ($this->context === 'admin' && \current_user_can('manage_options')) {
            return \array_merge($base_fields, ['created_at', 'ads_count', 'description', 'owner_id', 'ownership_status', 'ownership_expires', 'image_url', 'target_url', 'alt_text', 'website_name']);
        }

        if ($this->context === 'public') {
            return \array_merge($base_fields, ['owner_id', 'ownership_status', 'image_url', 'target_url', 'alt_text', 'website_name']);
        }

        return ['id', 'name', 'type'];
    }
    
    public function get_position($name_or_id) {
        if (empty($name_or_id)) {
            return false;
        }
        $cache_key = 'pos_' . (is_numeric($name_or_id) ? 'id_' . $name_or_id : 'name_' . md5($name_or_id));
        $cached_position = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_position) {
            return $cached_position;
        }

        $position_name = is_numeric($name_or_id) ? null : $name_or_id;
        $position_id = is_numeric($name_or_id) ? intval($name_or_id) : null;

        if (!$position_name && !$position_id) {
            return false;
        }

        global $wpdb;
        $ad_positions_table = $this->database->get_table('ad_positions');

        if ($position_name) {
            $query = $wpdb->prepare("SELECT * FROM `%1s` WHERE ad_position = %s", $ad_positions_table, $position_name);
            $result = $wpdb->get_row($query);
        } else {
            $query = $wpdb->prepare("SELECT * FROM `%1s` WHERE id = %d", $ad_positions_table, $position_id);
            $result = $wpdb->get_row($query);
        }

        if (!$result) {
            $this->cache->delete($cache_key, 'position_data');
            return false;
        }

            $position = new \stdClass();
            $position->id = $result->id;
            $position->name = $result->ad_position;
            $position->type = $result->type ?? 'banner';
            $position->width = $result->width ?? 300;
            $position->height = $result->height ?? 250;
            $position->description = $result->description ?? '';
            $position->status = $result->status ?? 'active';
            $position->created_at = $result->created_at;

            $ownership_state = $this->get_position_ownership_state($result->ad_position);
            $position->owner_id = $ownership_state['owner_id'];
            $position->ownership_status = $this->get_ownership_status($result, $ownership_state);
            $position->ownership_expires = $ownership_state['expiration_date'];
            
            $position->start_date = $result->start_date;
            $position->image_url = $result->image_url ?? '';
            $position->target_url = $result->target_url ?? '';
            $position->website_name = $result->website_name ?? '';
            $position->alt_text = $result->alt_text ?? '';
            $position->reserved_by = $result->reserved_by;
            $position->reserved_until = $result->reserved_until;

        $this->cache->set($cache_key, $position, 0, 'position_data');
        return $position;
    }
    
    public function get_position_ownership_state($position_name, $user_id = null) {
        $cache_key = 'ownership_state_' . md5($position_name) . '_' . ($user_id ?? 'guest');
        $cached_state = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_state) {
            return $cached_state;
        }

        $state = [
            'is_owned' => false,
            'is_owned_by_current_user' => false,
            'is_expired' => true,
            'is_reserved' => false,
            'is_reserved_by_current_user' => false,
            'owner_id' => null,
            'expiration_date' => null
        ];

        $ad_positions_table = $this->database->get_table('ad_positions');
        $position_data = $this->database->get_row(
            "SELECT expiration_date, reserved_by, reserved_until FROM `{$ad_positions_table}` WHERE ad_position = %s",
            [$position_name]
        );

        if (!$position_data) {
            $this->cache->set($cache_key, $state, 300, 'position_data');
            return $state;
        }

        $owner_id = $this->get_position_owner_from_usermeta($position_name);
        $state['owner_id'] = $owner_id;
        $state['expiration_date'] = $position_data->expiration_date;

        if ($owner_id && !empty($state['expiration_date'])) {
            $state['is_owned'] = true;
            $state['is_owned_by_current_user'] = ($user_id && $owner_id === (int) $user_id);
            $state['is_expired'] = strtotime($state['expiration_date']) < time();
        } else {
            $state['is_owned'] = false;
            $state['is_expired'] = true;
        }

        $reservation_info = $this->get_position_reservation_from_timers($position_name);
        if ($reservation_info) {
            $state['is_reserved'] = true;
            $state['is_reserved_by_current_user'] = ($user_id && (int) $reservation_info['user_id'] === (int) $user_id);
        }

        $cache_duration = $state['is_reserved'] ? 30 : 300;
        $this->cache->set($cache_key, $state, $cache_duration, 'position_data');
        return $state;
    }

    private function get_position_reservation_from_timers($position_name) {
        global $wpdb;
        
        $users_with_timers = $wpdb->get_results(
            "SELECT user_id, meta_value FROM {$wpdb->usermeta} WHERE meta_key = 'amp_countdown_timer'"
        );

        $current_time = time();
        foreach ($users_with_timers as $user_meta) {
            $timer_data = maybe_unserialize($user_meta->meta_value);

            if (!is_array($timer_data) || !isset($timer_data['status']) || $timer_data['status'] !== 'active') {
                continue;
            }

            $elapsed_seconds = $current_time - $timer_data['started_at'];
            if ($elapsed_seconds >= $timer_data['duration_seconds']) {
                continue;
            }

            if (isset($timer_data['cart_data']) && is_array($timer_data['cart_data'])) {
                foreach ($timer_data['cart_data'] as $cart_item) {
                    $reserved_position_name = null;
                    if (isset($cart_item['position'])) {
                        $reserved_position_name = $cart_item['position'];
                    } elseif (isset($cart_item['position_name'])) {
                        $reserved_position_name = $cart_item['position_name'];
                    } elseif (isset($cart_item['ad_position'])) {
                        $reserved_position_name = $cart_item['ad_position'];
                    }
                    
                    if ($reserved_position_name === $position_name) {
                        return [
                            'user_id' => $user_meta->user_id,
                            'remaining_seconds' => $timer_data['duration_seconds'] - $elapsed_seconds
                        ];
                    }
                }
            }
        }
        
        return false;
    }

    public function get_position_visibility_state($position_name, $user_id = null) {
        $state = [
            'is_purchasable' => false,
            'should_show_in_buy_tab' => false,
            'reason' => 'default',
            'days_remaining' => null,
        ];

        $ad_positions_table = $this->database->get_table('ad_positions');
        $position_row = $this->database->get_row("SELECT status FROM `{$ad_positions_table}` WHERE ad_position = %s", [$position_name]);
        if (!$position_row) {
             $state['reason'] = 'not_exist';
             return $state;
        }
        if ($position_row->status === 'inactive') {
            $state['reason'] = 'inactive';
            return $state;
        }
        $ownership = $this->get_position_ownership_state($position_name, $user_id);
        if ($ownership['is_reserved']) {
            if ($ownership['is_reserved_by_current_user']) {
                 $state['should_show_in_buy_tab'] = false;
                 $state['reason'] = 'reserved_by_user';
                 return $state;
            }
            $state['should_show_in_buy_tab'] = true;
            $state['is_purchasable'] = false;
            $state['reason'] = 'reserved';
            return $state;
        }
        if ($ownership['is_owned']) {
            if ($ownership['is_expired']) {
                $state['should_show_in_buy_tab'] = true;
                $state['is_purchasable'] = true;
                $state['reason'] = 'owned_expired';
            } else {
                if ($ownership['expiration_date']) {
                    $expiration_timestamp = strtotime($ownership['expiration_date']);
                    $days_remaining = floor(($expiration_timestamp - time()) / (60 * 60 * 24));
                    $state['days_remaining'] = $days_remaining;
                    $expiring_soon_threshold = 7; 
                    if ($days_remaining >= 0 && $days_remaining <= $expiring_soon_threshold) {
                        $state['should_show_in_buy_tab'] = true;
                        $state['is_purchasable'] = true;
                        $state['reason'] = 'expiring_soon';
                    } else {
                        $state['should_show_in_buy_tab'] = false;
                        $state['is_purchasable'] = false;
                        $state['reason'] = 'owned_active';
                    }
                } else {
                    $state['should_show_in_buy_tab'] = false;
                    $state['is_purchasable'] = false;
                    $state['reason'] = 'owned_active';
                }
            }
        } else {
            $state['should_show_in_buy_tab'] = true;
            $state['is_purchasable'] = true;
            $state['reason'] = 'available';
        }
        return $state;
    }

    public function can_add_to_cart($position_name, $user_id) {
         if (!$user_id) return false;
         $ownership_check = $this->validate_purchase_eligibility($position_name, $user_id);
         if (is_wp_error($ownership_check)) {
             return false;
         }
         $visibility = $this->get_position_visibility_state($position_name, $user_id);
         return $visibility['is_purchasable'];
    }

    public function validate_purchase_eligibility($position_name, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!$user_id) {
            return new \WP_Error('not_logged_in', 'กรุณาเข้าสู่ระบบเพื่อดำเนินการ');
        }

        $ownership_state = $this->get_position_ownership_state($position_name, $user_id);

        if ($ownership_state['is_owned'] && !$ownership_state['is_expired']) {
            if ($ownership_state['is_owned_by_current_user']) {
                if ($ownership_state['expiration_date']) {
                    $expiration_timestamp = strtotime($ownership_state['expiration_date']);
                    $days_remaining = floor(($expiration_timestamp - time()) / (60 * 60 * 24));
                    $expiring_soon_threshold = 7;
                    
                    if ($days_remaining >= 0 && $days_remaining <= $expiring_soon_threshold) {
                        return true;
                    } else {
                        return new \WP_Error('already_owned', 'คุณเป็นเจ้าของตำแหน่งนี้อยู่แล้ว และยังไม่ใกล้หมดอายุ (เหลืออีก ' . $days_remaining . ' วัน)');
                    }
                } else {
                    return new \WP_Error('already_owned', 'คุณเป็นเจ้าของตำแหน่งนี้อยู่แล้ว');
                }
            } else {
                $owner_id = $ownership_state['owner_id'];
                $expiration_date = $ownership_state['expiration_date'];
                return new \WP_Error('position_unavailable',
                    "ตำแหน่งนี้มีเจ้าของแล้ว (User ID: {$owner_id}) และยังไม่หมดอายุ (หมดอายุ: {$expiration_date})");
            }
        }

        if (user_can($user_id, 'manage_options')) {
            return true;
        }

        return true;
    }

    public function can_admin_override_ownership($user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }
        return user_can($user_id, 'manage_options');
    }

    public function validate_purchase_eligibility_with_admin_override($position_name, $user_id = null, $force_admin_override = false) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if ($force_admin_override && $this->can_admin_override_ownership($user_id)) {
            return true;
        }

        return $this->validate_purchase_eligibility($position_name, $user_id);
    }

    public function get_purchasable_positions($user_id = null) {
        $current_user_id = $user_id ?? get_current_user_id();

        $cache_key = 'purchasable_positions_' . ($current_user_id ?? 'guest');
        $cached_positions = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_positions) {
            return $cached_positions;
        }

        $all_positions_details = $this->get_positions(['limit' => 1000]);

        $purchasable_positions = [];
        foreach ($all_positions_details as $position) {
            $visibility = $this->get_position_visibility_state($position->name, $current_user_id);
            if ($visibility['should_show_in_buy_tab']) {
                $position->visibility = $visibility;
                $position->ownership = $this->get_position_ownership_state($position->name, $current_user_id);
                $purchasable_positions[] = $position;
            }
        }

        $this->cache->set($cache_key, $purchasable_positions, 0, 'position_data');
        return $purchasable_positions;
    }

    public function get_user_positions_with_details($user_id) {
        if (!$user_id) {
            return [];
        }

        $cache_key = 'user_positions_details_' . $user_id;
        $cached_data = $this->cache->get($cache_key, 'position_data');
        if (false !== $cached_data) {
            return $cached_data;
        }

        $user_position_names = get_user_meta($user_id, 'ad_positions', true);
        if (empty($user_position_names) || !is_array($user_position_names)) {
            return [];
        }

        $all_positions_details = $this->get_positions([
            'limit' => 1000,
            'include_ownership' => true
        ]);

        $user_positions = [];
        foreach ($all_positions_details as $position) {
            if (in_array($position->name, $user_position_names)) {
                 $position->ownership = $this->get_position_ownership_state($position->name, $user_id);
                 $position->visibility = $this->get_position_visibility_state($position->name, $user_id);
                 $user_positions[] = $position;
            }
        }
        $this->cache->set($cache_key, $user_positions, 0, 'position_data');
        return $user_positions;
    }

    public function get_position_owner_from_usermeta($position_name) {
        global $wpdb;

        $user_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta}
             WHERE meta_key = 'ad_positions'
             AND meta_value LIKE %s",
            '%' . $wpdb->esc_like('"' . $position_name . '"') . '%'
        ));

        if ($user_id) {
            $user_positions = get_user_meta($user_id, 'ad_positions', true);
            if (is_array($user_positions) && in_array($position_name, $user_positions)) {
                return (int)$user_id;
            }
        }

        return false;
    }

    public function get_user_purchase_history($user_id) {
        if (!$user_id || !is_numeric($user_id)) {
            return [];
        }

        $cache_key = "purchase_history_{$user_id}";
        $cached_history = $this->cache->get($cache_key, 'user_profiles');
        if (false !== $cached_history) {
            return $cached_history;
        }

        $db = \AdManagementPro\Core\Database::instance();
        $payments_table = $db->get_table('ad_payments');

        if (!$payments_table) {
            error_log('AMP Error: ad_payments table not found in database class.');
            return [];
        }

        global $wpdb;
        $history = $wpdb->get_results($wpdb->prepare(
            "SELECT *
            FROM `{$payments_table}`
            WHERE user_id = %d
            ORDER BY payment_date DESC",
            $user_id
        ));

        if ($wpdb->last_error) {
            error_log('AMP DB Error in get_user_purchase_history: ' . $wpdb->last_error);
            return [];
        }

        $this->cache->set($cache_key, $history, 0, 'user_profiles');
        return $history;
    }
    
    public function can_modify() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }
    
    public function can_modify_ownership() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }

    public function can_modify_others_positions() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }

    public function can_modify_expiration() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }
    
    public function can_delete_positions() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }
    
    public function can_reset_positions() {
        return $this->context === 'admin' && current_user_can('manage_options');
    }
    
    public function can_modify_content() {
        if ($this->context === 'admin') {
            return current_user_can('manage_options') || current_user_can('amp_advertiser_access');
        }
        if ($this->context === 'public') {
            return current_user_can('manage_options') || current_user_can('amp_advertiser_access');
        }
        return false;
    }

    public function can_modify_content_with_bypass($position_name, $user_id = null) {
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        if (!user_can($user_id, 'manage_options') && !user_can($user_id, 'amp_advertiser_access')) {
            return false;
        }

        if ($this->context === 'admin') {
            return true;
        }

        if ($this->context === 'public') {
            $ownership_state = $this->get_position_ownership_state($position_name, $user_id);
            $is_owner = $ownership_state['is_owned_by_current_user'];
            $is_expired = $ownership_state['is_expired'];
            if (user_can($user_id, 'manage_options')) {
                $has_bypass = true;
            } else {
                $bypass_permission = get_user_meta($user_id, 'bypass_checkout', true);
                $has_bypass = (bool)$bypass_permission;
            }

            if (!$is_owner) {
                return false;
            }

            if ($has_bypass) {
                return true;
            }

            return !$is_expired;
        }

        return false;
    }
    
    public function can_create_positions() {
        return $this->context === 'admin' && \current_user_can('manage_options');
    }
    
    private function validate_permission_for_action($action) {
        switch ($action) {
            case 'modify_ownership':
                if (!$this->can_modify_ownership()) {
                    return new \WP_Error('permission_denied', 'Only administrators can modify position ownership.');
                }
                break;
            case 'modify_expiration':
                if (!$this->can_modify_expiration()) {
                    return new \WP_Error('permission_denied', 'Only administrators can modify expiration dates.');
                }
                break;
            case 'delete_position':
                if (!$this->can_delete_positions()) {
                    return new \WP_Error('permission_denied', 'Only administrators can delete positions.');
                }
                break;
            case 'reset_position':
                if (!$this->can_reset_positions()) {
                    return new \WP_Error('permission_denied', 'Only administrators can reset positions.');
                }
                break;
            case 'create_position':
                if (!$this->can_create_positions()) {
                    return new \WP_Error('permission_denied', 'Only administrators can create new positions.');
                }
                break;
            case 'modify_content':
                if (!$this->can_modify_content()) {
                    return new \WP_Error('permission_denied', 'You do not have permission to modify position content.');
                }
                break;
            default:
                if (!$this->can_modify()) {
                    return new \WP_Error('permission_denied', 'Permission denied.');
                }
        }
        return true;
    }
    
    public function assign_positions_to_user($user_id, $positions_to_assign = []) {
        $permission_check = $this->validate_permission_for_action('modify_ownership');
        if (is_wp_error($permission_check)) {
            return $permission_check;
        }

        if (!get_userdata($user_id)) {
            return new \WP_Error('invalid_user', 'User not found.');
        }
        $positions_to_assign = array_map('sanitize_text_field', $positions_to_assign);
        $current_positions = get_user_meta($user_id, 'ad_positions', true);
        if (!is_array($current_positions)) $current_positions = [];
        $positions_to_add = array_diff($positions_to_assign, $current_positions);
        $positions_to_remove = array_diff($current_positions, $positions_to_assign);
        if (!empty($positions_to_add)) {
            foreach ($positions_to_add as $position_to_add) {
                $existing_owner = $this->get_position_owner_from_usermeta($position_to_add);
                if ($existing_owner && $existing_owner !== $user_id) {
                    error_log("AMP Ownership Conflict: Position '{$position_to_add}' is already owned by user ID {$existing_owner}. Cannot assign to user ID {$user_id}.");
                    continue;
                }
            }
        }
       
        $final_positions = array_merge(array_diff($current_positions, $positions_to_remove), $positions_to_add);
        update_user_meta($user_id, 'ad_positions', $final_positions);

        $this->cache->clear_group('position_data');
        $this->cache->clear_group('user_dashboard');

        return true;
    }

    public function update_position_ownership($position_name, $user_id, $expiration_date_str) {
        if (!(defined('REST_REQUEST') && REST_REQUEST)) {
            $permission_check = $this->validate_permission_for_action('modify_ownership');
            if (is_wp_error($permission_check)) {
                return $permission_check;
            }
        }

        if (empty($position_name) || !is_numeric($user_id)) {
            return new \WP_Error('invalid_data', 'Invalid position name or user ID.');
        }

        $position_exists = $this->database->get_var("SELECT COUNT(*) FROM {$this->database->get_table('ad_positions')} WHERE ad_position = %s", [$position_name]);
        if (!$position_exists) {
            return new \WP_Error('invalid_position', 'Position does not exist.');
        }

        $new_user_id = (int)$user_id;
        $current_owner_id = $this->get_position_owner_from_usermeta($position_name);
        
        $data_to_update = [];
        $format = [];
        
        if ($new_user_id > 0 && !get_userdata($new_user_id)) {
            return new \WP_Error('invalid_user', 'The assigned user does not exist.');
        }

        $new_expiration_date = null;
        if (!empty($expiration_date_str)) {
            $new_expiration_date = date('Y-m-d H:i:s', strtotime($expiration_date_str));
        }

        $data_to_update['expiration_date'] = $new_expiration_date;
        $format[] = '%s';
        
        if ($current_owner_id > 0 && $new_user_id !== $current_owner_id) {
            $old_user_positions = get_user_meta($current_owner_id, 'ad_positions', true);
            if (is_array($old_user_positions) && ($key = array_search($position_name, $old_user_positions)) !== false) {
                unset($old_user_positions[$key]);
                update_user_meta($current_owner_id, 'ad_positions', $old_user_positions);
            }
            if (function_exists('clear_position_clicks')) {
                clear_position_clicks($position_name);
                error_log("AMP: Statistics reset for position '{$position_name}' due to ownership change from user {$current_owner_id} to user {$new_user_id}");
            }
        }

        if ($new_user_id > 0) {
            $new_user_positions = get_user_meta($new_user_id, 'ad_positions', true);
            if (!is_array($new_user_positions)) $new_user_positions = [];
            if (!in_array($position_name, $new_user_positions)) {
                $new_user_positions[] = $position_name;
                update_user_meta($new_user_id, 'ad_positions', $new_user_positions);
            }
        }

        if (!empty($data_to_update)) {
            $this->database->update(
                'ad_positions',
                $data_to_update,
                ['ad_position' => $position_name],
                $format,
                ['%s']
            );
        }

        $this->clear_position_cache($position_name);
        return true;
    }

    public function update_position_content($position_name, $content_data) {
        if ($this->context === 'public') {
            if (!$this->can_modify_content_with_bypass($position_name)) {
                return new \WP_Error('permission_denied', 'You do not have permission to modify position content.');
            }
        } else {
            $permission_check = $this->validate_permission_for_action('modify_content');
            if (is_wp_error($permission_check)) {
                return $permission_check;
            }
        }

        $position_exists = $this->database->get_var("SELECT COUNT(*) FROM {$this->database->get_table('ad_positions')} WHERE ad_position = %s", [$position_name]);
        if (!$position_exists) {
            return new \WP_Error('invalid_position', 'Position does not exist.');
        }

        $field_mapping = [
            'image' => 'image_url',
            'link' => 'target_url',
            'website_name' => 'alt_text'
        ];
        
        $update_data = [];
        $update_format = [];

        foreach ($field_mapping as $input_field => $db_field) {
            if (isset($content_data[$input_field])) {
                switch ($input_field) {
                    case 'image':
                    case 'link':
                        $update_data[$db_field] = esc_url_raw($content_data[$input_field]);
                        break;
                    case 'website_name':
                        $update_data[$db_field] = sanitize_text_field($content_data[$input_field]);
                        break;
                }
                $update_format[] = '%s';
            }
        }

        if (empty($update_data)) {
            return true;
        }

        $result = $this->database->update(
            'ad_positions',
            $update_data,
            ['ad_position' => $position_name],
            $update_format,
            ['%s']
        );

        if ($result !== false) {
            $this->cache->clear_group('position_data');
        }

        return $result !== false;
    }

    private function get_ownership_status($position_data, $ownership_state = null) {
        if ($ownership_state === null) {
            $ownership_state = $this->get_position_ownership_state($position_data->ad_position ?? '');
        }

        if (!$ownership_state['is_owned']) {
            return 'available';
        }

        if ($ownership_state['is_expired']) {
            return 'expired';
        }

        return 'active';
    }

    public function create_position($data) {
        $permission_check = $this->validate_permission_for_action('create_position');
        if (is_wp_error($permission_check)) {
            return $permission_check;
        }

        $sanitized = [
            'ad_position' => sanitize_text_field($data['name'] ?? ''),
            'type' => sanitize_text_field($data['type'] ?? 'banner'),
            'width' => intval($data['width'] ?? 300),
            'height' => intval($data['height'] ?? 250),
            'status' => in_array($data['status'] ?? '', ['active', 'inactive']) ? $data['status'] : 'active',
            'description' => sanitize_textarea_field($data['description'] ?? ''),
            'image_url' => '',
            'target_url' => '',
            'website_name' => '',
            'alt_text' => ''
        ];

        if (empty($sanitized['ad_position'])) {
            return new \WP_Error('missing_name', 'Position name is required');
        }

        $existing_position = $this->get_position($sanitized['ad_position']);
        if ($existing_position) {
            error_log("AMP: Position '{$sanitized['ad_position']}' already exists in database");
            return new \WP_Error('duplicate_name', 'Position name already exists');
        }

        $result = $this->database->insert(
            'ad_positions',
            $sanitized,
            ['%s', '%s', '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s', '%d', '%d']
        );

        if (!$result) {
            $error_msg = $this->database->last_error();
            error_log("AMP: Failed to create position '{$sanitized['ad_position']}': " . $error_msg);
            return new \WP_Error('database_error', 'Failed to create position in database: ' . $error_msg);
        }

        $this->database->insert(
            'ad_price_calculation',
            [
                'ad_position' => $sanitized['ad_position'],
                'usdt_price' => 1000.00,
                'thb_price' => 35000.00
            ],
            ['%s', '%f', '%f']
        );

        $this->cache->clear_group('position_data');
        do_action('amp_position_created', $sanitized['ad_position'], $sanitized);

        return $sanitized['ad_position'];
    }

    public function bulk_create_positions($positions_data) {
        $permission_check = $this->validate_permission_for_action('create_position');
        if (is_wp_error($permission_check)) {
            return $permission_check;
        }

        if (!is_array($positions_data) || empty($positions_data)) {
            return new \WP_Error('invalid_data', 'Positions data must be a non-empty array');
        }

        $created_count = 0;
        $skipped_count = 0;
        $errors = [];

        $this->database->start_transaction();

        try {
            foreach ($positions_data as $position_data) {
                if (!isset($position_data['name']) || !isset($position_data['width']) || !isset($position_data['height'])) {
                    $errors[] = "Missing required fields for position";
                    continue;
                }

                $position_name = strtoupper(trim($position_data['name']));
                $width = intval($position_data['width']);
                $height = intval($position_data['height']);

                $existing = $this->database->get_var(
                    "SELECT COUNT(*) FROM {$this->database->get_table('ad_positions')} WHERE ad_position = %s",
                    [$position_name]
                );

                if ($existing > 0) {
                    $skipped_count++;
                    continue;
                }

                $result = $this->database->insert('ad_positions', [
                    'ad_position' => $position_name,
                    'type' => 'banner',
                    'width' => $width,
                    'height' => $height,
                    'description' => "Auto-created position {$position_name}",
                    'status' => 'active',
                    'image_url' => '',
                    'target_url' => '',
                    'website_name' => '',
                    'alt_text' => ''
                ], [
                    '%s', '%s', '%d', '%d', '%s', '%s', '%s', '%s', '%s', '%s'
                ]);

                if ($result === false) {
                    $errors[] = "Failed to create position: {$position_name}";
                    continue;
                }

                $this->database->insert('ad_price_calculation', [
                    'ad_position' => $position_name,
                    'usdt_price' => 1000.00,
                    'thb_price' => 35000.00
                ], [
                    '%s', '%f', '%f'
                ]);

                do_action('amp_position_created', $position_name, ['width' => $width, 'height' => $height]);

                $created_count++;
            }

            $this->database->commit();
            
            if ($created_count > 0) {
                $this->cache->clear_group('position_data');
            }

            return [
                'created_count' => $created_count,
                'skipped_count' => $skipped_count,
                'errors' => $errors,
                'total_processed' => count($positions_data)
            ];

        } catch (\Exception $e) {
            $this->database->rollback();
            return new \WP_Error('bulk_create_failed', 'Bulk creation failed: ' . $e->getMessage());
        }
    }

    public function delete_position($position_name) {
        $permission_check = $this->validate_permission_for_action('delete_position');
        if (is_wp_error($permission_check)) {
            return $permission_check;
        }

        error_log("AMP: Starting delete_position for: '{$position_name}'");

        global $wpdb;
        $ad_positions_table = $this->database->get_table('ad_positions');
        $existing_position = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$ad_positions_table} WHERE ad_position = %s", $position_name));

        if (!$existing_position) {
            return new \WP_Error('position_not_found', "Position '{$position_name}' not found in database");
        }

        $this->database->start_transaction();

        try {
            $deleted_positions = $this->database->delete(
                'ad_positions',
                ['ad_position' => $position_name],
                ['%s']
            );

            $this->database->delete(
                'ad_clicks',
                ['ad_position' => $position_name],
                ['%s']
            );

            $this->database->delete(
                'ad_price_calculation',
                ['ad_position' => $position_name],
                ['%s']
            );

            $this->database->delete(
                'ad_payments',
                ['ad_position' => $position_name],
                ['%s']
            );

            $this->database->commit();
        } catch (\Exception $e) {
            $this->database->rollback();
            return new \WP_Error('delete_failed', 'Failed to delete position: ' . $e->getMessage());
        }

        if (function_exists('clear_position_clicks')) {
            clear_position_clicks($position_name);
        }

        global $wpdb;

        $users_with_positions = $wpdb->get_results("
            SELECT user_id, meta_value
            FROM {$wpdb->usermeta}
            WHERE meta_key = 'ad_positions'
        ");

        foreach ($users_with_positions as $user_meta) {
            $positions = maybe_unserialize($user_meta->meta_value);

            if (is_array($positions) && in_array($position_name, $positions)) {
                $updated_positions = array_diff($positions, array($position_name));
                update_user_meta($user_meta->user_id, 'ad_positions', $updated_positions);
            }
        }
        
        $this->cache->clear_group('position_data');
        $this->cache->clear_group('user_dashboard');
        do_action('amp_position_deleted', $position_name);

        return true;
    }

    public function reset_position_ownership($position_name) {
        $permission_check = $this->validate_permission_for_action('reset_position');
        if (is_wp_error($permission_check)) {
            return $permission_check;
        }

        $old_user_id = $this->get_position_owner_from_usermeta($position_name);

        $current_position_data = $this->database->get_row(
            "SELECT image_url FROM {$this->database->get_table('ad_positions')} WHERE ad_position = %s",
            [$position_name]
        );

        if (!$current_position_data) {
            return new \WP_Error('position_not_found', 'Position not found');
        }

        $old_image_url = $current_position_data->image_url ?? '';

        if (!empty($old_image_url)) {
            $this->delete_position_image_file($old_image_url, $position_name);
        }

        $update_result = $this->database->update('ad_positions',
            [
                'expiration_date' => null,
                'start_date' => null,
                'image_url' => '',
                'target_url' => '',
                'website_name' => '',
                'alt_text' => ''
            ],
            ['ad_position' => $position_name],
            [null, null, '%s', '%s', '%s', '%s'],
            ['%s']
        );

        if ($update_result === false) {
            return new \WP_Error('database_error', 'Failed to update position data');
        }

        if ($old_user_id) {
            $user_positions = get_user_meta($old_user_id, 'ad_positions', true);
            if (is_array($user_positions) && in_array($position_name, $user_positions)) {
                $updated_positions = array_diff($user_positions, array($position_name));
                update_user_meta($old_user_id, 'ad_positions', $updated_positions);
            }
        }

        $this->clear_position_cache($position_name);

        return true;
    }

    private function delete_position_image_file($image_url, $position_name) {
        if (empty($image_url)) {
            return false;
        }

        try {
            $upload_dir = wp_upload_dir();
            $base_url = $upload_dir['baseurl'];
            $base_dir = $upload_dir['basedir'];

            if (strpos($image_url, $base_url) === 0) {
                $file_path = str_replace($base_url, $base_dir, $image_url);
                $file_path = wp_normalize_path($file_path);

                if (file_exists($file_path)) {
                    $attachment_id = attachment_url_to_postid($image_url);

                    if ($attachment_id) {
                        $is_used_elsewhere = $this->is_image_used_elsewhere($image_url, $position_name);

                        if (!$is_used_elsewhere) {
                            wp_delete_attachment($attachment_id, true);
                            return true;
                        }
                    } else {
                        wp_delete_file($file_path);
                        return true;
                    }
                }
            }
        } catch (\Exception $e) {
            return false;
        }

        return false;
    }

    private function is_image_used_elsewhere($image_url, $exclude_position) {
        $count = $this->database->get_var(
            "SELECT COUNT(*) FROM {$this->database->get_table('ad_positions')}
             WHERE image_url = %s AND ad_position != %s",
            [$image_url, $exclude_position]
        );

        return $count > 0;
    }

    public function reset_user_position_ownership($user_id, $position_name) {
        if ($this->context === 'public') {
            $current_user_id = get_current_user_id();
            $ownership_state = $this->get_position_ownership_state($position_name, $current_user_id);
            $is_owner = $ownership_state['is_owned_by_current_user'];
            $is_expired = $ownership_state['is_expired'];
            $has_bypass = amp_user_has_bypass_permission($current_user_id);
            if (!$is_owner) {
                return new \WP_Error('permission_denied', 'คุณไม่ใช่เจ้าของโฆษณานี้');
            }
            if (!$has_bypass && $is_expired) {
                return new \WP_Error('permission_denied', 'คุณสามารถยกเลิกโฆษณาได้เฉพาะเมื่อยังไม่หมดอายุ หรือมีสิทธิ์ bypass');
            }
        } else {
            $permission_check = $this->validate_permission_for_action('reset_position');
            if (is_wp_error($permission_check)) {
                return $permission_check;
            }
        }

        $user = get_userdata($user_id);
        if (!$user) {
            return new \WP_Error('invalid_user', 'User not found');
        }

        $user_positions = get_user_meta($user_id, 'ad_positions', true);
        if (is_array($user_positions) && in_array($position_name, $user_positions)) {
            $updated_positions = array_diff($user_positions, array($position_name));
            update_user_meta($user_id, 'ad_positions', $updated_positions);
        }

        $this->database->update('ad_positions',
            [
                'expiration_date' => null,
                'start_date' => null,
                'image_url' => '',
                'target_url' => '',
                'website_name' => '',
                'alt_text' => '',
                'status' => 'active'
            ],
            ['ad_position' => $position_name],
            ['%s', '%s', '%s', '%s', '%s', '%s', '%s'],
            ['%s']
        );

        $this->clear_position_cache($position_name);
        return true;
    }

    public function set_security_manager($security_manager) {
        $this->security = $security_manager;
    }
    
    public function clear_position_cache($position_name = null) {
        if ($position_name) {
            $this->cache->delete('pos_name_' . md5($position_name), 'position_data');
            $this->cache->delete('ownership_state_' . md5($position_name) . '_guest', 'position_data');
        }
        $this->cache->clear_group('position_data');
        $this->cache->clear_group('user_dashboard');
    }

    public function calculate_renewal_expiration_date($position_name, $user_id, $duration_days) {
        $ownership_state = $this->get_position_ownership_state($position_name, $user_id);
        
        if ($ownership_state['is_owned'] && $ownership_state['owner_id'] == $user_id && !$ownership_state['is_expired']) {
            $current_expiration = $ownership_state['expiration_date'];
            if ($current_expiration) {
                $new_expiration = date('Y-m-d H:i:s', strtotime($current_expiration . " +{$duration_days} days"));
                error_log("AMP: Position '{$position_name}' renewal - extending from {$current_expiration} to {$new_expiration} (+{$duration_days} days)");
                return $new_expiration;
            }
        }
        
        $start_date = current_time('mysql');
        $new_expiration = date('Y-m-d H:i:s', strtotime($start_date . " +{$duration_days} days"));
        error_log("AMP: Position '{$position_name}' new purchase - from {$start_date} to {$new_expiration} ({$duration_days} days)");
        return $new_expiration;
    }
}