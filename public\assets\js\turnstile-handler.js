(function() {
    'use strict';

    const turnstileState = {
        scriptLoaded: false,
        activeWidgetId: null,
        currentToken: null,
        siteKey: null,
        container: null,
        renderInterval: null
    };

    document.addEventListener('DOMContentLoaded', () => {
        if (window.ampAuthData?.turnstileEnabled) {
            turnstileState.siteKey = window.ampAuthData.turnstileSiteKey;
            if (turnstileState.siteKey) {
                waitForTurnstile();
            }
        }
    });

    document.addEventListener('visibilitychange', () => {
        if (!document.hidden && turnstileState.scriptLoaded) {
            setTimeout(() => {
                renderWidgetForActiveTab();
            }, 500);
        }
    });

    window.addEventListener('focus', () => {
        if (turnstileState.scriptLoaded) {
            setTimeout(() => {
                renderWidgetForActiveTab();
            }, 500);
        }
    });

    function waitForTurnstile() {
        if (typeof window.turnstile !== 'undefined') {
            turnstileState.scriptLoaded = true;
            renderWidgetForActiveTab();
            
            const form = getActiveForm();
            if (form) {
                setButtonState(form, false, 'กรุณายืนยันตัวตน...');
            }
        } else {
            let attempts = 0;
            const interval = setInterval(() => {
                attempts++;
                if (typeof window.turnstile !== 'undefined') {
                    clearInterval(interval);
                    turnstileState.scriptLoaded = true;
                    renderWidgetForActiveTab();
                    const form = getActiveForm();
                    if (form) {
                       setButtonState(form, false, 'กรุณายืนยันตัวตน...');
                    }
                } else if (attempts > 50) { // 10 seconds timeout
                    clearInterval(interval);
                    onScriptError();
                }
            }, 200);
        }
    }

    function getActiveTurnstileContainer() {
        const activeFormContainer = document.querySelector('.amp-auth-form-container.active');
        if (activeFormContainer) {
            const turnstileContainer = activeFormContainer.querySelector('.ad-login-captcha');
            if (turnstileContainer && turnstileContainer.offsetParent !== null) {
                return turnstileContainer;
            }
        }

        const allContainers = document.querySelectorAll('.ad-login-captcha');
        for (const container of allContainers) {
            if (container.offsetParent !== null) {
                const parentForm = container.closest('.amp-auth-form-container');
                if (parentForm && parentForm.classList.contains('active')) {
                return container;
                }
            }
        }

        return null;
    }

    function getActiveForm() {
        const activeContainer = document.querySelector('.amp-auth-form-container.active');
        return activeContainer ? activeContainer.querySelector('form') : null;
    }

    function setButtonState(form, enabled, message = '') {
        if (!form) return;

        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = !enabled;
            submitButton.setAttribute('title', message);
        }
        
        const googleButton = form.querySelector('#google-login-btn');
        if (googleButton) {
            googleButton.disabled = !enabled;
            googleButton.setAttribute('title', message);
        }
    }
    
    function onScriptError() {
        console.error('[Turnstile] Script failed to load.');
        document.querySelectorAll('form').forEach(form => {
            setButtonState(form, true, 'การยืนยันตัวตนล้มเหลว สามารถส่งข้อมูลได้');

            const turnstileInput = form.querySelector('input[name="cf-turnstile-response"]');
            if (turnstileInput) {
                turnstileInput.value = 'bypass_csp_restriction';
            }
        });
    }

    function renderWidgetForActiveTab() {
        if (!turnstileState.scriptLoaded || typeof turnstile === 'undefined') {
            return;
        }

        const newContainer = getActiveTurnstileContainer();

        if (turnstileState.activeWidgetId && turnstileState.container === newContainer) {
            return;
        }

        if (turnstileState.activeWidgetId) {
            try {
                turnstile.remove(turnstileState.activeWidgetId);
            } catch (e) {
                console.warn('[Turnstile] Failed to remove widget:', e);
            }
            turnstileState.activeWidgetId = null;
            turnstileState.container = null;
        }

        if (!newContainer) {
            return;
        }

        const form = getActiveForm();
        if (form) {
        setButtonState(form, false, 'กรุณายืนยันตัวตน...');
        }

        turnstileState.container = newContainer;

        const widgetId = turnstile.render(newContainer, {
            sitekey: turnstileState.siteKey,
            theme: document.body.classList.contains('dark-mode') ? 'dark' : 'light',
            callback: function(token) {
                turnstileState.currentToken = token;
                updateAllFormTokens(token);
                const currentForm = getActiveForm();
                if (currentForm) {
                    setButtonState(currentForm, true, 'การยืนยันตัวตนสำเร็จ');
                }
            },
            'expired-callback': function() {
                turnstileState.currentToken = null;
                updateAllFormTokens('');
                const currentForm = getActiveForm();
                if (currentForm) {
                    setButtonState(currentForm, false, 'Token หมดอายุ กรุณายืนยันใหม่');
                    turnstile.reset(turnstileState.activeWidgetId);
                }
            },
            'error-callback': function() {
                const currentForm = getActiveForm();
                if (currentForm) {
                    setButtonState(currentForm, false, 'เกิดข้อผิดพลาด กรุณายืนยันใหม่');
                }
            }
        });

        turnstileState.activeWidgetId = widgetId;
    }

    function updateAllFormTokens(token) {
        document.querySelectorAll('input[name="cf-turnstile-response"]').forEach(input => {
            input.value = token;
        });
    }

    // This function will be called by auth.js when a tab changes.
    window.renderTurnstileForActiveTab = renderWidgetForActiveTab;

    window.resetTurnstileWidget = function() {
        if (turnstileState.scriptLoaded && turnstileState.activeWidgetId && typeof turnstile !== 'undefined') {
            const form = getActiveForm();
            if(form){
                setButtonState(form, false, 'กรุณายืนยันตัวตนอีกครั้ง...');
            }
            turnstileState.currentToken = null;
            updateAllFormTokens('');
            turnstile.reset(turnstileState.activeWidgetId);
        }
    };

    window.getTurnstileToken = function() {
        if (!turnstileState.currentToken || !turnstileState.container) {
            return null;
        }

        const activeContainer = getActiveTurnstileContainer();
        if (activeContainer !== turnstileState.container) {
            return null;
        }

        return turnstileState.currentToken;
    };

})();