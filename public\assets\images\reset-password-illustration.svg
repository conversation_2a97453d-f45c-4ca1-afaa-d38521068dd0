<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="paint0_linear" x1="200" y1="0" x2="200" y2="300" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4361EE" stop-opacity="0.1"/>
      <stop offset="1" stop-color="#7209B7" stop-opacity="0.2"/>
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#paint0_linear)"/>
  
  <!-- Background Elements -->
  <circle cx="50" cy="50" r="30" fill="#4361EE" fill-opacity="0.1"/>
  <circle cx="350" cy="250" r="40" fill="#7209B7" fill-opacity="0.1"/>
  <circle cx="300" cy="80" r="20" fill="#4361EE" fill-opacity="0.15"/>
  <circle cx="100" cy="220" r="25" fill="#7209B7" fill-opacity="0.15"/>
  
  <!-- Lock and Key -->
  <circle cx="200" cy="150" r="40" fill="#4361EE" fill-opacity="0.2"/>
  <rect x="180" y="140" width="40" height="50" rx="5" fill="#FFFFFF" stroke="#4361EE" stroke-width="2"/>
  
  <!-- Keyhole -->
  <circle cx="200" cy="155" r="8" fill="#4361EE" fill-opacity="0.5"/>
  <rect x="197" y="155" width="6" height="15" rx="2" fill="#4361EE" fill-opacity="0.5"/>
  
  <!-- Key -->
  <circle cx="140" cy="150" r="10" fill="#4361EE"/>
  <rect x="150" y="145" width="30" height="5" rx="2" fill="#4361EE"/>
  <rect x="155" y="150" width="5" height="10" rx="2" fill="#4361EE"/>
  <rect x="165" y="150" width="5" height="10" rx="2" fill="#4361EE"/>
  
  <!-- Refresh Icon -->
  <circle cx="260" cy="150" r="20" fill="#FFFFFF" stroke="#4361EE" stroke-width="2"/>
  <path d="M250 150 A10 10 0 1 1 260 160" stroke="#4361EE" stroke-width="2" fill="none"/>
  <path d="M260 160 L265 160 L265 155" stroke="#4361EE" stroke-width="2" fill="none"/>
  
  <!-- Password Strength Meter -->
  <rect x="150" y="210" width="100" height="10" rx="5" fill="#E0E0E0"/>
  <rect x="150" y="210" width="75" height="10" rx="5" fill="#4361EE"/>
  
  <!-- Checkmark -->
  <circle cx="300" cy="210" r="15" fill="#FFFFFF" stroke="#4361EE" stroke-width="2"/>
  <path d="M295 210 L298 215 L305 205" stroke="#4361EE" stroke-width="2" fill="none"/>
  
  <!-- Shield -->
  <path d="M200 60 L230 70 L230 100 C230 120, 200 130, 200 130 C200 130, 170 120, 170 100 L170 70 Z" fill="#FFFFFF" stroke="#4361EE" stroke-width="2"/>
  <path d="M190 95 L195 100 L210 85" stroke="#4361EE" stroke-width="2" fill="none"/>
</svg>
