<?php
if (!defined('WPINC')) {
    die;
}

function display_payment_settings_content() {
    if (!current_user_can('manage_options')) {
        wp_die('Administrator access required for payment settings');
    }
    
    require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-payment-handler.php';
    $payment_handler = AMP_Payment_Handler::instance();
    
    $notice = '';
    $encryption_manager = \AMP_Encryption_Manager::instance();
    $api_key = $encryption_manager->get_secret('plisio_api_key');
    $test_mode = $payment_handler->get_plisio_setting('test_mode');
    $selected_currency = get_option('plisio_selected_currency', 'USDT.TRON');
    
    $plisio_api_for_currencies = new Plisio_API($api_key);
    $currencies_response = $plisio_api_for_currencies->get_supported_currencies();
    $available_currencies = [];
    if (is_array($currencies_response) && isset($currencies_response['status']) && $currencies_response['status'] === 'success') {
        foreach ($currencies_response['data'] as $currency) {
            if (isset($currency['hidden']) && $currency['hidden'] == 0 && isset($currency['cid']) && isset($currency['name'])) {
                 $available_currencies[$currency['cid']] = $currency['name'] . ' (' . $currency['currency'] . ')';
            }
        }
    }

    $plisio_settings_nonce = wp_create_nonce('save_plisio_settings');
    ?>
    <?php if (!empty($notice)) : ?>
        <?php echo $notice; ?>
    <?php endif; ?>
    <form method="post" action="" id="plisio-settings-form">
        <input type="hidden" id="plisio_settings_nonce" name="security" value="<?php echo esc_attr($plisio_settings_nonce); ?>">
        <div class="plisio-settings-grid">
            <div class="plisio-card">
                <h3>🔧 การตั้งค่า API</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="plisio_api_key">🔑 Plisio API Key</label></th>
                        <td>
                            <?php
                            $has_api_key = !empty($api_key);
                            if ($has_api_key): ?>
                                <div class="secret-key-display">
                                    <span class="secret-placeholder">••••••••••••••••••••••••••••••••</span>
                                    <button type="button" class="button button-secondary delete-secret-btn" data-secret="plisio_api_key" data-name="Plisio API Key">
                                        🗑️ ลบ
                                    </button>
                                </div>
                            <?php else: ?>
                                <div class="api-key-input-wrapper">
                                    <input type="text" id="plisio_api_key" name="plisio_api_key" value="" class="regular-text" placeholder="กรอก Plisio API Key ของคุณ">
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label>🔍 การตรวจสอบด้วยตนเอง</label></th>
                        <td>
                            <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 16px;">
                                <h4 style="margin: 0 0 12px 0; color: #0c5460; font-size: 16px;">🛡️ ระบบความปลอดภัยขั้นสูง</h4>
                                <p style="margin: 0 0 12px 0; font-size: 14px; color: #0c5460; line-height: 1.5;">
                                    <strong>Plisio ไม่ใช้ webhook secret</strong> - ระบบใช้การตรวจสอบแบบ Manual Verification เพื่อความปลอดภัยสูงสุด
                                </p>
                                <ul style="margin: 0; padding-left: 20px; font-size: 14px; color: #0c5460; line-height: 1.6;">
                                    <li>✅ ตรวจสอบ IP Address จาก Plisio อัตโนมัติ</li>
                                    <li>✅ ยืนยันข้อมูลการชำระเงินผ่าน API Callback</li>
                                    <li>✅ ผู้ดูแลระบบสามารถยืนยันการชำระเงินด้วยตนเอง</li>
                                    <li>✅ ป้องกันการซื้อซ้ำและธุรกรรมที่ซ้ำกัน</li>
                                    <li>✅ บันทึกประวัติการทำงานทั้งหมดเพื่อตรวจสอบ</li>
                                </ul>
                                <div style="margin: 12px 0 0 0; padding: 8px; background: rgba(255,255,255,0.7); border-radius: 4px;">
                                    <p style="margin: 0; font-size: 13px; color: #6c757d;">
                                        💡 <strong>คำแนะนำ:</strong> ไปที่แท็บ <strong>"Manual Verification"</strong> เพื่อจัดการการชำระเงินที่รอการยืนยัน
                                    </p>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="plisio_test_mode">🧪 โหมดทดสอบ</label></th>
                        <td>
                            <div class="toggle-switch-container">
                                <label class="toggle-switch">
                                    <input type="checkbox" name="plisio_test_mode" id="plisio_test_mode" value="1" <?php checked($test_mode, '1'); ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label"><?php echo $test_mode === '1' ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน'; ?></span>
                            </div>
                            <div class="test-mode-explanation" style="margin-top: 10px; padding: 12px; background: <?php echo $test_mode === '1' ? '#fff3cd' : '#d1ecf1'; ?>; border-left: 4px solid <?php echo $test_mode === '1' ? '#ffc107' : '#17a2b8'; ?>; border-radius: 4px;">
                                <p style="margin: 0; font-size: 13px; line-height: 1.4;">
                                    <strong><?php echo $test_mode === '1' ? '⚠️ โหมดทดสอบ:' : 'ℹ️ โหมดปกติ:'; ?></strong><br>
                                    <?php if ($test_mode === '1'): ?>
                                        • ✅ ข้ามการตรวจสอบ API กับ Plisio<br>
                                        • ✅ ข้ามการตรวจสอบจำนวนเงิน<br>
                                        • ✅ ข้ามการตรวจสอบความเป็นเจ้าของ<br>
                                        • ✅ รับ webhook จาก External IP ได้<br>
                                        • ⚡ <strong>เหมาะสำหรับการทดสอบระบบ</strong>
                                    <?php else: ?>
                                        • 🔒 ตรวจสอบ API กับ Plisio อย่างเข้มข้น<br>
                                        • 🔒 ตรวจสอบจำนวนเงินที่ตรงกัน<br>
                                        • 🔒 ตรวจสอบความเป็นเจ้าของและการจอง<br>
                                        • 🔒 ระบบป้องกันการทุจริตครบถ้วน<br>
                                        • 🚀 <strong>เหมาะสำหรับการใช้งานจริง</strong>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="plisio-card">
                <h3>💰 การตั้งค่าสกุลเงิน</h3>
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="plisio_selected_currency">💎 สกุลเงินที่ใช้</label></th>
                        <td>
                            <div id="currency-selector-container">
                                <?php if (!empty($available_currencies)): ?>
                                    <select id="plisio_selected_currency" name="plisio_selected_currency">
                                        <?php foreach ($available_currencies as $cid => $name): ?>
                                            <option value="<?php echo esc_attr($cid); ?>" <?php selected($selected_currency, $cid); ?>>
                                                <?php echo esc_html($name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <p style="margin: 8px 0 0 0; font-size: 12px; color: #6c757d;">
                                        ⚡ เลือกสกุลเงิน Crypto ที่คุณต้องการเปิดรับการชำระเงิน
                                    </p>
                                <?php else: ?>
                                    <div class="currency-badge" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 12px 16px; border-radius: 8px; display: inline-block;">
                                        <strong style="font-size: 16px;">ไม่สามารถโหลดสกุลเงินได้</strong>
                                        <span class="currency-desc" style="display: block; font-size: 12px; opacity: 0.9; margin-top: 4px;">กรุณาตรวจสอบ API Key และการเชื่อมต่อ</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="plisio-card">
            <h3>🔗 URL สำหรับ Callback</h3>
            <div class="callback-urls-grid">
                <div class="callback-url-card">
                    <div class="callback-url-header">
                        <h4>📊 Status Callback URL</h4>
                        <p style="font-size: 12px; color: #6c757d; margin: 4px 0 0 0;">สำหรับรับสถานะการชำระเงิน</p>
                    </div>
                    <div class="url-input-group">
                        <input type="text" readonly value="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook')); ?>" class="regular-text">
                        <button type="button" class="button copy-url" data-clipboard-text="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook')); ?>">📋 คัดลอก</button>
                    </div>
                </div>
                <div class="callback-url-card">
                    <div class="callback-url-header">
                        <h4>✅ Success Callback URL</h4>
                        <p style="font-size: 12px; color: #6c757d; margin: 4px 0 0 0;">สำหรับการชำระเงินสำเร็จ</p>
                    </div>
                    <div class="url-input-group">
                        <input type="text" readonly value="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook-success')); ?>" class="regular-text">
                        <button type="button" class="button copy-url" data-clipboard-text="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook-success')); ?>">📋 คัดลอก</button>
                    </div>
                </div>
                <div class="callback-url-card">
                    <div class="callback-url-header">
                        <h4>❌ Fail Callback URL</h4>
                        <p style="font-size: 12px; color: #6c757d; margin: 4px 0 0 0;">สำหรับการชำระเงินล้มเหลว</p>
                    </div>
                    <div class="url-input-group">
                        <input type="text" readonly value="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook-fail')); ?>" class="regular-text">
                        <button type="button" class="button copy-url" data-clipboard-text="<?php echo esc_url(home_url('/wp-json/ad-management-pro/v1/plisio-webhook-fail')); ?>">📋 คัดลอก</button>
                    </div>
                </div>
            </div>
            <div class="callback-urls-note" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px; margin-top: 16px;">
                <p style="margin: 0; color: #856404;">
                    <strong>📝 คำแนะนำ:</strong> คัดลอก URL ทั้งหมดนี้และตั้งค่าใน
                    <a href="https://plisio.net/account/api" target="_blank" style="color: #856404; text-decoration: underline;">
                        🌐 Plisio Dashboard → API Settings
                    </a>
                </p>
            </div>
        </div>

        <div class="plisio-card">
            <h3>🧪 การทดสอบและตรวจสอบ</h3>
            <div class="test-connection-grid">
                <div class="test-connection-card">
                    <div class="test-card-header">
                        <h4>🔌 ทดสอบการเชื่อมต่อ API</h4>
                    </div>
                    <p class="test-description">ตรวจสอบการเชื่อมต่อกับ Plisio API และความถูกต้องของ API Key</p>
                    <button type="button" id="test-plisio-connection" class="ad-btn ad-btn-secondary">🔍 ทดสอบการเชื่อมต่อ</button>
                </div>
                <div class="test-connection-card">
                    <div class="test-card-header">
                        <h4>📄 ทดสอบการสร้างใบแจ้งหนี้</h4>
                    </div>
                    <p class="test-description">ทดสอบการสร้างใบแจ้งหนี้เพื่อยืนยันการทำงานของระบบ</p>
                    <div class="test-invoice-form">
                        <div class="amount-input-wrapper">
                            <label for="test_invoice_amount">💵 จำนวนเงิน (USD):</label>
                            <input type="number" id="test_invoice_amount" min="1" step="0.01" value="5.00" class="form-control" placeholder="5.00">
                        </div>
                        <button type="button" id="test-plisio-invoice" class="ad-btn ad-btn-primary">🚀 สร้างใบแจ้งหนี้ทดสอบ</button>
                    </div>
                </div>
            </div>
            <div id="plisio-test-result" class="test-result-container"></div>
        </div>
        <p class="submit">
            <button type="submit" name="save_plisio_settings" id="save-plisio-settings" class="button button-primary">💾 บันทึกการตั้งค่า</button>
        </p>
    </form>

    <div class="plisio-card">
        <h3>📋 คำแนะนำการตั้งค่า</h3>
        <div class="instructions-grid">
            <div class="instruction-step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <h4>🆕 สร้างบัญชี Plisio</h4>
                    <p>สมัครสมาชิกใหม่ที่ <a href="https://plisio.net" target="_blank" style="color: #4361ee; text-decoration: underline;">🌐 Plisio.net</a> (ฟรี)</p>
                </div>
            </div>
            <div class="instruction-step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <h4>🔑 รับ API Key</h4>
                    <p>เข้าไปที่ <a href="https://plisio.net/account/api" target="_blank" style="color: #4361ee; text-decoration: underline;">⚙️ API Settings</a> และคัดลอก API Key ของคุณ</p>
                </div>
            </div>
            <div class="instruction-step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <h4>📝 กรอก API Key</h4>
                    <p>นำ API Key มาวางในช่อง "Plisio API Key" ด้านบน</p>
                </div>
            </div>
            <div class="instruction-step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <h4>🔗 ตั้งค่า Callback URLs</h4>
                    <p>คัดลอก URL ทั้ง 3 ตัวด้านบนและตั้งค่าใน Plisio Dashboard</p>
                </div>
            </div>
            <div class="instruction-step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <h4>✅ ทดสอบการเชื่อมต่อ</h4>
                    <p>บันทึกการตั้งค่าและกดปุ่ม "ทดสอบการเชื่อมต่อ" เพื่อยืนยัน</p>
                </div>
            </div>
        </div>
        <div style="background: #e8f5e8; border: 1px solid #c3e6c3; border-radius: 8px; padding: 16px; margin-top: 20px;">
            <p style="margin: 0; color: #2d5a2d; font-size: 14px; line-height: 1.5;">
                <strong>🎉 เสร็จแล้ว!</strong> หลังจากตั้งค่าเสร็จสิ้น ระบบจะพร้อมรับการชำระเงินผ่าน USDT (TRC20)
                และคุณสามารถยืนยันการชำระเงินแบบ Manual ได้ที่แท็บ "Manual Verification"
            </p>
            <p style="margin: 8px 0 0 0; color: #2d5a2d; font-size: 13px;">
                <strong>🔒 ความปลอดภัย:</strong> ระบบใช้ HMAC-SHA1 signature verification แทนการตรวจสอบ IP addresses เพื่อความปลอดภัยสูงสุด
            </p>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-unified.css?v=' . filemtime(plugin_dir_path(dirname(__FILE__)) . 'assets/css/admin-unified.css'); ?>" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .secret-key-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .secret-placeholder {
            font-family: monospace;
            background: #f1f1f1;
            padding: 8px 12px;
            border-radius: 4px;
            color: #666;
            border: 1px solid #ddd;
            min-width: 200px;
        }
        .delete-secret-btn {
            background: #dc3545 !important;
            color: white !important;
            border: none !important;
            padding: 6px 12px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 12px !important;
        }
        .delete-secret-btn:hover {
            background: #c82333 !important;
        }
        @media (max-width: 768px) {
            .secret-key-display {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>

    <script>
    jQuery(document).ready(function($) {
        $('.delete-secret-btn').off('click.payment-settings').on('click.payment-settings', function(e) {
            e.preventDefault();

            const secretKey = $(this).data('secret');
            const secretName = $(this).data('name');
            const button = $(this);

            Swal.fire({
                title: 'ยืนยันการลบ',
                text: `คุณต้องการลบ ${secretName} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: '🗑️ ลบ',
                cancelButtonText: '❌ ยกเลิก',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    button.prop('disabled', true).html('🔄 กำลังลบ...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_secret_key',
                            nonce: '<?php echo wp_create_nonce('delete_secret_key'); ?>',
                            secret_key: secretKey
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'สำเร็จ!',
                                    text: 'ลบ Secret Key เรียบร้อยแล้ว',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'เกิดข้อผิดพลาด!',
                                    text: response.data.message || 'ไม่สามารถลบได้',
                                    icon: 'error'
                                });
                                button.prop('disabled', false).html('🗑️ ลบ');
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด!',
                                text: 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
                                icon: 'error'
                            });
                            button.prop('disabled', false).html('🗑️ ลบ');
                        }
                    });
                }
            });
        });

        function updateCurrencyDropdown(apiKey) {
            var container = $('#currency-selector-container');
            var nonce = $('#plisio_settings_nonce').val();
            container.html('<p>กำลังโหลดสกุลเงิน...</p>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'amp_get_plisio_currencies',
                    api_key: apiKey,
                    security: nonce
                },
                success: function(response) {
                    if (response.success && response.data.currencies.length > 0) {
                        var select = $('<select id="plisio_selected_currency" name="plisio_selected_currency"></select>');
                        var currentSelected = '<?php echo esc_js($selected_currency); ?>';

                        response.data.currencies.forEach(function(currency) {
                            var option = $('<option></option>')
                                .val(currency.cid)
                                .text(currency.name);
                            if (currency.cid === currentSelected) {
                                option.prop('selected', true);
                            }
                            select.append(option);
                        });

                        var description = $('<p style="margin: 8px 0 0 0; font-size: 12px; color: #6c757d;">⚡ เลือกสกุลเงิน Crypto ที่คุณต้องการเปิดรับการชำระเงิน</p>');
                        container.empty().append(select).append(description);
                    } else {
                         var errorMessage = response.data.message || 'ไม่สามารถโหลดสกุลเงินได้ กรุณาตรวจสอบ API Key และการเชื่อมต่อ';
                         container.html('<div class="currency-badge" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 12px 16px; border-radius: 8px; display: inline-block;"><strong style="font-size: 16px;">เกิดข้อผิดพลาด</strong><span class="currency-desc" style="display: block; font-size: 12px; opacity: 0.9; margin-top: 4px;">' + errorMessage + '</span></div>');
                    }
                },
                error: function(xhr, status, error) {
                     container.html('<div class="currency-badge" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 12px 16px; border-radius: 8px; display: inline-block;"><strong style="font-size: 16px;">เกิดข้อผิดพลาด</strong><span class="currency-desc" style="display: block; font-size: 12px; opacity: 0.9; margin-top: 4px;">' + error + '</span></div>');
                }
            });
        }

        $('#plisio_api_key').on('blur', function() {
            var apiKey = $(this).val();
            if(apiKey) {
                updateCurrencyDropdown(apiKey);
            }
        });

        $('#test-plisio-connection').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();
            $btn.prop('disabled', true).html('⏳ กำลังทดสอบ...');

            var apiKey = $('#plisio_api_key').val() || 'USE_STORED_ENCRYPTED';

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_plisio_connection',
                    api_key: apiKey,
                    security: $('#plisio_settings_nonce').val()
                },
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            title: '🎉 สำเร็จ!',
                            text: (response.data && response.data.message) || '✅ การทดสอบการเชื่อมต่อสำเร็จ',
                            icon: 'success',
                            confirmButtonText: 'ตกลง'
                        });
                    } else {
                        Swal.fire({
                            title: '❌ ข้อผิดพลาด!',
                            text: (response && response.data && response.data.message) || '⚠️ การทดสอบการเชื่อมต่อล้มเหลว',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        title: 'ข้อผิดพลาด!',
                        text: 'ไม่สามารถเชื่อมต่อได้: ' + error,
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        });
        $('#test-plisio-invoice').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();
            var amount = $('#test_invoice_amount').val();
            if (!amount || amount <= 0) {
                Swal.fire({
                    title: 'ข้อผิดพลาด!',
                    text: 'กรุณากรอกจำนวนเงินที่ถูกต้อง',
                    icon: 'error',
                    confirmButtonText: 'ตกลง'
                });
                return;
            }
            $btn.prop('disabled', true).html('⏳ กำลังสร้าง...');
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_plisio_invoice',
                    api_key: $('#plisio_api_key').val(),
                    amount: amount,
                    security: $('#plisio_settings_nonce').val()
                },
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            title: '🎉 สำเร็จ!',
                            text: '📄 สร้างใบแจ้งหนี้ทดสอบสำเร็จ',
                            icon: 'success',
                            confirmButtonText: 'ตกลง'
                        });
                    } else {
                        Swal.fire({
                            title: 'ข้อผิดพลาด!',
                            text: (response && response.data && response.data.message) || 'การสร้างใบแจ้งหนี้ล้มเหลว',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        title: 'ข้อผิดพลาด!',
                        text: 'ไม่สามารถสร้างใบแจ้งหนี้ได้: ' + error,
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        });
        $('#plisio-settings-form').on('submit', function(e) {
            e.preventDefault();
            var $btn = $('#save-plisio-settings');
            var originalText = $btn.html();
            $btn.prop('disabled', true).html('⏳ กำลังบันทึก...');
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'save_plisio_settings',
                    api_key: $('#plisio_api_key').val(),
                    test_mode: $('#plisio_test_mode').is(':checked') ? '1' : '0',
                    selected_currency: $('#plisio_selected_currency').val(),
                    security: $('#plisio_settings_nonce').val()
                },
                success: function(response) {
                    if (response && response.success) {
                        Swal.fire({
                            title: '🎉 สำเร็จ!',
                            text: '💾 บันทึกการตั้งค่าเรียบร้อยแล้ว',
                            icon: 'success',
                            confirmButtonText: 'ตกลง'
                        });
                    } else {
                        Swal.fire({
                            title: 'ข้อผิดพลาด!',
                            text: (response && response.data && response.data.message) || 'ไม่สามารถบันทึกได้',
                            icon: 'error',
                            confirmButtonText: 'ตกลง'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        title: 'ข้อผิดพลาด!',
                        text: 'ไม่สามารถบันทึกได้: ' + error,
                        icon: 'error',
                        confirmButtonText: 'ตกลง'
                    });
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        });
        $('.copy-url').on('click', function() {
            var text = $(this).data('clipboard-text');
            navigator.clipboard.writeText(text).then(function() {
                Swal.fire({
                    title: '📋 คัดลอกแล้ว!',
                    text: '✅ URL ถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            }).catch(function() {
                var textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                Swal.fire({
                    title: '📋 คัดลอกแล้ว!',
                    text: '✅ URL ถูกคัดลอกไปยังคลิปบอร์ดแล้ว',
                    icon: 'success',
                    timer: 1500,
                    showConfirmButton: false
                });
            });
        });
    });
    </script>
    <?php
}



