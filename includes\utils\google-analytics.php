<?php
if (!defined('WPINC')) {
    die;
}

function amp_ensure_ga_autoloader() {
    static $loaded = false;
    if ($loaded) {
        return true;
    }
    $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
    $autoload_path = $plugin_dir . 'admin/google-api-client/vendor/autoload.php';
    if (file_exists($autoload_path)) {
        require_once $autoload_path;
        $loaded = true;
        return true;
    }
    return false;
}

if (!function_exists('amp_get_ga_instance')) {
    function amp_get_ga_instance() {
        static $ga_instance = null;

        if ($ga_instance === null) {
            if (class_exists('AMP_Google_Analytics')) {
                $ga_instance = new AMP_Google_Analytics();
            }
        }

        return $ga_instance;
    }
}

if (!function_exists('amp_get_realtime_users')) {
    function amp_get_realtime_users() {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return 0;
        }

        $result = $ga->get_realtime_users();
        if (is_wp_error($result)) {
            return 0;
        }

        return isset($result['total']) ? intval($result['total']) : 0;
    }
}

if (!function_exists('amp_get_monthly_users')) {
    function amp_get_monthly_users($days = 30) {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return 0;
        }

        $result = $ga->get_total_users($days);
        if (is_wp_error($result)) {
            return 0;
        }

        return isset($result['total']) ? intval($result['total']) : 0;
    }
}



if (!function_exists('amp_get_monthly_pageviews')) {
    function amp_get_monthly_pageviews($days = 30) {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return 0;
        }

        $result = $ga->get_total_pageviews($days);
        if (is_wp_error($result)) {
            return 0;
        }

        return isset($result['total']) ? intval($result['total']) : 0;
    }
}

if (!function_exists('amp_get_monthly_pageviews_exclude_homepage')) {
    function amp_get_monthly_pageviews_exclude_homepage($days = 30) {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return 0;
        }

        $result = $ga->get_total_pageviews_exclude_homepage($days);
        if (is_wp_error($result)) {
            return 0;
        }

        $total = isset($result['total']) ? intval($result['total']) : 0;
        $reduction_percentage = get_option('pageviews_reduction_percentage', '9');
        $reduction_percentage = floatval($reduction_percentage);
        $reduction_multiplier = (100 - $reduction_percentage) / 100;
        $total = $total * $reduction_multiplier;

        return intval($total);
    }
}

if (!function_exists('amp_get_daily_users_data')) {
    function amp_get_daily_users_data($days = 90) {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return false;
        }

        $result = $ga->get_daily_users_data($days);
        if (is_wp_error($result)) {
            return false;
        }

        return $result;
    }
}

if (!function_exists('amp_get_last_24_hours_users_data')) {
    function amp_get_last_24_hours_users_data() {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return false;
        }

        $result = $ga->get_last_24_hours_users_data();
        if (is_wp_error($result)) {
            return false;
        }

        return $result;
    }
}

if (!function_exists('amp_get_top_pages')) {
    function amp_get_top_pages($days = 30, $limit = 10) {
        $ga = amp_get_ga_instance();
        if (!$ga || !$ga->is_configured()) {
            return false;
        }

        $result = $ga->get_top_pages($days, $limit);
        if (is_wp_error($result)) {
            return false;
        }

        return $result;
    }
}

if (!function_exists('amp_is_ga_configured')) {
    function amp_is_ga_configured() {
        $ga = amp_get_ga_instance();
        return $ga && $ga->is_configured();
    }
}

if (!function_exists('test_ga4_connection')) {
    function test_ga4_connection($property_id) {
        try {
            if (empty($property_id)) {
                return array(
                    'success' => false,
                    'message' => 'Property ID is required'
                );
            }
            $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
            $admin_dir = $plugin_dir . "admin";
            $json_file_path = $admin_dir . "/analytics/analytic-pro.json";
            if (!amp_ensure_ga_autoloader()) {
                return array(
                    "success" => false,
                    "message" => "Google API PHP Client not installed or autoloader not found.",
                );
            }
            if (!file_exists($json_file_path)) {
                return array(
                    "success" => false,
                    "message" => "Service Account JSON file not found.",
                    "details" => "JSON file path: " . $json_file_path
                );
            }
            if (!class_exists("Google\\Client") || !class_exists("Google\\Service\\AnalyticsData")) {
                return array(
                    "success" => false,
                    "message" => "Required Google API classes not found.",
                    "details" => "Google\\Client: " . (class_exists("Google\\Client") ? "Yes" : "No") .
                                 ", Google\\Service\\AnalyticsData: " . (class_exists("Google\\Service\\AnalyticsData") ? "Yes" : "No")
                );
            }
            $client = new Google\Client();
            $client->setAuthConfig($json_file_path);
            $client->addScope("https://www.googleapis.com/auth/analytics.readonly");
            $analytics = new Google\Service\AnalyticsData($client);
            $rtRequest = new Google\Service\AnalyticsData\RunRealtimeReportRequest();
            $rtRequest->setDimensions([new Google\Service\AnalyticsData\Dimension(['name' => 'country'])]);
            $rtRequest->setMetrics([new Google\Service\AnalyticsData\Metric(['name' => 'activeUsers'])]);
            $response = $analytics->properties->runRealtimeReport("properties/" . $property_id, $rtRequest);
            $total_active_users = 0;
            if (isset($response["rows"]) && is_array($response["rows"])) {
                foreach ($response["rows"] as $row) {
                    $total_active_users += (int)$row["metricValues"][0]["value"];
                }
            }
            $total_users_30_days = 0;
            try {
                $request = new Google\Service\AnalyticsData\RunReportRequest([
                    'dateRanges' => [
                        ['startDate' => '30daysAgo', 'endDate' => 'today']
                    ],
                    'metrics' => [
                        ['name' => 'activeUsers']
                    ],
                    'limit' => 10
                ]);

                $response = $analytics->properties->runReport("properties/" . $property_id, $request);

                if ($response && $response->getRows() && count($response->getRows()) > 0) {
                    foreach ($response->getRows() as $row) {
                        $value = $row->getMetricValues()[0]->getValue();
                        $total_users_30_days += (int)$value;
                    }
                }
            } catch (Exception $e) {

                $total_users_30_days = 0;
            }

            return array(
                "success" => true,
                "message" => "✓ เชื่อมต่อสำเร็จ! สามารถดึงข้อมูลได้",
                "active_users" => $total_active_users,
                "total_users_30_days" => $total_users_30_days,
                "data" => array(
                    "active_users" => $total_active_users,
                    "total_users_30_days" => $total_users_30_days
                )
            );

        } catch (Exception $e) {
            $error_message = $e->getMessage();
            $details = $e->getTraceAsString();


            if (strpos($error_message, 'API hasn\'t been used') !== false) {
                $error_message = 'API ยังไม่ได้ Enable ใน Cloud Console';
                $details = 'คุณต้องเปิดใช้งาน Google Analytics Data API ใน Google Cloud Console ก่อน';
            } elseif (strpos($error_message, 'PERMISSION_DENIED') !== false) {
                $error_message = 'Service Account ไม่มีสิทธิ์ Viewer ใน GA4';
                $details = 'ตรวจสอบว่าได้เพิ่ม Service Account เป็น Viewer ใน GA4 Property แล้ว';
            } elseif (strpos($error_message, 'INVALID_ARGUMENT') !== false) {
                $error_message = 'Property ID ไม่ถูกต้อง';
                $details = 'ตรวจสอบว่า Property ID ถูกต้อง (ตัวอย่าง: *********)';
            }

            return array(
                "success" => false,
                "message" => $error_message,
                "details" => $details
            );
        }
    }
}

class AMP_Google_Analytics {
    private $client;
    private $analytics;
    private $property_id;
    
    public function __construct() {
        $this->property_id = get_option('ga4_property_id', '');
        $this->init_client();
    }
    
    private function init_client() {
        try {
            $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
            $json_file_path = $plugin_dir . 'admin/analytics/analytic-pro.json';
            if (!amp_ensure_ga_autoloader() || !file_exists($json_file_path)) {
                return false;
            }
            if (!class_exists('Google\\Client') || !class_exists('Google\\Service\\AnalyticsData')) {
                return false;
            }
            $this->client = new Google\Client();
            $this->client->setAuthConfig($json_file_path);
            $this->client->addScope('https://www.googleapis.com/auth/analytics.readonly');
            $this->analytics = new Google\Service\AnalyticsData($this->client);
            return true;
        } catch (Exception $e) {
            error_log('GA4 Init Error: ' . $e->getMessage());
            return false;
        }
    }
    
    public function is_configured() {
        return !empty($this->property_id) && 
               $this->client !== null && 
               $this->analytics !== null;
    }
    
    public function get_realtime_users() {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }
        
        try {
            $request = new Google\Service\AnalyticsData\RunRealtimeReportRequest();
            $request->setDimensions([new Google\Service\AnalyticsData\Dimension(['name' => 'country'])]);
            $request->setMetrics([new Google\Service\AnalyticsData\Metric(['name' => 'activeUsers'])]);
            
            $response = $this->analytics->properties->runRealtimeReport('properties/' . $this->property_id, $request);
            
            $total = 0;
            if (isset($response['rows']) && is_array($response['rows'])) {
                foreach ($response['rows'] as $row) {
                    $total += (int)$row['metricValues'][0]['value'];
                }
            }
            
            $result = ['total' => $total];
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }
    
    public function get_total_users($days = 30) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }

        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_total_users_' . $days . 'days';
        $cached_data = $cache->get($cache_key, 'ga_analytics');
        if (false !== $cached_data) {
            return $cached_data;
        }

        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => ($days + 1) . 'daysAgo', 'endDate' => 'yesterday']
                ],
                'metrics' => [
                    ['name' => 'activeUsers']
                ]
            ]);

            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);

            $total = 0;
            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                foreach ($response->getRows() as $row) {
                    $total += (int)$row->getMetricValues()[0]->getValue();
                }
            }

            $result = ['total' => $total];
            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    public function get_total_pageviews($days = 30) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }

        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_total_pageviews_' . $days . 'days';
        $cached_data = $cache->get($cache_key, 'ga_analytics');
        if (false !== $cached_data) {
            return $cached_data;
        }

        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => ($days + 1) . 'daysAgo', 'endDate' => 'yesterday']
                ],
                'metrics' => [
                    ['name' => 'screenPageViews']
                ]
            ]);

            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);

            $total = 0;
            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                foreach ($response->getRows() as $row) {
                    $total += (int)$row->getMetricValues()[0]->getValue();
                }
            }

            $result = ['total' => $total];
            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    public function get_total_pageviews_exclude_homepage($days = 30) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }

        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_total_pageviews_exclude_homepage_' . $days . 'days';
        $cached_data = $cache->get($cache_key, 'ga_analytics');
        if (false !== $cached_data) {
            return $cached_data;
        }

        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => ($days + 1) . 'daysAgo', 'endDate' => 'yesterday']
                ],
                'dimensions' => [
                    ['name' => 'pagePath']
                ],
                'metrics' => [
                    ['name' => 'screenPageViews']
                ],
                'dimensionFilter' => [
                    'notExpression' => [
                        'filter' => [
                            'fieldName' => 'pagePath',
                            'stringFilter' => [
                                'matchType' => 'EXACT',
                                'value' => '/'
                            ]
                        ]
                    ]
                ]
            ]);

            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);

            $total = 0;
            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                foreach ($response->getRows() as $row) {
                    $total += (int)$row->getMetricValues()[0]->getValue();
                }
            }

            $result = ['total' => $total];
            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    public function get_daily_users_data($days = 90) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }

        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_daily_users_' . $days . '_days';
        $cached_result = $cache->get($cache_key, 'ga_analytics');

        if ($cached_result !== false) {
            return $cached_result;
        }

        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => ($days + 1) . 'daysAgo', 'endDate' => 'yesterday']
                ],
                'dimensions' => [
                    ['name' => 'date']
                ],
                'metrics' => [
                    ['name' => 'activeUsers']
                ],
                'orderBys' => [
                    ['dimension' => ['dimensionName' => 'date']]
                ]
            ]);

            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);

            $dates = [];
            $values = [];

            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                foreach ($response->getRows() as $row) {
                    $date = $row->getDimensionValues()[0]->getValue();
                    $users = (int)$row->getMetricValues()[0]->getValue();

                    $formatted_date = date('d M', strtotime($date));
                    $dates[] = $formatted_date;
                    $values[] = $users;
                }
            }

            $result = [
                'success' => true,
                'dates' => $dates,
                'values' => $values,
                'total_days' => count($dates)
            ];

            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    public function get_last_24_hours_users_data() {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }
    
        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_last_24_completed_hours';
        $cached_result = $cache->get($cache_key, 'ga_analytics');
    
        if ($cached_result !== false) {
            return $cached_result;
        }
    
        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => '2daysAgo', 'endDate' => 'today']
                ],
                'dimensions' => [
                    ['name' => 'dateHour']
                ],
                'metrics' => [
                    ['name' => 'activeUsers']
                ],
                'orderBys' => [
                    ['dimension' => ['dimensionName' => 'dateHour']]
                ]
            ]);
    
            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);
    
            $dates = [];
            $values = [];
            $all_rows = [];
    
            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                $all_rows = $response->getRows();
            }
    
            $current_utc_hour = gmdate('YmdH');
            
            $completed_hour_rows = [];
            foreach ($all_rows as $row) {
                $row_date_hour = $row->getDimensionValues()[0]->getValue();
                if ($row_date_hour < $current_utc_hour) {
                    $completed_hour_rows[] = $row;
                }
            }
            
            $last_24_rows = array_slice($completed_hour_rows, -24);
    
            foreach ($last_24_rows as $row) {
                $dateHour = $row->getDimensionValues()[0]->getValue();
                $users = (int)$row->getMetricValues()[0]->getValue();
    
                $datetime = DateTime::createFromFormat('YmdH', $dateHour);
                if ($datetime) {
                    $formatted_date = $datetime->format('d M H:00');
                    $dates[] = $formatted_date;
                    $values[] = $users;
                }
            }
    
            $result = [
                'success' => true,
                'dates' => $dates,
                'values' => $values,
                'total_days' => count($dates)
            ];
    
            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    public function get_top_pages($days = 30, $limit = 10) {
        if (!$this->is_configured()) {
            return new WP_Error('not_configured', 'Google Analytics not configured');
        }

        $cache = \AMP_Cache_Manager::instance();
        $cache_key = 'ga_top_pages_' . $days . 'days_' . $limit . 'limit';
        $cached_data = $cache->get($cache_key, 'ga_analytics');
        if (false !== $cached_data) {
            return $cached_data;
        }

        try {
            $request = new Google\Service\AnalyticsData\RunReportRequest([
                'dateRanges' => [
                    ['startDate' => ($days + 1) . 'daysAgo', 'endDate' => 'yesterday']
                ],
                'dimensions' => [
                    ['name' => 'pagePath'],
                    ['name' => 'pageTitle']
                ],
                'metrics' => [
                    ['name' => 'screenPageViews']
                ],
                'orderBys' => [
                    ['metric' => ['metricName' => 'screenPageViews'], 'desc' => true]
                ],
                'limit' => $limit
            ]);

            $response = $this->analytics->properties->runReport('properties/' . $this->property_id, $request);

            $pages = [];
            $total_pageviews = 0;

            if ($response && $response->getRows() && count($response->getRows()) > 0) {
                foreach ($response->getRows() as $row) {
                    $pagePath = $row->getDimensionValues()[0]->getValue();
                    $pageTitle = $row->getDimensionValues()[1]->getValue();
                    $pageviews = (int)$row->getMetricValues()[0]->getValue();
                    $cleanTitle = $this->clean_page_title($pageTitle, $pagePath);
                    $pages[] = [
                        'page' => $cleanTitle,
                        'path' => $pagePath,
                        'visitors' => $pageviews
                    ];

                    $total_pageviews += $pageviews;
                }
            }

            $result = [
                'success' => true,
                'pages' => $pages,
                'total_pageviews' => $total_pageviews
            ];

            $cache->set($cache_key, $result, 3600, 'ga_analytics');
            return $result;
        } catch (Exception $e) {
            return new WP_Error('api_error', $e->getMessage());
        }
    }

    private function clean_page_title($title, $path) {
        if (empty($title) || $title === '(not set)' || $title === 'not set') {
            if ($path === '/') {
                return 'หน้าแรก';
            }
            $cleanPath = trim($path, '/');
            $cleanPath = str_replace(['-', '_'], ' ', $cleanPath);
            $cleanPath = ucwords($cleanPath);
            return $cleanPath ?: 'หน้าแรก';
        }
        $title = preg_replace('/\s*\|\s*.*$/', '', $title); 
        $title = preg_replace('/\s*-\s*.*$/', '', $title);  

        return trim($title) ?: 'ไม่มีชื่อ';
    }
}