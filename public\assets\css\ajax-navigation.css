
.ad-dashboard-main {
    transition: opacity 0.3s ease;
    position: relative;
    max-width: 1600px;
    display: flex;
;
    margin: 0 auto;
}

.ad-dashboard-main.loading {
    opacity: 0.7;
}

.tab-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
}

.tab-loading i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

.tab-loading span {
    font-size: 1.2rem;
    color: var(--text-color);
}

.tab-loading-progress {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    max-width: 500px;
    text-align: center;
    padding: 2rem;
}

.progress-container {
    background: var(--theme-gradient-light);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--divider-color);
    width: 100%;
}

.progress-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 1.5rem;
}

.progress-header i {
    font-size: 1.8rem;
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

.progress-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
}

.progress-bar-container {
    width: 100%;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: rgba(67, 97, 238, 0.1);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    margin-bottom: 1rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4361ee, #7209b7, #f72585);
    background-size: 200% 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    animation: progressShimmer 2s infinite;
    position: relative;
}

.progress-fill.progress-error {
    background: linear-gradient(90deg, #e74c3c, #c0392b);
    animation: none;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-color);
}

#progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1rem;
}

#progress-status {
    opacity: 0.8;
    font-style: italic;
}

.retry-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.retry-btn:hover {
    background: var(--primary-color-dark);
    transform: translateY(-2px);
}

@keyframes progressShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

.tab-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background: var(--theme-gradient-light);
    border-radius: 16px;
    margin: 2rem auto;
    max-width: 600px;
    border: 1px solid var(--divider-color);
}

.tab-error i {
    font-size: 3rem;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.tab-error span {
    font-size: 1.2rem;
    color: var(--text-color);
}

.tab-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    background: var(--theme-gradient-light);
    border-radius: 16px;
    margin: 2rem auto;
    max-width: 600px;
    border: 1px solid var(--divider-color);
}

.tab-success i {
    font-size: 3rem;
    color: #27ae60;
    margin-bottom: 1rem;
}

.tab-success h3 {
    font-size: 1.5rem;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.tab-success p {
    font-size: 1rem;
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

body.dark-mode .tab-loading i {
    color: var(--primary-color);
}

body.dark-mode .progress-container {
    background: rgba(30, 30, 30, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .progress-bar {
    background: rgba(255, 255, 255, 0.1);
}

body.dark-mode .progress-title,
body.dark-mode .progress-text {
    color: rgba(255, 255, 255, 0.9);
}

body.dark-mode #progress-status {
    color: rgba(255, 255, 255, 0.7);
}

body.dark-mode .tab-loading span,
body.dark-mode .tab-error span {
    color: var(--light-text);
}

body.dark-mode .tab-error {
    background-color: rgba(231, 76, 60, 0.05);
}

@media (max-width: 768px) {
    .tab-loading i {
        font-size: 2rem;
    }

    .tab-loading span,
    .tab-error span {
        font-size: 1rem;
    }

    .tab-error i {
        font-size: 2.5rem;
    }
}
