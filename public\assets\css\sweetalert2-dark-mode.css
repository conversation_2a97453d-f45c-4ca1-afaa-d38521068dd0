.dark-mode .swal2-container {
    background-color: rgba(0, 0, 0, 0.6) !important;
}
.dark-mode .swal2-popup {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border: 1px solid var(--border-color);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3) !important;
}
.dark-mode .swal2-title {
    color: var(--text-color) !important;
}
.dark-mode .swal2-html-container {
    color: var(--light-text) !important;
}
.dark-mode .swal2-styled.swal2-confirm {
    background-color: var(--primary-color) !important;
}

.dark-mode .swal2-styled.swal2-cancel {
    background-color: #6c757d !important;
}

.dark-mode .swal2-styled.swal2-deny {
    background-color: var(--error-color) !important;
}

.dark-mode .swal2-input,
.dark-mode .swal2-textarea,
.dark-mode .swal2-select,
.dark-mode .swal2-radio,
.dark-mode .swal2-checkbox {
    background-color: var(--input-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

.dark-mode .swal2-input:focus,
.dark-mode .swal2-textarea:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25) !important;
}

.dark-mode .swal2-icon.swal2-error {
    border-color: var(--error-color) !important;
    color: var(--error-color) !important;
}

.dark-mode .swal2-icon.swal2-warning {
    border-color: #f1c40f !important;
    color: #f1c40f !important;
}

.dark-mode .swal2-icon.swal2-success {
    border-color: var(--success-color) !important;
    color: var(--success-color) !important;
}

.dark-mode .swal2-icon.swal2-info {
    border-color: #3498db !important;
    color: #3498db !important;
}

.dark-mode .swal2-icon.swal2-question {
    border-color: #9b59b6 !important;
    color: #9b59b6 !important;
}

.swal2-success-line-tip,
.swal2-success-line-long {
    background-color: white !important;
}

.dark-mode .swal2-success-line-tip,
.dark-mode .swal2-success-line-long {
    background-color: white !important;
}

.swal2-icon.swal2-success [class^='swal2-success-line'] {
    background-color: white !important;
}

.dark-mode .swal2-icon.swal2-success [class^='swal2-success-line'] {
    background-color: white !important;
}

.swal2-icon.swal2-success .swal2-success-ring {
    border-color: var(--success-color) !important;
}

.dark-mode .swal2-icon.swal2-success .swal2-success-ring {
    border-color: var(--success-color) !important;
}

.swal2-icon.swal2-success::before,
.swal2-icon.swal2-success::after {
    background-color: white !important;
}

.dark-mode .swal2-icon.swal2-success::before,
.dark-mode .swal2-icon.swal2-success::after {
    background-color: white !important;
}

.swal2-icon.swal2-success {
    border-color: var(--success-color) !important;
}

.swal2-icon.swal2-success .swal2-success-line-tip,
.swal2-icon.swal2-success .swal2-success-line-long {
    background-color: white !important;
    height: 5px !important;
    border-radius: 2px !important;
}

.dark-mode .swal2-icon.swal2-success .swal2-success-line-tip,
.dark-mode .swal2-icon.swal2-success .swal2-success-line-long {
    background-color: white !important;
    height: 5px !important;
    border-radius: 2px !important;
}

.swal2-icon.swal2-success .swal2-success-line-tip {
    left: 28px !important;
    top: 46px !important;
    width: 16px !important;
    transform: rotate(45deg) !important;
}

.swal2-icon.swal2-success .swal2-success-line-long {
    right: 8px !important;
    top: 38px !important;
    width: 47px !important;
    transform: rotate(-45deg) !important;
}

.swal2-icon.swal2-success .swal2-success-ring {
    display: none !important;
}

.swal2-icon.swal2-success {
    background: transparent !important;
    border: none !important;
}

.dark-mode .swal2-icon.swal2-success {
    background: transparent !important;
    border: none !important;
}

.dark-mode .swal2-close {
    color: var(--light-text) !important;
}

.dark-mode .swal2-close:hover {
    color: var(--error-color) !important;
}

.dark-mode .swal2-timer-progress-bar {
    background-color: var(--primary-color) !important;
}

.dark-mode .duration-popup .duration-option {
    background-color: var(--input-bg) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-color) !important;
}

.dark-mode .duration-popup .duration-option.selected {
    border-color: var(--primary-color) !important;
    background-color: rgba(67, 97, 238, 0.1) !important;
}

.dark-mode .duration-popup .duration-option-price {
    color: var(--primary-color) !important;
}

.dark-mode .duration-popup .duration-option-discount {
    background-color: rgba(46, 204, 113, 0.1) !important;
    color: var(--success-color) !important;
}

.dark-mode .edit-ad-popup .edit-ad-form input,
.dark-mode .edit-ad-popup .edit-ad-form textarea {
    background-color: var(--input-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

.dark-mode .edit-ad-popup .edit-ad-form label {
    color: var(--text-color) !important;
}

.dark-mode .swal2-toast {
    background-color: var(--card-bg) !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2) !important;
}

.dark-mode .swal2-toast .swal2-title {
    color: var(--text-color) !important;
}

.swal2-actions {
    justify-content: center !important;
    text-align: center !important;
}

.swal2-styled {
    text-decoration: none !important;
}

.swal2-styled:hover {
    text-decoration: none !important;
}

.dark-mode .modern-session-popup {
    background: linear-gradient(145deg, #1a1d29, #252837) !important;
    box-shadow:
        0 32px 64px rgba(0, 0, 0, 0.4),
        0 16px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.dark-mode .modern-session-warning {
    color: var(--text-color);
}

.dark-mode .warning-title {
    color: var(--text-color) !important;
}

.dark-mode .warning-message {
    color: var(--light-text) !important;
}

.dark-mode .warning-message strong {
    color: #ff6b6b !important;
}

.dark-mode .warning-info-box {
    background: linear-gradient(135deg, rgba(255, 243, 224, 0.1), rgba(255, 236, 179, 0.1)) !important;
    border: 1px solid rgba(255, 167, 38, 0.3) !important;
}

.dark-mode .warning-info-box i {
    color: #ffa726 !important;
}

.dark-mode .warning-info-box span {
    color: var(--light-text) !important;
}

.dark-mode .modern-btn-primary {
    background: linear-gradient(135deg, #4361ee, #7209b7) !important;
}

.dark-mode .modern-btn-secondary {
    background: linear-gradient(135deg, #495057, #343a40) !important;
}

.dark-mode .warning-info-box[style*="rgba(231, 76, 60"] {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1)) !important;
    border: 1px solid rgba(231, 76, 60, 0.3) !important;
}

.dark-mode .warning-info-box[style*="rgba(231, 76, 60"] i {
    color: #e74c3c !important;
}

.dark-mode .warning-info-box[style*="rgba(231, 76, 60"] span {
    color: var(--light-text) !important;
}
