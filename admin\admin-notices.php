<?php
if (!defined('ABSPATH')) {
    exit;
}

add_action('admin_notices', 'amp_show_installation_notices');

function amp_show_installation_notices() {
    if (!current_user_can('activate_plugins')) {
        return;
    }
    global $wpdb;
    $required_tables = [
        $wpdb->prefix . 'ad_payments',
        $wpdb->prefix . 'ad_positions',
        $wpdb->prefix . 'ad_discount_rates',
        $wpdb->prefix . 'ad_price_calculation',
        $wpdb->prefix . 'ad_price_global_settings',
        $wpdb->prefix . 'plisio_settings',
        $wpdb->prefix . 'ad_login_attempts'
    ];
    $missing_tables = [];
    if (is_array($required_tables)) {
        foreach ($required_tables as $table) {
            if (is_string($table) && $wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $table)) !== $table) {
                $missing_tables[] = $table;
            }
        }
    }
    if (!empty($missing_tables)) {
        ?>
        <div class="notice notice-error is-dismissible">
            <h3>🚨 Ad Management Pro - Database Setup Required</h3>
            <p><strong>Missing tables:</strong> <?php echo implode(', ', $missing_tables); ?></p>
            <p>The plugin cannot function properly without these database tables.</p>
            <p>
                <button type="button" class="button button-primary" onclick="ampDeactivateReactivate()">
                    🔄 Deactivate & Reactivate Plugin
                </button>
                <button type="button" class="button button-secondary" onclick="ampCreateTables()">
                    🔧 Create Tables Only
                </button>
            </p>
            <script>
            function ampCreateTables() {
                if (confirm('This will attempt to create the missing database tables. Continue?')) {
                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', ajaxurl, true);
                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            if (xhr.status === 200) {
                                alert('Tables created successfully! Please refresh the page.');
                                location.reload();
                            } else {
                                alert('Error creating tables. Please try deactivating and reactivating the plugin.');
                            }
                        }
                    };
                    xhr.send('action=amp_create_tables&nonce=' + encodeURIComponent('<?php echo wp_create_nonce("amp_create_tables"); ?>'));
                }
            }

            function ampDeactivateReactivate() {
                if (confirm('This will deactivate and reactivate the plugin to trigger table creation. Continue?')) {
                    window.location.href = '<?php echo wp_nonce_url(admin_url('plugins.php?action=deactivate&plugin=ad-management-pro/ad-management-pro.php'), 'deactivate-plugin_ad-management-pro/ad-management-pro.php'); ?>';
                }
            }
            </script>
        </div>
        <?php
    }
    
    $google_api_setup_needed = get_option('amp_google_api_setup_needed', false);
    $google_api_dir = plugin_dir_path(dirname(__FILE__)) . 'admin/google-api-client';
    $autoload_exists = file_exists($google_api_dir . '/vendor/autoload.php');

    if (!$autoload_exists) {
        $installation_error = get_option('amp_google_api_error', '');
        ?>
        <div class="notice notice-warning">
            <h3>⚠️ Ad Management Pro - Google API Client ยังไม่ได้ติดตั้ง</h3>
            <p>Google API Client จำเป็นสำหรับการเชื่อมต่อกับ Google Analytics</p>
            <?php if (!empty($installation_error)): ?>
                <p><strong>ข้อผิดพลาดล่าสุด:</strong> <?php echo esc_html($installation_error); ?></p>
            <?php endif; ?>
            <p>
                <strong>วิธีแก้ไข:</strong>
                <button type="button" id="retry-google-api-install" class="button button-primary">
                    🔄 ติดตั้งตอนนี้
                </button>
                <a href="<?php echo admin_url('plugins.php'); ?>" class="button button-secondary">
                    🔄 Deactivate และ Activate Plugin ใหม่
                </a>
            </p>
            <script>
            document.getElementById('retry-google-api-install').addEventListener('click', function() {
                var button = this;
                button.textContent = '⏳ กำลังติดตั้ง...';
                button.disabled = true;
                
                fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=retry_google_api_install&nonce=<?php echo wp_create_nonce('retry_google_api'); ?>'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        button.textContent = '✅ ติดตั้งสำเร็จ';
                        button.style.backgroundColor = '#4CAF50';
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    } else {
                        button.textContent = '❌ ล้มเหลว - ลองใหม่';
                        button.disabled = false;
                        alert('การติดตั้งล้มเหลว: ' + (data.data || 'Unknown error'));
                    }
                })
                .catch(error => {
                    button.textContent = '❌ เกิดข้อผิดพลาด';
                    button.disabled = false;
                    alert('เกิดข้อผิดพลาด: ' + error.message);
                });
            });
            </script>
        </div>
        <?php
    }
}

add_action('admin_notices', 'amp_show_success_notices');
function amp_show_success_notices() {
    if (get_transient('amp_installation_success')) {
        ?>
        <div class="notice notice-success is-dismissible">
            <h3>🎉 Ad Management Pro - Installation Completed!</h3>
            <p>All database tables have been created successfully.</p>
            <p>
                <a href="<?php echo admin_url('admin.php?page=ad-management-dashboard'); ?>" class="button button-primary">
                    📊 Go to Dashboard
                </a>
                <a href="<?php echo admin_url('admin.php?page=general-settings'); ?>" class="button">
                    ⚙️ Settings
                </a>
            </p>
        </div>
        <?php
        delete_transient('amp_installation_success');
    }
}

function handle_retry_google_api_install() {
    if (!current_user_can('activate_plugins')) {
        wp_die('Insufficient permissions');
    }
    $nonce = sanitize_text_field($_POST['nonce']);
    if (!wp_verify_nonce($nonce, 'retry_google_api')) {
        wp_send_json_error('Invalid nonce');
        return;
    }
    if (class_exists('AdManagementPro')) {
        $plugin = AdManagementPro::instance();
        if (method_exists($plugin, 'install_google_api_client')) {
            if ($plugin->install_google_api_client()) {
                delete_option('amp_google_api_error');
                update_option('amp_google_api_setup_needed', false);
                update_option('amp_google_api_installed', true);
                wp_send_json_success('Google API Client installed successfully');
            } else {
                $error = get_option('amp_google_api_error', 'Unknown error occurred');
                wp_send_json_error($error);
            }
        } else {
            wp_send_json_error('Installation method not available');
        }
    } else {
        wp_send_json_error('Plugin class not found');
    }
}
add_action('wp_ajax_retry_google_api_install', 'handle_retry_google_api_install');