<?php
if (!defined('WPINC')) {
    die;
}

function display_google_settings_content() {
    if (!current_user_can('manage_options')) {
        return;
    }

    $notice = '';

    $ga_view_id = get_option('ga_view_id', '');
    $ga_client_id = get_option('ga_client_id', '');
    $ga_client_secret = get_option('ga_client_secret', '');
    $ga_nonce = wp_create_nonce('save_ga_settings');

    $ga4_nonce = wp_create_nonce('ga4_service_account_nonce');
    $google_settings_nonce = wp_create_nonce('google_settings_nonce');

    echo $notice;
    ?>

    <p>ตั้งค่าการเชื่อมต่อกับบริการของ Google</p>

    <form method="post" action="" id="google-settings-form">
        <input type="hidden" name="google_settings_nonce" value="<?php echo esc_attr($google_settings_nonce); ?>">

        <div class="plisio-settings-grid">
            <div class="plisio-card">
                <h3><i class="fab fa-google"></i> Google Analytics 4 (Service Account)</h3>
                <table class="form-table">
            <tr>
                <th scope="row"><label>สถานะ Google API Client</label></th>
                <td>
                    <?php
                    $client_installed = false;
                    $client_version = 'ไม่ได้ติดตั้ง';
                    $client_status_class = 'error';

                    $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
                    $admin_dir = $plugin_dir . 'admin';
                    $google_api_client_dir = $admin_dir . '/google-api-client';
                    $autoload_path = $google_api_client_dir . '/vendor/autoload.php';

                    $debug_info = '';
                    $debug_info .= 'Client directory: ' . $google_api_client_dir . ' - ' . (file_exists($google_api_client_dir) ? 'exists' : 'not found') . "\n";
                    $debug_info .= 'Autoload path: ' . $autoload_path . ' - ' . (file_exists($autoload_path) ? 'exists' : 'not found') . "\n";

                    $autoload_works = false;
                    if (file_exists($autoload_path)) {
                        try {
                            require_once $autoload_path;
                            $autoload_works = class_exists('Google\\Client') || class_exists('Google_Client');
                            $debug_info .= 'Autoloader test: ' . ($autoload_works ? 'works' : 'failed') . "\n";
                            $debug_info .= 'Google\\Client class: ' . (class_exists('Google\\Client') ? 'available' : 'not available') . "\n";
                            $debug_info .= 'Google\\Service\\AnalyticsData class: ' . (class_exists('Google\\Service\\AnalyticsData') ? 'available' : 'not available') . "\n";
                        } catch (Exception $e) {
                            $debug_info .= 'Autoloader error: ' . $e->getMessage() . "\n";
                        }
                    }

                    $vendor_autoload = $google_api_client_dir . '/vendor/autoload.php';
                    $src_client_file = $google_api_client_dir . '/src/Google/Client.php';
                    
                    if (file_exists($vendor_autoload) && (file_exists($src_client_file) || $autoload_works)) {
                        $client_installed = true;
                        $client_status_class = 'success';
                        $client_version = 'ติดตั้งแล้ว';

                        $composer_file = $google_api_client_dir . '/vendor/composer/installed.json';
                        if (file_exists($composer_file)) {
                            $composer_data = json_decode(file_get_contents($composer_file), true);
                            if (isset($composer_data['packages']) && is_array($composer_data['packages'])) {
                                foreach ($composer_data['packages'] as $package) {
                                    if (isset($package['name']) && $package['name'] === 'google/apiclient') {
                                        if (isset($package['version'])) {
                                            $client_version = $package['version'];
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        $client_installed = false;
                        $client_status_class = 'error';
                        $client_version = 'ไม่ได้ติดตั้ง';
                    }
                    ?>
                    <div class="ga4-client-status <?php echo $client_status_class; ?>">
                        <?php if ($client_installed): ?>
                            <i class="fas fa-check-circle"></i> Google API PHP Client: <strong><?php echo esc_html($client_version); ?></strong>
                        <?php else: ?>
                            <i class="fas fa-exclamation-circle"></i> Google API PHP Client: <strong><?php echo esc_html($client_version); ?></strong>
                        <?php endif; ?>
                    </div>

                    <?php if (!$client_installed): ?>
                    <div class="status error" style="margin-top: 15px; padding: 10px; background: #f8d7da; color: #721c24; border-radius: 5px;">
                        <p><strong>⚠️ Google API Client ยังไม่ได้ติดตั้ง</strong></p>
                        <p>กรุณา deactivate และ activate plugin ใหม่เพื่อให้ระบบติดตั้งอัตโนมัติ</p>
                    </div>
                    <p class="description" style="margin-top: 10px;">
                        Google API Client จำเป็นสำหรับการเชื่อมต่อกับ Google Analytics และ Google OAuth<br>
                        ระบบจะติดตั้งอัตโนมัติเมื่อ activate plugin
                    </p>
                    <?php elseif (file_exists($vendor_autoload) && isset($autoload_works) && $autoload_works === false): ?>
                    <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
                    <div class="ga4-debug-info" style="margin-top: 10px; padding: 10px; background: #fff3cd; border-left: 4px solid #ffc107; font-family: monospace; white-space: pre-wrap; font-size: 12px;">
                        <strong>⚠️ Google API Client ติดตั้งแล้วแต่ทำงานไม่ปกติ:</strong><br>
                        <?php echo esc_html($debug_info); ?>
                        <p style="margin-top: 10px; color: #856404;"><strong>แนะนำ:</strong> ลองติดตั้งใหม่โดยการ deactivate และ activate plugin อีกครั้ง</p>
                    </div>
                    <?php endif; ?>
                    <?php endif; ?>
                    <p class="description">สถานะการติดตั้ง Google API PHP Client สำหรับเชื่อมต่อกับ Google Analytics</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="ga4_property_id">Property ID</label></th>
                <td>
                    <input type="text" id="ga4_property_id" name="ga4_property_id" value="<?php echo esc_attr(get_option('ga4_property_id', '')); ?>" class="regular-text">
                    <p class="description">Google Analytics 4 Property ID (เช่น 123456789) สำหรับดึงข้อมูลผู้เข้าชม</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="use_ga_for_pricing">ใช้ Google Analytics ในการคำนวณราคา</label></th>
                <td>
                    <div class="toggle-switch-container">
                        <?php $use_ga = get_option('use_ga_for_pricing', 'yes'); ?>
                        <label class="toggle-switch">
                            <input type="checkbox" name="use_ga_for_pricing" id="use_ga_for_pricing" value="yes" <?php checked($use_ga, 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label"><?php echo $use_ga === 'yes' ? 'เปิดใช้งาน' : 'ปิดใช้งาน'; ?></span>
                    </div>
                    <p class="description">เลือกว่าจะใช้ข้อมูลจำนวนผู้เข้าชมจาก Google Analytics หรือใช้ค่าที่ตั้งไว้ในการคำนวณราคา</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="exclude_homepage_from_pageviews">กรอง Page Views หน้าแรก</label></th>
                <td>
                    <div class="toggle-switch-container">
                        <?php $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no'); ?>
                        <label class="toggle-switch">
                            <input type="checkbox" name="exclude_homepage_from_pageviews" id="exclude_homepage_from_pageviews" value="yes" <?php checked($exclude_homepage, 'yes'); ?>>
                            <span class="toggle-slider"></span>
                        </label>
                        <span class="toggle-label"><?php echo $exclude_homepage === 'yes' ? 'กรองหน้าแรก' : 'รวมหน้าแรก'; ?></span>
                    </div>
                    <p class="description">เลือกว่าจะกรองหน้าแรกออกจากการนับ Page Views ใน Dashboard หรือไม่ (ใช้สำหรับ Dashboard User เท่านั้น)</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="pageviews_reduction_percentage">เปอร์เซ็นต์การลด Page Views</label></th>
                <td>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <?php $reduction_percentage = get_option('pageviews_reduction_percentage', '9'); ?>
                        <input type="number" name="pageviews_reduction_percentage" id="pageviews_reduction_percentage"
                               value="<?php echo esc_attr($reduction_percentage); ?>"
                               min="0" max="50" step="0.1" class="small-text" style="width: 80px;">
                        <span>%</span>
                    </div>
                    <p class="description">กำหนดเปอร์เซ็นต์การลด Page Views เพิ่มเติม เมื่อเปิดการกรองหน้าแรก (เพื่อคำนึงถึงหน้าอื่นๆ ที่ไม่ได้นำมาคำนวณ)</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="ga4_service_account_json">Service Account JSON</label></th>
                <td>
                    <div class="ga4-json-upload-container">
                        <?php
                        $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
                        $admin_dir = $plugin_dir . 'admin';
                        $json_file_path = $admin_dir . '/analytics/analytic-pro.json';
                        $json_file_url = plugins_url('admin/analytics/analytic-pro.json', dirname(dirname(__FILE__)));
                        $json_file_exists = file_exists($json_file_path);

                        if ($json_file_exists):
                        ?>
                            <div class="ga4-json-file-info">
                                <i class="fas fa-file-code"></i>
                                <span>analytic-pro.json</span>
                                <a href="<?php echo esc_url($json_file_url); ?>" target="_blank" class="button button-small">
                                    <i class="fas fa-eye"></i> ดูไฟล์
                                </a>
                                <button type="button" id="ga4-remove-json" class="button button-small">
                                    <i class="fas fa-trash"></i> ลบไฟล์
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="ga4-json-upload-ui">
                                <div class="ga4-json-dropzone" id="ga4-json-dropzone">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>ลากไฟล์ JSON มาวางที่นี่ หรือ <span>คลิกเพื่อเลือกไฟล์</span></p>
                                    <input type="file" id="ga4_json_file" name="ga4_json_file" accept=".json" style="display: none;">
                                </div>
                                <div class="ga4-json-progress" style="display: none;">
                                    <div class="ga4-json-progress-bar"></div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <p class="description">อัพโหลดไฟล์ Service Account JSON จาก Google Cloud Console</p>
                    </div>
                </td>
            </tr>
            <tr>
                <th scope="row"><label>การทดสอบการเชื่อมต่อ</label></th>
                <td>
                    <button type="button" id="ga4-test-connection" class="ad-btn ad-btn-secondary">
                        <i class="fas fa-sync-alt"></i> ทดสอบการเชื่อมต่อ GA4
                    </button>
                    <div id="ga4-test-result" style="margin-top: 10px;"></div>
                </td>
            </tr>
        </table>
            </div>

            <div class="plisio-card">
                <h3><i class="fab fa-google"></i> Google OAuth Login</h3>
                <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="google_login_enabled">เปิดใช้งาน Google Login</label>
                </th>
                <td>
                    <label>
                        <input type="checkbox" id="google_login_enabled" name="google_login_enabled" <?php checked(get_option('amp_google_login_enabled', 0), 1); ?>>
                        เปิดใช้งานการเข้าสู่ระบบด้วย Google
                    </label>
                    <p class="description">อนุญาตให้ผู้ใช้เข้าสู่ระบบด้วยบัญชี Google ของพวกเขา</p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="google_client_id">Google Client ID</label>
                </th>
                <td>
                    <?php
                    $encryption_manager = \AMP_Encryption_Manager::instance();
                    $has_client_id = !empty($encryption_manager->get_secret('amp_google_client_id'));
                    if ($has_client_id): ?>
                        <div class="secret-key-display">
                            <span class="secret-placeholder">••••••••••••••••••••••••••••••••</span>
                            <button type="button" class="button button-secondary delete-secret-btn" data-secret="amp_google_client_id" data-name="Google Client ID">
                                🗑️ ลบ
                            </button>
                        </div>
                    <?php else: ?>
                        <input type="text" id="google_client_id" name="google_client_id" value="" class="regular-text" placeholder="กรอก Google Client ID">
                    <?php endif; ?>
                    <p class="description">ใส่ Google Client ID ของคุณจาก <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console</a></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="google_client_secret">Google Client Secret</label>
                </th>
                <td>
                    <?php
                    $has_client_secret = !empty($encryption_manager->get_secret('amp_google_client_secret'));
                    if ($has_client_secret): ?>
                        <div class="secret-key-display">
                            <span class="secret-placeholder">••••••••••••••••••••••••••••••••</span>
                            <button type="button" class="button button-secondary delete-secret-btn" data-secret="amp_google_client_secret" data-name="Google Client Secret">
                                🗑️ ลบ
                            </button>
                        </div>
                    <?php else: ?>
                        <input type="password" id="google_client_secret" name="google_client_secret" value="" class="regular-text" placeholder="กรอก Google Client Secret">
                    <?php endif; ?>
                    <p class="description">ใส่ Google Client Secret จาก Google Cloud Console ของคุณ</p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="google_redirect_uri">Redirect URIs</label>
                </th>
                <td>
                    <div style="margin-bottom: 10px;">
                        <label><strong>Login Page:</strong></label>
                        <input type="text" value="<?php echo esc_url(home_url('/login/?action=google_callback')); ?>" class="regular-text" readonly>
                        <button type="button" class="button button-secondary copy-uri-btn" data-uri="<?php echo esc_url(home_url('/login/?action=google_callback')); ?>">คัดลอก</button>
                    </div>
                    <div>
                        <label><strong>Dashboard Page:</strong></label>
                        <input type="text" value="<?php echo esc_url(home_url('/dashboard/?action=google_callback')); ?>" class="regular-text" readonly>
                        <button type="button" class="button button-secondary copy-uri-btn" data-uri="<?php echo esc_url(home_url('/dashboard/?action=google_callback')); ?>">คัดลอก</button>
                    </div>
                    <p class="description">
                        <strong>⚠️ สำคัญ:</strong> เพิ่ม URL ทั้งสองนี้ในการตั้งค่า <strong>Authorized redirect URIs</strong> ใน Google Cloud Console<br>
                        <a href="https://console.cloud.google.com/apis/credentials" target="_blank">🔗 เปิด Google Cloud Console</a>
                    </p>
                    <div style="background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px; padding: 10px; margin-top: 10px;">
                        <strong>📋 ขั้นตอนการตั้งค่า Google Cloud Console:</strong>
                        <ol style="margin: 5px 0 0 20px;">
                            <li>เปิด <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console</a></li>
                            <li>เลือก OAuth 2.0 Client ID ของคุณ</li>
                            <li>ในส่วน "Authorized redirect URIs" ให้เพิ่ม URL ทั้งสองข้างต้น</li>
                            <li>คลิก "Save" เพื่อบันทึกการตั้งค่า</li>
                            <li>รอ 5-10 นาทีให้การตั้งค่าใหม่มีผล</li>
                        </ol>
                    </div>
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin-top: 10px;">
                        <strong>⚠️ หากเกิดข้อผิดพลาด "redirect_uri_mismatch":</strong>
                        <ul style="margin: 5px 0 0 20px;">
                            <li>ตรวจสอบว่า URL ข้างต้นถูกเพิ่มใน Google Cloud Console แล้ว</li>
                            <li>ตรวจสอบว่าไม่มีช่องว่างหรือตัวอักษรพิเศษใน URL</li>
                            <li>รอ 5-10 นาทีหลังจากบันทึกการตั้งค่าใน Google Cloud Console</li>
                            <li>ลองเคลียร์ cache ของเบราว์เซอร์</li>
                        </ul>
                    </div>
                </td>
            </tr>
        </table>
            </div>
        </div>

        <p class="submit">
            <button type="button" id="save-google-settings" class="ad-btn ad-btn-primary">
                <i class="fas fa-save"></i> บันทึกการตั้งค่าทั้งหมด
            </button>
            <span id="google-settings-status"></span>
        </p>
    </form>

    <style>
        .secret-key-display {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .secret-placeholder {
            font-family: monospace;
            background: #f1f1f1;
            padding: 8px 12px;
            border-radius: 4px;
            color: #666;
            border: 1px solid #ddd;
            min-width: 200px;
        }
        .delete-secret-btn {
            background: #dc3545 !important;
            color: white !important;
            border: none !important;
            padding: 6px 12px !important;
            border-radius: 4px !important;
            cursor: pointer !important;
            font-size: 12px !important;
        }
        .delete-secret-btn:hover {
            background: #c82333 !important;
        }
        @media (max-width: 768px) {
            .secret-key-display {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
    jQuery(document).ready(function($) {
        $('.delete-secret-btn').off('click.google-settings').on('click.google-settings', function(e) {
            e.preventDefault();

            const secretKey = $(this).data('secret');
            const secretName = $(this).data('name');
            const button = $(this);

            Swal.fire({
                title: 'ยืนยันการลบ',
                text: `คุณต้องการลบ ${secretName} หรือไม่?\n\nการดำเนินการนี้ไม่สามารถยกเลิกได้`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: '🗑️ ลบ',
                cancelButtonText: '❌ ยกเลิก',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    button.prop('disabled', true).html('🔄 กำลังลบ...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'delete_secret_key',
                            nonce: '<?php echo wp_create_nonce('delete_secret_key'); ?>',
                            secret_key: secretKey
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'สำเร็จ!',
                                    text: 'ลบ Secret Key เรียบร้อยแล้ว',
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                }).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'เกิดข้อผิดพลาด!',
                                    text: response.data.message || 'ไม่สามารถลบได้',
                                    icon: 'error'
                                });
                                button.prop('disabled', false).html('🗑️ ลบ');
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด!',
                                text: 'เกิดข้อผิดพลาดในการเชื่อมต่อ',
                                icon: 'error'
                            });
                            button.prop('disabled', false).html('🗑️ ลบ');
                        }
                    });
                }
            });
        });

        var dropzone = document.getElementById('ga4-json-dropzone');
        var fileInput = document.getElementById('ga4_json_file');

        if (dropzone && fileInput) {
            dropzone.addEventListener('click', function() {
                fileInput.click();
            });

            dropzone.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('dragover');
            });

            dropzone.addEventListener('dragenter', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.add('dragover');
            });

            dropzone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('dragover');
            });

            dropzone.addEventListener('drop', function(e) {
                e.preventDefault();
                e.stopPropagation();
                this.classList.remove('dragover');

                if (e.dataTransfer.files.length) {
                    fileInput.files = e.dataTransfer.files;
                    uploadFile(e.dataTransfer.files[0]);
                }
            });

            fileInput.addEventListener('change', function() {
                if (this.files.length) {
                    var fileName = this.files[0].name;
                    var fileExt = fileName.split('.').pop().toLowerCase();

                    if (fileExt !== 'json') {
                        alert('กรุณาอัพโหลดไฟล์ JSON เท่านั้น (นามสกุล .json)');
                        this.value = '';
                        return;
                    }

                    uploadFile(this.files[0]);
                }
            });
        }

        function uploadFile(file) {
            if (!file) {
                alert('ไม่พบไฟล์ที่ต้องการอัพโหลด');
                return;
            }

            if (file.type !== 'application/json') {
                alert('กรุณาอัพโหลดไฟล์ JSON เท่านั้น');
                return;
            }

            var formData = new FormData();
            formData.append('action', 'upload_ga4_json');
            formData.append('nonce', '<?php echo $ga4_nonce; ?>');
            formData.append('json_file', file);

            document.querySelector('.ga4-json-progress').style.display = 'block';

            var xhr = new XMLHttpRequest();
            xhr.open('POST', ajaxurl, true);

            xhr.upload.onprogress = function(e) {
                if (e.lengthComputable) {
                    var percent = (e.loaded / e.total) * 100;
                    document.querySelector('.ga4-json-progress-bar').style.width = percent + '%';
                }
            };

            xhr.onload = function() {
                document.querySelector('.ga4-json-progress').style.display = 'none';

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);

                        if (response.success) {
                            var uploadUI = document.querySelector('.ga4-json-upload-ui');
                            if (uploadUI) {
                                var fileInfo = document.createElement('div');
                                fileInfo.className = 'ga4-json-file-info';
                                fileInfo.innerHTML =
                                    '<i class="fas fa-file-code"></i>' +
                                    '<span>analytic-pro.json</span>' +
                                    '<a href="' + response.data.file_url + '" target="_blank" class="button button-small">' +
                                    '<i class="fas fa-eye"></i> ดูไฟล์</a>' +
                                    '<button type="button" id="ga4-remove-json" class="button button-small">' +
                                    '<i class="fas fa-trash"></i> ลบไฟล์</button>';

                                uploadUI.parentNode.replaceChild(fileInfo, uploadUI);
                                bindRemoveButton();
                            }
                        } else {
                            var errorMessage = (response.data && response.data.message) ? response.data.message : 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์';
                            alert('เกิดข้อผิดพลาดในการอัพโหลดไฟล์: ' + errorMessage);
                        }
                    } catch (e) {
                        alert('เกิดข้อผิดพลาดในการอัพโหลดไฟล์: การตอบกลับไม่ถูกต้อง');
                    }
                } else {
                    alert('เกิดข้อผิดพลาดในการอัพโหลดไฟล์: ' + xhr.status);
                }
            };

            xhr.onerror = function() {
                document.querySelector('.ga4-json-progress').style.display = 'none';
                alert('เกิดข้อผิดพลาดในการอัพโหลดไฟล์ กรุณาลองใหม่อีกครั้ง');
            };

            xhr.send(formData);
        }

        function bindRemoveButton() {
            var removeButton = document.getElementById('ga4-remove-json');
            if (removeButton) {
                removeButton.addEventListener('click', function() {
                    if (confirm('คุณแน่ใจหรือไม่ที่จะลบไฟล์ JSON นี้?')) {
                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', ajaxurl, true);
                        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                try {
                                    var response = JSON.parse(xhr.responseText);
                                    if (response.success) {
                                        location.reload();
                                    } else {
                                        var errorMessage = (response.data && response.data.message) ? response.data.message : 'เกิดข้อผิดพลาดในการลบไฟล์';
                                        alert('เกิดข้อผิดพลาดในการลบไฟล์: ' + errorMessage);
                                    }
                                } catch (e) {
                                    alert('เกิดข้อผิดพลาดในการลบไฟล์: การตอบกลับไม่ถูกต้อง');
                                }
                            } else {
                                alert('เกิดข้อผิดพลาดในการลบไฟล์: ' + xhr.status);
                            }
                        };

                        xhr.onerror = function() {
                            alert('เกิดข้อผิดพลาดในการลบไฟล์');
                        };

                        xhr.send('action=remove_ga4_json&nonce=<?php echo $ga4_nonce; ?>');
                    }
                });
            }
        }

        bindRemoveButton();

        $('.copy-uri-btn').on('click', function() {
            const uri = $(this).data('uri');
            const btn = $(this);
            navigator.clipboard.writeText(uri).then(function() {
                const originalText = btn.text();
                btn.text('คัดลอกแล้ว!').addClass('button-primary').removeClass('button-secondary');
                setTimeout(function() {
                    btn.text(originalText).removeClass('button-primary').addClass('button-secondary');
                }, 2000);
            }).catch(function() {
                const tempInput = document.createElement('input');
                tempInput.value = uri;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('คัดลอก URL แล้ว');
            });
        });

        $('#ga4-test-connection').on('click', function() {
            const button = $(this);
            const propertyId = $('#ga4_property_id').val();

            if (!propertyId) {
                alert('กรุณาระบุ Property ID ก่อนทดสอบการเชื่อมต่อ');
                return;
            }

            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังทดสอบ...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_ga4_connection',
                    property_id: propertyId,
                    nonce: '<?php echo $ga4_nonce; ?>'
                },
                success: function(response) {
                    button.prop('disabled', false).html('<i class="fas fa-sync-alt"></i> ทดสอบการเชื่อมต่อ GA4');

                    if (response.success) {
                        var result = 'การเชื่อมต่อสำเร็จ!';

                        var activeUsers = response.data ? response.data.active_users : response.active_users;
                        var totalUsers30Days = response.data ? response.data.total_users_30_days : response.total_users_30_days;
                        
                        if (activeUsers !== undefined) {
                            result += ' ผู้ใช้งานขณะนี้: ' + activeUsers + ' คน';
                        }
                        if (totalUsers30Days !== undefined) {
                            result += ' ผู้ใช้งาน 30 วัน: ' + totalUsers30Days + ' คน';
                        }
                        
                        $('#ga4-test-result').html('<div style="color: green;">' + result + '</div>');
                    } else {
                        var errorMessage = response.data ? response.data.message : 'เกิดข้อผิดพลาดไม่ทราบสาเหตุ';
                        $('#ga4-test-result').html('<div style="color: red;">การเชื่อมต่อล้มเหลว: ' + errorMessage + '</div>');
                    }
                },
                error: function(xhr) {
                    button.prop('disabled', false).html('<i class="fas fa-sync-alt"></i> ทดสอบการเชื่อมต่อ GA4');
                    $('#ga4-test-result').html('<div style="color: red;">เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + xhr.statusText + '</div>');
                }
            });
        });

        $('#save-google-settings').on('click', function() {
            const button = $(this);
            const statusEl = $('#google-settings-status');

            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังบันทึก...');
            statusEl.removeClass('success error').hide();

            const formData = {
                action: 'save_google_settings',
                nonce: '<?php echo $google_settings_nonce; ?>',
                ga4_property_id: $('#ga4_property_id').val(),
                google_login_enabled: $('#google_login_enabled').is(':checked') ? 1 : 0,
                google_client_id: $('#google_client_id').val(),
                google_client_secret: $('#google_client_secret').val(),
                use_ga: $('#use_ga_for_pricing').is(':checked') ? 'yes' : 'no',
                exclude_homepage_from_pageviews: $('#exclude_homepage_from_pageviews').is(':checked') ? 'yes' : 'no',
                pageviews_reduction_percentage: $('#pageviews_reduction_percentage').val()
            };

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่าทั้งหมด');

                    if (response.success) {
                        var successMessage = (response.data && response.data.message) ? response.data.message : 'บันทึกสำเร็จ';
                        statusEl.addClass('success').html('<i class="fas fa-check-circle"></i> ' + successMessage).show();

                        setTimeout(function() {
                            statusEl.fadeOut();
                        }, 3000);
                    } else {
                        var errorMessage = (response.data && response.data.message) ? response.data.message : 'เกิดข้อผิดพลาด';
                        statusEl.addClass('error').html('<i class="fas fa-exclamation-circle"></i> ' + errorMessage).show();
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('<i class="fas fa-save"></i> บันทึกการตั้งค่าทั้งหมด');
                    statusEl.addClass('error').html('<i class="fas fa-exclamation-circle"></i> เกิดข้อผิดพลาดในการบันทึก').show();
                }
            });
        });
    });
    </script>
    <?php
}

function handle_upload_ga4_json() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ga4_service_account_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    if (empty($_FILES['json_file']) || $_FILES['json_file']['error'] !== UPLOAD_ERR_OK) {
        wp_send_json_error(['message' => 'No file uploaded or upload error']);
        return;
    }

    $file = $_FILES['json_file'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($file['type'] !== 'application/json' && $file_extension !== 'json') {
        wp_send_json_error(['message' => 'Invalid file type. JSON file required']);
        return;
    }

    $json_content = file_get_contents($file['tmp_name']);
    if ($json_content === false) {
        wp_send_json_error(['message' => 'Cannot read file content']);
        return;
    }
    
    $malicious_patterns = [
        '/<\?(?:php|=|\s)/i',
        '/<script[^>]*>/i', 
        '/<%.*%>/s',
        '/eval\s*\(/i',
        '/exec\s*\(/i',
        '/system\s*\(/i',
        '/shell_exec\s*\(/i',      
        '/javascript:/i',
        '/vbscript:/i',
        '/data:text\/html/i', 
    ];
    
    foreach ($malicious_patterns as $pattern) {
        if (preg_match($pattern, $json_content)) {
            wp_send_json_error(['message' => 'File contains malicious content']);
            return;
        }
    }
    
    $decoded_json = json_decode($json_content, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        wp_send_json_error(['message' => 'Invalid JSON format: ' . json_last_error_msg()]);
        return;
    }
    
    $required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email'];
    foreach ($required_fields as $field) {
        if (!isset($decoded_json[$field])) {
            wp_send_json_error(['message' => 'Invalid Google Service Account JSON format']);
            return;
        }
    }
    
    if ($decoded_json['type'] !== 'service_account') {
        wp_send_json_error(['message' => 'Not a service account JSON file']);
        return;
    }

    $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
    $target_dir = $plugin_dir . 'admin/analytics';

    if (!file_exists($target_dir)) {
        wp_mkdir_p($target_dir);
    }

    $target_file = $target_dir . '/analytic-pro.json';

    $safe_filename = sanitize_file_name(basename($file['name']));
    if ($safe_filename !== basename($file['name']) || strpos($safe_filename, '..') !== false) {
        wp_send_json_error(['message' => 'Invalid filename']);
        return;
    }
    
    $real_target_path = realpath(dirname($target_file));
    $expected_dir = realpath($target_dir);
    if (!$real_target_path || strpos($real_target_path, $expected_dir) !== 0) {
        wp_send_json_error(['message' => 'Invalid upload path']);
        return;
    }
    if (!move_uploaded_file($file['tmp_name'], $target_file)) {
        wp_send_json_error(['message' => 'Failed to save file']);
        return;
    }

    $file_url = plugins_url('admin/analytics/analytic-pro.json', dirname(dirname(__FILE__)));

    wp_send_json_success([
        'message' => 'Service account file uploaded successfully',
        'file_url' => $file_url
    ]);
}

function handle_remove_ga4_json() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ga4_service_account_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $plugin_dir = plugin_dir_path(dirname(dirname(__FILE__)));
    $json_file_path = $plugin_dir . 'admin/analytics/analytic-pro.json';

    if (file_exists($json_file_path)) {
        if (unlink($json_file_path)) {
            wp_send_json_success(['message' => 'File removed successfully']);
        } else {
            wp_send_json_error(['message' => 'Failed to remove file']);
        }
    } else {
        wp_send_json_success(['message' => 'File not found']);
    }
}

function handle_save_google_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'google_settings_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    if (isset($_POST['ga4_property_id'])) {
        update_option('ga4_property_id', sanitize_text_field($_POST['ga4_property_id']));
    }

    if (isset($_POST['google_login_enabled'])) {
        update_option('amp_google_login_enabled', intval($_POST['google_login_enabled']));
    }

    $encryption_manager = \AMP_Encryption_Manager::instance();

    if (isset($_POST['google_client_id'])) {
        $client_id = sanitize_text_field($_POST['google_client_id']);
        if (!empty($client_id)) {
            $encryption_manager->set_secret('amp_google_client_id', $client_id);
        }
    }

    if (isset($_POST['google_client_secret'])) {
        $client_secret = sanitize_text_field($_POST['google_client_secret']);
        if (!empty($client_secret)) {
            $encryption_manager->set_secret('amp_google_client_secret', $client_secret);
        }
    }

    if (isset($_POST['use_ga'])) {
        update_option('use_ga_for_pricing', sanitize_text_field($_POST['use_ga']));
    }

    if (isset($_POST['exclude_homepage_from_pageviews'])) {
        update_option('exclude_homepage_from_pageviews', sanitize_text_field($_POST['exclude_homepage_from_pageviews']));
    }

    if (isset($_POST['pageviews_reduction_percentage'])) {
        $percentage = floatval($_POST['pageviews_reduction_percentage']);
        $percentage = max(0, min(50, $percentage));
        update_option('pageviews_reduction_percentage', $percentage);
    }

    wp_send_json_success(['message' => 'บันทึกการตั้งค่า Google เรียบร้อยแล้ว']);
}

function handle_test_ga4_connection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ga4_service_account_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $property_id = sanitize_text_field($_POST['property_id'] ?? '');
    
    if (empty($property_id)) {
        wp_send_json_error(['message' => 'Property ID is required']);
        return;
    }

    require_once AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
    
    if (!function_exists('test_ga4_connection')) {
        wp_send_json_error(['message' => 'Google Analytics function not found']);
        return;
    }
    
    $result = test_ga4_connection($property_id);
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}

add_action('wp_ajax_upload_ga4_json', 'handle_upload_ga4_json');
add_action('wp_ajax_remove_ga4_json', 'handle_remove_ga4_json');
add_action('wp_ajax_save_google_settings', 'handle_save_google_settings'); 
add_action('wp_ajax_test_ga4_connection', 'handle_test_ga4_connection'); 