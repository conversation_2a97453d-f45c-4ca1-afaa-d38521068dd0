<?php
namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

require_once plugin_dir_path(__FILE__) . '../utils/click-statistics.php';
require_once ABSPATH . 'wp-includes/pluggable.php';
require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';

class PublicCoreManager {
    
    private static $instance = null;
    private $database;
    private $cache;
    private $security;
    private $utilities;
    private $user_id;
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->database = class_exists('\AMP_Database') ? \AMP_Database::instance() : null;
        $this->cache = class_exists('\AMP_Cache_Manager') ? \AMP_Cache_Manager::instance() : null;
        $this->security = class_exists('\AdManagementPro\Core\UnifiedSecurityManager') ? \AdManagementPro\Core\UnifiedSecurityManager::instance() : null;
        $this->utilities = class_exists('\AMP_Utilities') ? \AMP_Utilities::instance() : null;
        
        $this->user_id = \get_current_user_id();
        
        if ($this->database !== null && $this->cache !== null) {
            $this->init_hooks();
        }
    }
    
    private function init_hooks() {
        \add_action('wp_ajax_amp_get_user_dashboard_data', [$this, 'ajax_get_dashboard_data']);
    }
    
    private function is_initialized() {
        return $this->database !== null && $this->cache !== null;
    }
    
    public function get_user_dashboard_data($user_id = null) {
        if (!$this->is_initialized()) {
            return new \WP_Error('system_not_initialized', 'System components not properly initialized');
        }

        $user_id = $user_id ?: $this->user_id;

        $cache_key = 'user_dashboard_' . $user_id;
        $cached_data = $this->cache->get($cache_key, 'user_dashboard');
        if (false !== $cached_data) {
            return $cached_data;
        }

        $user = \get_userdata($user_id);
        if (!$user) {
            return new \WP_Error('invalid_user', 'User not found');
        }
        
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
        $positions_data = $position_manager->get_user_positions_with_details($user_id);
        $click_stats = get_user_position_clicks($user_id, true);
        $exchange_rates = $this->get_exchange_rates();
        
        $total_clicks = 0;
        if(is_array($click_stats) && !empty($click_stats)){
            $total_clicks = array_sum(array_column($click_stats, 'total_clicks'));
        }

        $expiring_soon = 0;
        foreach ($positions_data as $position) {
            if (isset($position->visibility['days_remaining']) && $position->visibility['days_remaining'] <= 7) {
                $expiring_soon++;
            }
        }
        
        $dashboard_data = [
            'user' => [
                'id' => $user_id,
                'login' => $user->user_login,
                'display_name' => $user->display_name,
                'email' => $user->user_email
            ],
            'positions' => $positions_data,
            'click_stats' => $click_stats,
            'exchange_rates' => $exchange_rates,
            'summary' => [
                'total_ads' => count($positions_data),
                'total_clicks' => $total_clicks,
                'expiring_soon' => $expiring_soon
            ]
        ];
        
        $this->cache->set($cache_key, $dashboard_data, 3600, 'user_dashboard');
        return $dashboard_data;
    }
    
    private function get_exchange_rates() {
        $cache_key = 'exchange_rates_global';
        $cached_rates = $this->cache->get($cache_key, 'exchange_rates');
        if (false !== $cached_rates) {
            return $cached_rates;
        }

        if (class_exists('\AMP_Utilities')) {
            $utilities = \AMP_Utilities::instance();

            if (method_exists($utilities, 'should_update_exchange_rate') && $utilities->should_update_exchange_rate()) {
                if (method_exists($utilities, 'update_exchange_rate')) {
                    $utilities->update_exchange_rate();
                }
            }
        }

        global $wpdb;
        $query = "SELECT setting_name, setting_value FROM {$wpdb->prefix}ad_price_global_settings
                 WHERE setting_name IN ('thb_rate', 'exchange_rate_last_updated')";

        $results = $wpdb->get_results($query);

        $rates = [
            'thb_rate' => 35.5,
            'last_updated' => time()
        ];
        
        if (is_array($results)) {
            foreach ($results as $row) {
                if (is_object($row) && isset($row->setting_name)) {
                    if ($row->setting_name === 'thb_rate') {
                        $rates['thb_rate'] = (float) $row->setting_value;
                    } elseif ($row->setting_name === 'exchange_rate_last_updated') {
                        $rates['last_updated'] = (int) $row->setting_value;
                    }
                }
            }
        }
        
        $this->cache->set($cache_key, $rates, 3600, 'exchange_rates');
        return $rates;
    }
    
    public function ajax_get_dashboard_data() {
        if (!check_ajax_referer('amp_nonce', 'nonce', false)) {
            \wp_send_json_error('Invalid nonce');
        }
        
        $user_id = intval($_POST['user_id'] ?? $this->user_id);
        if ($user_id <= 0) {
            $user_id = $this->user_id;
        }
        
        $data = $this->get_user_dashboard_data($user_id);
        
        if (\is_wp_error($data)) {
            \wp_send_json_error($data->get_error_message());
        }
        
        \wp_send_json_success($data);
    }
}