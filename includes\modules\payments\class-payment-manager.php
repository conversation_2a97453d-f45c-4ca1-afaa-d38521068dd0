<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/core/class-price-calculator.php';
require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-plisio-api.php';

class AMP_Payment_Manager {
    private static $instance = null;
    
    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('wp_ajax_create_payment_link', [$this, 'handle_create_payment_link']);
        add_action('wp_ajax_process_checkout', [$this, 'handle_process_checkout']);
        add_action('wp_ajax_create_payment', [$this, 'handle_create_payment']);
        add_action('wp_ajax_confirm_payment', [$this, 'handle_confirm_payment']);
    }
    
    public function handle_create_payment_link() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Create payment link security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }
            
            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => 'กรุณาเข้าสู่ระบบ']);
            }
            
            $method = sanitize_text_field($_POST['method'] ?? 'api');
            $total_amount = floatval($_POST['total_amount'] ?? 0);
            
            if ($total_amount <= 0) {
                $cart = get_user_meta($user_id, 'amp_cart', true);
                if (empty($cart) || !is_array($cart)) {
                    wp_send_json_error(['message' => 'ตะกร้าสินค้าว่างเปล่า']);
                }
                
                $price_calculator = \AMP_Price_Calculator::instance();
                $total_amount = 0;
                foreach ($cart as $item) {
                    $position_name = $item['position_name'] ?? '';
                    $duration = (int)($item['duration'] ?? 30);
                    $price_details = $price_calculator->calculate_price_details($position_name, $duration);
                    $total_amount += $price_details['final_price'];
                }
            }
            
            if ($method === 'api') {
                $encryption_manager = \AMP_Encryption_Manager::instance();
                $api_key = $encryption_manager->get_secret('plisio_api_key');

                if (empty($api_key)) {
                    wp_send_json_error(['message' => 'ระบบชำระเงิน Crypto ยังไม่ได้ตั้งค่า']);
                }

                $plisio = new \Plisio_API($api_key);
                
                $order_id = 'AMP-' . $user_id . '-' . time();
                
                $invoice_params = [
                    'order_number' => $order_id,
                    'order_name' => 'Ad Management Pro - Cart Checkout',
                    'source_amount' => $total_amount,
                    'source_currency' => 'USD',
                    'email' => 'noreply@' . parse_url(home_url(), PHP_URL_HOST)
                ];
                
                $result = $plisio->create_invoice($invoice_params);

                if (is_wp_error($result)) {
                    wp_send_json_error(['message' => 'ไม่สามารถสร้าง Invoice ได้: ' . $result->get_error_message()]);
                }
                
                if (isset($result['status']) && $result['status'] === 'success') {
                    $cart = get_user_meta($user_id, 'amp_cart', true);
                    if (!is_array($cart)) {
                        $cart = [];
                    }
                    
                    update_user_meta($user_id, 'amp_pending_order', [
                        'order_id' => $order_id,
                        'cart' => $cart,
                        'total' => $total_amount,
                        'invoice_id' => $result['data']['txn_id'] ?? '',
                        'created_at' => time()
                    ]);
                    
                    wp_send_json_success([
                        'payment_url' => $result['data']['invoice_url'] ?? '',
                        'txn_id' => $result['data']['txn_id'] ?? '',
                        'total_amount' => $total_amount,
                        'order_id' => $order_id
                    ]);
                } else {
                    wp_send_json_error(['message' => 'ไม่สามารถสร้าง Invoice ได้']);
                }
            } else {
                wp_send_json_error(['message' => 'วิธีการชำระเงินไม่ถูกต้อง']);
            }
            
        } catch (Exception $e) {
            error_log("Payment Manager - Exception: " . $e->getMessage());
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ']);
        }
    }
    
    public function handle_process_checkout() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Process checkout security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => 'กรุณาเข้าสู่ระบบ']);
            }

            require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
            $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');

            $cart = get_user_meta($user_id, 'amp_cart', true);
            if (!is_array($cart) || empty($cart)) {
                wp_send_json_error(['message' => 'ตะกร้าสินค้าว่างเปล่า']);
            }

            foreach ($cart as $item) {
                $position_name = $item['position_name'] ?? '';
                if (!empty($position_name)) {
                    $eligibility_check = $position_manager->validate_purchase_eligibility($position_name, $user_id);
                    if (is_wp_error($eligibility_check)) {
                        wp_send_json_error(['message' => "ตำแหน่ง {$position_name}: " . $eligibility_check->get_error_message()]);
                    }
                }
            }
            
            $price_calculator = \AMP_Price_Calculator::instance();
            $cart_items = [];
            $total_price = 0;
            
            foreach ($cart as $key => $item) {
                $position_name = $item['position_name'] ?? str_replace('pos_', '', $key);
                $duration = (int)($item['duration'] ?? 30);
                $price_details = $price_calculator->calculate_price_details($position_name, $duration);
                
                $cart_items[] = [
                    'position_name' => $position_name,
                    'duration_text' => $duration . ' วัน',
                    'price' => $price_details['final_price'] . ' USDT'
                ];
                
                $total_price += $price_details['final_price'];
            }
            
            wp_send_json_success([
                'cart_items' => $cart_items,
                'total_price' => $total_price . ' USDT',
                'total_amount' => $total_price
            ]);
            
        } catch (Exception $e) {
            error_log("Payment Manager - Process Checkout Exception: " . $e->getMessage());
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ']);
        }
    }
    
    public function handle_create_payment() {
        try {
            $nonce_field = $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce_field, 'amp_dashboard_action')) {
                error_log("AMP: Create payment security check failed - Expected: amp_dashboard_action, Received: " . substr($nonce_field, 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }
            
            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            
            $user_id = get_current_user_id();
            $cart = get_user_meta($user_id, 'amp_cart', true);
            if (!is_array($cart)) {
                $cart = [];
            }
            
            if (empty($cart)) {
                wp_send_json_error(['message' => 'ตะกร้าสินค้าว่างเปล่า']);
            }
            
            $payment_method = sanitize_text_field($_POST['payment_method'] ?? 'api');
            $total_amount = floatval($_POST['total_amount'] ?? 0);
            
            if ($total_amount <= 0) {
                $price_calculator = \AMP_Price_Calculator::instance();
                $total_amount = 0;
                foreach ($cart as $item) {
                    $position_name = $item['position_name'] ?? '';
                    $duration = (int)($item['duration'] ?? 30);
                    $price_details = $price_calculator->calculate_price_details($position_name, $duration);
                    $total_amount += $price_details['final_price'];
                }
            }
            
            if ($payment_method === 'api' || $payment_method === 'crypto') {
                $encryption_manager = \AMP_Encryption_Manager::instance();
                $api_key = $encryption_manager->get_secret('plisio_api_key');

                if (empty($api_key)) {
                    wp_send_json_error(['message' => 'ระบบชำระเงิน Crypto ยังไม่ได้ตั้งค่า']);
                }

                $plisio = new \Plisio_API($api_key);
                
                $user = wp_get_current_user();
                $order_id = 'AMP-' . $user_id . '-' . time();
                
                $invoice_params = [
                    'order_number' => $order_id,
                    'order_name' => 'Ad Management Pro - Cart Checkout',
                    'source_amount' => $total_amount,
                    'source_currency' => 'USD',
                    'email' => 'noreply@' . parse_url(home_url(), PHP_URL_HOST)
                ];
                
                $invoice = $plisio->create_invoice($invoice_params);
                
                if (is_wp_error($invoice)) {
                    wp_send_json_error(['message' => 'ไม่สามารถสร้างใบแจ้งหนี้ได้: ' . $invoice->get_error_message()]);
                }
                
                update_user_meta($user_id, 'amp_pending_order', [
                    'order_id' => $order_id,
                    'cart' => $cart,
                    'total' => $total_amount,
                    'invoice_id' => $invoice['txn_id'],
                    'created_at' => time()
                ]);
                
                wp_send_json_success([
                    'payment_url' => $invoice['invoice_url'],
                    'order_id' => $order_id,
                    'total' => $total_amount,
                    'invoice_id' => $invoice['txn_id']
                ]);
            } else {
                wp_send_json_error(['message' => 'วิธีการชำระเงินไม่ถูกต้อง']);
            }
            
        } catch (Exception $e) {
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ']);
        }
    }
    
    public function handle_confirm_payment() {
        try {
            if (!wp_verify_nonce($_POST['security'] ?? '', 'amp_dashboard_action')) {
                error_log("AMP: Confirm payment security check failed - Expected: amp_dashboard_action, Received: " . substr($_POST['security'] ?? '', 0, 10) . "...");
                wp_send_json_error(['message' => 'Security check failed']);
            }
            
            if (!is_user_logged_in()) {
                wp_send_json_error(['message' => 'User not logged in']);
            }
            
            $user_id = get_current_user_id();
            $transaction_id = sanitize_text_field($_POST['transaction_id'] ?? '');
            
            if (empty($transaction_id)) {
                wp_send_json_error(['message' => 'ไม่พบ Transaction ID']);
            }
            
            $pending_order = get_user_meta($user_id, 'amp_pending_order', true);
            if (!$pending_order || !is_array($pending_order)) {
                wp_send_json_error(['message' => 'ไม่พบข้อมูลการสั่งซื้อ']);
            }
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'amp_payments';
            
            $payment_data = [
                'user_id' => $user_id,
                'order_id' => $pending_order['order_id'],
                'transaction_id' => $transaction_id,
                'amount' => $pending_order['total'],
                'currency' => 'USDT',
                'status' => 'pending',
                'payment_method' => 'crypto',
                'created_at' => current_time('mysql'),
                'cart_data' => json_encode($pending_order['cart'])
            ];
            
            $inserted = $wpdb->insert($table_name, $payment_data);
            
            if ($inserted === false) {
                wp_send_json_error(['message' => 'ไม่สามารถบันทึกข้อมูลการชำระเงินได้']);
            }
            
            delete_user_meta($user_id, 'amp_pending_order');
            delete_user_meta($user_id, 'amp_cart');
            
            wp_send_json_success([
                'message' => 'ยืนยันการชำระเงินเรียบร้อยแล้ว',
                'order_id' => $pending_order['order_id']
            ]);
            
        } catch (Exception $e) {
            error_log("Payment Manager - Confirm Payment Exception: " . $e->getMessage());
            wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในระบบ']);
        }
    }
}

AMP_Payment_Manager::instance();
