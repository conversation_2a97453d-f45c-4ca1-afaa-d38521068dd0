<?php
namespace AdManagementPro\Core\Setup;

if (!defined('WPINC')) {
    die;
}

class Deactivator {

    public static function deactivate() {
        try {
            self::forward_to_plugin_deactivate();
            self::remove_roles();
            self::clear_scheduled_hooks();
            self::flush_rules();
            self::remove_dynamic_link_file();
        } catch (Exception $e) {
            error_log('AMP Deactivation Error: ' . $e->getMessage());
        }
    }

    private static function forward_to_plugin_deactivate() {
        try {
            if (class_exists('\\AdManagementPro\\Core\\Plugin')) {
                $plugin = \AdManagementPro\Core\Plugin::instance();
                if (method_exists($plugin, 'deactivate')) {
                    $plugin->deactivate();
                }
            }
        } catch (Exception $e) {
            error_log('Plugin instance deactivation error: ' . $e->getMessage());
        }
    }

    private static function clear_scheduled_hooks() {
        try {
            wp_clear_scheduled_hook('amp_check_disconnected_timers_hook');
        } catch (Exception $e) {
            error_log('Scheduled hook cleanup error: ' . $e->getMessage());
        }
    }

    private static function remove_roles() {
        try {
            if (function_exists('remove_role')) {
                \remove_role('ad_manager');
                \remove_role('ad_advertiser');
                \remove_role('viewer_admin');
            }
        } catch (Exception $e) {
            error_log('Role removal error: ' . $e->getMessage());
        }
    }

    private static function flush_rules() {
        try {
            \flush_rewrite_rules();
        } catch (Exception $e) {
            error_log('Flush rules error: ' . $e->getMessage());
        }
    }

    private static function remove_dynamic_link_file() {
        $file_path = ABSPATH . 'dynamic-link.php';
        if (file_exists($file_path)) {
            if (!unlink($file_path)) {
                error_log('Ad Management Pro: Failed to remove dynamic-link.php from WordPress root.');
            }
        }
    }
} 