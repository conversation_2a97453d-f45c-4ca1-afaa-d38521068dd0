<?php

if (!defined('ABSPATH')) {
    exit;
}

$action = $_GET['action'] ?? '';
if ($action === 'google_callback') {
    try {
        $ajax_handlers = \AdManagementPro\Core\AjaxHandlers::instance();
        if ($ajax_handlers) {
            $ajax_handlers->handle_google_callback();
        } else {
            wp_redirect(home_url('/dashboard/?error=handler_failed'));
        }
    } catch (Exception $e) {
        error_log('AMP Google Callback Exception: ' . $e->getMessage());
        wp_redirect(home_url('/dashboard/?error=google_system_error'));
    }
    exit;
}

if (!is_user_logged_in()) {
    $redirect_url = home_url('/login/');
    if (isset($_SERVER['REQUEST_URI']) && !empty($_SERVER['REQUEST_URI'])) {
        $redirect_url .= '?redirect_to=' . urlencode($_SERVER['REQUEST_URI']);
    }
    wp_redirect($redirect_url);
    exit;
}

$user_id = get_current_user_id();

$current_user = wp_get_current_user();

$user_role = current_user_can('manage_options') ? 'administrator' :
              (current_user_can('amp_advertiser_access') ? 'advertiser' : false);

$is_admin = current_user_can('manage_options');

$logo_url = get_option('site_logo_url', '');
if (empty($logo_url)) {
    $logo_url = plugin_dir_url(dirname(__FILE__)) . 'admin/assets/images/logo.svg';
}

$cart = get_user_meta($current_user->ID, 'amp_cart', true);
if (!is_array($cart)) {
    $cart = array();
}
$cart_count = count($cart);

$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'overview';

if (isset($_GET['amp_ajax_tab']) && $_GET['amp_ajax_tab'] == '1') {

    
    $tab_file = '';
    switch ($active_tab) {
        case 'overview':
            $tab_file = 'tabs/overview.php';
            break;
        case 'buy':
            $tab_file = 'tabs/buy.php';
            break;
        case 'my-ads':
            $tab_file = 'tabs/my-ads.php';
            break;
        case 'profile':
            $tab_file = 'tabs/profile.php';
            break;
        case 'cart':
            $tab_file = 'tabs/cart.php';
            break;
        case 'purchase-history':
            $tab_file = 'tabs/purchase-table.php';
            break;
        case 'analytics':
            $tab_file = 'tabs/ga4-analytics.php';
            break;
        case 'admin-contact':
            $tab_file = 'tabs/overview.php';
            break;
        default:
            $tab_file = 'tabs/overview.php';
    }

    $file_path = plugin_dir_path(__FILE__) . $tab_file;
    if (file_exists($file_path)) {
        include($file_path);
    } else {
        echo '<p>Error: Tab content not found.</p>';
    }

    exit;
}

if (!defined('WP_USE_THEMES')) {
    define('WP_USE_THEMES', false);
}

ob_start();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด - ระบบจัดการป้ายโฆษณา</title>
    <link rel="icon" href="<?php echo esc_url($logo_url); ?>">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery-migrate@3.4.1/dist/jquery-migrate.min.js" crossorigin="anonymous"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/dashboard.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/overview.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/checkout.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/sweetalert2-dark-mode.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/purchase-table.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/ajax-navigation.css'); ?>">
    <link rel="stylesheet" href="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/css/analytics.css'); ?>">
    <meta name="description" content="ระบบจัดการป้ายโฆษณาออนไลน์">
    <meta name="author" content="Ad Management Pro">
    <meta name="theme-color" content="#4361ee">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #7209b7;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --error-color: #e74c3c;
            --success-color: #2ecc71;
            --card-bg: #fff;
            --input-bg: #f8f9fa;
            --sidebar-bg: #fff;
            --header-bg: #fff;
            --main-bg: #f5f7fa;
            --shadow: 0 2px 10px rgb(0 0 0 / 48%);
            --sidebar-width: 260px;
            --header-height: 70px;
        }

        html, body {
            margin: 0;
            padding: 0;
            font-family: 'Prompt', sans-serif;
            background-color: var(--main-bg);
            color: var(--text-color);
            min-height: 100vh;
        }

        * {
            box-sizing: border-box;
        }

        .page-transition {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--main-bg);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-in-out;
        }

        .preloader.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .preloader-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(67, 97, 238, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }



        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="ad-dashboard-body">
    <div class="preloader">
        <div class="preloader-spinner"></div>
    </div>

    <div class="ad-dashboard-wrapper page-transition">
        <div class="ad-dashboard-sidebar">
            <div class="ad-dashboard-sidebar-header">
                <img src="<?php echo esc_url($logo_url); ?>" alt="Logo" class="ad-dashboard-logo">
                <h2>แดชบอร์ด</h2>
            </div>

            <nav class="ad-dashboard-nav">
                <ul>
                    <li class="<?php echo $active_tab === 'overview' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'overview')); ?>">
                            <span class="menu-emoji">🏠</span> ภาพรวม
                        </a>
                    </li>
                    <li class="<?php echo $active_tab === 'buy' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'buy')); ?>">
                            <span class="menu-emoji">🛒</span> ซื้อป้ายโฆษณา
                        </a>
                    </li>
                    <li class="<?php echo $active_tab === 'my-ads' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'my-ads')); ?>">
                            <span class="menu-emoji">📺</span> ป้ายโฆษณาของฉัน
                        </a>
                    </li>
                    <li class="<?php echo $active_tab === 'purchase-history' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'purchase-history')); ?>">
                            <span class="menu-emoji">📋</span> ประวัติ
                        </a>
                    </li>
                    <li class="<?php echo $active_tab === 'analytics' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'analytics')); ?>">
                            <span class="menu-emoji">📊</span> สถิติเว็บไซต์
                        </a>
                    </li>
                    <li class="<?php echo $active_tab === 'profile' ? 'active' : ''; ?>">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'profile')); ?>">
                            <span class="menu-emoji">👤</span> โปรไฟล์
                        </a>
                    </li>
                    <?php if ($is_admin): ?>
                    <li>
                        <a href="<?php echo admin_url(); ?>">
                            <span class="menu-emoji">⚙️</span> ระบบแอดมิน
                        </a>
                    </li>
                    <?php else: ?>
                    <?php
                    $telegram_link = get_option('telegram_default_link', 'https://t.me/adshunterhd');
                    if (strpos($telegram_link, 'http') !== 0) {
                        if (strpos($telegram_link, '@') === 0) {
                            $telegram_link = 'https://t.me/' . substr($telegram_link, 1);
                        } else {
                            $telegram_link = 'https://t.me/' . $telegram_link;
                        }
                    }
                    ?>
                    <li>
                        <a href="<?php echo esc_url($telegram_link); ?>" target="_blank">
                            <span class="menu-emoji">💬</span> ติดต่อผู้ดูแล
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>

            <div class="ad-dashboard-sidebar-footer">
                <div class="theme-toggle">
                    <input type="checkbox" id="theme-toggle-checkbox" class="theme-toggle-checkbox">
                    <label for="theme-toggle-checkbox" class="theme-toggle-label">
                        <i class="fas fa-sun"></i>
                        <i class="fas fa-moon"></i>
                        <span class="theme-toggle-ball"></span>
                    </label>
                </div>
                <a href="<?php echo esc_url(wp_logout_url(home_url())); ?>" class="ad-dashboard-logout">
                    <span class="menu-emoji">🚪</span> ออกจากระบบ
                </a>
            </div>
        </div>

        <div class="ad-dashboard-content">
            <header class="ad-dashboard-header">
                <div class="ad-dashboard-header-left">
                    <button class="ad-dashboard-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="ad-dashboard-title">
                        <?php
                        switch ($active_tab) {
                            case 'overview':
                                echo 'ภาพรวม';
                                break;
                            case 'buy':
                                echo 'ซื้อป้ายโฆษณา';
                                break;
                            case 'my-ads':
                                echo 'ป้ายโฆษณาของฉัน';
                                break;
                            case 'profile':
                                echo 'โปรไฟล์';
                                break;
                            case 'purchase-history':
                                echo 'ประวัติการซื้อ';
                                break;
                            case 'cart':
                                echo 'ตะกร้าสินค้า';
                                break;
                            case 'analytics':
                                echo 'สถิติเว็บไซต์';
                                break;
                            case 'admin-contact':
                                echo 'ติดต่อผู้ดูแล';
                                break;
                            default:
                                echo 'ภาพรวม';
                        }
                        ?>
                    </h1>
                </div>

                <div class="ad-dashboard-header-right">
                    <div class="exchange-rate-display-container">
                        <span class="exchange-rate-icon">💲</span>
                        <span class="exchange-rate-label">THB/USDT:</span>
                        <span class="exchange-rate-value" id="live-exchange-rate">...</span>
                    </div>
                    <div class="ad-dashboard-cart">
                        <a href="<?php echo esc_url(add_query_arg('tab', 'cart')); ?>" class="ad-dashboard-cart-icon">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="ad-dashboard-cart-count"><?php echo esc_html($cart_count); ?></span>
                        </a>
                    </div>

                    <div class="ad-dashboard-user" id="profile-menu-toggle">
                        <span class="ad-dashboard-username"><?php echo esc_html($current_user->display_name); ?></span>
                        <div class="ad-dashboard-avatar">
                            <?php
                            $custom_avatar_id = get_user_meta($current_user->ID, 'custom_avatar', true);
                            if ($custom_avatar_id) {
                                $avatar_url = wp_get_attachment_image_url($custom_avatar_id, 'thumbnail');
                                if ($avatar_url) {
                                    echo '<img src="' . esc_url($avatar_url) . '" alt="' . esc_attr($current_user->display_name) . '">';
                                } else {
                                    echo get_avatar($current_user->ID, 40);
                                }
                            } else {
                                echo get_avatar($current_user->ID, 40);
                            }
                            ?>
                        </div>

                        <div class="profile-dropdown" id="profile-dropdown">
                            <a href="<?php echo esc_url(add_query_arg('tab', 'profile')); ?>" class="profile-dropdown-item">
                                <span class="menu-emoji">👤</span> โปรไฟล์
                            </a>
                            <div class="profile-dropdown-divider"></div>
                            <a href="<?php echo esc_url(wp_logout_url(home_url())); ?>" class="profile-dropdown-item ad-dashboard-logout">
                                <span class="menu-emoji">🚪</span> ออกจากระบบ
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <main class="ad-dashboard-main">
                <div id="development-mode-banner" class="development-mode-banner" style="background: linear-gradient(135deg, #ff9800, #f57c00); color: white; padding: 15px 20px; margin-bottom: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(255, 152, 0, 0.3); animation: pulse 2s infinite; display: none;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 24px;">🚧</div>
                        <div>
                            <h3 style="margin: 0; font-size: 16px; font-weight: 600;">โหมดพัฒนา (Development Mode)</h3>
                            <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;">ระบบกำลังอยู่ในโหมดพัฒนา การซื้อสินค้าถูกปิดใช้งานชั่วคราว</p>
                        </div>
                    </div>
                </div>
                <style>
                @keyframes pulse {
                    0% { box-shadow: 0 2px 10px rgba(255, 152, 0, 0.3); }
                    50% { box-shadow: 0 2px 20px rgba(255, 152, 0, 0.6); }
                    100% { box-shadow: 0 2px 10px rgba(255, 152, 0, 0.3); }
                }
                </style>
                <?php
                switch ($active_tab) {
                    case 'overview':
                        include(plugin_dir_path(__FILE__) . 'tabs/overview.php');
                        break;
                    case 'buy':
                        include(plugin_dir_path(__FILE__) . 'tabs/buy.php');
                        break;
                    case 'my-ads':
                        include(plugin_dir_path(__FILE__) . 'tabs/my-ads.php');
                        break;
                    case 'profile':
                        include(plugin_dir_path(__FILE__) . 'tabs/profile.php');
                        break;
                    case 'cart':
                        include(plugin_dir_path(__FILE__) . 'tabs/cart.php');
                        break;
                    case 'purchase-history':
                        include(plugin_dir_path(__FILE__) . 'tabs/purchase-table.php');
                        break;
                    case 'analytics':
                        include(plugin_dir_path(__FILE__) . 'tabs/ga4-analytics.php');
                        break;
                    case 'admin-contact':
                        include(plugin_dir_path(__FILE__) . 'tabs/overview.php');
                        break;
                    default:
                        include(plugin_dir_path(__FILE__) . 'tabs/overview.php');
                }
                ?>
            </main>
            <footer class="ad-dashboard-footer">
                <div class="ad-dashboard-footer-content">
                    <div class="ad-dashboard-footer-logo">
                        <img src="<?php echo esc_url($logo_url); ?>" alt="Logo" class="ad-dashboard-footer-logo-img">
                    </div>
                    <div class="ad-dashboard-footer-copyright">
                        &copy; <?php echo date('Y'); ?> Ad Management Pro. All rights reserved.
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <?php
    ?>
    <script src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/js/error-handler.js'); ?>"></script>

    <?php
    $security_manager = \AdManagementPro\Core\UnifiedSecurityManager::instance();
    $current_nonce = $security_manager->get_current_nonce();
    ?>
    <script<?php if ($current_nonce) echo ' nonce="' . esc_attr($current_nonce) . '"'; ?>>
        if (typeof jQuery !== 'undefined' && typeof jQuery.migrateMute !== 'undefined') {
            jQuery.migrateMute = true;
        }
        window.addEventListener('load', function() {
            if (typeof jQuery !== 'undefined') {
                window.jQueryLoaded = true;
                if (typeof jQuery.migrateWarnings !== 'undefined') {
                    jQuery.migrateMute = true;
                }
            }
        });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script<?php if ($current_nonce) echo ' nonce="' . esc_attr($current_nonce) . '"'; ?>>
        document.addEventListener('DOMContentLoaded', function() {
            const isDarkMode = document.body.classList.contains('dark-mode');
            if (isDarkMode) {
                Swal.getContainer()?.classList.add('dark-mode');
            }
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.attributeName === 'class') {
                        const isDark = document.body.classList.contains('dark-mode');
                        if (isDark) {
                            Swal.getContainer()?.classList.add('dark-mode');
                        } else {
                            Swal.getContainer()?.classList.remove('dark-mode');
                        }
                    }
                });
            });
            observer.observe(document.body, { attributes: true });
        });
    </script>

    <script<?php if ($current_nonce) echo ' nonce="' . esc_attr($current_nonce) . '"'; ?>>
        <?php
        $database = \AdManagementPro\Core\Database::instance();
        $global_settings_table = $database->get_table('ad_price_global_settings');

        $use_timer_row = $database->get_row("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", ['use_reservation_timer']);
        $timeout_row = $database->get_row("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", ['reservation_timeout']);

        if (!$timeout_row || !$use_timer_row) {
            $fallback_table = $database->get_table('ad_global_settings');
            if ($fallback_table) {
                if (!$timeout_row) {
                    $timeout_row = $database->get_row("SELECT setting_value FROM {$fallback_table} WHERE setting_name = %s", ['reservation_timeout']);
                }
                if (!$use_timer_row) {
                    $use_timer_row = $database->get_row("SELECT setting_value FROM {$fallback_table} WHERE setting_name = %s", ['use_reservation_timer']);
                }
            }
        }

        $timeout_minutes = $timeout_row ? intval($timeout_row->setting_value) : 3;
        $use_reservation_timer = $use_timer_row ? intval($use_timer_row->setting_value) : 1;
        ?>
        window.adDashboardData = {
            ajaxurl: '<?php echo admin_url('admin-ajax.php'); ?>',
            dashboardUrl: '<?php echo esc_url(home_url('/dashboard/')); ?>',
            loginUrl: '<?php echo esc_url(home_url('/login/')); ?>',
            nonce: '<?php echo wp_create_nonce('amp_dashboard_action'); ?>',
            user_id: '<?php echo $current_user->ID; ?>',
            username: '<?php echo $current_user->user_login; ?>',
            current_user: '<?php echo esc_js($current_user->user_login); ?>',
            cart_count: <?php echo intval($cart_count ?? 0); ?>,
            cart_items: <?php echo json_encode(get_user_meta($current_user->ID, 'amp_cart', true) ?: []); ?>,
            reservation_timeout: <?php echo intval(($timeout_minutes ?? 60) * 60); ?>,
            reservation_minutes: <?php echo intval($timeout_minutes ?? 60); ?>,
            use_reservation_timer: '<?php echo esc_js($use_reservation_timer ?? 'no'); ?>',
            positionSizes: <?php
                try {
                    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
                    $all_positions = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
                    $position_sizes = [];
                    if ($all_positions && is_array($all_positions)) {
                        foreach ($all_positions as $pos) {
                            if (isset($pos->name, $pos->width, $pos->height)) {
                                $position_sizes[$pos->name] = ['width' => intval($pos->width), 'height' => intval($pos->height)];
                            }
                        }
                    }
                    echo json_encode($position_sizes);
                } catch (Exception $e) {
                    echo '{}';
                }
            ?>,
            has_bypass_checkout: <?php echo get_user_meta($current_user->ID, 'bypass_checkout', true) ? 'true' : 'false'; ?>,
            plugin_url: '<?php echo plugin_dir_url(dirname(__FILE__)); ?>'
        };
        window.ampReservationSettings = {
            useReservationSystem: <?php echo ($use_reservation_timer ?? 0) ? 'true' : 'false'; ?>,
            reservationTimeout: <?php echo intval($timeout_minutes ?? 60); ?>
        };

        <?php if ($active_tab === 'overview' || $active_tab === 'analytics') : ?>
         <?php endif; ?>


        window.adDashboardData.google_login_enabled = <?php echo json_encode((bool) get_option('amp_google_login_enabled', false)); ?>;
        window.adDashboardData.google_client_id = '<?php
            try {
                $encryption_manager = \AMP_Encryption_Manager::instance();
                echo esc_js($encryption_manager->get_secret('amp_google_client_id') ?? '');
            } catch (Exception $e) {
                echo '';
            }
        ?>';
        window.adDashboardData.turnstile_enabled = <?php echo json_encode((bool) get_option('amp_turnstile_enabled', false)); ?>;
        window.adDashboardData.turnstile_site_key = '<?php
            try {
                $encryption_manager = \AMP_Encryption_Manager::instance();
                echo esc_js($encryption_manager->get_secret('amp_turnstile_site_key') ?? '');
            } catch (Exception $e) {
                echo '';
            }
        ?>';

        window.adDashboardData.session_timeout_minutes = <?php echo intval(get_option('amp_session_timeout', 60)); ?>;
        window.adDashboardData.is_admin = <?php echo json_encode(current_user_can('manage_options')); ?>;
        window.adSizes = window.adDashboardData.positionSizes;
        window.addEventListener('load', function() {
            document.querySelector('.preloader').classList.add('hidden');
        });

        if (window.jQuery) {
            function addCSRFToken(e, xhr, settings) {
                if (settings.type === "POST") {
                    xhr.setRequestHeader('X-WP-Nonce', window.adDashboardData.nonce);
                }
            }
            jQuery(document).off('ajaxSend', addCSRFToken);
            jQuery(document).on('ajaxSend', addCSRFToken);
            window.updateCartCount = function(newCount) {
                const cartCount = jQuery('.ad-dashboard-cart-count');
                const cartIcon = jQuery('.ad-dashboard-cart-icon');

                if (!cartIcon.length) {
                    return;
                }

                if (newCount > 0) {
                    if (cartCount.length) {
                        cartCount.text(newCount).show();
                        cartCount.addClass('cart-count-update');
                        setTimeout(function() {
                            cartCount.removeClass('cart-count-update');
                        }, 300);
                    } else {
                        const newCountElement = jQuery('<span class="ad-dashboard-cart-count cart-count-new">' + newCount + '</span>');
                        cartIcon.append(newCountElement);
                        setTimeout(function() {
                            newCountElement.removeClass('cart-count-new');
                        }, 10);
                    }
                } else {
                    if (cartCount.length) {
                        cartCount.text('0').hide();
                    }
                }
            };
        }
    </script>

    <script>
        function checkDevelopmentModeStatus() {
            if (typeof adDashboardData === 'undefined') {
                return;
            }

            $.ajax({
                url: adDashboardData.ajaxurl,
                type: 'POST',
                data: {
                    action: 'check_development_mode',
                    security: adDashboardData.nonce
                },
                success: function(response) {
                    const banner = $('#development-mode-banner');
                    if (response.success && response.data.is_enabled) {
                        banner.show();
                    } else {
                        banner.hide();
                    }
                },
                error: function() {
                    console.warn('Unable to check development mode status');
                }
            });
        }

        $(document).ready(function() {
            checkDevelopmentModeStatus();
        });

        window.checkDevelopmentModeStatus = checkDevelopmentModeStatus;
    </script>

    <?php
        $encryption_manager = \AMP_Encryption_Manager::instance();
        if (get_option('amp_google_login_enabled', false) && !empty($encryption_manager->get_secret('amp_google_client_id'))):
    ?>
    <script src="https://accounts.google.com/gsi/client" async defer nonce="<?php echo wp_create_nonce('google_api_script'); ?>"></script>
    <?php endif; ?>



    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/js/timer-system.js'); ?>?ver=<?php echo time(); ?>"></script>
    <script src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/js/dashboard.js'); ?>?ver=<?php echo time(); ?>"></script>
    <script src="<?php echo esc_url(plugin_dir_url(dirname(__FILE__)) . 'public/assets/js/checkout.js'); ?>?ver=<?php echo time(); ?>"></script>
</body>
</html>
<?php
echo ob_get_clean();
?>
