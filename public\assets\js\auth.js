(function () {
  "use strict";

  if (typeof jQuery !== "undefined" && typeof jQuery.migrateMute === "undefined") {
    jQuery.migrateMute = true;
  }

  class AuthManager {
    constructor() {
      this.elements = {};
      this.config = {
        maxRetries: 3,
        recoveryTimeout: 1000,
        messageTimeout: 5000,
        successTimeout: 3000
      };
      this.state = {
        loginAttemptCounter: 0,
        progressInterval: null,
        currentProgress: 0,
        sessionRedirectInProgress: false,
        timers: new Set(),
        intervals: new Set()
      };
      this.recoveryStrategies = {
        'nonce': () => this.refreshNonces(),
        'session': () => this.refreshSessionData(),
        'network': () => this.retryRequest(),
        'auth': () => this.fullSystemRecovery()
      };
    }

    addTimer(timer) {
      this.state.timers.add(timer);
      return timer;
    }

    addInterval(interval) {
      this.state.intervals.add(interval);
      return interval;
    }

    cleanup() {
      this.state.timers.forEach(timer => clearTimeout(timer));
      this.state.intervals.forEach(interval => clearInterval(interval));
      this.state.timers.clear();
      this.state.intervals.clear();
      if (this.state.progressInterval) {
        clearInterval(this.state.progressInterval);
      }
    }

    init() {
      this.checkSessionBeforeLogin();
      this.cacheElements();
      this.initEventListeners();
      this.initFeatures();
      this.setupCleanup();
    }

    setupCleanup() {
      window.addEventListener('beforeunload', () => this.cleanup());
      window.addEventListener('pagehide', () => this.cleanup());
    }

    cacheElements() {
      this.elements = {
        authTitle: document.getElementById("auth-title"),
        darkModeToggle: document.getElementById("dark-mode-toggle"),
        tabs: document.querySelectorAll(".amp-auth-tab"),
        formContainers: document.querySelectorAll(".amp-auth-form-container"),
        loginForm: document.getElementById("amp-login-form"),
        registerForm: document.getElementById("register-form"),
        forgotPasswordForm: document.getElementById("forgot-password-form"),
        resetPasswordForm: document.getElementById("reset-password-form"),
        passwordInput: document.getElementById('register-password'),
        resetPasswordInput: document.getElementById('reset-password')
      };
    }

    initFeatures() {
      if (typeof ampAuthData !== "undefined") {
        this.handleInitialTab();
        if (ampAuthData.googleLoginEnabled === true) {
          this.initGoogleLogin();
        }
      }

      this.initTabs();
      this.initPasswordToggles();
      this.initDarkMode();
      this.initPasswordStrengthMeter();
      this.setupPeriodicRefresh();
    }

    handleInitialTab() {
      if (ampAuthData.activeTab) {
        const initialTab = document.querySelector(`.amp-auth-tab[data-tab="${ampAuthData.activeTab}"]`);
        const initialContainer = document.getElementById(`${ampAuthData.activeTab}-form-container`);
        
        document.querySelectorAll('.amp-auth-tab.active, .amp-auth-form-container.active')
          .forEach(el => el.classList.remove('active'));

        if (initialTab) initialTab.classList.add('active');
        if (initialContainer) initialContainer.classList.add('active');
      }
    }

    setupPeriodicRefresh() {
      const refreshInterval = this.addInterval(setInterval(() => {
        if (typeof window.syncSecurityDataPeriodically !== 'function') {
          this.refreshNonces();
        }
      }, 10 * 60 * 1000));

      this.addTimer(setTimeout(() => refreshInterval, 30000));
    }

    initEventListeners() {
      const formHandlers = [
        [this.elements.loginForm, (e) => this.handleLoginSubmit(e)],
        [this.elements.registerForm, (e) => this.handleRegisterSubmit(e)],
        [this.elements.forgotPasswordForm, (e) => this.handleForgotPasswordSubmit(e)],
        [this.elements.resetPasswordForm, (e) => this.handleResetPasswordSubmit(e)]
      ];

      formHandlers.forEach(([form, handler]) => {
        if (form) form.addEventListener("submit", handler);
      });
    }

    async checkSessionBeforeLogin() {
      try {
        if (!window.ampAuthData?.ajaxurl ||
            window.location.search.includes('action=') ||
            !window.location.pathname.includes('/login')) {
          return;
        }

        const response = await fetch(window.ampAuthData.ajaxurl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
          },
          body: 'action=amp_check_login_status',
          credentials: 'same-origin'
        });

        if (!response.ok) return;

        const data = await response.json();
        if (data.success && data.data.logged_in && data.data.session_valid && !this.state.sessionRedirectInProgress) {
          this.state.sessionRedirectInProgress = true;
          this.showSuccessMessage('คุณเข้าสู่ระบบอยู่แล้ว กำลังนำไปยังแดชบอร์ด...');
          this.addTimer(setTimeout(() => {
            window.location.replace(data.data.redirect_url || '/dashboard/');
          }, 1000));
        }
      } catch (error) {
        
      }
    }

    getSmartRedirectUrl() {
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect_to');
      const from = urlParams.get('from');

      if (redirectTo) {
        try {
          const url = new URL(redirectTo, window.location.origin);
          if (url.origin === window.location.origin) {
            return redirectTo;
          }
        } catch (e) {
          console.warn('Invalid redirect_to URL:', redirectTo);
        }
      }

      if (from && from.startsWith('/dashboard')) {
        return window.location.origin + from;
      }

      return ampAuthData.dashboardUrl || '/dashboard/';
    }

    handleLoginSubmit(e) {
      e.preventDefault();
      this.clearErrors();
      if (this.validateLoginForm()) {
        this.performLogin(this.elements.loginForm, 0);
      }
    }

    handleRegisterSubmit(e) {
      e.preventDefault();
      this.clearErrors();
      if (this.validateRegisterForm()) {
        this.performRegister(this.elements.registerForm, 0);
      }
    }

    handleForgotPasswordSubmit(e) {
      e.preventDefault();
      this.clearErrors();
      if (this.validateForgotPasswordForm()) {
        this.performFormSubmission(this.elements.forgotPasswordForm, 'forgot_password');
      }
    }

    handleResetPasswordSubmit(e) {
      e.preventDefault();
      this.clearErrors();
      if (this.validateResetPasswordForm()) {
        this.performFormSubmission(this.elements.resetPasswordForm, 'reset_password');
      }
    }

    async performLogin(form, retryCount = 0) {
      return this.performFormSubmission(form, 'login', retryCount);
    }

    async performRegister(form, retryCount = 0) {
      return this.performFormSubmission(form, 'register', retryCount);
    }

    async performFormSubmission(form, action, retryCount = 0) {
      const submitButton = form.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.textContent;
      const maxRetries = this.config.maxRetries;

      const buttonTexts = {
        login: retryCount > 0 ? `กำลังลองใหม่... (${retryCount}/${maxRetries})` : 'กำลังตรวจสอบ...',
        register: retryCount > 0 ? `กำลังลองใหม่... (${retryCount}/${maxRetries})` : 'กำลังลงทะเบียน...',
        forgot_password: 'กำลังส่ง...',
        reset_password: 'กำลังรีเซ็ต...'
      };

      submitButton.textContent = buttonTexts[action];
      submitButton.disabled = true;

      if (retryCount > 0) {
        this.showErrorMessage(`กำลังพยายาม${this.getActionText(action)}... (ครั้งที่ ${retryCount}/${maxRetries})`);
      }

      const formData = this.prepareFormData(form, action);

      try {
        const response = await this.makeRequest(formData);
        const data = await this.parseResponse(response);

        if (data.success) {
          await this.handleSuccess(data, action, form, submitButton, originalButtonText);
        } else {
          await this.handleError(data, action, form, submitButton, originalButtonText, retryCount);
        }
      } catch (error) {
        await this.handleNetworkError(error, action, form, submitButton, originalButtonText, retryCount);
      }
    }

    getActionText(action) {
      const actionTexts = {
        login: 'เข้าสู่ระบบ',
        register: 'ลงทะเบียน',
        forgot_password: 'ส่งลิงก์รีเซ็ต',
        reset_password: 'รีเซ็ตรหัสผ่าน'
      };
      return actionTexts[action] || 'ดำเนินการ';
    }

    prepareFormData(form, action) {
      const formData = new FormData(form);
      formData.append('action', `amp_${action}`);

      const nonceMap = {
        login: 'amp_login_nonce',
        register: 'amp_register_nonce',
        forgot_password: 'amp_forgot_password_nonce',
        reset_password: 'amp_reset_password_nonce'
      };

      const nonceName = nonceMap[action];
      const nonceValue = window.ampAuthData?.loginNonce;

      if (!formData.has(nonceName) && nonceValue) {
        formData.append(nonceName, nonceValue);
      }

      if (window.getTurnstileToken) {
        const turnstileResponse = window.getTurnstileToken(action === 'forgot_password' ? 'forgot-password' : action);
        if (turnstileResponse) {
          formData.set('cf-turnstile-response', turnstileResponse);
        }
      } else if (window.turnstileState && window.turnstileState.currentToken) {
        formData.set('cf-turnstile-response', window.turnstileState.currentToken);
      }

      return formData;
    }

    async makeRequest(formData) {
      const response = await fetch(ampAuthData.ajaxurl, {
        method: "POST",
        body: formData,
        credentials: "same-origin",
        headers: {
          "X-Requested-With": "XMLHttpRequest",
          "Cache-Control": "no-cache",
          "Accept": "application/json, text/javascript, */*; q=0.01"
        },
      });

      if (response.status === 403) {
        throw new Error("การเข้าถึงถูกปฏิเสธ - ปัญหาการตรวจสอบความปลอดภัย");
      }

      if (!response.ok) {
        throw new Error("Network response was not ok: " + response.statusText);
      }

      return response;
    }

    async parseResponse(response) {
      const text = await response.text();
      try {
        return JSON.parse(text);
      } catch (e) {
        throw new Error("Invalid JSON response from server.");
      }
    }

    async handleSuccess(data, action, form, submitButton, originalButtonText) {
      this.state.loginAttemptCounter = 0;
      
      const successHandlers = {
        login: () => this.handleLoginSuccess(data),
        register: () => this.handleRegisterSuccess(data),
        forgot_password: () => this.handleForgotPasswordSuccess(data, form),
        reset_password: () => this.handleResetPasswordSuccess(data)
      };

      if (data.security_update && data.nonces) {
        this.updateSecurityData(data.nonces);
      }

      if (successHandlers[action]) {
        await successHandlers[action]();
      }

      if (window.syncSessionStateAfterLogin && typeof window.syncSessionStateAfterLogin === 'function' && action === 'login') {
        try {
          await window.syncSessionStateAfterLogin(data);
        } catch (error) {}
      }
    }

    async handleLoginSuccess(data) {
      const message = data.data?.message || data.message || "เข้าสู่ระบบสำเร็จ กำลังนำคุณไปยังแดชบอร์ด...";
      const redirectUrl = data.data?.redirect || data.redirect || this.getSmartRedirectUrl();

      this.showSuccessMessage(message);

      this.addTimer(setTimeout(() => {
        if (redirectUrl && redirectUrl !== window.location.href && !this.state.sessionRedirectInProgress) {
          this.state.sessionRedirectInProgress = true;
          window.location.replace(redirectUrl);
        }
      }, 1200));
    }

    async handleRegisterSuccess(data) {
      const message = data.data?.message || data.message || "สมัครสมาชิกสำเร็จ!";
      const redirectUrl = data.data?.redirect || data.redirect;

      this.showSuccessMessage(message);

      this.addTimer(setTimeout(() => {
        if (redirectUrl) {
          window.location.href = redirectUrl;
        } else {
          const loginTab = document.querySelector('.amp-auth-tab[data-tab="login"]');
          if (loginTab) loginTab.click();
        }
      }, 1500));
    }

    async handleForgotPasswordSuccess(data, form) {
      const message = data.data?.message || data.message || "ส่งลิงก์รีเซ็ตรหัสผ่านไปยังอีเมลของคุณแล้ว";
      this.showSuccessMessage(message);
      form.reset();
    }

    async handleResetPasswordSuccess(data) {
      const message = data.data?.message || data.message || "รีเซ็ตรหัสผ่านสำเร็จ! คุณสามารถเข้าสู่ระบบด้วยรหัสผ่านใหม่ได้ทันที";
      const redirectUrl = data.data?.redirect || data.redirect || ampAuthData.homeUrl + "login/";

      this.showSuccessMessage(message);

      this.addTimer(setTimeout(() => {
        window.location.href = redirectUrl;
      }, 1500));
    }

    async handleError(data, action, form, submitButton, originalButtonText, retryCount) {
      this.state.loginAttemptCounter++;
      const errorMessage = data.data?.message || data.message || data.error || `เกิดข้อผิดพลาดในการ${this.getActionText(action)}`;
      const errorCode = data.data?.error_code || data.error_code;
      const retryAllowed = data.data?.retry_allowed || data.retry_allowed;
      const freshNonces = data.data?.nonces || data.nonces;
      const requiresManualAction = data.data?.requires_manual_action || data.requires_manual_action;

      if (requiresManualAction) {
        this.showErrorMessage(errorMessage);
        this.resetSubmitButton(submitButton, originalButtonText);
        return;
      }

      if ((retryAllowed || this.isRecoverableError(errorMessage) || errorCode === 'NONCE_VERIFICATION_FAILED') && retryCount < this.config.maxRetries) {
        if (errorCode === 'NONCE_VERIFICATION_FAILED') {
          this.showErrorMessage(`กำลังขอ security token ใหม่... (ครั้งที่ ${retryCount + 1}/${this.config.maxRetries})`);
        } else {
          this.showErrorMessage(`กำลังรีเฟรชข้อมูลความปลอดภัย... (ครั้งที่ ${retryCount + 1}/${this.config.maxRetries})`);
        }

        if (freshNonces) {
          this.updateSecurityData(freshNonces);
        }

        const recoverySuccess = await this.attemptSmartRecovery(errorMessage, retryCount, errorCode);
        if (recoverySuccess) {
          this.addTimer(setTimeout(() => {
            this.performFormSubmission(form, action, retryCount + 1);
          }, this.config.recoveryTimeout));
          return;
        }
      }

      if (data.data?.action === 'email_not_verified') {
        const emailInput = form.querySelector('input[name="username"]');
        const userEmail = emailInput ? emailInput.value : '';

        if (typeof window.showEmailVerificationNotice === 'function') {
          window.showEmailVerificationNotice(userEmail);
        } else {
          this.showErrorMessage(errorMessage);
        }
      } else {
        this.showErrorMessage(errorMessage);
      }

      this.resetSubmitButton(submitButton, originalButtonText);
    }

    async handleNetworkError(error, action, form, submitButton, originalButtonText, retryCount) {
      if (retryCount < this.config.maxRetries && (error.message.includes('Network') || error.message.includes('fetch'))) {
        this.showErrorMessage('กำลังลองเชื่อมต่อใหม่...');
        this.addTimer(setTimeout(() => {
          this.performFormSubmission(form, action, retryCount + 1);
        }, 1500));
        return;
      }

      this.showErrorMessage('Could not connect to the server. ' + (error.message || ''));
      this.resetSubmitButton(submitButton, originalButtonText);
    }

    resetSubmitButton(submitButton, originalButtonText) {
      submitButton.textContent = originalButtonText;
      submitButton.disabled = false;
      if (window.ampAuthData?.turnstileEnabled && typeof window.resetTurnstileWidget === 'function') {
        window.resetTurnstileWidget();
      }
    }

    validateLoginForm() {
      const username = this.elements.loginForm.querySelector("#login-username");
      const password = this.elements.loginForm.querySelector("#login-password");

      if (!username.value.trim()) {
        this.showErrorMessage("กรุณากรอกชื่อผู้ใช้หรืออีเมล");
        return false;
      }

      if (!password.value) {
        this.showErrorMessage("กรุณากรอกรหัสผ่าน");
        return false;
      }

      return true;
    }

    validateRegisterForm() {
      const fields = {
        username: this.elements.registerForm.querySelector("#register-username"),
        email: this.elements.registerForm.querySelector("#register-email"),
        password: this.elements.registerForm.querySelector("#register-password"),
        confirmPassword: this.elements.registerForm.querySelector("#register-confirm-password"),
        terms: this.elements.registerForm.querySelector("#register-terms")
      };

      const validations = [
        [!fields.username?.value.trim(), "กรุณากรอกชื่อผู้ใช้"],
        [!fields.email?.value.trim(), "กรุณากรอกอีเมล"],
        [!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(fields.email?.value.trim() || ''), "กรุณากรอกอีเมลให้ถูกต้อง"],
        [!fields.password?.value, "กรุณากรอกรหัสผ่าน"],
        [fields.password?.value.length < 8, "รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัวอักษร"],
        [!fields.confirmPassword?.value, "กรุณายืนยันรหัสผ่าน"],
        [fields.password?.value !== fields.confirmPassword?.value, "รหัสผ่านไม่ตรงกัน"],
        [fields.terms && !fields.terms.checked, "คุณต้องยอมรับข้อกำหนดและเงื่อนไขการใช้งาน"]
      ];

      for (const [condition, message] of validations) {
        if (condition) {
          this.showErrorMessage(message);
          return false;
        }
      }

      return true;
    }

    validateForgotPasswordForm() {
      const email = this.elements.forgotPasswordForm.querySelector("#forgot-email");

      if (!email?.value.trim()) {
        this.showErrorMessage("กรุณากรอกอีเมล");
        return false;
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value.trim())) {
        this.showErrorMessage("กรุณากรอกอีเมลให้ถูกต้อง");
        return false;
      }

      return true;
    }

    validateResetPasswordForm() {
      const fields = {
        password: this.elements.resetPasswordForm.querySelector("#reset-password"),
        confirmPassword: this.elements.resetPasswordForm.querySelector("#reset-confirm-password"),
        token: this.elements.resetPasswordForm.querySelector('input[name="token"]'),
        user: this.elements.resetPasswordForm.querySelector('input[name="user"]')
      };

      const validations = [
        [!fields.token?.value || !fields.user?.value, "ข้อมูลไม่ครบถ้วน กรุณาใช้ลิงก์จากอีเมลเพื่อรีเซ็ตรหัสผ่าน"],
        [!fields.password?.value, "กรุณากรอกรหัสผ่านใหม่"],
        [fields.password?.value.length < 8, "รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัวอักษร"],
        [!fields.confirmPassword?.value, "กรุณายืนยันรหัสผ่าน"],
        [fields.password?.value !== fields.confirmPassword?.value, "รหัสผ่านไม่ตรงกัน"]
      ];

      for (const [condition, message] of validations) {
        if (condition) {
          this.showErrorMessage(message);
          return false;
        }
      }

      return true;
    }

    showErrorMessage(message) {
      this.clearErrors();
      this.showNotification(message, 'error', '❌');
    }

    showSuccessMessage(message) {
      this.clearErrors();
      this.showNotification(message, 'success', '✅');
    }

    showNotification(message, type, icon) {
      const notificationElement = document.createElement("div");
      notificationElement.className = `amp-auth-${type} amp-notification ${type}`;
      notificationElement.innerHTML = `
        <div class="notification-content">
          <i class="notification-icon">${icon}</i>
          <span class="notification-message">${message}</span>
          <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
      `;

      const container = this.getActiveContainer();
      const form = container.querySelector("form");
      
      if (form) {
        container.insertBefore(notificationElement, form);
      } else {
        container.prepend(notificationElement);
      }

      const timeout = type === 'success' ? this.config.successTimeout : this.config.messageTimeout;
      this.addTimer(setTimeout(() => {
        if (notificationElement?.parentNode) {
          notificationElement.remove();
        }
      }, timeout));
    }

    getActiveContainer() {
      return document.querySelector(".amp-auth-form-container.active") || 
             document.getElementById('login-form-container') || 
             document.body;
    }

    clearErrors() {
      document.querySelectorAll(".amp-auth-error, .amp-auth-success, .amp-notification")
        .forEach(element => element.remove());
    }

    initTabs() {
      if (!this.elements.tabs) return;
      
      this.elements.tabs.forEach(tab => {
        tab.addEventListener("click", () => this.handleTabClick(tab));
      });
    }

    handleTabClick(tab) {
      const tabId = tab.getAttribute("data-tab");
      
      this.elements.tabs.forEach(t => t.classList.remove("active"));
      this.elements.formContainers.forEach(c => c.classList.remove("active"));

      tab.classList.add("active");
      const targetContainer = document.getElementById(tabId + "-form-container");
      if (targetContainer) {
        targetContainer.classList.add("active");
      }

      if (typeof window.renderTurnstileForActiveTab === 'function') {
        window.renderTurnstileForActiveTab();
      }
      
      this.updatePageTitle(tabId);
      this.updateURL(tabId);
      this.state.loginAttemptCounter = 0;
    }

    updatePageTitle(tabId) {
      if (this.elements.authTitle) {
        const titles = {
          'login': 'เข้าสู่ระบบ',
          'register': 'สมัครสมาชิก',
          'forgot-password': 'ลืมรหัสผ่าน',
          'reset-password': 'รีเซ็ตรหัสผ่าน'
        };
        this.elements.authTitle.textContent = titles[tabId] || 'เข้าสู่ระบบ';
      }
    }

    updateURL(tabId) {
      const url = new URL(window.location.href);
      url.searchParams.set("tab", tabId);
      ['action', 'token', 'user'].forEach(param => url.searchParams.delete(param));
      window.history.pushState({}, "", url);
    }

    initPasswordToggles() {
      document.querySelectorAll(".toggle-password").forEach(button => {
        button.removeEventListener("click", this.togglePasswordVisibility);
        button.addEventListener("click", (e) => this.togglePasswordVisibility(e));
      });
    }

    togglePasswordVisibility(e) {
      const button = e.currentTarget;
      const wrapper = button.closest('.ad-login-input-wrapper');
      const form = button.closest('form');
      const input = wrapper.querySelector("input");

      if (!input || !form) return;

      const isRegisterForm = form.id === 'register-form';
      const isResetForm = form.id === 'reset-password-form';
      const newType = input.type === "password" ? "text" : "password";

      if (isRegisterForm || isResetForm) {
        const otherInputId = (input.id === 'register-password' || input.id === 'reset-password')
          ? (isRegisterForm ? 'register-confirm-password' : 'reset-confirm-password')
          : (isRegisterForm ? 'register-password' : 'reset-password');
          
        const otherInput = document.getElementById(otherInputId);
        
        input.type = newType;
        button.classList.toggle("show", newType === 'text');
        
        if (otherInput) {
          const otherToggle = otherInput.closest('.ad-login-input-wrapper').querySelector('.toggle-password');
          otherInput.type = newType;
          if (otherToggle) {
            otherToggle.classList.toggle("show", newType === 'text');
          }
        }
      } else {
        input.type = newType;
        button.classList.toggle("show", newType === 'text');
      }
    }

    initDarkMode() {
      if (!this.elements.darkModeToggle) return;
      
      const manuallyToggled = document.cookie.includes("amp_dark_mode=");
      
      if (!manuallyToggled) {
        const currentHour = new Date().getHours();
        const isNightTime = currentHour >= 18 || currentHour < 6;
        document.body.classList.toggle("dark-mode", isNightTime);
      }
      
      this.elements.darkModeToggle.removeEventListener("click", this.darkModeToggleHandler);
      this.elements.darkModeToggle.addEventListener("click", () => this.darkModeToggleHandler());
    }

    darkModeToggleHandler() {
      const body = document.body;
      const isDarkMode = body.classList.contains("dark-mode");
      body.classList.toggle("dark-mode");
      document.cookie = `amp_dark_mode=${!isDarkMode}; path=/; max-age=31536000`;
      window.manuallyToggled = true;
    }

    initGoogleLogin() {
      if (typeof ampAuthData === "undefined" || !ampAuthData.googleLoginEnabled) return;

      document.querySelectorAll("#google-login-btn").forEach(button => {
        button.addEventListener("click", (e) => this.handleGoogleLogin(e, button));
      });
    }

    handleGoogleLogin(e, button) {
      e.preventDefault();

      if (button.disabled) {
        this.showErrorMessage("กรุณายืนยันตัวตนด้วย Cloudflare Turnstile ก่อนเข้าสู่ระบบด้วย Google");
        return;
      }

      if (!ampAuthData.googleClientId) {
        this.showErrorMessage("การตั้งค่า Google Login ไม่สมบูรณ์ กรุณากรอกข้อมูล Google Client ID ในหน้าตั้งค่า Google ก่อนใช้งาน");
        return;
      }

      let loginPageUrl = window.location.origin + window.location.pathname;
      if (!loginPageUrl.endsWith('/')) {
        loginPageUrl += '/';
      }
      
      const redirectUri = loginPageUrl + '?action=google_callback';
      const oauthNonce = ampAuthData.nonce || ampAuthData.loginNonce;
      const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
        `client_id=${encodeURIComponent(ampAuthData.googleClientId)}&` +
        `redirect_uri=${encodeURIComponent(redirectUri)}&` +
        `scope=${encodeURIComponent('https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/userinfo.profile')}&` +
        `response_type=code&` +
        `state=${encodeURIComponent(oauthNonce)}&` +
        `access_type=online&` +
        `prompt=select_account`;

      window.location.href = authUrl;
    }

    initPasswordStrengthMeter() {
      [this.elements.passwordInput, this.elements.resetPasswordInput].forEach(input => {
        if (input) {
          input.addEventListener('input', (e) => this.handlePasswordStrength(e));
        }
      });
    }

    handlePasswordStrength(e) {
      const form = e.target.closest('form');
      if (!form) return;

      const elements = {
        strengthText: form.querySelector('#password-strength-text'),
        strengthProgress: form.querySelector('#password-strength-progress'),
        requirements: {
          length: form.querySelector('#length-requirement'),
          uppercase: form.querySelector('#uppercase-requirement'),
          lowercase: form.querySelector('#lowercase-requirement'),
          number: form.querySelector('#number-requirement'),
          special: form.querySelector('#special-requirement')
        }
      };

      const password = e.target.value;
      const { strength, requirements } = this.calculatePasswordStrength(password);
      
      this.updateStrengthUI(strength, elements.strengthText, elements.strengthProgress);
      this.updateRequirementsUI(requirements, elements.requirements);
    }

    calculatePasswordStrength(password) {
      let strength = 0;
      const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /[0-9]/.test(password),
        special: /[^A-Za-z0-9]/.test(password)
      };

      Object.values(requirements).forEach(met => {
        if (met) strength++;
      });

      return { strength, requirements };
    }

    updateStrengthUI(strength, strengthTextEl, strengthProgressEl) {
      const strengthLevels = [
        { label: 'ไม่ระบุ', color: '#e0e0e0', percent: 0 },
        { label: 'อ่อนมาก', color: '#e74c3c', percent: 20 },
        { label: 'อ่อน', color: '#e67e22', percent: 40 },
        { label: 'ปานกลาง', color: '#f1c40f', percent: 60 },
        { label: 'ดี', color: '#2ecc71', percent: 80 },
        { label: 'ดีมาก', color: '#27ae60', percent: 100 }
      ];

      const level = strengthLevels[strength] || strengthLevels[0];

      if (strengthTextEl) strengthTextEl.textContent = level.label;
      if (strengthProgressEl) {
        strengthProgressEl.style.width = level.percent + '%';
        strengthProgressEl.style.backgroundColor = level.color;
      }
    }

    updateRequirementsUI(requirements, elements) {
      Object.entries(requirements).forEach(([key, met]) => {
        if (elements[key]) {
          elements[key].classList.toggle('met', met);
        }
      });
    }

    getTurnstileToken(formType) {
      const activeContainer = this.getActiveContainer();
      let turnstileContainer = activeContainer.querySelector('.turnstile-container');

      if (!turnstileContainer) {
        turnstileContainer = activeContainer.querySelector('[data-sitekey]');
      }
      
      if (!turnstileContainer) {
          console.error(`Turnstile container not found for form type: ${formType} or in active container.`);
          return null;
      }

      const widgetId = turnstileContainer.querySelector('iframe')?.id;
      if (typeof turnstile !== 'undefined' && widgetId) {
        try {
          const response = turnstile.getResponse(widgetId);
          return response || null;
        } catch (e) {
            console.error('Error getting Turnstile response:', e);
            return null;
        }
      }
      return null;
    }

    isRecoverableError(errorMessage) {
      const recoverableErrors = [
        'Invalid Nonce', 'การตรวจสอบความปลอดภัยล้มเหลว', 'session', 'expired',
        'Security check failed', 'Nonce verification failed', 'Session timeout',
        'Authentication failed', 'CSRF token', 'Token expired', 'Session invalid',
        'Login session', 'Security token', 'Verification failed', 'Access denied',
        'Unauthorized', 'Forbidden'
      ];

      return recoverableErrors.some(err => errorMessage.toLowerCase().includes(err.toLowerCase()));
    }

    async attemptSmartRecovery(errorMessage, retryCount, errorCode = null) {
      const context = this.getRecoveryContext();

      if (errorCode === 'NONCE_VERIFICATION_FAILED') {
        this.showErrorMessage(`กำลังขอ security token ใหม่... (${retryCount + 1}/${this.config.maxRetries})`);
        const nonceRefreshSuccess = await this.refreshNonces();
        if (nonceRefreshSuccess) {
          return true;
        }
      }

      const recoverySteps = this.getContextualRecoverySteps(errorMessage, context, retryCount);

      for (const step of recoverySteps) {
        this.showErrorMessage(`${this.getRecoveryStepMessage(step, context)}... (${retryCount + 1}/${this.config.maxRetries})`);
        const success = await this.executeRecoveryStep(step);
        if (success) return true;
      }

      return false;
    }

    getRecoveryContext() {
      const url = window.location.pathname;
      const isPopup = document.querySelector('.overlay-login-form') !== null;

      if (isPopup) return 'popup';
      if (url.includes('/login')) return 'login_page';
      if (url.includes('/dashboard')) return 'dashboard';
      return 'general';
    }

    getContextualRecoverySteps(errorMessage, context, retryCount) {
      const baseSteps = [];
      const lowerMsg = errorMessage.toLowerCase();

      if (lowerMsg.includes('nonce') || lowerMsg.includes('token') || lowerMsg.includes('csrf')) {
        baseSteps.push('refreshNonces');
      }

      if (lowerMsg.includes('session') || lowerMsg.includes('expired') || lowerMsg.includes('timeout')) {
        baseSteps.push('refreshSession');
      }

      if (lowerMsg.includes('auth') || lowerMsg.includes('login') || lowerMsg.includes('access')) {
        baseSteps.push('fullRecovery');
      }

      if (baseSteps.length === 0) {
        baseSteps.push('refreshNonces');
      }

      const contextSteps = {
        popup: ['refreshNonces', 'refreshSession'],
        login_page: retryCount >= 2 ? ['fullRecovery', 'forceRefresh'] : baseSteps,
        dashboard: ['refreshSession', 'refreshNonces'],
        general: baseSteps
      };

      return contextSteps[context] || baseSteps;
    }

    getRecoveryStepMessage(step, context) {
      const messages = {
        refreshNonces: {
          popup: 'กำลังรีเฟรชข้อมูลความปลอดภัย',
          login_page: 'กำลังอัพเดทข้อมูลการเข้าสู่ระบบ',
          dashboard: 'กำลังซิงค์ข้อมูลความปลอดภัย',
          general: 'กำลังรีเฟรชข้อมูลความปลอดภัย'
        },
        refreshSession: {
          popup: 'กำลังต่ออายุเซสชัน',
          login_page: 'กำลังตรวจสอบสถานะการเข้าสู่ระบบ',
          dashboard: 'กำลังรีเฟรชเซสชัน',
          general: 'กำลังต่ออายุเซสชัน'
        },
        fullRecovery: {
          popup: 'กำลังกู้คืนระบบ',
          login_page: 'กำลังรีเซ็ตการเข้าสู่ระบบ',
          dashboard: 'กำลังกู้คืนการเชื่อมต่อ',
          general: 'กำลังกู้คืนระบบ'
        },
        forceRefresh: {
          popup: 'กำลังรีเฟรชหน้าเว็บ',
          login_page: 'กำลังรีเฟรชหน้าเว็บ',
          dashboard: 'กำลังรีเฟรชหน้าเว็บ',
          general: 'กำลังรีเฟรชหน้าเว็บ'
        }
      };

      return messages[step]?.[context] || messages[step]?.general || 'กำลังดำเนินการ';
    }

    async executeRecoveryStep(step) {
      return await this.recoveryStrategies[step]?.() || false;
    }

    async forcePageRefresh() {
      try {
        this.showErrorMessage('กำลังรีเฟรชหน้าเว็บ...');
        this.addTimer(setTimeout(() => window.location.reload(), 1000));
        return true;
      } catch (error) {
        return false;
      }
    }

    async refreshSessionData() {
      try {
        if (typeof window.syncSecurityDataPeriodically === 'function') {
          await window.syncSecurityDataPeriodically();
          return true;
        }
        return await this.refreshNonces();
      } catch (error) {
        return false;
      }
    }

    async fullSystemRecovery() {
      try {
        for (const step of [this.refreshNonces, this.refreshSessionData]) {
          const success = await step.call(this);
          if (success) return true;
        }
        return false;
      } catch (error) {
        return false;
      }
    }

    async retryRequest() {
      return true;
    }

    updateSecurityData(nonces) {
      if (!nonces) return;

      if (window.ampAuthData) {
        window.ampAuthData.loginNonce = nonces.dashboardNonce;
      }

      if (window.adDashboardData) {
        window.adDashboardData.nonce = nonces.dashboardNonce;
      }

      this.updateAllFormNonces(nonces);
    }

    updateAllFormNonces(nonces) {
      document.querySelectorAll('form').forEach(form => {
        form.querySelectorAll('input[name*="_nonce"]').forEach(input => {
          input.value = nonces.dashboardNonce;
        });
      });
    }

    async refreshNonces() {
      try {
        if (typeof window.syncSecurityDataPeriodically === 'function') {
          await window.syncSecurityDataPeriodically();
          return true;
        } else if (window.ampAuthData) {
          const response = await fetch(window.ampAuthData.ajaxurl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'action=amp_refresh_nonces',
            credentials: 'same-origin'
          });

          if (!response.ok) return false;

          const data = await response.json();
          if (data.success) {
            this.updateSecurityData(data);
            return true;
          }
        }
        return false;
      } catch (error) {
        return false;
      }
    }
  }

  const authManager = new AuthManager();

  window.initPasswordToggles = function () {
    authManager.initPasswordToggles();
  };

  window.initAuthTabs = function () {
    authManager.initTabs();
  };

  window.getTurnstileToken = function(formType) {
    return authManager.getTurnstileToken(formType);
  };

  window.updateAllFormNonces = function(nonces) {
    authManager.updateAllFormNonces(nonces);
  };

  document.addEventListener("DOMContentLoaded", function () {
    authManager.init();
  });

})();