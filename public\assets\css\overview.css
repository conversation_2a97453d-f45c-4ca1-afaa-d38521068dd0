.overview-container {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
}

.overview-welcome {
    text-align: center;
    margin-bottom: 30px;
    background: var(--theme-gradient);
    border-radius: 24px;
    box-shadow: var(--shadow-lg);
    padding: 35px;
    position: relative;
    overflow: hidden;
    border: none;
}

.overview-welcome::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.overview-welcome h2 {
    font-size: 28px;
    margin-bottom: 10px;
    color: white;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.overview-welcome p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
}

.overview-stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow);
    padding: 25px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 1px solid var(--divider-color);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.stat-card:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--theme-gradient-light);
    z-index: 0;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--theme-gradient-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.stat-card:hover .stat-icon {
    background: var(--theme-gradient);
    transform: scale(1.1);
}

.stat-card:hover .stat-icon i {
    color: white;
}

.stat-icon i {
    font-size: 24px;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.stat-content {
    position: relative;
    z-index: 1;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: var(--light-text);
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
}

.overview-section {
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow);
    padding: 30px;
    margin-bottom: 40px;
    border: 1px solid var(--divider-color);
}

.overview-section h3 {
    font-size: 20px;
    margin: 0 0 20px 0;
    color: var(--text-color);
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 15px;
    position: relative;
}

.overview-section h3::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--theme-gradient);
}

.overview-ads {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.overview-ad-card {
    background: var(--card-bg);
    border: 1px solid var(--divider-color);
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.overview-ad-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.overview-ad-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.overview-ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.overview-ad-card:hover .overview-ad-image img {
    transform: scale(1.05);
}

.placeholder-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(67, 97, 238, 0.1);
}

.placeholder-image i {
    font-size: 40px;
    color: var(--primary-color);
}

.overview-ad-details {
    padding: 20px;
}

.overview-ad-details h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: var(--text-color);
}

.overview-ad-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.overview-ad-stat {
    display: flex;
    align-items: center;
    color: var(--light-text);
    font-size: 14px;
}

.overview-ad-stat i {
    margin-right: 5px;
    color: var(--primary-color);
}

.overview-ad-actions {
    display: flex;
    gap: 10px;
}

.overview-ad-btn {
    padding: 8px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s ease;
}

.overview-ad-btn:hover {
    background-color: var(--primary-hover);
}

.renew-btn {
    background-color: var(--success-color);
}

.renew-btn:hover {
    background-color: #27ae60;
}

.overview-empty {
    text-align: center;
    padding: 50px 20px;
    background-color: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--shadow);
    margin-bottom: 40px;
}

.overview-empty-icon {
    font-size: 60px;
    color: var(--primary-color);
    margin-bottom: 20px;
    opacity: 0.5;
}

.overview-empty h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.overview-empty p {
    color: var(--light-text);
    margin-bottom: 30px;
}

.overview-cta-btn {
    padding: 12px 25px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.overview-cta-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
}

.overview-summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.overview-summary-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
    display: flex;
    align-items: flex-start;
    border-left: 4px solid var(--primary-color);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
}

.summary-icon i {
    font-size: 20px;
    color: var(--primary-color);
}

.summary-content {
    flex: 1;
}

.summary-content h4 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: var(--text-color);
    font-weight: 600;
}

.summary-stats {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-stat {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.stat-label {
    color: var(--light-text);
}

.stat-value {
    font-weight: 600;
    color: var(--text-color);
}

@media (max-width: 991px) {
    .overview-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .overview-summary-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .overview-stats-grid {
        grid-template-columns: 1fr;
    }

    .overview-ad-stats {
        flex-direction: column;
        gap: 10px;
    }
}
