<?php
if (!defined('ABSPATH')) {
    exit;
}

if (!current_user_can('manage_options')) {
    wp_die('คุณไม่มีสิทธิ์เข้าถึงหน้านี้');
}

require_once AMP_PLUGIN_DIR . 'includes/modules/payments/class-payment-handler.php';
$payment_handler = AMP_Payment_Handler::instance();
wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', array(), '11.0.0', true);
?>

<div class="settings-card">
    <h2>🔍 Manual Payment Verification</h2>
    <div class="verification-tabs">
        <button class="tab-btn active" data-tab="pending">⏳ รอดำเนินการ</button>
        <button class="tab-btn" data-tab="history">📋 ประวัติการชำระ</button>
        <button class="tab-btn" data-tab="logs">📊 Webhook Logs</button>
        <button class="tab-btn" data-tab="simulator">⚡️ Webhook Simulator</button>
    </div>

    <div id="pending-tab" class="verification-tab-content active">
        <div class="section-header">
            <h2>คำสั่งซื้อที่รอดำเนินการ</h2>
            <button id="refresh-pending" class="btn-refresh">🔄 รีเฟรช</button>
        </div>

        <div id="pending-orders-container">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลดข้อมูล...</p>
            </div>
        </div>
    </div>

    <div id="history-tab" class="verification-tab-content">
        <div class="section-header">
            <h2>ประวัติการชำระเงิน</h2>
            <div class="header-actions">
                <button id="clear-payment-history" class="btn-danger">🗑️ ล้างประวัติ</button>
                <div class="pagination-controls">
                    <button id="prev-page" class="btn-pagination">← ก่อนหน้า</button>
                    <span id="page-info">หน้า 1</span>
                    <button id="next-page" class="btn-pagination">ถัดไป →</button>
                </div>
            </div>
        </div>

        <div id="payment-history-container">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลดประวัติ...</p>
            </div>
        </div>
    </div>

    <div id="logs-tab" class="verification-tab-content">
        <div class="section-header">
            <h2>Webhook Logs</h2>
            <div class="header-actions">
                <button id="refresh-logs" class="btn-refresh">🔄 รีเฟรช</button>
                <button id="clear-logs" class="btn-danger">🗑️ ล้าง Logs</button>
                <button id="logs-stats" class="btn-info">📊 สถิติ</button>
            </div>
        </div>

        <div class="webhook-logs-tabs">
            <button class="webhook-tab-btn" data-mode="official">🔥 Official Webhooks</button>
            <button class="webhook-tab-btn" data-mode="simulated">🧪 Simulated Webhooks</button>
            <button class="webhook-tab-btn active" data-mode="all">📋 All Logs</button>
        </div>

        <div id="logs-stats-container" style="display: none;">
            <div class="stats-card">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">Webhook Logs:</span>
                        <span id="webhook-files-count">-</span> ไฟล์
                        (<span id="webhook-size">-</span>)
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Payment History:</span>
                        <span id="payment-files-count">-</span> ไฟล์
                        (<span id="payment-size">-</span>)
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">รวมทั้งหมด:</span>
                        <span id="total-files-count">-</span> ไฟล์
                        (<span id="total-size">-</span>)
                    </div>
                </div>
            </div>
        </div>

        <div id="webhook-logs-container">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลด logs...</p>
            </div>
        </div>
    </div>

    <div id="simulator-tab" class="verification-tab-content">
        <div class="section-header">
            <h2>⚡️ Webhook Simulator</h2>
            <p>จำลองการรับ Webhook จาก Plisio เพื่อทดสอบสถานะต่างๆ ของการชำระเงิน</p>
        </div>

        <div class="simulator-form">
            <div class="simulator-notice">
                <div class="notice-icon">🧪</div>
                <div class="notice-content">
                    <h4>Webhook Simulator - โหมดทดสอบ</h4>
                    <p>เครื่องมือนี้จำลองการส่ง webhook จาก Plisio เพื่อทดสอบระบบการชำระเงิน<br>
                    <strong>หมายเหตุ:</strong> Transaction ID จะถูกสร้างอัตโนมัติหากไม่ระบุ</p>
                </div>
            </div>

            <div class="status-preview-section">
                <h4>🎨 สถานะที่ส่งได้ใน Simulator (5 สถานะ):</h4>
                <div class="status-preview-grid">
                    <div class="status-preview-item">
                        <span class="history-status status-completed">completed</span>
                        <small>สำเร็จ (เขียว)</small>
                    </div>
                    <div class="status-preview-item">
                        <span class="history-status status-mismatch">mismatch</span>
                        <small>จ่ายเกิน - สำเร็จ (เหลือง)</small>
                    </div>
                    <div class="status-preview-item">
                        <span class="history-status status-cancelled">cancelled</span>
                        <small>ยกเลิก (เทา)</small>
                    </div>
                    <div class="status-preview-item">
                        <span class="history-status status-expired">expired</span>
                        <small>หมดอายุ (ส้ม)</small>
                    </div>
                    <div class="status-preview-item">
                        <span class="history-status status-failed">error</span>
                        <small>ข้อผิดพลาด (แดง)</small>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #dc3545;">
                    <small><strong>หมายเหตุ:</strong> <code>signature_failed</code> (แดง) เป็นสถานะที่ระบบตอบกลับมาเมื่อตรวจสอบลายเซ็นไม่ผ่าน ไม่สามารถส่งทดสอบได้</small>
                </div>
            </div>

            <div class="form-group">
                <label for="sim-order-number">📋 หมายเลขคำสั่งซื้อ (Order Number) <span class="required">*</span></label>
                <input type="text" id="sim-order-number" placeholder="เช่น AMP-34-1750830134" required>
                <small>ใส่ Order ID ที่ต้องการทดสอบ (ต้องเป็นรายการที่อยู่ในสถานะรอดำเนินการ)</small>
            </div>

            <div class="form-group">
                <label for="sim-txn-id">🆔 Transaction ID <span class="optional">(ไม่บังคับ)</span></label>
                <div class="input-with-btn">
                    <input type="text" id="sim-txn-id" placeholder="จะสร้างอัตโนมัติหากไม่ระบุ">
                    <button id="generate-txn-id" class="btn-secondary">🎲 สร้างอัตโนมัติ</button>
                </div>
                <small>รหัสอ้างอิงของ Plisio สำหรับการชำระเงินนี้</small>
            </div>

            <div class="form-group">
                <label for="sim-amount">💰 จำนวนเงิน (USD) <span class="required">*</span></label>
                <input type="number" id="sim-amount" step="0.01" placeholder="เช่น 108.00" required min="0.01">
                <small>จำนวนเงินที่ต้องการทดสอบว่าได้รับ</small>
            </div>
            
            <div class="form-group">
                <label for="sim-status">📊 สถานะ Webhook (Status)</label>
                <select id="sim-status">
                    <optgroup label="✅ สถานะสำเร็จ">
                        <option value="completed">✅ completed (ชำระเงินสำเร็จ)</option>
                        <option value="mismatch">💰 mismatch (จ่ายเกิน - สำเร็จ)</option>
                    </optgroup>
                    <optgroup label="❌ สถานะล้มเหลว">
                        <option value="cancelled">🚫 cancelled (ยกเลิก)</option>
                        <option value="expired">⏰ expired (หมดอายุ)</option>
                        <option value="error">❗ error (ข้อผิดพลาด)</option>
                    </optgroup>
                </select>
                <small>เลือกสถานะ Plisio Official ที่ต้องการทดสอบ (signature_failed จะเกิดขึ้นอัตโนมัติเมื่อลายเซ็นผิด)</small>
            </div>

            <button id="send-simulation-btn" class="btn-primary">⚡️ ส่ง Webhook จำลอง</button>
            <button id="test-status-colors" class="btn-secondary" style="margin-top: 10px;">🎨 ทดสอบสีสถานะ</button>
        </div>

        <div id="simulation-result-container" style="display:none; margin-top: 20px;">
             <h4>ผลลัพธ์การจำลอง:</h4>
             <pre id="simulation-result"></pre>
        </div>
    </div>
</div>

<div id="signature-log-modal" class="modal-overlay" style="display:none;">
    <div class="modal-content details-modal">
        <div class="modal-header">
            <h3>📝 ผลการตรวจสอบลายเซ็น (Signature Verification)</h3>
            <span class="modal-close" onclick="closeSignatureLogModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="signature-log-content">
                <!-- Details will be loaded here by JavaScript -->
            </div>
        </div>
        <div class="modal-footer">
            <button onclick="closeSignatureLogModal()" class="btn-cancel">ปิด</button>
        </div>
    </div>
</div>

<div id="verification-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>🔍 ยืนยันการชำระเงิน</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="verification-form">
                <div class="form-group">
                    <label>หมายเลขคำสั่งซื้อ:</label>
                    <input type="text" id="verify-order-id" readonly>
                </div>
                <div class="form-group">
                    <label>ผู้ใช้:</label>
                    <input type="text" id="verify-user" readonly>
                </div>
                <div class="form-group">
                    <label>จำนวนเงิน:</label>
                    <input type="text" id="verify-amount" readonly>
                </div>
                <div class="form-group">
                    <label>Transaction ID (ถ้ามี):</label>
                    <input type="text" id="verify-txn-id" placeholder="ใส่ Transaction ID จาก Plisio">
                </div>
                <div class="form-group">
                    <label>รายการสินค้า:</label>
                    <div id="verify-cart-items"></div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="confirm-payment" class="btn-success">✅ ยืนยันการชำระเงิน</button>
            <button id="cancel-verification" class="btn-cancel">❌ ยกเลิก</button>
        </div>
    </div>
</div>



<!-- API Verification Results Modal -->
<div id="api-verification-modal" class="modal">
    <div class="modal-content verification-modal">
        <div class="modal-header">
            <h3>🔍 ผลการตรวจสอบ API</h3>
            <span class="close" onclick="closeLogDetails()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="api-verification-content">
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>กำลังตรวจสอบ...</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button onclick="closeLogDetails()" class="btn-cancel">ปิด</button>
        </div>
    </div>
</div>

<!-- Clear Payment History Popup -->
<div id="clear-payment-popup" class="amp-popup-overlay" style="display: none;">
    <div class="amp-popup-container">
        <div class="amp-popup-header">
            <h3>🗑️ ยืนยันการลบประวัติการชำระเงิน</h3>
            <button class="amp-popup-close" id="close-payment-popup">&times;</button>
        </div>
        <div class="amp-popup-content">
            <div class="warning-icon">⚠️</div>
            <p class="warning-text">คุณแน่ใจหรือไม่ที่จะลบประวัติการชำระเงินทั้งหมด?</p>
            <div class="warning-details">
                <p><strong>การดำเนินการนี้จะลบ:</strong></p>
                <ul>
                    <li>📄 ไฟล์ประวัติการชำระเงินทั้งหมด</li>
                    <li>📁 โฟลเดอร์ที่เก็บข้อมูลการชำระเงิน</li>
                    <li>📊 ข้อมูลสถิติการชำระเงิน</li>
                    <li>💳 รายละเอียด transaction ทั้งหมด</li>
                </ul>
                <div class="danger-notice">
                    <strong>⚠️ คำเตือน:</strong> การดำเนินการนี้ไม่สามารถย้อนกลับได้!
                </div>
            </div>
        </div>
        <div class="amp-popup-footer">
            <button class="btn-cancel" id="cancel-clear-payment">❌ ยกเลิก</button>
            <button class="btn-confirm-delete" id="confirm-clear-payment">🗑️ ลบทั้งหมด</button>
        </div>
    </div>
</div>

<div id="clear-logs-popup" class="amp-popup-overlay" style="display: none;">
    <div class="amp-popup-container">
        <div class="amp-popup-header">
            <h3>🗑️ ยืนยันการลบ Webhook Logs</h3>
            <button class="amp-popup-close" id="close-logs-popup">&times;</button>
        </div>
        <div class="amp-popup-content">
            <div class="warning-icon">⚠️</div>
            <p class="warning-text">คุณแน่ใจหรือไม่ที่จะลบ Webhook Logs ทั้งหมด?</p>
            <div class="warning-details">
                <p><strong>การดำเนินการนี้จะลบ:</strong></p>
                <ul>
                    <li>📄 ไฟล์ log ทั้งหมดในระบบ</li>
                    <li>📁 โฟลเดอร์ที่เก็บ webhook logs</li>
                    <li>📊 ข้อมูลสถิติที่เกี่ยวข้อง</li>
                </ul>
                <div class="danger-notice">
                    <strong>⚠️ คำเตือน:</strong> การดำเนินการนี้ไม่สามารถย้อนกลับได้!
                </div>
            </div>
        </div>
        <div class="amp-popup-footer">
            <button class="btn-cancel" id="cancel-clear-logs">❌ ยกเลิก</button>
            <button class="btn-confirm-delete" id="confirm-clear-logs">🗑️ ลบทั้งหมด</button>
        </div>
    </div>
</div>

<style>
.verification-tabs {
    display: flex;
    background: var(--light-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: var(--light-color);
    color: var(--text-muted);
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
}

.webhook-logs-tabs {
    display: flex;
    background: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.webhook-tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: var(--light-color);
    color: var(--text-muted);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.webhook-tab-btn:hover {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
}

.webhook-tab-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

.verification-tab-content {
    display: none;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow);
}

.verification-tab-content.active {
    display: block;
    animation: fadeInUp 0.3s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .pagination-controls {
        justify-content: center;
    }
}

.section-header h2 {
    color: var(--text-color);
    font-weight: 600;
    margin: 0;
}

.btn-refresh, .btn-pagination {
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-refresh:hover, .btn-pagination:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-danger {
    padding: 8px 16px;
    border: 1px solid #dc3545;
    background: #dc3545;
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-info {
    padding: 8px 16px;
    border: 1px solid #17a2b8;
    background: #17a2b8;
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

.stats-card {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-label {
    font-weight: 600;
    color: var(--text-muted);
}

.loading-state {
    text-align: center;
    padding: 60px 40px;
    color: var(--text-muted);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.order-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.order-header h3 {
    color: var(--text-color);
    margin: 0;
    font-weight: 600;
}

.order-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 5px;
    letter-spacing: 0.5px;
}

.info-value {
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
}

.cart-items {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-top: 10px;
    border: 1px solid var(--border-color);
}

.cart-item {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 14px;
}

.cart-item:last-child {
    border-bottom: none;
}

.order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-api-verify {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-api-verify:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-verify {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-verify:hover {
    background: var(--success-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-simulate {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.btn-simulate:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.btn-delete {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
}

.btn-delete:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}


.status-verified {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    background: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.empty-state h3 {
    margin-bottom: 10px;
    color: var(--text-color);
    font-weight: 600;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

#page-info {
    font-weight: 600;
    color: var(--text-color);
    padding: 8px 16px;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--card-bg);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 600px;
    box-shadow: var(--shadow-hover);
    border: 1px solid var(--border-color);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: var(--primary-color);
    color: white;
    padding: 20px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-weight: 600;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: var(--light-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    background: var(--card-bg);
    color: var(--text-color);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.form-group input[readonly] {
    background: var(--light-color);
    color: var(--text-muted);
}

.btn-success {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-success:hover {
    background: var(--success-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.amp-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 999999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.amp-popup-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    max-width: 500px;
    width: 90%;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: popupSlideIn 0.3s ease-out;
    border: 2px solid #e74c3c;
}

.amp-popup-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 20px 25px;
    border-radius: 18px 18px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.amp-popup-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.amp-popup-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}

.amp-popup-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.amp-popup-content {
    padding: 30px 25px;
    text-align: center;
}

.warning-icon {
    font-size: 48px;
    margin-bottom: 15px;
    animation: pulse 2s infinite;
}

.warning-text {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
}

.warning-details {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 20px;
    text-align: left;
    margin-bottom: 20px;
}

.warning-details ul {
    margin: 10px 0;
    padding-left: 20px;
}

.warning-details li {
    margin: 8px 0;
    color: #856404;
}

.danger-notice {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 12px;
    border-radius: 6px;
    margin-top: 15px;
    font-weight: 600;
}

.amp-popup-footer {
    padding: 20px 25px;
    display: flex;
    gap: 15px;
    justify-content: center;
    border-top: 1px solid #dee2e6;
}

.btn-cancel {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.btn-cancel:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
}

.btn-confirm-delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-confirm-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
}

@keyframes popupSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.amp-notification {
    position: fixed;
    top: 30px;
    right: 30px;
    padding: 20px 25px;
    border-radius: 15px;
    color: white;
    font-weight: 600;
    z-index: 999999;
    transform: translateX(450px);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 5px 15px rgba(0, 0, 0, 0.1);
    max-width: 420px;
    min-width: 300px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    line-height: 1.4;
}

.amp-notification.success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-left: 4px solid #1e7e34;
}

.amp-notification.success::before {
    content: "✅";
    font-size: 20px;
    flex-shrink: 0;
}

.amp-notification.error {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-left: 4px solid #bd2130;
}

.amp-notification.error::before {
    content: "❌";
    font-size: 20px;
    flex-shrink: 0;
}

.amp-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.amp-notification:hover {
    transform: translateX(-5px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25), 0 8px 20px rgba(0, 0, 0, 0.15);
}

.close {
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 50%;
}

.close:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
}

.details-modal {
    max-width: 800px;
    width: 95%;
}

.verification-modal {
    max-width: 700px;
    width: 90%;
}

.details-content {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 15px 0;
    border: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    white-space: pre-line;
    line-height: 1.6;
    color: var(--text-color);
    max-height: 500px;
    overflow-y: auto;
}

.verification-content {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 15px 0;
    border: 1px solid var(--border-color);
    font-family: 'Courier New', monospace;
    white-space: pre-line;
    line-height: 1.6;
    color: var(--text-color);
    max-height: 400px;
    overflow-y: auto;
}

.verification-success {
    border-left: 4px solid var(--success-color);
    background: rgba(40, 167, 69, 0.1);
}

.verification-error {
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.detail-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-color);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px dotted var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: var(--text-muted);
    flex: 1;
}

.detail-value {
    font-weight: 600;
    color: var(--text-color);
    text-align: right;
    flex: 1;
    word-break: break-all;
}

/* Webhook Logs Cards Styles */
.webhook-logs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.webhook-log-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.webhook-log-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    border-color: var(--primary-color);
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.log-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.log-date {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

.log-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    white-space: nowrap;
}



.status-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-info {
    background: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(67, 97, 238, 0.3);
}

.history-status[class*="status-warning"] { background-color: #ffc107; color: #333; }
.history-status[class*="status-info"] { background-color: #17a2b8; }
.history-card[class*="status-warning"] { border-left-color: #ffc107; }
.history-card[class*="status-info"] { border-left-color: #17a2b8; }

.btn-details {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-details:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
}

.log-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.summary-label {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

.summary-value {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 600;
    font-family: monospace;
    background: var(--light-color);
    padding: 4px 8px;
    border-radius: 4px;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mode-live {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white !important;
    font-weight: 700;
}

.mode-test {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529 !important;
    font-weight: 700;
}

/* Modal Overlay for Log Details */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.modal-overlay .modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 90vh; /* Set max height for the modal */
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    display: flex; /* Use Flexbox */
    flex-direction: column; /* Arrange items vertically */
}

.modal-header {
    flex-shrink: 0; /* Prevent header from shrinking */
}

.modal-body {
    padding: 20px 30px;
    overflow-y: auto; /* Make body scrollable */
    flex-grow: 1; /* Allow body to take up available space */
}

.modal-footer {
    flex-shrink: 0; /* Prevent footer from shrinking */
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.log-detail-info {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px dotted var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
}

.log-detail-data {
    margin-top: 20px;
}

.log-detail-data h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-weight: 600;
}

.json-data {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    color: #333;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

@media (max-width: 768px) {
    .webhook-logs-grid {
        grid-template-columns: 1fr;
    }

    .log-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .btn-details {
        align-self: flex-start;
    }

    .modal-overlay .modal-content {
        width: 95%;
        margin: 20px;
    }
}

.transaction-link, .transaction-link-modal {
    color: #007cba;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 600;
    transition: color 0.2s ease;
}

.transaction-link:hover, .transaction-link-modal:hover {
    color: #005a87;
    text-decoration: none;
}

.log-detail-data pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.simulator-form {
    background: var(--light-color);
    padding: 30px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.simulator-form .form-group {
    margin-bottom: 25px;
}

.simulator-form .form-group small {
    display: block;
    margin-top: 8px;
    color: var(--text-muted);
    font-size: 13px;
}

.simulator-form .btn-primary {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
}

.simulator-notice {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffc107;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.notice-icon {
    font-size: 24px;
    flex-shrink: 0;
}

.notice-content h4 {
    margin: 0 0 8px 0;
    color: #856404;
    font-weight: 600;
}

.notice-content p {
    margin: 0;
    color: #856404;
    line-height: 1.5;
}

.required {
    color: #dc3545;
    font-weight: 700;
}

.status-preview-section {
    background: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 25px;
}

.status-preview-section h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-weight: 600;
}

.status-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-preview-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.status-preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-preview-item .history-status {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
    min-width: 100px;
    text-align: center;
}

.status-preview-item small {
    font-size: 11px;
    color: var(--text-muted);
    text-align: center;
    font-weight: 500;
}

.optional {
    color: #6c757d;
    font-weight: 500;
    font-size: 12px;
}

#simulation-result-container {
    margin-top: 25px;
    background: var(--light-color);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

#simulation-result-container h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

#simulation-result {
    background: #2d3748;
    color: #f7fafc;
    padding: 15px;
    border-radius: var(--border-radius);
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 400px;
    overflow-y: auto;
}

.input-with-btn {
    display: flex;
    gap: 10px;
}

.input-with-btn input {
    flex-grow: 1;
}

.input-with-btn .btn-secondary {
    padding: 10px 15px;
    white-space: nowrap;
}

.btn-secondary {
    padding: 8px 16px;
    border: 1px solid #6c757d;
    background: #6c757d;
    color: white;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
}



.log-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.summary-label {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 500;
}

.summary-value {
    font-size: 13px;
    color: var(--text-color);
    font-weight: 600;
    font-family: monospace;
    background: var(--light-color);
    padding: 4px 8px;
    border-radius: 4px;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.mode-live {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white !important;
    font-weight: 700;
}

.mode-test {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529 !important;
    font-weight: 700;
}

/* Modal Overlay for Log Details */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.modal-overlay .modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 800px;
    max-height: 90vh; /* Set max height for the modal */
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    display: flex; /* Use Flexbox */
    flex-direction: column; /* Arrange items vertically */
}

.modal-header {
    flex-shrink: 0; /* Prevent header from shrinking */
}

.modal-body {
    padding: 20px 30px;
    overflow-y: auto; /* Make body scrollable */
    flex-grow: 1; /* Allow body to take up available space */
}

.modal-footer {
    flex-shrink: 0; /* Prevent footer from shrinking */
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.log-detail-info {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px dotted var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
}

.log-detail-data {
    margin-top: 20px;
}

.log-detail-data h4 {
    margin: 0 0 15px 0;
    color: var(--text-color);
    font-weight: 600;
}

.json-data {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.5;
    color: #333;
    max-height: 400px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

@media (max-width: 768px) {
    .webhook-logs-grid {
        grid-template-columns: 1fr;
    }

    .log-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }



    .modal-overlay .modal-content {
        width: 95%;
        margin: 20px;
    }
}

.transaction-link, .transaction-link-modal {
    color: #007cba;
    cursor: pointer;
    text-decoration: underline;
    font-weight: 600;
    transition: color 0.2s ease;
}

.transaction-link:hover, .transaction-link-modal:hover {
    color: #005a87;
    text-decoration: none;
}

.log-detail-data pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.log-detail-data .json-data {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    color: #333;
    max-height: 350px; /* Set max height */
    overflow-y: auto;   /* Enable vertical scroll */
    white-space: pre-wrap;
    word-wrap: break-word;
}

.modal-body {
    padding: 30px;
}

/* New Payment History Card Styles */
#payment-history-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

.history-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-left: 5px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.history-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-hover);
}

.history-card[class*="status-completed"] { border-left-color: #28a745; }
.history-card[class*="status-mismatch"] { border-left-color: #ffc107; }
.history-card[class*="status-cancelled"] { border-left-color: #6c757d; }
.history-card[class*="status-expired"] { border-left-color: #fd7e14; }
.history-card[class*="status-failed"] { border-left-color: #dc3545; }
.history-card.status-manual-verification { border-left-color: #17a2b8; }


.history-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.history-header-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.history-header-info .order-id {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.history-header-info .payment-date {
    font-size: 13px;
    color: var(--text-muted);
}

.history-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: white;
}

.history-status[class*="status-completed"] { background-color: #28a745; }
.history-status[class*="status-mismatch"] { background-color: #ffc107; color: #333; }
.history-status[class*="status-cancelled"] { background-color: #6c757d; }
.history-status[class*="status-expired"] { background-color: #fd7e14; }
.history-status[class*="status-failed"] { background-color: #dc3545; }
.history-status.status-manual-verification { background-color: #17a2b8; }


.history-body {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.history-item {
    display: flex;
    flex-direction: column;
}

.history-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 4px;
    text-transform: uppercase;
}

.history-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    word-break: break-all;
}

.history-value.user-info {
    font-weight: 600;
}

.history-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    margin-top: auto;
}

.btn-reverify {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    font-weight: 600;
    padding: 8px 16px;
    font-size: 13px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-reverify:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.4);
}


@media (max-width: 768px) {
    .verification-tabs, .webhook-logs-tabs {
        flex-direction: column;
    }
}

.signature-log-grid {
     display: grid;
     gap: 15px;
     font-family: 'Courier New', monospace;
     font-size: 13px;
}
.signature-log-section {
    background: var(--card-bg);
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}
.signature-log-section h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}
.signature-log-section code {
    display: block;
    word-wrap: break-word;
    white-space: pre-wrap;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    color: #333;
}
.signature-log-section .status-completed {
    font-weight: bold;
    color: var(--success-color);
}
.signature-log-section .status-failed {
    font-weight: bold;
    color: #dc3545;
}

.btn-debug-signature {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    padding: 6px 12px;
    font-size: 11px;
    font-weight: 600;
}
.btn-debug-signature:hover {
    background: linear-gradient(135deg, #5a6268, #4e555b);
}
</style>

<script>
var ajaxurl = '<?php echo admin_url("admin-ajax.php"); ?>';

function getStatusInfo(status) {
    const statusMap = {
        'completed': { text: 'COMPLETED', class: 'status-completed', color: '#28a745' },
        'mismatch': { text: 'MISMATCH', class: 'status-mismatch', color: '#ffc107' },
        'cancelled': { text: 'CANCELLED', class: 'status-cancelled', color: '#6c757d' },
        'expired': { text: 'EXPIRED', class: 'status-expired', color: '#fd7e14' },
        'error': { text: 'ERROR', class: 'status-failed', color: '#dc3545' },
        'signature_failed': { text: 'SIGNATURE_FAILED', class: 'status-failed', color: '#dc3545' },
        'pending': { text: 'PENDING', class: 'status-warning', color: '#ffc107' }
    };
    
    if (status && status.includes('_failed')) {
        const baseStatus = status.replace('_failed', '');
        const displayText = baseStatus.toUpperCase() + '_FAILED';
        
        if (baseStatus === 'cancelled') {
            return { text: displayText, class: 'status-cancelled', color: '#6c757d' };
        } else if (baseStatus === 'expired') {
            return { text: displayText, class: 'status-expired', color: '#fd7e14' };
        } else if (baseStatus === 'error') {
            return { text: displayText, class: 'status-failed', color: '#dc3545' };
        } else if (baseStatus === 'signature') {
            return { text: 'SIGNATURE_FAILED', class: 'status-failed', color: '#dc3545' };
        } else {
            return { text: displayText, class: 'status-failed', color: '#dc3545' };
        }
    }
    
    return statusMap[status] || { text: status || 'UNKNOWN', class: 'status-info', color: '#17a2b8' };
}

function copyLogData(index) {
    if (!window.webhookLogsData || !window.webhookLogsData[index]) {
        alert('ไม่พบข้อมูล log');
        return;
    }
    const log = window.webhookLogsData[index];
    let eventData = '';
    try {
        eventData = JSON.stringify(log.data || log, null, 2);
    } catch (e) {
        eventData = 'Error parsing data: ' + e.message;
    }
    navigator.clipboard.writeText(eventData).then(function() {
        showSuccessMessage('📋 คัดลอกข้อมูลดิบเรียบร้อยแล้ว');
    }).catch(function() {
        const textArea = document.createElement('textarea');
        textArea.value = eventData;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showSuccessMessage('📋 คัดลอกข้อมูลดิบเรียบร้อยแล้ว (fallback)');
    });
}

function showSuccessMessage(message) {
    const cleanMessage = message.replace(/^✅\s*/, '');
    const notification = jQuery('<div class="amp-notification success"><span>' + cleanMessage + '</span></div>');
    jQuery('body').append(notification);
    setTimeout(() => {
        notification.addClass('show');
    }, 100);
    setTimeout(() => {
        notification.removeClass('show');
        setTimeout(() => notification.remove(), 400);
    }, 4000);
}

function showErrorMessage(message) {
    const cleanMessage = message.replace(/^❌\s*/, '');
    const notification = jQuery('<div class="amp-notification error"><span>' + cleanMessage + '</span></div>');
    jQuery('body').append(notification);
    setTimeout(() => {
        notification.addClass('show');
    }, 100);
    setTimeout(() => {
        notification.removeClass('show');
        setTimeout(() => notification.remove(), 400);
    }, 4000);
}

jQuery(document).ready(function($) {
    let currentPage = 1;
    let currentUser = null;
    let currentOrderId = null;
    let currentWebhookMode = 'all';

    $('.tab-btn').click(function() {
        const tab = $(this).data('tab');
        $('.tab-btn').removeClass('active');
        $('.verification-tab-content').removeClass('active');
        $(this).addClass('active');
        $(`#${tab}-tab`).addClass('active');

        if (tab === 'pending') {
            loadPendingOrders();
        } else if (tab === 'history') {
            loadPaymentHistory(1);
        } else if (tab === 'logs') {
            loadWebhookLogs();
        }
    });

    $('#refresh-pending').click(loadPendingOrders);
    $('#refresh-logs').click(loadWebhookLogs);
    $('#clear-logs').click(clearWebhookLogs);
    $('#clear-payment-history').click(clearPaymentHistory);
    $('#logs-stats').click(toggleLogsStats);

    $('.webhook-tab-btn').click(function() {
        const mode = $(this).data('mode');
        $('.webhook-tab-btn').removeClass('active');
        $(this).addClass('active');
        
        currentWebhookMode = mode;
        loadWebhookLogs();
    });

    $('#close-logs-popup, #cancel-clear-logs').click(function() {
        $('#clear-logs-popup').hide();
    });

    $('#close-payment-popup, #cancel-clear-payment').click(function() {
        $('#clear-payment-popup').hide();
    });

    $('#confirm-clear-logs').click(function() {
        $('#clear-logs-popup').hide();

        const button = $(this);
        const originalText = button.html();
        button.prop('disabled', true).html('🗑️ กำลังลบ...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'clear_webhook_logs',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('✅ ' + response.data.message);
                    loadWebhookLogs();
                    loadLogsStats();
                } else {
                    showErrorMessage('❌ เกิดข้อผิดพลาด: ' + (response.data?.message || 'ไม่ทราบสาเหตุ'));
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
            },
            complete: function() {
                button.prop('disabled', false).html(originalText);
            }
        });
    });

    $('#confirm-clear-payment').click(function() {
        $('#clear-payment-popup').hide();

        const button = $(this);
        const originalText = button.html();
        button.prop('disabled', true).html('🗑️ กำลังลบ...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'clear_payment_history',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    showSuccessMessage('✅ ' + response.data.message);
                    loadPaymentHistory(1);
                    loadLogsStats();
                } else {
                    showErrorMessage('❌ เกิดข้อผิดพลาด: ' + (response.data?.message || 'ไม่ทราบสาเหตุ'));
                }
            },
            error: function(xhr, status, error) {
                showErrorMessage('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
            },
            complete: function() {
                button.prop('disabled', false).html(originalText);
            }
        });
    });

    $('#clear-logs-popup').click(function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    $('#clear-payment-popup').click(function(e) {
        if (e.target === this) {
            $(this).hide();
        }
    });

    $('#prev-page').click(function() {
        if (currentPage > 1) {
            loadPaymentHistory(currentPage - 1);
        }
    });

    $('#next-page').click(function() {
        loadPaymentHistory(currentPage + 1);
    });

    $('.close, #cancel-verification').click(function() {
        $('#verification-modal').hide();
    });

    $('#confirm-payment').click(function() {
        const txnId = $('#verify-txn-id').val().trim();

        if (!currentUser || !currentOrderId) {
            alert('ข้อมูลไม่ครบถ้วน');
            return;
        }

        $(this).prop('disabled', true).text('กำลังประมวลผล...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'manual_verify_payment',
                user_id: currentUser,
                order_id: currentOrderId,
                txn_id: txnId,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('ยืนยันการชำระเงินสำเร็จ!');
                    $('#verification-modal').hide();
                    loadPendingOrders();
                } else {
                    if (response.data && response.data.show_manual_option && response.data.api_failed) {
                        if (confirm('การตรวจสอบ API ไม่ผ่าน\n\nคุณต้องการยืนยันด้วยตนเองหรือไม่?\n(กดตกลงเพื่อยืนยันแบบ Manual Override)')) {
                            forceManualVerification(currentUser, currentOrderId, txnId);
                        }
                    } else {
                        alert('เกิดข้อผิดพลาด: ' + response.data.message);
                    }
                }
            },
            error: function() {
                alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
            },
            complete: function() {
                $('#confirm-payment').prop('disabled', false).text('✅ ยืนยันการชำระเงิน');
            }
        });
    });

    function forceManualVerification(userId, orderId, txnId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'manual_verify_payment',
                user_id: userId,
                order_id: orderId,
                txn_id: txnId,
                force_manual: '1',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('ยืนยันการชำระเงินสำเร็จ (Manual Override)!');
                    $('#verification-modal').hide();
                    loadPendingOrders();
                } else {
                    alert('เกิดข้อผิดพลาด: ' + response.data.message);
                }
            },
            error: function() {
                alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
            }
        });
    }

    function loadPendingOrders() {
        $('#pending-orders-container').html(`
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลดข้อมูล...</p>
            </div>
        `);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_pending_payments',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displayPendingOrders(response.data.orders);
                } else {
                    $('#pending-orders-container').html(`
                        <div class="empty-state">
                            <h3>เกิดข้อผิดพลาด</h3>
                            <p>${response.data?.message || 'ไม่ทราบสาเหตุ'}</p>
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                $('#pending-orders-container').html(`
                    <div class="empty-state">
                        <h3>เกิดข้อผิดพลาด</h3>
                        <p>ไม่สามารถโหลดข้อมูลได้: ${error}</p>
                    </div>
                `);
            }
        });
    }

    function generateActionButtons(order, cartItemsHtml) {
        const isTestMode = <?php echo json_encode($payment_handler->get_plisio_setting('test_mode', '0') === '1'); ?>;

        if (order.is_verified) {
            // ยืนยันแล้ว - แสดงเฉพาะปุ่มรายละเอียด
            return `
                <button class="btn-details" onclick="showTransactionDetails('${order.invoice_id || ''}', ${order.user_id || 0}, '${order.order_id || ''}')">
                    📋 รายละเอียด
                </button>
                <span class="status-verified">✅ ยืนยันแล้ว</span>
            `;
        } else {
            // ยังไม่ยืนยัน - แสดงปุ่มตรวจสอบและยืนยัน
            let buttons = `
                <button class="btn-api-verify" onclick="verifyTransactionAPI('${order.invoice_id || ''}', ${order.user_id || 0}, '${order.order_id || ''}')">
                    🔍 ตรวจสอบ API
                </button>
                <button class="btn-verify" onclick="openVerificationModal(${order.user_id || 0}, '${order.order_id || ''}', '${(order.user_login || '').replace(/'/g, '\\\'').replace(/"/g, '\\"')}', ${order.total || 0}, '${order.invoice_id || ''}', '${cartItemsHtml.replace(/'/g, '\\\'').replace(/"/g, '\\"')}')">
                    ✅ ยืนยันการชำระ
                </button>
            `;

            // เพิ่มปุ่มจำลองเมื่อเป็นโหมดทดสอบ
            if (isTestMode) {
                buttons += `
                    <button class="btn-simulate" onclick="showSimulatePaymentOptions('${order.invoice_id || ''}', ${order.user_id || 0}, '${order.order_id || ''}', ${order.total || 0})">
                        🧪 จำลอง Webhook
                    </button>
                `;
            }

            return buttons;
        }
    }

    function displayPendingOrders(orders) {
        if (!orders || orders.length === 0) {
            $('#pending-orders-container').html(`
                <div class="empty-state">
                    <h3>🎉 ไม่มีคำสั่งซื้อที่รอดำเนินการ</h3>
                    <p>ทุกการชำระเงินได้รับการประมวลผลแล้ว</p>
                </div>
            `);
            return;
        }

        let html = '';
        orders.forEach(order => {
            let cartItemsHtml = '';
            if (order.cart_items && order.cart_items.length > 0) {
                cartItemsHtml = order.cart_items.map(item =>
                    `<div class="cart-item">${item.position_name || ''} (${item.duration || 0} วัน)</div>`
                ).join('');
            }

            html += `
                <div class="order-card">
                    <div class="order-header">
                        <h3>📋 ${order.order_id || ''}</h3>
                        <div class="order-actions">
                            ${generateActionButtons(order, cartItemsHtml)}
                        </div>
                    </div>
                    <div class="order-info">
                        <div class="info-item">
                            <span class="info-label">ผู้ใช้</span>
                            <span class="info-value">${order.user_login || ''} (${order.user_email || ''})</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">จำนวนเงิน</span>
                            <span class="info-value">$${order.total}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Invoice ID</span>
                            <span class="info-value">${order.invoice_id || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">วันที่สร้าง</span>
                            <span class="info-value">${order.created_at || ''}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">จำนวนรายการ</span>
                            <span class="info-value">${order.cart_count || 0} รายการ</span>
                        </div>
                    </div>
                    <div class="cart-items">
                        <strong>รายการสินค้า:</strong>
                        ${cartItemsHtml}
                    </div>
                </div>
            `;
        });

        $('#pending-orders-container').html(html);
    }

    function loadPaymentHistory(page) {
        currentPage = page;
        $('#payment-history-container').html(`
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลดประวัติ...</p>
            </div>
        `);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_payment_history',
                page: page,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    displayPaymentHistory(response.data);
                } else {
                    $('#payment-history-container').html(`
                        <div class="empty-state">
                            <h3>เกิดข้อผิดพลาด</h3>
                            <p>${response.data.message}</p>
                        </div>
                    `);
                }
            },
            error: function() {
                $('#payment-history-container').html(`
                    <div class="empty-state">
                        <h3>เกิดข้อผิดพลาด</h3>
                        <p>ไม่สามารถโหลดข้อมูลได้</p>
                    </div>
                `);
            }
        });
    }

    function displayPaymentHistory(data) {
        $('#page-info').text(`หน้า ${data.current_page} จาก ${data.total_pages}`);
        $('#prev-page').prop('disabled', data.current_page <= 1);
        $('#next-page').prop('disabled', data.current_page >= data.total_pages);

        const container = $('#payment-history-container');
        container.empty();

        if (!data.payments || data.payments.length === 0) {
            container.html(`
                <div class="empty-state">
                    <h3>🗂️ ไม่มีประวัติการชำระเงิน</h3>
                    <p>ยังไม่มีการทำธุรกรรมที่เสร็จสมบูรณ์</p>
                </div>
            `);
            return;
        }

        data.payments.forEach(payment => {
            const statusInfo = getStatusInfo(payment.status);
            let statusText = statusInfo.text;
            let statusClass = statusInfo.class;

            let actionButtons = '';

            if (['failed', 'cancelled', 'expired', 'error'].includes(statusText.replace('_simulated', ''))) {
                actionButtons += `
                    <button class="btn-reverify" onclick="openReVerificationModal('${payment.order_id}', ${payment.user_id}, '${payment.user_login}', '${payment.amount}', '${payment.transaction_id}')">
                        🔄 ยืนยันอีกครั้ง
                    </button>
                `;
            }

            const cardHtml = `
                <div class="history-card ${statusClass}">
                    <div class="history-header">
                        <div class="history-header-info">
                            <div class="order-id">Order: ${payment.order_id || 'N/A'}</div>
                            <div class="payment-date">📅 ${payment.payment_date || 'N/A'}</div>
                        </div>
                        <div class="history-status ${statusClass}">
                            ${statusText}
                        </div>
                    </div>
                    <div class="history-body">
                        <div class="history-item">
                            <span class="history-label">👤 ผู้ใช้</span>
                            <span class="history-value user-info">${payment.user_login || 'N/A'} (${payment.user_email || 'N/A'})</span>
                        </div>
                        <div class="history-item">
                            <span class="history-label">💰 จำนวนเงิน</span>
                            <span class="history-value">$${payment.amount || '0.00'}</span>
                        </div>
                        <div class="history-item">
                            <span class="history-label">📦 รายการ</span>
                             <span class="history-value">${payment.ad_position || 'N/A'} (${payment.duration || 0} วัน)</span>
                        </div>
                        <div class="history-item">
                            <span class="history-label">🔑 Transaction ID</span>
                            <span class="history-value">${payment.transaction_id || 'N/A'}</span>
                        </div>
                    </div>
                    <div class="history-footer">
                        ${actionButtons}
                        <button class="btn-details" onclick="showPaymentDetails('${payment.order_id}')">
                            📋 รายละเอียด
                        </button>
                        <button class="btn-delete" onclick="deletePaymentRecord('${payment.order_id}', ${payment.user_id})">
                            🗑️ ลบประวัติ
                        </button>
                    </div>
                </div>
            `;
            container.append(cardHtml);
        });
    }

    function loadWebhookLogs() {
        $('#webhook-logs-container').html(`
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังโหลด logs...</p>
            </div>
        `);

        var fallbackTimeout = setTimeout(function() {
            $('#webhook-logs-container').html(`
                <div class="empty-state">
                    <h3>⏰ หมดเวลาการรอ</h3>
                    <p>ระบบใช้เวลานานเกินไป กรุณาลองใหม่อีกครั้ง</p>
                    <button onclick="loadWebhookLogs()" class="btn-refresh">🔄 ลองใหม่</button>
                </div>
            `);
        }, 15000);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_webhook_logs',
                filter_mode: currentWebhookMode === 'all' ? null : currentWebhookMode,
                limit: 50,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },

            success: function(response) {
                try {
                    if (response && response.success) {
                        const logs = response.data?.logs || [];
                        displayWebhookLogs(logs);
                    } else {
                        $('#webhook-logs-container').html(`
                            <div class="empty-state">
                                <h3>เกิดข้อผิดพลาด</h3>
                                <p>${response?.data?.message || 'ไม่ทราบสาเหตุ'}</p>
                            </div>
                        `);
                    }
                } catch (e) {
                    $('#webhook-logs-container').html(`
                        <div class="empty-state">
                            <h3>JavaScript Error</h3>
                            <p>เกิดข้อผิดพลาดในการประมวลผล: ${e.message}</p>
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                
                let errorMessage = error;
                if (xhr.responseText) {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMessage = errorResponse.data?.message || error;
                    } catch (e) {
                        errorMessage = xhr.responseText.substring(0, 200);
                    }
                }
                
                $('#webhook-logs-container').html(`
                    <div class="empty-state">
                        <h3>เกิดข้อผิดพลาด</h3>
                        <p>ไม่สามารถโหลดข้อมูลได้: ${errorMessage}</p>
                        <small>Status: ${status}, XHR Status: ${xhr.status}</small>
                    </div>
                `);
            },
            complete: function() {
                clearTimeout(fallbackTimeout);
            },
            timeout: 10000
        });
    }

    function displayWebhookLogs(logs) {
        try {
            if (!logs || !Array.isArray(logs) || logs.length === 0) {
                let emptyMessage = 'ไม่มี Webhook Logs';
                let emptyDescription = 'ยังไม่มีการรับ webhook จาก Plisio';
                
                if (currentWebhookMode === 'live') {
                    emptyMessage = 'ไม่มี Official Webhooks';
                    emptyDescription = 'ยังไม่มีการรับ webhook จริงจาก Plisio';
                } else if (currentWebhookMode === 'test') {
                    emptyMessage = 'ไม่มี Simulated Webhooks';
                    emptyDescription = 'ยังไม่มีการจำลอง webhook ในระบบ';
                }
                
                $('#webhook-logs-container').html(`
                    <div class="empty-state">
                        <h3>${emptyMessage}</h3>
                        <p>${emptyDescription}</p>
                        <p><small>โฟลเดอร์ webhook logs ยังไม่ถูกสร้าง หรือยังไม่มีข้อมูล log</small></p>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="webhook-logs-grid">
            `;

            logs.forEach((log, index) => {
                if (typeof log !== 'object' || log === null) {
                    return;
                }

                let ipAddress = log.ip || 'unknown';
                let txnId = log.data?.txn_id || log.data?.order_number || log.transaction_id || '-';
                let eventType = log.event_type || log.status || 'N/A';
                let date = log.date || 'N/A';
                let processingMode = log.processing_mode || 'unknown';

                let ipDisplay = ipAddress;
                let ipIcon = '🌐';
                if (ipAddress && ipAddress !== 'unknown') {
                    if (ipAddress === '*************' || ipAddress.startsWith('104.') || ipAddress.startsWith('172.')) {
                        ipDisplay = `${ipAddress} <span style="color: #28a745; font-weight: 600;">(Plisio Official)</span>`;
                        ipIcon = '✅';
                    } else if (ipAddress.startsWith('173.245.') || ipAddress.startsWith('103.21.') || ipAddress.startsWith('103.22.')) {
                        ipDisplay = `${ipAddress} <span style="color: #17a2b8; font-weight: 600;">(CloudFlare)</span>`;
                        ipIcon = '☁️';
                    } else {
                        ipDisplay = `${ipAddress} <span style="color: #ffc107; font-weight: 600;">(External)</span>`;
                        ipIcon = '⚠️';
                    }
                }

                const statusInfo = getStatusInfo(eventType);
                let statusClass = statusInfo.class;

                html += `
                    <div class="webhook-log-card">
                        <div class="log-header">
                            <div class="log-info">
                                <span class="log-date">📅 ${date}</span>
                                <span class="log-status ${statusClass}">${eventType}</span>
                            </div>
                            <button class="btn-details" data-log-index="${index}">
                                📋 ดูรายละเอียด
                            </button>
                        </div>
                        <div class="log-summary">
                            <div class="summary-item">
                                <span class="summary-label">🆔 Transaction:</span>
                                <span class="summary-value transaction-link" data-txn="${txnId}">${txnId}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">${ipIcon} IP Address:</span>
                                <span class="summary-value">${ipDisplay}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">🔧 Mode:</span>
                                <span class="summary-value ${processingMode === 'test' ? 'mode-test' : 'mode-live'}">${processingMode.toUpperCase()}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">📁 File:</span>
                                <span class="summary-value" title="${log.file_path || 'N/A'}">${(log.file_path || 'legacy').substring(0, 25)}...</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
            $('#webhook-logs-container').html(html);
            window.webhookLogsData = logs;
            $('.btn-details').off('click').on('click', function() {
                const index = $(this).data('log-index');
                showLogDetails(index);
            });

            $('.transaction-link').off('click').on('click', function() {
                const txnId = $(this).data('txn');
                if (txnId && txnId !== '-') {
                    const plisioUrl = `https://plisio.net/invoice/${txnId}`;
                    window.open(plisioUrl, '_blank');
                }
            });

        } catch (error) {
            $('#webhook-logs-container').html(`
                <div class="empty-state">
                    <h3>เกิดข้อผิดพลาดในการแสดงผล</h3>
                    <p>JavaScript Error: ${error.message}</p>
                    <button onclick="loadWebhookLogs()" class="btn-refresh">🔄 ลองใหม่</button>
                </div>
            `);
        }
    }

    function showLogDetails(index) {
        if (!window.webhookLogsData || !window.webhookLogsData[index]) {
            alert('ไม่พบข้อมูล log');
            return;
        }

        const log = window.webhookLogsData[index];
        let eventData = '';

        try {
            eventData = JSON.stringify(log.data || log, null, 2);
        } catch (e) {
            eventData = 'Error parsing data: ' + e.message;
        }

        let ipAddress = log.ip || 'unknown';
        let ipDisplay = ipAddress;
        let ipIcon = '🌐';
        if (ipAddress && ipAddress !== 'unknown') {
            if (ipAddress === '*************' || ipAddress.startsWith('104.') || ipAddress.startsWith('172.')) {
                ipDisplay = `${ipAddress} <span style="color: #28a745; font-weight: 600;">(Plisio Official)</span>`;
                ipIcon = '✅';
            } else if (ipAddress.startsWith('173.245.') || ipAddress.startsWith('103.21.') || ipAddress.startsWith('103.22.')) {
                ipDisplay = `${ipAddress} <span style="color: #17a2b8; font-weight: 600;">(CloudFlare)</span>`;
                ipIcon = '☁️';
            } else {
                ipDisplay = `${ipAddress} <span style="color: #ffc107; font-weight: 600;">(External)</span>`;
                ipIcon = '⚠️';
            }
        }

        const modalHtml = `
            <div id="log-details-modal" class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>📋 รายละเอียด Webhook Log</h3>
                        <button class="modal-close" onclick="closeLogDetails()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="log-detail-info">
                            <div class="detail-section">
                                <div class="detail-title">🌍 Environment Information (ข้อมูลแวดล้อม)</div>
                                <div class="detail-row">
                                    <strong>📅 วันที่:</strong> ${log.date || 'N/A'}
                                </div>
                                <div class="detail-row">
                                    <strong>🔔 Event Type:</strong> ${log.event_type || 'N/A'}
                                </div>
                                <div class="detail-row">
                                    <strong>${ipIcon} IP Address:</strong> <span>${ipDisplay}</span>
                                </div>
                                <div class="detail-row">
                                    <strong>🔧 Processing Mode:</strong> 
                                    <span class="summary-value ${log.processing_mode === 'test' ? 'mode-test' : 'mode-live'}">${(log.processing_mode || 'live').toUpperCase()}</span>
                                </div>
                                <div class="detail-row">
                                    <strong>📁 Filename:</strong> <span style="word-break: break-all;">${log.file_path || 'legacy'}</span>
                                </div>
                            </div>
                        </div>
                        <div class="log-detail-data">
                            <h4>📦 Raw Plisio Payload (ข้อมูลดิบจาก Plisio):</h4>
                            <div class="detail-row">
                                <strong>🆔 Transaction ID:</strong>
                                <span class="transaction-link-modal" data-txn="${log.data?.txn_id || log.data?.order_number || '-'}">${log.data?.txn_id || log.data?.order_number || '-'}</span>
                            </div>
                            <div class="detail-row">
                                <strong>📦 Data Size:</strong> ${JSON.stringify(log.data || {}).length} characters
                            </div>
                            <pre class="json-data">${eventData}</pre>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn-secondary" onclick="closeLogDetails()">ปิด</button>
                        <button class="btn-primary" onclick="copyLogData(${index})" data-log-index="${index}">📋 คัดลอกข้อมูลดิบ</button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(modalHtml);
        $('#log-details-modal').fadeIn(200);

        $('#log-details-modal').off('click').on('click', function(e) {
            if (e.target === this) {
                closeLogDetails();
            }
        });

        $('.transaction-link-modal').off('click').on('click', function() {
            const txnId = $(this).data('txn');
            if (txnId && txnId !== '-') {
                const plisioUrl = `https://plisio.net/invoice/${txnId}`;
                window.open(plisioUrl, '_blank');
            }
        });

        $('.btn-primary[data-log-index]').off('click').on('click', function() {
            const logIndex = $(this).data('log-index');
            copyLogData(logIndex);
        });
    }

    function closeLogDetails() {
        $('#log-details-modal').fadeOut(200, function() {
            $(this).remove();
        });
        $('#transaction-details-modal').hide();
        $('#api-verification-modal').hide();
        $('.modal').hide();
    }

    function clearWebhookLogs() {
        $('#clear-logs-popup').show();
    }

    function clearPaymentHistory() {
        $('#clear-payment-popup').show();
    }

    function toggleLogsStats() {
        const container = $('#logs-stats-container');
        const button = $('#logs-stats');

        if (container.is(':visible')) {
            container.slideUp();
            button.text('📊 สถิติ');
        } else {
            container.slideDown();
            button.text('📊 ซ่อนสถิติ');
            loadLogsStats();
        }
    }

    function loadLogsStats() {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_logs_stats',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    $('#webhook-files-count').text(data.webhook_logs.files);
                    $('#webhook-size').text(data.webhook_logs.size_formatted);
                    $('#payment-files-count').text(data.payment_history.files);
                    $('#payment-size').text(data.payment_history.size_formatted);
                    $('#total-files-count').text(data.total_files);
                    $('#total-size').text(data.total_size > 0 ? formatBytes(data.total_size) : '0 B');
                }
            },
            error: function(xhr, status, error) {
            }
        });
    }

    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    window.verifyTransactionAPI = function(invoiceId, userId, orderId) {
        if (!invoiceId || invoiceId === 'N/A') {
            alert('ไม่มี Transaction ID สำหรับการตรวจสอบ');
            return;
        }
        const button = event.target;
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '🔄 กำลังตรวจสอบ...';
        $('#api-verification-modal').show();
        $('#api-verification-content').html(`
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>กำลังตรวจสอบ API...</p>
            </div>
        `);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'verify_transaction_api_only',
                txn_id: invoiceId,
                user_id: userId,
                order_id: orderId,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                let contentClass = response.success ? 'verification-success' : 'verification-error';
                let icon = response.success ? '✅' : '❌';
                let title = response.success ? 'การตรวจสอบ API สำเร็จ!' : 'การตรวจสอบ API ไม่ผ่าน!';

                $('#api-verification-content').html(`
                    <div class="verification-content ${contentClass}">
                        <div class="detail-title">${icon} ${title}</div>
                        <div class="details-content">${response.data.message}</div>
                    </div>
                `);
            },
            error: function(xhr, status, error) {
                $('#api-verification-content').html(`
                    <div class="verification-content verification-error">
                        <div class="detail-title">❌ เกิดข้อผิดพลาด</div>
                        <div class="details-content">เกิดข้อผิดพลาดในการเชื่อมต่อ: ${error}</div>
                    </div>
                `);
            },
            complete: function() {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        });
    };



    window.openVerificationModal = function(userId, orderId, userLogin, total, invoiceId, cartItems) {
        currentUser = userId;
        currentOrderId = orderId;

        $('#verify-order-id').val(orderId);
        $('#verify-user').val(userLogin);
        $('#verify-amount').val('$' + total);
        $('#verify-txn-id').val(invoiceId);
        $('#verify-cart-items').html(cartItems);

        $('#verification-modal').show();
    };
    window.closeDetailsModal = function() {
        $('#transaction-details-modal').hide();
    };
    window.closeVerificationModal = function() {
        $('#api-verification-modal').hide();
    };
    window.closeLogDetails = closeLogDetails;
    
    window.deletePaymentRecord = function(orderId, userId) {
        if (!confirm('คุณแน่ใจหรือไม่ที่จะลบประวัติการชำระเงินนี้?\n\nการกระทำนี้ไม่สามารถย้อนกลับได้และจะลบข้อมูลทั้งจากไฟล์และฐานข้อมูล')) {
            return;
        }
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'delete_payment_record',
                order_id: orderId,
                user_id: userId,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('ลบประวัติการชำระเงินสำเร็จ!');
                    loadPaymentHistory(currentPage);
                } else {
                    alert('เกิดข้อผิดพลาด: ' + response.data.message);
                }
            },
            error: function() {
                alert('เกิดข้อผิดพลาดในการเชื่อมต่อ');
            }
        });
    };

    $(window).click(function(event) {
        if (event.target.id === 'transaction-details-modal') {
            closeDetailsModal();
        }
        if (event.target.id === 'api-verification-modal') {
            closeVerificationModal();
        }
    });
    window.showPaymentDetails = function(orderId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_payment_details',
                order_id: orderId,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    let detailsHtml = `
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4>📋 Payment Details: ${orderId}</h4>
                            <div style="background: white; padding: 15px; border-radius: 6px; margin: 10px 0;">
                                <h5>Basic Information:</h5>
                                <p><strong>User:</strong> ${data.user_login} (${data.user_email})</p>
                                <p><strong>Amount:</strong> $${data.total_amount}</p>
                                <p><strong>Status:</strong> ${data.status}</p>
                                <p><strong>Transaction ID:</strong> ${data.transaction_id}</p>
                                <p><strong>Date:</strong> ${data.timestamp}</p>
                            </div>
                    `;
                    
                    if (data.cart_items && data.cart_items.length > 0) {
                        detailsHtml += `
                            <div style="background: white; padding: 15px; border-radius: 6px; margin: 10px 0;">
                                <h5>Cart Items:</h5>
                                <ul>
                        `;
                        data.cart_items.forEach(item => {
                            detailsHtml += `<li>${item.position_name} (${item.duration} วัน)</li>`;
                        });
                        detailsHtml += `</ul></div>`;
                    }
                    
                    if (data.simulation_type) {
                        detailsHtml += `
                            <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 10px 0; border-left: 4px solid #ffc107;">
                                <h5>🧪 Simulation Information:</h5>
                                <p><strong>Type:</strong> ${data.simulation_type}</p>
                                <p><strong>Method:</strong> ${data.payment_method}</p>
                                <p><strong>Purpose:</strong> ${data.purchase_type}</p>
                            </div>
                        `;
                    }
                    
                    detailsHtml += `</div>`;
                    
                    Swal.fire({
                        title: 'Payment Details',
                        html: detailsHtml,
                        width: '800px',
                        showCloseButton: true,
                        showConfirmButton: false
                    });
                } else {
                    Swal.fire('Error', 'ไม่สามารถโหลดรายละเอียดได้: ' + (response.data?.message || 'Unknown error'), 'error');
                }
            },
            error: function() {
                Swal.fire('Error', 'เกิดข้อผิดพลาดในการเชื่อมต่อ', 'error');
            }
        });
    };

    setTimeout(function() {
        loadPendingOrders();
    }, 500);

    $('#generate-txn-id').click(function() {
        $('#sim-txn-id').val('SIM-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9));
    });

    $('#send-simulation-btn').click(function() {
        const orderId = $('#sim-order-number').val().trim();
        const txnId = $('#sim-txn-id').val().trim();
        const amount = $('#sim-amount').val();
        const status = $('#sim-status').val();

        if (!orderId) {
            showErrorMessage('❌ กรุณากรอกหมายเลขคำสั่งซื้อ (Order Number)');
            return;
        }

        if (!amount || amount <= 0) {
            showErrorMessage('❌ กรุณากรอกจำนวนเงินที่ถูกต้อง');
            return;
        }

        executePaymentSimulation(txnId, null, orderId, amount, status, true);
    });

    $('#test-status-colors').click(function() {
        const testStatuses = [
            'completed', 'mismatch', 'cancelled', 'expired', 'error',
            'cancelled_failed', 'expired_failed', 'error_failed', 'signature_failed'
        ];
        
        let testHtml = '<div style="padding: 20px;"><h3>🎨 ทดสอบสีสถานะ</h3>';
        testStatuses.forEach(status => {
            const statusInfo = getStatusInfo(status);
            testHtml += `
                <div style="margin: 10px 0; display: flex; align-items: center; gap: 15px;">
                    <span style="width: 150px; font-family: monospace;">${status}</span>
                    <span class="history-status ${statusInfo.class}" style="padding: 5px 15px;">${statusInfo.text}</span>
                    <small style="color: ${statusInfo.color};">● ${statusInfo.color}</small>
                </div>
            `;
        });
        testHtml += '</div>';
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'ทดสอบสีสถานะ',
                html: testHtml,
                width: 600,
                showConfirmButton: true,
                confirmButtonText: 'ปิด'
            });
        } else {
            alert('SweetAlert2 ไม่พร้อมใช้งาน');
        }
    });

    window.showSimulatePaymentOptions = function(invoiceId, userId, orderId, amount) {
        if (typeof Swal === 'undefined') {
            alert('SweetAlert2 ไม่พร้อมใช้งาน กรุณารีเฟรชหน้าและลองใหม่');
            return;
        }

        Swal.fire({
            title: '🧪 จำลอง Webhook Payment',
            html: `
                <div style="text-align: left; margin: 20px 0;">
                    <p><strong>📋 Order ID:</strong> ${orderId}</p>
                    <p><strong>💰 จำนวนเงิน:</strong> $${amount}</p>
                    <p><strong>🆔 Invoice ID:</strong> ${invoiceId}</p>
                    <p><strong>👤 User ID:</strong> ${userId}</p>
                </div>
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; font-size: 14px;">
                        <strong>⚠️ โหมดทดสอบ:</strong><br>
                        ระบบจะส่ง webhook เพียงครั้งเดียวเลียนแบบพฤติกรรมจริงของ Plisio<br>
                        เลือกสถานะที่ต้องการทดสอบ
                    </p>
                </div>
                <div style="margin: 20px 0;">
                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">เลือกสถานะ Webhook:</label>
                    <select id="webhook-status" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <optgroup label="✅ สถานะสำเร็จ">
                            <option value="completed">✅ Completed (สำเร็จ)</option>
                            <option value="mismatch">💰 Mismatch (จ่ายเกิน - สำเร็จ)</option>
                        </optgroup>
                        <optgroup label="❌ สถานะล้มเหลว">
                            <option value="cancelled">🚫 Cancelled (ยกเลิก)</option>
                            <option value="expired">⏰ Expired (หมดอายุ)</option>
                            <option value="error">❗ Error (ข้อผิดพลาด)</option>
                        </optgroup>
                    </select>
                </div>
            `,
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: '🚀 ส่ง Webhook',
            cancelButtonText: '❌ ยกเลิก',
            confirmButtonColor: '#007cba',
            cancelButtonColor: '#dc3545',
            preConfirm: () => {
                const status = document.getElementById('webhook-status').value;
                return status;
            }
        }).then((result) => {
            if (result.isConfirmed) {
                executePaymentSimulation(invoiceId, userId, orderId, amount, result.value, false);
            }
        });
    };

    function executePaymentSimulation(invoiceId, userId, orderId, amount, status, fromSimulatorTab = false) {
        let button, originalText;

        if (fromSimulatorTab) {
            button = $('#send-simulation-btn');
            originalText = button.html();
            button.prop('disabled', true).html('⚡️ กำลังส่ง...');
            $('#simulation-result-container').slideDown();
            $('#simulation-result').html('Awaiting response from server...');
        } else {
            Swal.fire({
                title: 'กำลังจำลอง Webhook...',
                text: `กำลังส่งสถานะ '${status}' สำหรับ Order ID: ${orderId}`,
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
        }
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'simulate_payment_webhook',
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>',
                order_id: orderId,
                invoice_id: invoiceId,
                user_id: userId || 0,
                amount: amount,
                status: status
            },
            success: function(response) {
                if (fromSimulatorTab) {
                    let resultText = '';
                    if (response.success) {
                        resultText += "✅ สถานะ: สำเร็จ (SUCCESS)\n\n";
                        resultText += "======= ข้อมูลที่ส่งไป (Official Plisio Format) =======\n";
                        if (response.data && response.data.sent_data) {
                            for (const [key, value] of Object.entries(response.data.sent_data)) {
                                if (Array.isArray(value)) {
                                    resultText += `${key}: [${value.join(', ')}]\n`;
                                } else {
                                    resultText += `${key}: ${value}\n`;
                                }
                            }
                        } else {
                            resultText += `Order ID: ${orderId}\n`;
                            resultText += `Amount: $${amount}\n`;
                            resultText += `Status: ${status}\n`;
                            resultText += `Transaction ID: ${invoiceId || 'Auto-generated'}\n`;
                        }
                        resultText += "\n======= ผลลัพธ์จากเซิร์ฟเวอร์ =======\n";
                        resultText += `Message: ${response.data.message || 'No message'}\n`;
                        resultText += `HTTP Status Code: ${response.data.response_code || 'Unknown'}\n`;
                        resultText += `Response Body: ${response.data.response_body || 'OK'}\n`;
                        resultText += `History Saved: ${response.data.history_saved ? 'Yes' : 'No'}\n`;
                        resultText += `Webhook URL: ${response.data.webhook_url || 'Unknown'}\n`;
                        resultText += `Status Updated: ${response.data.status_updated ? 'Yes' : 'No'}`;
                        showSuccessMessage('✅ ส่ง Webhook จำลองสำเร็จ!');
                    } else {
                        resultText += "❌ สถานะ: ล้มเหลว (FAILED)\n\n";
                        resultText += "======= ข้อมูลที่พยายามส่ง =======\n";
                        if (response.data && response.data.sent_data) {
                            for (const [key, value] of Object.entries(response.data.sent_data)) {
                                if (Array.isArray(value)) {
                                    resultText += `${key}: [${value.join(', ')}]\n`;
                                } else {
                                    resultText += `${key}: ${value}\n`;
                                }
                            }
                        } else {
                            resultText += `Order ID: ${orderId}\n`;
                            resultText += `Amount: $${amount}\n`;
                            resultText += `Status: ${status}\n`;
                            resultText += `Transaction ID: ${invoiceId || 'Auto-generated'}\n`;
                        }
                        resultText += "\n======= ผลลัพธ์ที่ได้รับ =======\n";
                        resultText += `Message: ${response.data?.message || 'No message'}\n`;
                        resultText += `HTTP Status Code: ${response.data?.response_code || 'Unknown'}\n`;
                        resultText += `Response Body: ${response.data?.response_body || 'No response body'}\n`;
                        resultText += `History Saved: ${response.data?.history_saved ? 'Yes' : 'No'}\n`;
                        resultText += `Status Updated: ${response.data?.status_updated ? 'Yes' : 'No'}`;
                        if (response.data?.webhook_url) {
                            resultText += `\nWebhook URL: ${response.data.webhook_url}`;
                        }
                        showErrorMessage('❌ ' + (response.data?.message || 'เกิดข้อผิดพลาดไม่ทราบสาเหตุ'));
                    }
                    $('#simulation-result').text(resultText);
                } else {
                     if (response.success) {
                        const successDetails = `
                            <div style="text-align: left;">
                                <p><strong>✅ สถานะ:</strong> ${response.data?.message || 'สำเร็จ'}</p>
                                <p><strong>📊 HTTP Code:</strong> ${response.data?.response_code || 'Unknown'}</p>
                                <p><strong>📝 Response:</strong> ${response.data?.response_body || 'OK'}</p>
                                <p><strong>💾 บันทึกประวัติ:</strong> ${response.data?.history_saved ? 'สำเร็จ' : 'ล้มเหลว'}</p>
                                <p><strong>🔄 อัพเดตสถานะ:</strong> ${response.data?.status_updated ? 'สำเร็จ' : 'ล้มเหลว'}</p>
                            </div>
                        `;
                        Swal.fire({
                            icon: 'success',
                            title: 'จำลองสำเร็จ!',
                            html: successDetails,
                        });
                    } else {
                        const errorDetails = `
                            <div style="text-align: left;">
                                <p><strong>❌ ข้อผิดพลาด:</strong> ${response.data?.message || 'ไม่ทราบสาเหตุ'}</p>
                                <p><strong>📊 HTTP Code:</strong> ${response.data?.response_code || 'Unknown'}</p>
                                <p><strong>📝 Response:</strong> ${response.data?.response_body || 'No response'}</p>
                                <p><strong>💾 บันทึกประวัติ:</strong> ${response.data?.history_saved ? 'สำเร็จ' : 'ล้มเหลว'}</p>
                            </div>
                        `;
                        Swal.fire({
                            icon: 'error',
                            title: 'จำลองล้มเหลว',
                            html: errorDetails,
                        });
                    }
                }

                loadPendingOrders();
                loadWebhookLogs();

            },
            error: function(xhr, status, error) {
                 if (fromSimulatorTab) {
                    let errorText = '';
                    errorText += "❌ สถานะ: ล้มเหลว (CONNECTION ERROR)\n\n";
                    errorText += "======= ข้อมูลที่พยายามส่ง =======\n";
                    errorText += `Order ID: ${orderId}\n`;
                    errorText += `Amount: $${amount}\n`;
                    errorText += `Status: ${status}\n`;
                    errorText += `Transaction ID: ${invoiceId || 'Auto-generated'}\n\n`;
                    errorText += "======= ข้อผิดพลาดการเชื่อมต่อ =======\n";
                    errorText += `AJAX Status: ${status}\n`;
                    errorText += `Error: ${error}`;
                    $('#simulation-result').text(errorText);
                    showErrorMessage('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ');
                } else {
                    Swal.fire('ผิดพลาด', `เกิดข้อผิดพลาดในการเชื่อมต่อ: ${error}`, 'error');
                }
            },
            complete: function() {
                if (fromSimulatorTab) {
                    button.prop('disabled', false).html('⚡️ ส่ง Webhook จำลอง');
                }
            }
        });
    }

    window.loadPositionOwnershipDetails = function(userId, orderId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'get_position_ownership_details',
                user_id: userId,
                order_id: orderId,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    let htmlContent = `
                        <div style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; margin: 10px 0;">
                            <h4 style="margin: 0 0 15px 0; color: #495057;">📋 รายละเอียดตำแหน่งโฆษณา</h4>
                    `;
                    
                    response.data.positions.forEach(function(position) {
                        const statusStyle = position.can_process 
                            ? 'border-left: 4px solid #28a745; background: #f8fff9;' 
                            : 'border-left: 4px solid #ffc107; background: #fffef0;';
                        
                        htmlContent += `
                            <div style="background: #fff; border: 1px solid #ddd; border-radius: 6px; padding: 12px; margin: 8px 0; ${statusStyle}">
                                <strong>📍 ${position.position_name}</strong><br>
                                ⏱️ ระยะเวลา: ${position.duration} วัน<br>
                                📊 สถานะ: ${position.status_message}<br>
                        `;
                        
                        if (position.current_owner_id && position.current_owner_id !== userId) {
                            htmlContent += `👤 เจ้าของปัจจุบัน: User ID ${position.current_owner_id}<br>`;
                            if (position.expiration_date) {
                                htmlContent += `📅 หมดอายุ: ${position.expiration_date}<br>`;
                            }
                        }
                        htmlContent += `</div>`;
                    });
                    
                    htmlContent += `
                            <div style="background: #e7f1ff; border: 1px solid #b3d7ff; border-radius: 6px; padding: 12px; margin-top: 15px; color: #004085;">
                                <strong>🔧 สำหรับ Admin:</strong><br>
                                ${response.data.verification_notes}
                            </div>
                        </div>
                    `;
                    
                    $('#verify-cart-items').html(htmlContent);
                } else {
                    $('#verify-cart-items').html('<p style="color: red;">⚠️ ไม่สามารถโหลดข้อมูลได้: ' + (response.data?.message || 'Unknown error') + '</p>');
                }
            },
            error: function() {
                $('#verify-cart-items').html('<p style="color: red;">⚠️ เกิดข้อผิดพลาดในการโหลดข้อมูล</p>');
            }
        });
    };

    window.openReVerificationModal = function(orderId, userId, userLogin, amount, txnId) {
        currentUser = userId;
        currentOrderId = orderId;

        $('#verify-order-id').val(orderId);
        $('#verify-user').val(userLogin);
        $('#verify-amount').val('$' + amount);
        $('#verify-txn-id').val(txnId);
        $('#verify-cart-items').html('<p><em>กำลังโหลดรายละเอียด...</em></p>');
        
        // โหลดข้อมูลรายละเอียดตำแหน่งโฆษณา
        loadPositionOwnershipDetails(userId, orderId);
        
        $('#confirm-payment')
            .off('click')
            .text('✅ ยืนยันการชำระเงินอีกครั้ง')
            .on('click', function() {
                const button = $(this);
                button.prop('disabled', true).text('กำลังประมวลผล...');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'retry_manual_verification',
                        order_id: orderId,
                        user_id: userId,
                        txn_id: $('#verify-txn-id').val().trim(),
                        security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            showSuccessMessage('✅ ยืนยันการชำระเงินสำเร็จแล้ว');
                            $('#verification-modal').hide();
                            loadPaymentHistory(currentPage); // รีเฟรชหน้าประวัติ
                            loadPendingOrders(); // รีเฟรชหน้ารอดำเนินการ (เผื่อมี)
                        } else {
                            showErrorMessage('❌ เกิดข้อผิดพลาด: ' + (response.data?.message || 'ไม่ทราบสาเหตุ'));
                        }
                    },
                    error: function(xhr, status, error) {
                        showErrorMessage('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ: ' + error);
                    },
                    complete: function() {
                        button.prop('disabled', false).text('✅ ยืนยันการชำระเงินอีกครั้ง');
                        // รีเซ็ตปุ่มกลับไปเป็น action เดิม
                        resetVerificationModal();
                    }
                });
            });

        $('#verification-modal').show();
    };

    function resetVerificationModal() {
        // โหลดข้อมูลรายละเอียดตำแหน่งโฆษณาอีกครั้ง
        if (currentUser && currentOrderId) {
            loadPositionOwnershipDetails(currentUser, currentOrderId);
        }
        
        $('#confirm-payment').off('click').text('✅ ยืนยันการชำระเงิน').on('click', function() {
            const txnId = $('#verify-txn-id').val().trim();

            if (!currentUser || !currentOrderId) {
                Swal.fire({
                    icon: 'error',
                    title: 'ข้อมูลไม่ครบถ้วน',
                    text: 'กรุณาตรวจสอบข้อมูลผู้ใช้และหมายเลขคำสั่งซื้อ'
                });
                return;
            }

            // แสดง confirmation popup พร้อมข้อมูลเพิ่มเติม
            Swal.fire({
                title: '⚠️ ยืนยันการชำระเงิน',
                html: `
                    <div style="text-align: left;">
                        <p><strong>👤 ผู้ใช้:</strong> ${$('#verify-user').val()}</p>
                        <p><strong>🆔 Order ID:</strong> ${currentOrderId}</p>
                        <p><strong>💰 จำนวนเงิน:</strong> ${$('#verify-amount').val()}</p>
                        <p><strong>🔖 Transaction ID:</strong> ${txnId || 'ไม่ระบุ'}</p>
                        <hr>
                        <p style="color: #ff6b6b;"><strong>⚠️ หมายเหตุ:</strong> การยืนยันนี้จะประมวลผลแม้ว่าตำแหน่งจะมีเจ้าของอยู่ (Admin Override)</p>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: '✅ ยืนยันการชำระเงิน',
                cancelButtonText: '❌ ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    const button = $('#confirm-payment');
                    button.prop('disabled', true).text('กำลังประมวลผล...');

                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'manual_verify_payment',
                            user_id: currentUser,
                            order_id: currentOrderId,
                            txn_id: txnId,
                            security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: '✅ สำเร็จ!',
                                    html: `
                                        <div style="text-align: left;">
                                            <p><strong>ยืนยันการชำระเงินสำเร็จแล้ว</strong></p>
                                            ${response.data.details ? 
                                                `<hr>
                                                <p><strong>📧 อีเมลยืนยัน:</strong> ส่งแล้ว</p>
                                                <p><strong>👨‍💼 ยืนยันโดย Admin:</strong> User ID ${response.data.details.verified_by_admin}</p>` 
                                                : ''
                                            }
                                        </div>
                                    `
                                });
                                $('#verification-modal').hide();
                                loadPendingOrders();
                            } else {
                                Swal.fire({
                                    icon: 'error',
                                    title: '❌ เกิดข้อผิดพลาด',
                                    text: response.data.message || 'ไม่ทราบสาเหตุ'
                                });
                            }
                        },
                        error: function() {
                            Swal.fire({
                                icon: 'error',
                                title: '❌ ข้อผิดพลาดการเชื่อมต่อ',
                                text: 'กรุณาลองใหม่อีกครั้ง'
                            });
                        },
                        complete: function() {
                            button.prop('disabled', false).text('✅ ยืนยันการชำระเงิน');
                        }
                    });
                }
            });
        });
    }
});

function showSignatureLogDetails(transactionId) {
    const modal = document.getElementById('signature-log-modal');
    const contentDiv = document.getElementById('signature-log-content');
    contentDiv.innerHTML = '<div class="loading-state"><div class="loading-spinner"></div><p>กำลังโหลดข้อมูล...</p></div>';
    modal.style.display = 'flex';

    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'get_signature_log_details',
            security: '<?php echo wp_create_nonce('amp_admin_nonce'); ?>',
            transaction_id: transactionId
        },
        success: function(response) {
            if (response.success && response.data.log) {
                const log = response.data.log;
                let resultClass = log.result === 'success' ? 'verification-success' : 'verification-error';
                let resultText = log.result === 'success' ? 'สำเร็จ' : 'ล้มเหลว';

                contentDiv.innerHTML = `
                    <div class="signature-log-grid">
                        <div class="signature-log-section ${resultClass}">
                            <h4>ผลการตรวจสอบ</h4>
                            <p><strong>ผลลัพธ์:</strong> <span class="status-${log.result === 'success' ? 'success' : 'error'}">${resultText}</span></p>
                            <p><strong>เหตุผล:</strong> ${log.reason || 'N/A'}</p>
                            <p><strong>อัลกอริทึม:</strong> ${log.algorithm || 'N/A'}</p>
                            <p><strong>IP Address:</strong> ${log.ip_address || 'N/A'}</p>
                            <p><strong>เวลา:</strong> ${log.timestamp || 'N/A'}</p>
                        </div>

                        <div class="signature-log-section">
                            <h4>ลายเซ็นที่คำนวณได้ (Expected)</h4>
                            <code>${log.calculated_signature || 'N/A'}</code>
                        </div>

                        <div class="signature-log-section">
                            <h4>ลายเซ็นที่ได้รับมา (Received)</h4>
                            <code>${log.received_signature || 'N/A'}</code>
                        </div>

                        <div class="signature-log-section">
                            <h4>ข้อมูลที่ใช้สร้างลายเซ็น (String to Hash)</h4>
                            <code>${log.string_to_hash || 'N/A'}</code>
                        </div>
                    </div>
                `;
            } else {
                contentDiv.innerHTML = `<div class="empty-state"><h3>ไม่พบข้อมูล</h3><p>${response.data.message || 'ไม่พบข้อมูล Log สำหรับ Transaction นี้'}</p></div>`;
            }
        },
        error: function() {
            contentDiv.innerHTML = `<div class="empty-state"><h3>เกิดข้อผิดพลาด</h3><p>การเชื่อมต่อล้มเหลว</p></div>`;
        }
    });
}

function closeSignatureLogModal() {
    const modal = document.getElementById('signature-log-modal');
    modal.style.display = 'none';
}

function loadPaymentHistory(page = 1) {
    const container = document.getElementById('payment-history-container');
    const pageInfo = document.getElementById('page-info');
    container.innerHTML = `<div class="loading-state"><div class="loading-spinner"></div><p>กำลังโหลดประวัติ...</p></div>`;
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'get_payment_history',
            security: '<?php echo wp_create_nonce('amp_admin_nonce'); ?>',
            page: page
        },
        success: function(response) {
            if (response.success) {
                displayPaymentHistory(response.data);
                pageInfo.textContent = `หน้า ${response.data.current_page} จาก ${response.data.total_pages}`;
                $('#prev-page').prop('disabled', response.data.current_page <= 1);
                $('#next-page').prop('disabled', response.data.current_page >= response.data.total_pages);
            } else {
                container.innerHTML = `<div class="empty-state"><h3>เกิดข้อผิดพลาด</h3><p>${response.data.message || 'ไม่สามารถโหลดข้อมูลได้'}</p></div>`;
                pageInfo.textContent = 'ไม่มีข้อมูล';
                $('#prev-page').prop('disabled', true);
                $('#next-page').prop('disabled', true);
            }
        },
        error: function() {
            container.innerHTML = `<div class="empty-state"><h3>เกิดข้อผิดพลาด</h3><p>การเชื่อมต่อล้มเหลว</p></div>`;
            pageInfo.textContent = 'ไม่มีข้อมูล';
            $('#prev-page').prop('disabled', true);
            $('#next-page').prop('disabled', true);
        }
    });
}

function loadWebhookLogs(filter = 'all') {
    const container = document.getElementById('webhook-logs-container');
    container.innerHTML = `<div class="loading-state"><div class="loading-spinner"></div><p>กำลังโหลด logs...</p></div>`;
    
    jQuery.ajax({
        url: ajaxurl,
        type: 'POST',
        data: {
            action: 'get_webhook_logs',
            security: '<?php echo wp_create_nonce('amp_admin_nonce'); ?>',
            filter_mode: filter
        },
        success: function(response) {
            if (response.success) {
                renderWebhookLogs(response.data.logs);
            } else {
                container.innerHTML = `<div class="empty-state"><h3>เกิดข้อผิดพลาด</h3><p>${response.data.message || 'ไม่สามารถโหลดข้อมูลได้'}</p></div>`;
            }
        },
        error: function() {
            container.innerHTML = `<div class="empty-state"><h3>เกิดข้อผิดพลาด</h3><p>การเชื่อมต่อล้มเหลว</p></div>`;
        }
    });
}

function renderWebhookLogs(logs) {
    const container = document.getElementById('webhook-logs-container');
    if (!logs || logs.length === 0) {
        container.innerHTML = `<div class="empty-state"><h3>ไม่มี Webhook Logs</h3><p>ยังไม่มี Webhook ถูกส่งเข้ามาในระบบ</p></div>`;
        return;
    }

    const grid = document.createElement('div');
    grid.className = 'webhook-logs-grid';
    logs.forEach(log => {
        const card = document.createElement('div');
        card.className = 'webhook-log-card';

        const eventType = log.data.event_type || log.event_type || 'unknown_event';
        let statusClass = 'status-info';
        let statusText = eventType;

        if (log.verification_result) {
            if (log.verification_result.result === 'success') {
                statusClass = 'status-completed';
                statusText = 'Verified';
            } else {
                statusClass = 'status-failed';
                statusText = 'Verification Failed';
            }
        } else if (eventType === 'webhook_signature_failed' || (log.data && log.data.reason && log.data.reason.includes('mismatch')) ) {
             statusClass = 'status-failed';
             statusText = 'Verification Failed';
        } else {
            const logStatus = log.data?.status || eventType;
            const statusInfo = getStatusInfo(logStatus);
            statusClass = statusInfo.class;
            statusText = statusInfo.text;
        }

        const modeClass = (log.processing_mode === 'live') ? 'mode-live' : 'mode-test';

        let debugButton = '';
        if (log.verification_result && log.verification_result.result !== 'success') {
            debugButton = `<button class="btn-details btn-debug-signature" onclick="showSignatureLogDetails('${log.transaction_id}')">🐞 Debug Signature</button>`;
        }

        card.innerHTML = `
            <div class="log-header">
                <div class="log-info">
                    <span class="log-status ${statusClass}">${statusText}</span>
                    <span class="log-date">${log.date}</span>
                </div>
                <div class="log-actions">
                     ${debugButton}
                    <button class="btn-details" onclick="showLogDetails(this)">📄 ดูรายละเอียด</button>
                </div>
            </div>
            <div class="log-summary">
                <div class="summary-item">
                    <span class="summary-label">Transaction ID:</span>
                    <span class="summary-value">${log.transaction_id || 'N/A'}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Event Type:</span>
                    <span class="summary-value">${log.event_type}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">IP Address:</span>
                    <span class="summary-value">${log.ip || 'N/A'}</span>
                </div>
                 <div class="summary-item">
                    <span class="summary-label">Mode:</span>
                    <span class="summary-value ${modeClass}">${log.processing_mode || 'N/A'}</span>
                </div>
            </div>
            <div class="log-raw-data" style="display:none;">${escapeHtml(JSON.stringify(log.data, null, 2))}</div>
        `;
        grid.appendChild(card);
    });
    container.innerHTML = '';
    container.appendChild(grid);
}

function closeVerificationModal() {
    document.getElementById('verification-modal').style.display = 'none';
}

function showSuccessMessage(message) {
    const notification = document.createElement('div');
    notification.className = 'amp-notification success';
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 4000);
}

function showErrorMessage(message) {
    const notification = document.createElement('div');
    notification.className = 'amp-notification error';
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

jQuery(document).ready(function($) {
    $('#generate-txn-id').on('click', function() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000);
        const txnId = `SIM-${timestamp}-${random}`;
        $('#sim-txn-id').val(txnId);
    });

    $('#send-simulation-btn').on('click', function() {
        const orderNumber = $('#sim-order-number').val().trim();
        const txnId = $('#sim-txn-id').val().trim() || `SIM-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const amount = parseFloat($('#sim-amount').val());
        const status = $('#sim-status').val();

        if (!orderNumber) {
            Swal.fire({
                icon: 'error',
                title: 'ข้อมูลไม่ครบถ้วน',
                text: 'กรุณาใส่หมายเลขคำสั่งซื้อ'
            });
            return;
        }

        if (!amount || amount <= 0) {
            Swal.fire({
                icon: 'error',
                title: 'จำนวนเงินไม่ถูกต้อง',
                text: 'กรุณาใส่จำนวนเงินที่ถูกต้อง'
            });
            return;
        }

        const button = $(this);
        button.prop('disabled', true).text('กำลังส่ง...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'simulate_webhook',
                order_number: orderNumber,
                txn_id: txnId,
                amount: amount,
                status: status,
                security: '<?php echo wp_create_nonce("amp_admin_nonce"); ?>'
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: '✅ ส่ง Webhook จำลองสำเร็จ',
                        html: `
                            <div style="text-align: left;">
                                <p><strong>🆔 Transaction ID:</strong> ${txnId}</p>
                                <p><strong>📋 Order Number:</strong> ${orderNumber}</p>
                                <p><strong>💰 Amount:</strong> $${amount}</p>
                                <p><strong>📊 Status:</strong> ${status}</p>
                                <hr>
                                <p><strong>📝 ผลลัพธ์:</strong> ${response.data.message}</p>
                            </div>
                        `
                    });
                    
                    $('#simulation-result-container').show();
                    $('#simulation-result').text(JSON.stringify(response.data, null, 2));
                    
                    // รีเฟรช logs และ payment history
                    loadWebhookLogs();
                    loadPaymentHistory(currentPage);
                    loadPendingOrders();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: '❌ เกิดข้อผิดพลาด',
                        text: response.data.message || 'ไม่สามารถส่ง webhook จำลองได้'
                    });
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: '❌ ข้อผิดพลาดการเชื่อมต่อ',
                    text: 'กรุณาลองใหม่อีกครั้ง'
                });
            },
            complete: function() {
                button.prop('disabled', false).text('⚡️ ส่ง Webhook จำลอง');
            }
        });
    });

    $('#test-status-colors').on('click', function() {
        const statusList = [
            { status: 'completed', text: 'ชำระเงินสำเร็จ' },
            { status: 'mismatch', text: 'จ่ายเกิน - สำเร็จ' },
            { status: 'cancelled', text: 'ยกเลิก' },
            { status: 'expired', text: 'หมดอายุ' },
            { status: 'error', text: 'ข้อผิดพลาด' },
            { status: 'signature_failed', text: 'ตรวจสอบลายเซ็นไม่ผ่าน' }
        ];

        let html = '<div style="text-align: left;">';
        statusList.forEach(item => {
            const statusInfo = getStatusInfo(item.status);
            html += `<p><span class="history-status ${statusInfo.class}" style="margin-right: 10px;">${item.status}</span> ${item.text}</p>`;
        });
        html += '</div>';

        Swal.fire({
            title: '🎨 ตัวอย่างสีสถานะ',
            html: html,
            width: 600,
            confirmButtonText: 'ปิด'
        });
    });
});

</script>
