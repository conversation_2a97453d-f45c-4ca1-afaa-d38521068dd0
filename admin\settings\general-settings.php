<?php
if (!defined('WPINC')) {
    die;
}

function display_general_settings_page() {
    if (!current_user_can('manage_options')) {
        return;
    }
    $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';
    $notice = '';
    
    wp_enqueue_style('wp-color-picker');
    wp_enqueue_script('wp-color-picker');
    wp_enqueue_script('jquery-ui-tabs');
    wp_enqueue_script('jquery');
    wp_enqueue_media();
    ?>
    <link rel="stylesheet" href="<?php echo plugin_dir_url(dirname(__FILE__)) . 'assets/css/admin-unified.css?v=' . filemtime(plugin_dir_path(dirname(__FILE__)) . 'assets/css/admin-unified.css'); ?>" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <div class="wrap user-management-wrap">
        <div class="user-management-header">
            <h1>System Settings</h1>
            <div>
                <button class="modern-button button-primary" onclick="location.reload();">Refresh Settings</button>
            </div>
        </div>
        <?php echo $notice; ?>
        <div class="user-management-content">
            <div class="users-table-container">
                <div class="users-table-header">
                    <h2>Settings Configuration</h2>
                    <div class="users-table-stats">
                        <span class="stat-item">Tabs: <strong>9</strong></span>
                    </div>
                </div>
                <div id="settings-tabs" class="settings-tabs">
                    <a href="#general" class="tab-link <?php echo $active_tab == 'general' ? 'active' : ''; ?>" data-tab="general">General</a>
                    <a href="#payment" class="tab-link <?php echo $active_tab == 'payment' ? 'active' : ''; ?>" data-tab="payment">Payment</a>
                    <a href="#manual-verification" class="tab-link <?php echo $active_tab == 'manual-verification' ? 'active' : ''; ?>" data-tab="manual-verification">Manual Verification</a>
                    <a href="#price" class="tab-link <?php echo $active_tab == 'price' ? 'active' : ''; ?>" data-tab="price">Price Calculation</a>
                    <a href="#discount" class="tab-link <?php echo $active_tab == 'discount' ? 'active' : ''; ?>" data-tab="discount">Discount</a>
                    <a href="#google" class="tab-link <?php echo $active_tab == 'google' ? 'active' : ''; ?>" data-tab="google">Google</a>
                    <a href="#security" class="tab-link <?php echo $active_tab == 'security' ? 'active' : ''; ?>" data-tab="security">Security</a>
                    <a href="#cloudflare" class="tab-link <?php echo $active_tab == 'cloudflare' ? 'active' : ''; ?>" data-tab="cloudflare">CloudFlare</a>
                    <a href="#cache" class="tab-link <?php echo $active_tab == 'cache' ? 'active' : ''; ?>" data-tab="cache">Cache Management</a>
                </div>
        <div id="general" class="tab-content <?php echo $active_tab == 'general' ? 'active' : ''; ?>">
            <?php display_general_settings_content(); ?>
        </div>
        <div id="payment" class="tab-content <?php echo $active_tab == 'payment' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่าการชำระเงิน Plisio</h2>
                <?php
                require_once plugin_dir_path(__FILE__) . 'payment-settings.php';
                display_payment_settings_content();
                ?>
            </div>
        </div>
        <div id="manual-verification" class="tab-content <?php echo $active_tab == 'manual-verification' ? 'active' : ''; ?>">
            <?php
            require_once plugin_dir_path(__FILE__) . 'manual-verification.php';
            ?>
        </div>
        <div id="price" class="tab-content <?php echo $active_tab == 'price' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่าการคำนวณราคา</h2>
                <?php 
                require_once plugin_dir_path(__FILE__) . 'price-settings.php';
                display_price_settings_content(); 
                ?>
            </div>
        </div>
        <div id="discount" class="tab-content <?php echo $active_tab == 'discount' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่าส่วนลด</h2>
                <?php 
                require_once plugin_dir_path(__FILE__) . 'discount-settings.php';
                display_discount_settings_content(); 
                ?>
            </div>
        </div>
        <div id="google" class="tab-content <?php echo $active_tab == 'google' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่า Google</h2>
                <?php 
                require_once plugin_dir_path(__FILE__) . 'google-settings.php';
                display_google_settings_content(); 
                ?>
            </div>
        </div>
        <div id="security" class="tab-content <?php echo $active_tab == 'security' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่าความปลอดภัย</h2>
                <?php
                require_once plugin_dir_path(__FILE__) . 'security-settings.php';
                display_security_settings_content();
                ?>
            </div>
        </div>
        <div id="cloudflare" class="tab-content <?php echo $active_tab == 'cloudflare' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>ตั้งค่า CloudFlare</h2>
                <?php
                require_once plugin_dir_path(__FILE__) . 'cloudflare-settings.php';
                display_cloudflare_settings_content();
                ?>
            </div>
        </div>
        <div id="cache" class="tab-content <?php echo $active_tab == 'cache' ? 'active' : ''; ?>">
            <div class="settings-card">
                <h2>🚀 ระบบจัดการแคชข้อมูล</h2>
                <?php
                require_once plugin_dir_path(__FILE__) . 'cache-management.php';
                display_cache_management_content();
                ?>
            </div>
        </div>
        <script>
        (function() {
            function initializeTabSwitching() {
                if (typeof jQuery === 'undefined') {
                    setTimeout(initializeTabSwitching, 100);
                    return;
                }

                jQuery(document).ready(function($) {
                    $('#settings-tabs .tab-link').on('click', function(e) {
                        e.preventDefault();
                        var targetTab = $(this).attr('data-tab');
                        $('#settings-tabs .tab-link').removeClass('active');
                        $(this).addClass('active');
                        $('.tab-content').removeClass('active');
                        $('#' + targetTab).addClass('active');
                        var url = new URL(window.location);
                        url.searchParams.set('tab', targetTab);
                        window.history.pushState({}, '', url);
                    });
                });
            }

            initializeTabSwitching();
        })();
        </script>
            </div>
        </div>
    </div>
    <?php
}

function display_general_settings_content() {
    if (!current_user_can('manage_options')) {
        wp_die('Administrator access required for general settings');
    }

    $notice = '';
    
    $amp_telegram_default_link = get_option('telegram_default_link', '');
    $amp_site_logo_url = get_option('site_logo_url', '');
    $amp_wp_admin_protection = get_option('amp_wp_admin_protection', 'enabled');
    $amp_allowed_ip_addresses = get_option('amp_allowed_ip_addresses', '');
    $amp_whm_cpanel_detection = get_option('amp_whm_cpanel_detection', 'enabled');
    $amp_emergency_access_code = get_option('amp_emergency_access_code', '');
    $amp_development_mode = get_option('amp_development_mode', 'disabled');

    require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';
    $global_total_clicks = get_global_total_clicks();

    $general_settings_nonce = wp_create_nonce('save_general_settings');
    ?>
    
    <?php if (!empty($notice)) : ?>
        <?php echo $notice; ?>
    <?php endif; ?>

    <form method="post" action="" id="general-settings-form">
        <input type="hidden" name="general_settings_nonce" value="<?php echo esc_attr($general_settings_nonce); ?>">

        <div class="plisio-card">
            <h3>⚙️ การตั้งค่าทั่วไปและความปลอดภัย</h3>
            <div class="general-settings-grid">
                <div class="settings-column-left">
                    <table class="form-table">
                        <tr>
                                    <th scope="row"><label for="telegram_default_link">🔗 ลิงก์ Telegram เริ่มต้น</label></th>
                            <td>
            <input type="text" id="telegram_default_link" name="telegram_default_link"
                                       value="<?php echo esc_attr($amp_telegram_default_link); ?>"
                                       class="regular-text"
                                       placeholder="https://t.me/username">
                                <p class="description">ลิงก์ Telegram ที่จะใช้เป็นค่าเริ่มต้นในระบบ</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="amp_wp_admin_protection">🛡️ การป้องกัน wp-admin</label></th>
                            <td>
                                <div class="toggle-switch-container">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="amp_wp_admin_protection" id="amp_wp_admin_protection" 
                                               value="enabled" <?php checked($amp_wp_admin_protection, 'enabled'); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="toggle-label"><?php echo $amp_wp_admin_protection === 'enabled' ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน'; ?></span>
                                </div>
                                <p class="description">เปิดใช้งานระบบป้องกันการเข้าถึงหน้า wp-admin</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="amp_allowed_ip_addresses">🌐 IP ที่อนุญาต</label></th>
                            <td>
                                <input type="text" id="amp_allowed_ip_addresses" name="amp_allowed_ip_addresses" 
                                       value="<?php echo esc_attr($amp_allowed_ip_addresses); ?>" 
                                       class="regular-text" 
                                       placeholder="***********, ***********">
                                <p class="description">IP Address ที่อนุญาตให้เข้าถึงระบบ Admin (คั่นด้วยเครื่องหมายจุลภาค)</p>
                                
                                <div style="margin-top: 10px;">
                                    <button type="button" id="create-htaccess-protection" class="button button-secondary">
                                        🛡️ สร้างการป้องกัน wp-login.php
                                    </button>
                                    <button type="button" id="remove-htaccess-protection" class="button button-secondary" style="margin-left: 10px;">
                                        🗑️ ลบการป้องกัน wp-login.php
                                    </button>
                                </div>
                                <div id="htaccess-status" style="margin-top: 10px; font-weight: bold;"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="settings-column-right">
                    <table class="form-table">
                        <tr>
                            <th scope="row"><label for="amp_development_mode">🚧 โหมดพัฒนา (Development Mode)</label></th>
                            <td>
                                <div class="toggle-switch-container">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="amp_development_mode" id="amp_development_mode"
                                               value="enabled" <?php checked($amp_development_mode, 'enabled'); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="toggle-label"><?php echo $amp_development_mode === 'enabled' ? '🟡 โหมดพัฒนา' : '🟢 โหมดปกติ'; ?></span>
                                </div>
                                <p class="description">เมื่อเปิดใช้งาน จะป้องกันผู้ใช้ซื้อสินค้าและแจ้งเตือนว่าระบบอยู่ในโหมดพัฒนา</p>
                                <?php if ($amp_development_mode === 'enabled'): ?>
                                <div class="development-mode-warning" style="margin-top: 10px; padding: 12px; background: #fff3cd; border-left: 4px solid #ffc107; border-radius: 4px;">
                                    <p style="margin: 0; font-size: 13px; line-height: 1.4; color: #856404;">
                                        <strong>⚠️ คำเตือน:</strong> ระบบกำลังอยู่ในโหมดพัฒนา ผู้ใช้จะไม่สามารถซื้อสินค้าได้ในขณะนี้
                                    </p>
                                </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="amp_whm_cpanel_detection">🔧 ตรวจจับ WHM/cPanel</label></th>
                            <td>
                                <div class="toggle-switch-container">
                                    <label class="toggle-switch">
                                        <input type="checkbox" name="amp_whm_cpanel_detection" id="amp_whm_cpanel_detection"
                                               value="enabled" <?php checked($amp_whm_cpanel_detection, 'enabled'); ?>>
                                        <span class="toggle-slider"></span>
                                    </label>
                                    <span class="toggle-label"><?php echo $amp_whm_cpanel_detection === 'enabled' ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน'; ?></span>
                                </div>
                                <p class="description">เปิดใช้งานการตรวจจับ WHM/cPanel อัตโนมัติ</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="amp_emergency_access_code">🔑 รหัสเข้าถึงฉุกเฉิน</label></th>
                            <td>
                                <input type="text" id="amp_emergency_access_code" name="amp_emergency_access_code"
                                       value="<?php echo esc_attr($amp_emergency_access_code); ?>"
                                       class="regular-text"
                                       placeholder="emergency123">
                                <p class="description">รหัสสำหรับเข้าถึงระบบในกรณีฉุกเฉิน (รูปแบบ: /wp-login.php?emergency=[code])</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><label for="global_total_clicks">📊 ยอดคลิกโฆษณาทั้งหมด</label></th>
                            <td>
                                <input type="number" id="global_total_clicks" name="global_total_clicks"
                                       value="<?php echo esc_attr($global_total_clicks); ?>"
                                       class="regular-text"
                                       min="0"
                                       placeholder="0">
                                <p class="description">จำนวนคลิกโฆษณาทั้งหมดที่จะแสดงในหน้า Sale Page</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="logo-section">
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="site_logo_url">🖼️ โลโก้เว็บไซต์</label></th>
                        <td>
                            <div class="logo-upload-container">
                                <div class="logo-preview">
                                    <?php if ($amp_site_logo_url): ?>
                                        <img src="<?php echo esc_url($amp_site_logo_url); ?>" alt="Site Logo" style="max-width: 200px; max-height: 100px;">
                                    <?php else: ?>
                                        <div class="no-logo-placeholder">
                                            <i class="fas fa-image"></i>
                                            <span>No logo</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <input type="hidden" id="site_logo_url" name="site_logo_url" value="<?php echo esc_attr($amp_site_logo_url); ?>">
                                <div class="logo-buttons">
                                    <button type="button" id="upload_logo_button" class="ad-btn ad-btn-primary">
                                        <i class="fas fa-upload"></i> อัพโหลดโลโก้
                                    </button>
                                    <?php if ($amp_site_logo_url): ?>
                                        <button type="button" id="remove_logo_button" class="ad-btn ad-btn-danger">
                                            <i class="fas fa-trash"></i> ลบโลโก้
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <p class="description">อัพโหลดโลโก้สำหรับเว็บไซต์ของคุณ (รองรับ JPG, PNG, SVG)</p>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <p class="submit">
            <button type="button" name="submit_general_settings" id="save-general-settings" class="button button-primary">
                💾 บันทึกการตั้งค่า
            </button>
            <span id="general-settings-status"></span>
        </p>
    </form>

    <style>
        .general-settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }
        .settings-column-left,
        .settings-column-right {
            background: rgba(255, 255, 255, 0.5);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }
        .logo-section {
            border-top: 1px solid #e1e5e9;
            padding-top: 20px;
            margin-top: 20px;
        }
        .logo-upload-container {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .logo-preview {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            min-width: 200px;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9f9f9;
        }
        .logo-preview img {
            max-width: 200px;
            max-height: 100px;
            border-radius: 4px;
        }
        .no-logo-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            color: #666;
        }
        .no-logo-placeholder i {
            font-size: 24px;
            opacity: 0.5;
        }
        .no-logo-placeholder span {
            font-size: 14px;
        }
        .logo-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        @media (max-width: 768px) {
            .general-settings-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .logo-upload-container {
                flex-direction: column;
                align-items: flex-start;
            }
            .logo-preview {
                width: 100%;
                min-width: auto;
            }
        }
        .modern-popup-container {
            border-radius: 12px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
        }
        .modern-success-popup {
            border-radius: 12px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
        }

        .modern-btn {
            padding: 12px 24px !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            border: none !important;
            cursor: pointer !important;
            margin: 0 8px !important;
            min-width: 140px !important;
        }
        .modern-btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }
        .modern-btn-primary:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
        }
        .modern-btn-secondary {
            background: #6c757d !important;
            color: white !important;
        }
        .modern-btn-secondary:hover {
            background: #5a6268 !important;
            transform: translateY(-2px) !important;
        }
        .modern-btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
            color: white !important;
        }
        .modern-btn-danger:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4) !important;
        }
        .modern-btn-success {
            background: linear-gradient(135deg, #51cf66 0%, #40c057 100%) !important;
            color: white !important;
        }
        .modern-btn-success:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(81, 207, 102, 0.4) !important;
        }
        .modern-btn-warning {
            background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%) !important;
            color: #212529 !important;
        }
        .modern-btn-warning:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(255, 212, 59, 0.4) !important;
        }

        .swal2-actions {
            gap: 16px !important;
            margin: 25px auto 0 !important;
        }
        .swal2-popup {
            padding: 25px !important;
        }
        .swal2-html-container {
            margin: 20px 0 !important;
        }
    </style>

    <script>
    jQuery(document).ready(function($) {
        let mediaUploader;

        $('#upload_logo_button').on('click', function() {
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            mediaUploader = wp.media({
                title: 'เลือกโลโก้',
                library: {
                    type: ['image']
                },
                button: {
                    text: 'เลือกโลโก้นี้'
                },
                multiple: false
            });

            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#site_logo_url').val(attachment.url);
                
                $('.logo-preview').html('<img src="' + attachment.url + '" alt="Site Logo" style="max-width: 200px; max-height: 100px; border-radius: 4px;">');
                
                if ($('#remove_logo_button').length === 0) {
                    $('.logo-buttons').append('<button type="button" id="remove_logo_button" class="ad-btn ad-btn-danger"><i class="fas fa-trash"></i> ลบโลโก้</button>');
                }
            });

            mediaUploader.open();
        });

        $(document).on('click', '#remove_logo_button', function() {
            $('#site_logo_url').val('');
            $('.logo-preview').html('<div class="no-logo-placeholder"><i class="fas fa-image"></i><span>No logo</span></div>');
            $(this).remove();
        });

        $('#save-general-settings').on('click', function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const $status = $('#general-settings-status');
            const originalText = $btn.html();
            
            $btn.prop('disabled', true).html('⏳ กำลังบันทึก...');
            $status.removeClass('success error').text('');

            const formData = $('#general-settings-form').serialize() + '&action=save_general_settings';

            jQuery.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response && response.success) {
                        $status.addClass('success').text('✅ บันทึกการตั้งค่าเรียบร้อยแล้ว');
                        
                        $('.toggle-label').each(function() {
                            const checkbox = $(this).siblings('.toggle-switch').find('input[type="checkbox"]');
                            if (checkbox.length) {
                                $(this).text(checkbox.is(':checked') ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน');
                            }
                        });
                    } else {
                        $status.addClass('error').text('❌ เกิดข้อผิดพลาด: ' + (response && response.data ? response.data : 'ไม่สามารถบันทึกได้'));
                    }
                },
                error: function() {
                    $status.addClass('error').text('❌ เกิดข้อผิดพลาดในการเชื่อมต่อ');
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                    setTimeout(function() {
                        $status.fadeOut();
                    }, 3000);
                }
            });
        });

        $('.toggle-switch input[type="checkbox"]').on('change', function() {
            const $label = $(this).closest('.toggle-switch-container').find('.toggle-label');
            $label.text($(this).is(':checked') ? '🟢 เปิดใช้งาน' : '🔴 ปิดใช้งาน');
        });
        
        $('#create-htaccess-protection').on('click', function(e) {
            e.preventDefault();
            
            const allowedIps = $('#amp_allowed_ip_addresses').val().trim();
            if (!allowedIps) {
                Swal.fire({
                    title: 'ข้อมูลไม่ครบถ้วน',
                    text: 'กรุณาระบุ IP ที่อนุญาตก่อนสร้างการป้องกัน',
                    icon: 'warning',
                    confirmButtonText: 'ตกลง',
                    customClass: {
                        popup: 'modern-popup-container',
                        confirmButton: 'modern-btn modern-btn-primary'
                    },
                    buttonsStyling: false
                });
                return;
            }
            
            Swal.fire({
                title: 'ยืนยันการสร้างการป้องกัน',
                html: `
                    <div style="text-align: left; margin: 20px 0;">
                        <p><strong>🛡️ การป้องกัน wp-login.php</strong></p>
                        <p>ระบบจะสร้างกฎใน .htaccess เพื่อ:</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>ป้องกันการเข้าถึง wp-login.php จาก IP ที่ไม่ได้รับอนุญาต</li>
                            <li>อนุญาต IP ที่คุณระบุ: <code>${allowedIps}</code></li>
                            <li>รองรับ LiteSpeed Cache และ WHM/cPanel</li>
                        </ul>
                        <div style="background: #fff3cd; padding: 10px; border-radius: 4px; margin-top: 15px;">
                            <small><strong>⚠️ หมายเหตุ:</strong> ระบบจะสำรองไฟล์ .htaccess เดิมก่อนแก้ไข</small>
                        </div>
                    </div>
                `,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: '🛡️ สร้างการป้องกัน',
                cancelButtonText: '❌ ยกเลิก',
                customClass: {
                    popup: 'modern-popup-container',
                    confirmButton: 'modern-btn modern-btn-primary',
                    cancelButton: 'modern-btn modern-btn-secondary'
                },
                buttonsStyling: false,
                reverseButtons: true,
                width: '550px'
            }).then((result) => {
                if (result.isConfirmed) {
                    
                    jQuery.post(ajaxurl, {
                        action: 'create_wp_login_htaccess_protection',
                        security: jQuery('input[name="general_settings_nonce"]').val()
                    }, function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'สร้างการป้องกันสำเร็จ!',
                                html: `
                                    <div style="text-align: center; margin: 20px 0;">
                                        <div style="font-size: 48px; margin-bottom: 15px;">🛡️</div>
                                        <p><strong>${response.data.message}</strong></p>
                                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin-top: 20px;">
                                            <p style="margin: 0; color: #155724;"><strong>✅ การป้องกันพร้อมใช้งาน</strong></p>
                                            <small style="color: #155724;">wp-login.php ได้รับการป้องกันแล้ว</small>
                                        </div>
                                    </div>
                                `,
                                icon: 'success',
                                confirmButtonText: 'เยี่ยม!',
                                customClass: {
                                    popup: 'modern-success-popup',
                                    confirmButton: 'modern-btn modern-btn-success'
                                },
                                buttonsStyling: false
                            });
                            jQuery('#htaccess-status').html('<span style="color: green;">✅ ' + response.data.message + '</span>');
                        } else {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด',
                                text: response.data.message || 'ไม่สามารถสร้างการป้องกันได้',
                                icon: 'error',
                                confirmButtonText: 'ตกลง',
                                customClass: {
                                    popup: 'modern-popup-container',
                                    confirmButton: 'modern-btn modern-btn-danger'
                                },
                                buttonsStyling: false
                            });
                            jQuery("#htaccess-status").html('<span style="color: red;">❌ ' + (response.data.message || 'เกิดข้อผิดพลาด') + '</span>');
                        }
                    }).fail(function() {
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด',
                            text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                            icon: 'error',
                            confirmButtonText: 'ตกลง',
                            customClass: {
                                popup: 'modern-popup-container',
                                confirmButton: 'modern-btn modern-btn-danger'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        });
        
        $('#remove-htaccess-protection').on('click', function(e) {
            e.preventDefault();
            
            Swal.fire({
                title: 'ยืนยันการลบการป้องกัน',
                html: `
                    <div style="text-align: left; margin: 20px 0;">
                        <p><strong>🗑️ ลบการป้องกัน wp-login.php</strong></p>
                        <p>ระบบจะดำเนินการ:</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>ลบกฎการป้องกันออกจาก .htaccess</li>
                            <li>คืนค่าการเข้าถึง wp-login.php เป็นปกติ</li>
                            <li>รักษาไฟล์สำรอง .htaccess.backup ไว้</li>
                        </ul>
                        <div style="background: #f8d7da; padding: 10px; border-radius: 4px; margin-top: 15px;">
                            <small><strong>⚠️ คำเตือน:</strong> หลังจากลบแล้ว wp-login.php จะเข้าถึงได้จากทุก IP</small>
                        </div>
                    </div>
                `,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: '🗑️ ลบการป้องกัน',
                cancelButtonText: '❌ ยกเลิก',
                customClass: {
                    popup: 'modern-popup-container',
                    confirmButton: 'modern-btn modern-btn-danger',
                    cancelButton: 'modern-btn modern-btn-secondary'
                },
                buttonsStyling: false,
                reverseButtons: true,
                width: '550px'
            }).then((result) => {
                if (result.isConfirmed) {
                    
                    jQuery.post(ajaxurl, {
                        action: 'remove_wp_login_htaccess_protection',
                        security: jQuery('input[name="general_settings_nonce"]').val()
                    }, function(response) {
                        if (response.success) {
                            Swal.fire({
                                title: 'ลบการป้องกันสำเร็จ!',
                                html: `
                                    <div style="text-align: center; margin: 20px 0;">
                                        <div style="font-size: 48px; margin-bottom: 15px;">🗑️</div>
                                        <p><strong>${response.data.message}</strong></p>
                                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 20px;">
                                            <p style="margin: 0; color: #856404;"><strong>⚠️ การป้องกันถูกลบแล้ว</strong></p>
                                            <small style="color: #856404;">wp-login.php สามารถเข้าถึงได้จากทุก IP</small>
                                        </div>
                                    </div>
                                `,
                                icon: 'success',
                                confirmButtonText: 'เข้าใจแล้ว',
                                customClass: {
                                    popup: 'modern-success-popup',
                                    confirmButton: 'modern-btn modern-btn-warning'
                                },
                                buttonsStyling: false
                            });
                            jQuery('#htaccess-status').html('<span style="color: orange;">⚠️ ' + response.data.message + '</span>');
                        } else {
                            Swal.fire({
                                title: 'เกิดข้อผิดพลาด',
                                text: response.data.message || 'ไม่สามารถลบการป้องกันได้',
                                icon: 'error',
                                confirmButtonText: 'ตกลง',
                                customClass: {
                                    popup: 'modern-popup-container',
                                    confirmButton: 'modern-btn modern-btn-danger'
                                },
                                buttonsStyling: false
                            });
                            jQuery("#htaccess-status").html('<span style="color: red;">❌ ' + (response.data.message || 'เกิดข้อผิดพลาด') + '</span>');
                        }
                    }).fail(function() {
                        Swal.fire({
                            title: 'เกิดข้อผิดพลาด',
                            text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                            icon: 'error',
                            confirmButtonText: 'ตกลง',
                            customClass: {
                                popup: 'modern-popup-container',
                                confirmButton: 'modern-btn modern-btn-danger'
                            },
                            buttonsStyling: false
                        });
                    });
                }
            });
        });
    });
    </script>
    <?php
}

function handle_save_general_settings() {
    if (!isset($_POST['general_settings_nonce']) || !wp_verify_nonce($_POST['general_settings_nonce'], 'save_general_settings')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    $telegram_link = sanitize_text_field($_POST['telegram_default_link'] ?? '');
    $site_logo = sanitize_url($_POST['site_logo_url'] ?? '');
    $wp_admin_protection = isset($_POST['amp_wp_admin_protection']) ? 'enabled' : 'disabled';
    $allowed_ips = sanitize_text_field($_POST['amp_allowed_ip_addresses'] ?? '');
    $whm_detection = isset($_POST['amp_whm_cpanel_detection']) ? 'enabled' : 'disabled';
    $emergency_code = sanitize_text_field($_POST['amp_emergency_access_code'] ?? '');
    $development_mode = isset($_POST['amp_development_mode']) ? 'enabled' : 'disabled';
    $global_total_clicks = intval($_POST['global_total_clicks'] ?? 0);

    update_option('telegram_default_link', $telegram_link);
    update_option('site_logo_url', $site_logo);
    update_option('amp_wp_admin_protection', $wp_admin_protection);
    update_option('amp_allowed_ip_addresses', $allowed_ips);
    update_option('amp_whm_cpanel_detection', $whm_detection);
    update_option('amp_emergency_access_code', $emergency_code);
    update_option('amp_development_mode', $development_mode);

    require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';
    $global_click_result = set_global_total_clicks($global_total_clicks);

    if (!$global_click_result) {
        wp_send_json_error('Settings saved but failed to update global clicks');
        return;
    }

    wp_send_json_success('Settings saved successfully');
}

add_action('wp_ajax_save_general_settings', 'handle_save_general_settings');

function handle_create_wp_login_htaccess_protection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'save_general_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $plugin_instance = AdManagementPro::instance();
    $result = $plugin_instance->create_wp_login_htaccess_protection();
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}

function handle_remove_wp_login_htaccess_protection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'save_general_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $plugin_instance = AdManagementPro::instance();
    $result = $plugin_instance->remove_wp_login_htaccess_protection();
    
    if ($result['success']) {
        wp_send_json_success($result);
    } else {
        wp_send_json_error($result);
    }
}

add_action('wp_ajax_create_wp_login_htaccess_protection', 'handle_create_wp_login_htaccess_protection');
add_action('wp_ajax_remove_wp_login_htaccess_protection', 'handle_remove_wp_login_htaccess_protection');
