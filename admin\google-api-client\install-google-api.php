<?php
if(!defined('ABSPATH')&&!defined('WP_ADMIN')){
    $f=debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS,2)[1]['file']??'';
    if(!$f||strpos($f,'google-analytics-service-account.php')===false)die;
}

$zipUrl = 'https://github.com/googleapis/google-api-php-client/releases/download/v2.18.3/google-api-php-client-v2.18.3-PHP8.3.zip';
$zipFile = __DIR__.'/google-api.zip';
$tempDir = __DIR__.'/temp';
$destDir=__DIR__;

function dlZip($u, $p){
    if(file_exists($p)) {
        if(!unlink($p)) {
            die("Error: Cannot remove existing file $p");
        }
    }

    $c = curl_init($u);
    if($c === false) {
        die("Error: Failed to initialize cURL");
    }

    $f = fopen($p, 'w');
    if($f === false) {
        curl_close($c);
        die("Error: Cannot open file $p for writing");
    }

    curl_setopt_array($c, [
        CURLOPT_FILE => $f,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_TIMEOUT => 300
    ]);

    $ok = curl_exec($c);
    $code = curl_getinfo($c, CURLINFO_HTTP_CODE);
    $error = curl_error($c);

    curl_close($c);
    fclose($f);

    if(!$ok) {
        @unlink($p);
        die("Error: cURL download failed: $error");
    }

    if($code !== 200) {
        @unlink($p);
        die("Error: HTTP status $code when downloading from $u");
    }

    if(filesize($p) < 1024) {
        @unlink($p);
        die("Error: Downloaded file is too small (less than 1KB)");
    }
}

function unzipTo($zip, $dir){
    if(!file_exists($zip)) {
        die("Error: ZIP file $zip does not exist");
    }

    $z = new ZipArchive;
    $result = $z->open($zip);
    if($result !== true) {
        die("Error: Cannot open ZIP file $zip (error code: $result)");
    }

    if(is_dir($dir)) {
        rr($dir);
    }

    if(!mkdir($dir, 0755, true)) {
        die("Error: Cannot create directory $dir");
    }

    $extract_result = $z->extractTo($dir);
    $z->close();

    if($extract_result !== true) {
        die("Error: Failed to extract ZIP file to $dir");
    }
}

function rr($d){
    if(!is_dir($d)) {
        return;
    }
    $scan_result = scandir($d);
    if($scan_result === false) {
        return;
    }
    foreach(array_diff($scan_result, ['.', '..']) as $i){
        $p = "$d/$i";
        if(is_dir($p)) {
            rr($p);
        } else {
            unlink($p);
        }
    }
    rmdir($d);
}

function cp($s, $d){
    if(is_file($s)) {
        copy($s, $d);
        return;
    }
    if(!is_dir($s)) {
        return;
    }
    $scan_result = scandir($s);
    if($scan_result === false) {
        return;
    }
    $is_google_client = false;
    if(strpos($s, 'google-api-php-client') !== false && is_dir("$s/src") && is_dir("$s/vendor")) {
        $is_google_client = true;
    }
    foreach(array_diff($scan_result, ['.', '..']) as $i) {
        $src = "$s/$i";
        if($is_google_client) {
            $dst = "$d/$i";
        } else {
            if(is_dir($src) && strpos($i, 'google-api-php-client') === 0) {
                cp($src, $d);
                continue;
            }
            $dst = "$d/$i";
        }
        if(is_dir($src)) {
            if(!is_dir($dst)) {
                mkdir($dst, 0755, true);
            }
            cp($src, $dst);
        } else {
            copy($src, $dst);
        }
    }
}

dlZip($zipUrl,$zipFile);
unzipTo($zipFile,$tempDir);

if(!is_dir($tempDir)) {
    die("Error: Temp directory $tempDir does not exist or is not a directory");
}

$scan_result = scandir($tempDir);
if($scan_result === false) {
    die("Error: Cannot scan directory $tempDir");
}

$entries = array_diff($scan_result, ['.', '..']);
if(empty($entries)) {
    die("Error: No files extracted to $tempDir");
}

$root = $tempDir;

if(!is_dir("$root/src") || !is_dir("$root/vendor")) {
    $sub_scan = scandir($root);
    if($sub_scan === false) {
        die("Error: Cannot scan directory $root");
    }

    $found = false;
    foreach(array_diff($sub_scan, ['.', '..']) as $sub) {
        $sub_path = "$root/$sub";
        if(is_dir($sub_path)) {
            if(strpos($sub, 'google-api-php-client') === 0) {
                $root = $sub_path;
                $found = true;
                break;
            }
        }
    }

    if(!$found) {
        die("Error: Could not find Google API PHP Client directory structure");
    }
}

if(is_dir("$root/src")) {
    if(!is_dir("$destDir/src")) {
        mkdir("$destDir/src", 0755, true);
    }
    cp("$root/src", "$destDir/src");
}

if(is_dir("$root/vendor")) {
    if(!is_dir("$destDir/vendor")) {
        mkdir("$destDir/vendor", 0755, true);
    }
    cp("$root/vendor", "$destDir/vendor");
} else {
    if(!is_dir("$destDir/vendor")) {
        mkdir("$destDir/vendor", 0755, true);
    }
}

if(file_exists($zipFile)) {
    if(!unlink($zipFile)) {
    }
}

if(is_dir($tempDir)) {
    rr($tempDir);
}

echo "Google API PHP Client installed successfully!";

$installer_script = __FILE__;
if(file_exists($installer_script)) {
    @unlink($installer_script);
}