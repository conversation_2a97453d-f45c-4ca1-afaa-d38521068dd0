<?php

if (!defined('WPINC') && !defined('WP_USE_THEMES')) {
    die;
}

if (!function_exists('wp_mkdir_p')) {
    function wp_mkdir_p($target) {
        $wrapper = null;

        if (strpos($target, '://') !== false) {
            list($wrapper, $target) = explode('://', $target, 2);
        }

        $target = str_replace('//', '/', $target);

        $target = rtrim($target, '/');
        if (empty($target)) {
            $target = '/';
        }

        if (is_dir($target)) {
            return true;
        }

        $target_parent = dirname($target);
        while ('.' != $target_parent && !is_dir($target_parent) && dirname($target_parent) !== $target_parent) {
            $target_parent = dirname($target_parent);
        }

        if ($stat = @stat($target_parent)) {
            $dir_perms = $stat['mode'] & 0007777;
        } else {
            $dir_perms = 0755;
        }

        if (@mkdir($target, $dir_perms, true)) {
            return true;
        }

        return false;
    }
}

function record_ad_click($user_id, $position_id, $clicks = 1) {
    $stats_dir = WP_CONTENT_DIR . '/statistics';
    $users_dir = $stats_dir . '/users';

    if (!file_exists($stats_dir)) {
        wp_mkdir_p($stats_dir);
    }

    if (!file_exists($users_dir)) {
        wp_mkdir_p($users_dir);
    }

    $safe_user_id = intval($user_id);
    if ($safe_user_id <= 0) {
        return;
    }

    $safe_position_id = sanitize_text_field($position_id);
    if (empty($safe_position_id)) {
        return;
    }

    $safe_clicks = intval($clicks);
    if ($safe_clicks <= 0) {
        $safe_clicks = 1;
    }

    $user_dir = $users_dir . '/user_' . $safe_user_id;
    $daily_dir = $user_dir . '/daily';
    $total_dir = $user_dir . '/total';

    foreach ([$user_dir, $daily_dir, $total_dir] as $dir) {
        if (!file_exists($dir)) {
            $sanitized_dir = realpath(dirname($dir)) . '/' . basename($dir);
            if (strpos($sanitized_dir, WP_CONTENT_DIR . '/statistics/') === 0) {
                wp_mkdir_p($sanitized_dir);
            } else {
                return false;
            }
        }
    }

    $today = date('Y-m-d');

    $daily_file = $daily_dir . '/position_' . $safe_position_id . '.txt';

    $total_file = $total_dir . '/position_' . $safe_position_id . '.txt';

    $daily_result = record_daily_click($daily_file, $today, $safe_clicks);

    $total_result = record_total_click($total_file, $safe_clicks);

    $global_result = record_global_click($safe_clicks);

    return ($daily_result && $total_result && $global_result);
}

function record_daily_click($file_path, $date, $clicks) {
    $directory = dirname($file_path);
    if (!file_exists($directory)) {
        wp_mkdir_p($directory);
    }

    $file_handle = fopen($file_path, 'c+');
    if (!$file_handle) {
        return false;
    }

    if (flock($file_handle, LOCK_EX)) {
        $content = '';
        while (!feof($file_handle)) {
            $content .= fread($file_handle, 8192);
        }

        $lines = $content ? explode("\n", $content) : [];
        $updated = false;
        $new_content = [];

        foreach ($lines as $line) {
            if (empty(trim($line))) continue;

            $parts = explode(':', $line);
            if (count($parts) < 2) continue;

            list($line_date, $line_clicks) = $parts;

            if ($line_date === $date) {
                $line_clicks = (int)$line_clicks + $clicks;
                $new_content[] = "$date:$line_clicks";
                $updated = true;
            } else {
                $new_content[] = $line;
            }
        }

        if (!$updated) {
            $new_content[] = "$date:$clicks";
        }

        $filtered_content = keep_last_30_days($new_content);

        ftruncate($file_handle, 0);
        rewind($file_handle);
        fwrite($file_handle, implode("\n", $filtered_content));
        fflush($file_handle);
        flock($file_handle, LOCK_UN);
        fclose($file_handle);

        return true;
    }

    fclose($file_handle);
    return false;
}

function keep_last_30_days($lines) {
    usort($lines, function($a, $b) {
        $date_a = explode(':', $a)[0];
        $date_b = explode(':', $b)[0];
        return strcmp($date_b, $date_a);
    });

    return array_slice($lines, 0, 30);
}

function record_total_click($file_path, $clicks) {
    $directory = dirname($file_path);
    if (!file_exists($directory)) {
        wp_mkdir_p($directory);
    }

    $file_handle = fopen($file_path, 'c+');
    if (!$file_handle) {
        return false;
    }

    if (flock($file_handle, LOCK_EX)) {
        $content = '';
        while (!feof($file_handle)) {
            $content .= fread($file_handle, 8192);
        }

        $total_clicks = (int)trim($content);

        $total_clicks += $clicks;

        ftruncate($file_handle, 0);
        rewind($file_handle);
        fwrite($file_handle, $total_clicks);
        fflush($file_handle);
        flock($file_handle, LOCK_UN);
        fclose($file_handle);

        return true;
    }

    fclose($file_handle);
    return false;
}

function record_global_click($clicks = 1) {
    $stats_dir = WP_CONTENT_DIR . '/statistics';
    $global_dir = $stats_dir . '/global';

    // สร้างโฟลเดอร์หากไม่มี
    if (!file_exists($stats_dir)) {
        wp_mkdir_p($stats_dir);
    }

    if (!file_exists($global_dir)) {
        wp_mkdir_p($global_dir);
    }

    $safe_clicks = intval($clicks);
    if ($safe_clicks <= 0) {
        $safe_clicks = 1;
    }

    $global_file = $global_dir . '/total_clicks.txt';

    return record_total_click($global_file, $safe_clicks);
}

function get_global_total_clicks() {
    $global_file = WP_CONTENT_DIR . '/statistics/global/total_clicks.txt';

    if (!file_exists($global_file)) {
        return 0;
    }

    $content = file_get_contents($global_file);
    return intval(trim($content));
}

function set_global_total_clicks($total_clicks) {
    $stats_dir = WP_CONTENT_DIR . '/statistics';
    $global_dir = $stats_dir . '/global';

    // สร้างโฟลเดอร์หากไม่มี
    if (!file_exists($stats_dir)) {
        wp_mkdir_p($stats_dir);
    }

    if (!file_exists($global_dir)) {
        wp_mkdir_p($global_dir);
    }

    $safe_total_clicks = intval($total_clicks);
    if ($safe_total_clicks < 0) {
        $safe_total_clicks = 0;
    }

    $global_file = $global_dir . '/total_clicks.txt';

    $file_handle = fopen($global_file, 'w');
    if (!$file_handle) {
        return false;
    }

    if (flock($file_handle, LOCK_EX)) {
        fwrite($file_handle, $safe_total_clicks);
        fflush($file_handle);
        flock($file_handle, LOCK_UN);
        fclose($file_handle);
        return true;
    }

    fclose($file_handle);
    return false;
}

if (!function_exists('get_position_last_30_days_clicks')) {
    function get_position_last_30_days_clicks($user_id, $position_id) {
        $safe_user_id = intval($user_id);
        $safe_position_id = sanitize_text_field($position_id);

        if ($safe_user_id <= 0 || empty($safe_position_id)) {
            return array('daily_stats' => array(), 'total_clicks' => 0);
        }

        $daily_dir = WP_CONTENT_DIR . '/statistics/users/user_' . $safe_user_id . '/daily';
        $daily_file = $daily_dir . '/position_' . $safe_position_id . '.txt';

        if (!file_exists($daily_file)) {
            return array('daily_stats' => array(), 'total_clicks' => 0);
        }

        if (!is_readable($daily_file) || strpos(realpath($daily_file), WP_CONTENT_DIR . '/statistics/') !== 0) {
            return array('daily_stats' => array(), 'total_clicks' => 0);
        }

        require_once(ABSPATH . 'wp-admin/includes/file.php');
        WP_Filesystem();
        global $wp_filesystem;

        $content = $wp_filesystem->get_contents($daily_file);
        if ($content === false) {
            return array('daily_stats' => array(), 'total_clicks' => 0);
        }

        $lines = explode("\n", $content);

        $result = array();
        $total_clicks = 0;

        foreach ($lines as $line) {
            if (empty(trim($line))) continue;

            $parts = explode(':', $line);
            if (count($parts) < 2) continue;

            list($date, $clicks) = $parts;
            $clicks = (int)$clicks;

            $result[$date] = $clicks;
            $total_clicks += $clicks;
        }

        ksort($result);

        return array(
            'daily_stats' => $result,
            'total_clicks' => $total_clicks
        );
    }
}

if (!function_exists('get_position_total_clicks')) {
    function get_position_total_clicks($user_id, $position_id) {
        $safe_user_id = intval($user_id);
        $safe_position_id = sanitize_text_field($position_id);

        if ($safe_user_id <= 0 || empty($safe_position_id)) {
            return 0;
        }

        $total_dir = WP_CONTENT_DIR . '/statistics/users/user_' . $safe_user_id . '/total';
        $total_file = $total_dir . '/position_' . $safe_position_id . '.txt';

        if (!file_exists($total_file)) {
            return 0;
        }

        if (!is_readable($total_file) || strpos(realpath($total_file), WP_CONTENT_DIR . '/statistics/') !== 0) {
            return 0;
        }

        require_once(ABSPATH . 'wp-admin/includes/file.php');
        WP_Filesystem();
        global $wp_filesystem;

        $content = $wp_filesystem->get_contents($total_file);
        if ($content === false) {
            return 0;
        }

        return (int)trim($content);
    }
}

if (!function_exists('get_user_position_clicks')) {
    function get_user_position_clicks($user_id, $last_30_days = false) {
        $safe_user_id = intval($user_id);

        if ($safe_user_id <= 0) {
            return array();
        }

        $user_dir = WP_CONTENT_DIR . '/statistics/users/user_' . $safe_user_id;
        $dir_to_check = $last_30_days ? $user_dir . '/daily' : $user_dir . '/total';

        if (!file_exists($dir_to_check)) {
            return array();
        }

        $result = array();
        $total_clicks = 0;

        $files = scandir($dir_to_check);

        if (is_array($files)) {
            foreach ($files as $file) {
                if ($file === '.' || $file === '..') {
                    continue;
                }

                preg_match('/position_(.+)\.txt/', $file, $matches);

                if (count($matches) < 2) {
                    continue;
                }

                $position_id = $matches[1];

                if ($last_30_days) {
                    $position_data = get_position_last_30_days_clicks($safe_user_id, $position_id);
                    $clicks = $position_data['total_clicks'];
                } else {
                    $clicks = get_position_total_clicks($safe_user_id, $position_id);
                }

                $result[] = array(
                    'ad_position' => $position_id,
                    'total_clicks' => $clicks
                );

                $total_clicks += $clicks;
            }
        }

        return $result;
    }
}

if (!function_exists('get_user_total_clicks')) {
    function get_user_total_clicks($user_id, $last_30_days = false) {
        $stats = get_user_position_clicks($user_id, $last_30_days);
        $total = 0;

        if (is_array($stats) && !empty($stats)) {
            foreach ($stats as $stat) {
                $total += $stat['total_clicks'];
            }
        }

        return $total;
    }
}

if (!function_exists('clear_position_clicks')) {
    function clear_position_clicks($position_name) {
        global $wpdb;

        $users_with_position = $wpdb->get_results($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta}
             WHERE meta_key = 'ad_positions'
             AND meta_value LIKE %s",
            '%' . $wpdb->esc_like('"' . $position_name . '"') . '%'
        ));

        $success = true;
        foreach ($users_with_position as $user_meta) {
            $user_positions = get_user_meta($user_meta->user_id, 'ad_positions', true);
            if (is_array($user_positions) && in_array($position_name, $user_positions)) {
                $user_dir = WP_CONTENT_DIR . '/statistics/users/user_' . $user_meta->user_id;
                $daily_file = $user_dir . '/daily/position_' . $position_name . '.txt';
                $total_file = $user_dir . '/total/position_' . $position_name . '.txt';

                if (file_exists($daily_file)) {
                    $success = $success && (file_put_contents($daily_file, '') !== false);
                }

                if (file_exists($total_file)) {
                    $success = $success && (file_put_contents($total_file, '0') !== false);
                }
            }
        }

        return $success;
    }
}

if (!function_exists('clear_all_clicks')) {
    function clear_all_clicks() {
        $stats_dir = WP_CONTENT_DIR . '/statistics';

        if (!file_exists($stats_dir)) {
            return true;
        }

        require_once(ABSPATH . 'wp-admin/includes/file.php');
        WP_Filesystem();
        global $wp_filesystem;

        $result = $wp_filesystem->rmdir($stats_dir, true);

        if ($result) {
            wp_mkdir_p($stats_dir);
            $users_dir = $stats_dir . '/users';
            wp_mkdir_p($users_dir);

            @chmod($stats_dir, 0755);
            @chmod($users_dir, 0755);

            $htaccess_file = $stats_dir . '/.htaccess';
            $htaccess_content = "# Deny access to all files in this directory\n";
            $htaccess_content .= "<Files \"*\">\n";
            $htaccess_content .= "Order Allow,Deny\n";
            $htaccess_content .= "Deny from all\n";
            $htaccess_content .= "</Files>\n";

            $wp_filesystem->put_contents($htaccess_file, $htaccess_content, FS_CHMOD_FILE);
        }

        return $result;
    }
}

if (!function_exists('check_statistics_directory_writable')) {
    function check_statistics_directory_writable() {
        $stats_dir = WP_CONTENT_DIR . '/statistics';
        $users_dir = $stats_dir . '/users';

        if (!file_exists($stats_dir)) {
            wp_mkdir_p($stats_dir);
            @chmod($stats_dir, 0755);
        }

        if (!file_exists($users_dir)) {
            wp_mkdir_p($users_dir);
            @chmod($users_dir, 0755);
        }

        return is_writable($stats_dir) && is_writable($users_dir);
    }
}

if (!function_exists('calculate_user_cpc_cpm')) {
    function calculate_user_cpc_cpm($user_id, $user_positions, $database) {
        $use_ga_for_pricing = get_option('use_ga_for_pricing', 'no');
        $monthly_visitors = 0;
        $monthly_pageviews = 0;

        if ($use_ga_for_pricing === 'yes') {
            $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
            if (file_exists($ga_file) && function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                require_once($ga_file);
                $monthly_visitors = amp_get_monthly_users(31);
                $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no');
                if ($exclude_homepage === 'yes' && function_exists('amp_get_monthly_pageviews_exclude_homepage')) {
                    $monthly_pageviews = amp_get_monthly_pageviews_exclude_homepage(31);
                } else {
                    $monthly_pageviews = amp_get_monthly_pageviews(31);
                }
            }
        }

        $global_settings_table = $database->get_table('ad_price_global_settings');

        if ($monthly_visitors <= 0) {
            $visitors_setting = $database->get_var("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", ['monthly_visitors']);
            $monthly_visitors = $visitors_setting ? (int)$visitors_setting : 300000;
        }

        if ($monthly_pageviews <= 0) {
            $pageviews_setting = $database->get_var("SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s", ['monthly_pageviews']);
            $monthly_pageviews = $pageviews_setting ? (int)$pageviews_setting : ($monthly_visitors * 4);
        }

        $cpc_cpm_data = [];
        $total_cost = 0;
        $total_clicks = 0;
        $total_user_clicks = 0;
        $total_monthly_cost = 0;

        $user_ads = [];
        if (is_array($user_positions)) {
            foreach ($user_positions as $position) {
                if (is_object($position) && isset($position->name)) {
                    $user_ads[] = ['position' => $position->name];
                } elseif (is_array($position) && isset($position['position'])) {
                    $user_ads[] = ['position' => $position['position']];
                } elseif (is_string($position)) {
                    $user_ads[] = ['position' => $position];
                }
            }
        }

        foreach ($user_ads as $ad) {
            $position = $ad['position'];  
            $actual_payment_data = get_position_payment_data($database, $user_id, $position);

            $position_clicks = 0;
            if (function_exists('get_user_position_clicks')) {
                $position_click_stats = get_user_position_clicks($user_id, false);
                foreach ($position_click_stats as $stat) {
                    if ($stat['ad_position'] === $position) {
                        $position_clicks = (int)$stat['total_clicks'];
                        break;
                    }
                }
            }

            $position_impressions = (count($user_ads) > 0) ? ($monthly_pageviews / count($user_ads)) : 0;
            
            if ($actual_payment_data) {
                $amount = $actual_payment_data['amount'];
                $duration = $actual_payment_data['duration'];
                $payment_date = $actual_payment_data['payment_date'];
                
                $days_since_payment = 0;
                if ($payment_date) {
                    $payment_timestamp = strtotime($payment_date);
                    $current_timestamp = time();
                    $days_since_payment = max(0, floor(($current_timestamp - $payment_timestamp) / 86400));
                }
                
                $effective_days = min($duration, max(1, $days_since_payment));
                $daily_cost = $duration > 0 ? ($amount / $duration) : $amount;
                $accumulated_cost = $daily_cost * $effective_days;
                
                $total_monthly_cost += $daily_cost * 30;

                $cpc = $position_clicks > 0 ? ($amount / $position_clicks) : $amount;
                $cpm = $position_impressions > 0 ? (($amount / $position_impressions) * 1000) : 0;
                
                $cpc_cpm_data[$position] = [
                    'cpc' => $cpc,
                    'cpm' => $cpm,
                    'clicks' => $position_clicks,
                    'impressions' => $position_impressions,
                    'actual_price' => $amount,
                    'duration_days' => $duration,
                    'payment_date' => $payment_date,
                    'price_per_day' => $duration > 0 ? ($amount / $duration) : $amount,
                    'total_amount_paid' => $amount,
                    'days_since_payment' => $days_since_payment,
                    'effective_days' => $effective_days,
                    'daily_cost' => $daily_cost,
                    'accumulated_cost' => $accumulated_cost,
                    'cpc_based_on_total' => $position_clicks > 0 ? ($amount / $position_clicks) : $amount,
                    'cpc_based_on_accumulated' => $position_clicks > 0 ? ($accumulated_cost / $position_clicks) : $accumulated_cost
                ];
                
                $total_cost += $amount;
            } else {
                $price_table = $database->get_table('ad_price_calculation');
                $price_data = $database->get_row("SELECT usdt_price FROM {$price_table} WHERE ad_position = %s", [$position]);
                $fallback_price = $price_data ? (float)$price_data->usdt_price : 217;
                
                $total_monthly_cost += $fallback_price;

                $cpc = $position_clicks > 0 ? ($fallback_price / $position_clicks) : $fallback_price;
                $cpm = $position_impressions > 0 ? (($fallback_price / $position_impressions) * 1000) : 0;
                
                $cpc_cpm_data[$position] = [
                    'cpc' => $cpc,
                    'cpm' => $cpm,
                    'clicks' => $position_clicks,
                    'impressions' => $position_impressions,
                    'actual_price' => $fallback_price,
                    'duration_days' => 30,
                    'payment_date' => null,
                    'price_per_day' => $fallback_price / 30,
                    'fallback' => true
                ];
                
                $total_cost += $fallback_price;
            }
            
            $total_clicks += $position_clicks;
            $total_user_clicks += $position_clicks;
        }

        return [
            'positions' => $cpc_cpm_data,
            'average_cpc' => $total_user_clicks > 0 ? ($total_cost / $total_user_clicks) : 0,
            'average_cpm' => count($user_ads) > 0 ? (array_sum(array_column($cpc_cpm_data, 'cpm')) / count($user_ads)) : 0,
            'total_cost' => $total_cost,
            'total_monthly_cost' => $total_monthly_cost,
            'total_clicks' => $total_clicks,
            'monthly_visitors' => $monthly_visitors,
            'monthly_pageviews' => $monthly_pageviews
        ];
    }
}

if (!function_exists('get_position_payment_data')) {
    function get_position_payment_data($database, $user_id, $position) {
        if (!validate_position_payment_security($database, $user_id, $position)) {
            error_log("AMP Security: Invalid parameters for get_position_payment_data - User ID: {$user_id}, Position: {$position}");
            return null;
        }
        
        $payments_table = $database->get_table('ad_payments');
        
        $wpdb = $database->get_wpdb();
        if (!$wpdb->get_var($wpdb->prepare("SHOW TABLES LIKE %s", $payments_table))) {
            return null;
        }
        
        $payment_data = $database->get_row(
            "SELECT amount, duration, payment_date, payment_method, status 
             FROM {$payments_table} 
             WHERE user_id = %d AND ad_position = %s 
             AND status = 'completed' 
             ORDER BY payment_date DESC 
             LIMIT 1",
            [absint($user_id), sanitize_text_field($position)]
        );
        
        if ($payment_data) {
            return [
                'amount' => (float)$payment_data->amount,
                'duration' => (int)$payment_data->duration,
                'payment_date' => $payment_data->payment_date,
                'payment_method' => sanitize_text_field($payment_data->payment_method),
                'status' => sanitize_text_field($payment_data->status)
            ];
        }
        
        return null;
    }
}

if (!function_exists('calculate_position_cpc_detailed')) {
    function calculate_position_cpc_detailed($database, $user_id, $position) {
        $payment_data = get_position_payment_data($database, $user_id, $position);
        
        $position_clicks = 0;
        if (function_exists('get_position_total_clicks')) {
            $position_clicks = get_position_total_clicks($user_id, $position);
        }
        
        if ($payment_data) {
            $amount = $payment_data['amount'];
            $duration = $payment_data['duration'];
            $payment_date = $payment_data['payment_date'];
            
            $days_since_payment = 0;
            if ($payment_date) {
                $payment_timestamp = strtotime($payment_date);
                $current_timestamp = time();
                $days_since_payment = max(0, floor(($current_timestamp - $payment_timestamp) / 86400));
            }
            
            $effective_days = min($duration, max(1, $days_since_payment));
            $daily_cost = $duration > 0 ? ($amount / $duration) : $amount;
            $accumulated_cost = $daily_cost * $effective_days;
            
            return [
                'total_amount_paid' => $amount,
                'duration_days' => $duration,
                'days_since_payment' => $days_since_payment,
                'effective_days' => $effective_days,
                'daily_cost' => $daily_cost,
                'accumulated_cost' => $accumulated_cost,
                'total_clicks' => $position_clicks,
                'cpc_based_on_total' => $position_clicks > 0 ? ($amount / $position_clicks) : $amount,
                'cpc_based_on_accumulated' => $position_clicks > 0 ? ($accumulated_cost / $position_clicks) : $accumulated_cost,
                'payment_date' => $payment_date
            ];
        }
        
        return null;
    }
}

if (!function_exists('get_all_user_payment_data')) {
    function get_all_user_payment_data($database, $user_id) {
        $payments_table = $database->get_table('ad_payments');
        
        if (!$database->get_wpdb()->get_var($database->get_wpdb()->prepare("SHOW TABLES LIKE %s", $payments_table))) {
            return [];
        }
        
        $payments = $database->get_results(
            "SELECT ad_position, amount, duration, payment_date, payment_method, status, transaction_id
             FROM {$payments_table} 
             WHERE user_id = %d AND status = 'completed' 
             ORDER BY ad_position, payment_date DESC",
            [$user_id]
        );
        
        $payment_data = [];
        foreach ($payments as $payment) {
            $position = $payment->ad_position;
            if (!isset($payment_data[$position]) || strtotime($payment->payment_date) > strtotime($payment_data[$position]['payment_date'])) {
                $payment_data[$position] = [
                    'amount' => (float)$payment->amount,
                    'duration' => (int)$payment->duration,
                    'payment_date' => $payment->payment_date,
                    'payment_method' => $payment->payment_method,
                    'status' => $payment->status,
                    'transaction_id' => $payment->transaction_id
                ];
            }
        }
        
        return $payment_data;
    }
}

if (!function_exists('calculate_user_total_investment')) {
    function calculate_user_total_investment($database, $user_id, $active_only = true) {
        $payments_table = $database->get_table('ad_payments');
        
        if (!$database->get_wpdb()->get_var($database->get_wpdb()->prepare("SHOW TABLES LIKE %s", $payments_table))) {
            return 0;
        }
        
        $where_clause = "WHERE user_id = %d AND status = 'completed'";
        $params = [$user_id];
        
        if ($active_only) {
            $where_clause .= " AND DATE_ADD(payment_date, INTERVAL duration DAY) > NOW()";
        }
        
        $total = $database->get_var(
            "SELECT SUM(amount) FROM {$payments_table} {$where_clause}",
            $params
        );
        
        return $total ? (float)$total : 0;
    }
}

if (!function_exists('get_position_roi_data')) {
    function get_position_roi_data($database, $user_id, $position) {
        $payment_data = get_position_payment_data($database, $user_id, $position);
        
        if (!$payment_data) {
            return null;
        }
        
        $position_clicks = 0;
        $position_30day_clicks = 0;
        
        if (function_exists('get_position_total_clicks')) {
            $position_clicks = get_position_total_clicks($user_id, $position);
        }
        
        if (function_exists('get_position_last_30_days_clicks')) {
            $position_30day_clicks = get_position_last_30_days_clicks($user_id, $position);
        }
        
        $amount = $payment_data['amount'];
        $duration = $payment_data['duration'];
        $payment_date = $payment_data['payment_date'];
        
        $days_since_payment = 0;
        $is_active = false;
        
        if ($payment_date) {
            $payment_timestamp = strtotime($payment_date);
            $current_timestamp = time();
            $expiry_timestamp = $payment_timestamp + ($duration * 86400);
            $days_since_payment = max(0, floor(($current_timestamp - $payment_timestamp) / 86400));
            $is_active = $current_timestamp < $expiry_timestamp;
        }
        
        $daily_cost = $duration > 0 ? ($amount / $duration) : $amount;
        $cost_per_click = $position_clicks > 0 ? ($amount / $position_clicks) : $amount;
        
        $estimated_monthly_clicks = 0;
        if ($days_since_payment > 0 && $position_clicks > 0) {
            $clicks_per_day = $position_clicks / $days_since_payment;
            $estimated_monthly_clicks = $clicks_per_day * 30;
        }
        
        return [
            'position' => $position,
            'total_investment' => $amount,
            'duration_days' => $duration,
            'daily_cost' => $daily_cost,
            'total_clicks' => $position_clicks,
            'clicks_last_30_days' => $position_30day_clicks,
            'cost_per_click' => $cost_per_click,
            'days_since_payment' => $days_since_payment,
            'is_active' => $is_active,
            'estimated_monthly_clicks' => $estimated_monthly_clicks,
            'estimated_monthly_cpc' => $estimated_monthly_clicks > 0 ? ($daily_cost * 30) / $estimated_monthly_clicks : $daily_cost * 30,
            'payment_date' => $payment_date,
            'expires_date' => $payment_date ? date('Y-m-d H:i:s', strtotime($payment_date) + ($duration * 86400)) : null
        ];
    }
}

if (!function_exists('validate_position_payment_security')) {
    function validate_position_payment_security($database, $user_id, $position) {
        if (!is_numeric($user_id) || $user_id <= 0) {
            return false;
        }
        
        if (!is_string($position) || empty(trim($position))) {
            return false;
        }
        
        $sanitized_position = sanitize_text_field($position);
        if ($sanitized_position !== $position) {
            return false;
        }
        
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $position)) {
            return false;
        }
        
        return true;
    }
}

function get_daily_position_clicks_for_chart($user_id, $position_id) {
    if (function_exists('get_position_last_30_days_clicks')) {
        $stats = get_position_last_30_days_clicks($user_id, $position_id);
        $daily_stats = $stats['daily_stats'];

        $labels = [];
        $data = [];
        
        for ($i = 29; $i >= 0; $i--) {
            $date = (new DateTime())->sub(new DateInterval("P{$i}D"));
            $date_key = $date->format('Y-m-d');
            $labels[] = $date->format('M d');
            $data[] = isset($daily_stats[$date_key]) ? $daily_stats[$date_key] : 0;
        }

        return [
            'labels' => $labels,
            'data' => $data,
            'total_clicks' => $stats['total_clicks']
        ];
    }
    return null;
}