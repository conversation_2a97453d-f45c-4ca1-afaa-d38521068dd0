<?php
if (!defined('WPINC')) {
    die;
}

require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';
require_once AMP_PLUGIN_DIR . 'includes/core/class-utilities.php';

function initialize_price_calculation_settings() {
    $database = \AdManagementPro\Core\Database::instance();
    $table_name = $database->get_table('ad_price_calculation');
    if (!$database->get_wpdb()->get_var("SHOW TABLES LIKE '{$table_name}'")) {
        \AdManagementPro\Core\Database::create_tables();
    }
    $column_exists = $database->get_results("SHOW COLUMNS FROM `{$table_name}` LIKE 'usdt_price'");
    if (empty($column_exists)) {
        $database->get_wpdb()->query("ALTER TABLE `{$table_name}` ADD COLUMN usdt_price decimal(10,2) DEFAULT 1000.00 AFTER ad_position");
    }
    $column_exists = $database->get_results("SHOW COLUMNS FROM `{$table_name}` LIKE 'thb_price'");
    if (empty($column_exists)) {
        $database->get_wpdb()->query("ALTER TABLE `{$table_name}` ADD COLUMN thb_price decimal(10,2) DEFAULT 35000.00 AFTER usdt_price");
    }
    $column_exists = $database->get_results("SHOW COLUMNS FROM `{$table_name}` LIKE 'multiplier'");
    if (empty($column_exists)) {
        $database->get_wpdb()->query("ALTER TABLE `{$table_name}` ADD COLUMN multiplier decimal(10,2) DEFAULT 1.00 AFTER thb_price");
    }
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $positions = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
    foreach ($positions as $position) {
        $exists = $database->get_var("SELECT COUNT(*) FROM {$table_name} WHERE ad_position = %s", [$position->name]);
        if (!$exists) {
            $database->insert('ad_price_calculation', ['ad_position' => $position->name, 'usdt_price' => 1000.00, 'thb_price' => 35000.00, 'multiplier' => 1.00]);
        }
    }
    $database->get_wpdb()->query("UPDATE {$table_name} SET multiplier = 1.00 WHERE multiplier IS NULL OR multiplier = 0");
}

function display_price_settings_content() {
    $database = \AdManagementPro\Core\Database::instance();
    $notice = '';

    initialize_price_calculation_settings();
    
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $active_positions_data = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);
    $active_positions = wp_list_pluck($active_positions_data, 'name');
    $price_table = $database->get_table('ad_price_calculation');

    if (!empty($active_positions)) {
        $placeholders = implode(',', array_fill(0, count($active_positions), '%s'));
        $database->get_wpdb()->query( $database->get_wpdb()->prepare("DELETE FROM {$price_table} WHERE ad_position NOT IN ($placeholders)", $active_positions) );
    } else {
        $database->get_wpdb()->query("TRUNCATE TABLE {$price_table}");
    }

    $global_settings_rows = $database->get_results("SELECT setting_name, setting_value FROM {$database->get_table('ad_price_global_settings')}");
    $global_settings = wp_list_pluck($global_settings_rows, 'setting_value', 'setting_name');

    $divisor = $global_settings['visitors_divisor'] ?? 30;
    $monthly_visitors = $global_settings['monthly_visitors'] ?? 300000;
    $thb_rate = $global_settings['thb_rate'] ?? 35.5;

    $use_ga = get_option('use_ga_for_pricing', 'no');
    if ($use_ga === 'yes') {
        $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
        if (file_exists($ga_file)) {
            require_once($ga_file);
            if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                $ga_monthly_visitors = amp_get_monthly_users(31);
                if ($ga_monthly_visitors > 0) {
                    $monthly_visitors = $ga_monthly_visitors;
                }
            }
        }
    }
    $reservation_timeout = $global_settings['reservation_timeout'] ?? 3;
    $use_reservation_timer = $global_settings['use_reservation_timer'] ?? 1;
    $trial_multiplier = $global_settings['trial_multiplier'] ?? 1.8;
    $minimum_price = $global_settings['minimum_price'] ?? 6;

    $settings = $database->get_results("SELECT * FROM {$price_table}");
    if (is_array($settings)) {
        usort($settings, function($a, $b) {
            return strnatcmp($a->ad_position, $b->ad_position);
        });
    }
    $global_nonce = wp_create_nonce('save_global_settings');
    $position_nonce = wp_create_nonce('save_price_calculation_settings');

    echo $notice;

    $exchange_rate_last_updated = $global_settings['exchange_rate_last_updated'] ?? time();
    $exchange_rate_log = get_option('exchange_rate_log', array());
    ?>
    <div class="plisio-settings-grid">
        <div class="plisio-card auto-refresh-info">
            <h3>🔄 ระบบการอัพเดทราคาอัตโนมัติ</h3>
            <div class="auto-refresh-content">
                <div class="refresh-status">
                    <span class="status-icon">✅</span>
                    <span class="status-text">ระบบอัพเดทราคาอัตโนมัติ: <strong>เปิดใช้งาน</strong></span>
                </div>
                <div class="refresh-details">
                    <ul>
                        <li>📈 อัตราแลกเปลี่ยน: อัพเดทอัตโนมัติทุกครั้งที่เข้าหน้านี้</li>
                        <li>💰 ราคา Position: อัพเดททันทีเมื่อเปลี่ยนการตั้งค่า</li>
                        <li>🔄 Cache: ล้างอัตโนมัติเมื่อบันทึกการตั้งค่า</li>
                        <li>🌐 Public & Admin: อัพเดทพร้อมกันทุกส่วน</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="plisio-card">
            <h3>ข้อมูลอัตราแลกเปลี่ยน THB/USDT</h3>
            <div class="rate-info-container">
                <div class="rate-display">
                    <span class="rate-label">อัตราปัจจุบัน:</span>
                    <span class="rate-value"><?php echo number_format($thb_rate, 2); ?> THB/USDT</span>
                </div>
                <div class="rate-timestamp">
                    <?php if (!empty($exchange_rate_log)): ?>
                        <?php
                        $last_update = $exchange_rate_log[0];
                        $update_method = isset($last_update['method']) ? $last_update['method'] : 'manual';
                        $method_text = ($update_method === 'auto') ? 'อัตโนมัติ' : 'แมนวล';
                        ?>
                        <span class="last-updated">อัพเดทล่าสุด: <?php echo date('d/m/Y H:i:s', strtotime($last_update['date'])); ?> (<?php echo $method_text; ?>)</span>
                    <?php else: ?>
                        <span class="last-updated">ยังไม่มีข้อมูลการอัพเดท</span>
                    <?php endif; ?>
                </div>
                <div class="api-test-section">
                    <button type="button" id="test-apis-btn" class="button button-secondary">
                        🔍 ทดสอบ API ทั้งหมด
                    </button>
                    <div id="api-test-results" style="display: none; margin-top: 10px;">
                        <h4>ผลการทดสอบ API:</h4>
                        <div id="api-results-content"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="plisio-card">
            <h3>สูตรการคำนวณราคา</h3>
            <div class="formula-container">
                <div class="formula-box">
                    <p class="formula-title">สูตร:</p>
                    <p class="formula-content"><code>max(<?php echo number_format($monthly_visitors); ?> ÷ <?php echo $divisor; ?> ÷ <?php echo number_format($thb_rate, 1); ?> × ตัวคูณ + 2, <?php echo $minimum_price; ?>) = ราคา USDT</code></p>
                </div>
                <div class="formula-box">
                    <p class="formula-title">ตัวอย่าง:</p>
                    <?php
                    $example_price = intval(($monthly_visitors / $divisor / $thb_rate) * 0.9 + 2);
                    $final_example_price = max($example_price, $minimum_price);
                    ?>
                    <p class="formula-example">max(<?php echo number_format($monthly_visitors); ?> ÷ <?php echo $divisor; ?> ÷ <?php echo number_format($thb_rate, 1); ?> × 0.9 + 2, <?php echo $minimum_price; ?>) = max(<?php echo $example_price; ?>, <?php echo $minimum_price; ?>) = <?php echo $final_example_price; ?> USDT</p>
                </div>
                <div class="formula-box">
                    <p class="formula-title">หมายเหตุ:</p>
                    <p class="formula-note">ระบบจะใช้ราคาขั้นต่ำ <?php echo $minimum_price; ?> USDT หากราคาที่คำนวณได้ต่ำกว่าค่าที่กำหนด</p>
                </div>
            </div>
        </div>
    </div>
    <form method="post" action="" id="global-settings-form">
        <input type="hidden" name="global_settings_nonce" value="<?php echo esc_attr($global_nonce); ?>">
        <div class="plisio-card">
            <h3>การตั้งค่าทั่วไป</h3>
            <table class="form-table">
            <tr>
                <th scope="row"><label for="visitors_divisor">ตัวหาร (จำนวนวัน)</label></th>
                <td>
                    <input type="number" name="visitors_divisor" id="visitors_divisor" value="<?php echo esc_attr($divisor); ?>" min="1" class="small-text">
                    <p class="description">ใช้สำหรับหารจำนวนผู้เข้าชมเพื่อคำนวณราคาพื้นฐาน</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="monthly_visitors">จำนวนผู้เข้าชมรายเดือน</label></th>
                <td>
                    <div class="visitors-input-group">
                        <input type="number" name="monthly_visitors" id="monthly_visitors" value="<?php echo esc_attr($monthly_visitors); ?>" min="1" class="regular-text <?php echo get_option('use_ga_for_pricing', 'yes') === 'yes' ? 'readonly-ga-field' : ''; ?>">
                        <?php if (get_option('use_ga_for_pricing', 'yes') === 'yes'): ?>
                            <span class="ga-synced-badge">GA Synced</span>
                        <?php endif; ?>
                        <label class="use-ga-data">
                            <input type="checkbox" name="use_ga_data" id="use_ga_data" <?php checked(get_option('use_ga_for_pricing', 'yes'), 'yes'); ?>>
                            ใช้ข้อมูลจาก Google Analytics
                        </label>
                    </div>
                    <p class="description">จำนวนผู้เข้าชมเฉลี่ยต่อเดือนที่ใช้ในการคำนวณราคา</p>
                    <?php if (get_option('use_ga_for_pricing', 'yes') === 'yes'): ?>
                        <p class="ga-notice">หากต้องการดึงข้อมูลจาก Google Analytics โปรดตั้งค่าในแถบ Google</p>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="thb_rate">อัตราแลกเปลี่ยน THB/USDT</label></th>
                <td>
                    <div class="exchange-rate-field">
                        <input type="number" name="thb_rate" id="thb_rate" value="<?php echo esc_attr($thb_rate); ?>" step="0.01" min="0.01" class="regular-text">
                    </div>
                    <p class="description">อัตราแลกเปลี่ยนเงินบาทต่อ 1 USDT (อัพเดทอัตโนมัติทุกครั้งที่เข้าหน้านี้)</p>
                    <p class="exchange-rate-last-update">
                        <?php
                        $exchange_rate_log = get_option('exchange_rate_log', array());
                        if (!empty($exchange_rate_log)) {
                            $last_update = $exchange_rate_log[0];
                            $update_method = isset($last_update['method']) ? $last_update['method'] : 'manual';
                            $method_text = ($update_method === 'auto') ? 'อัตโนมัติ' : 'แมนวล';
                            echo 'อัพเดทล่าสุด: ' . date('d/m/Y H:i', strtotime($last_update['date'])) . ' น. (' . $method_text . ')';
                        }
                        ?>
                    </p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="trial_multiplier">ตัวคูณสำหรับโปรโมชั่น 7 วัน</label></th>
                <td>
                    <input type="number" name="trial_multiplier" id="trial_multiplier" value="<?php echo esc_attr($trial_multiplier); ?>" step="0.1" min="0.1" class="small-text">
                    <p class="description">ตัวคูณสำหรับคำนวณราคาโปรโมชั่น 7 วัน (ค่าเริ่มต้น: 1.8)</p>
                    <p class="formula-example">สูตร: ราคาปกติ/30 × <?php echo esc_html($trial_multiplier); ?> × 7 วัน</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="minimum_price">ราคาขั้นต่ำ (USDT)</label></th>
                <td>
                    <input type="number" name="minimum_price" id="minimum_price" value="<?php echo esc_attr($minimum_price); ?>" step="1" min="1" class="small-text">
                    <p class="description">ราคาขั้นต่ำสำหรับทุกตำแหน่งโฆษณา ไม่มีราคาต่ำกว่านี้ (ค่าเริ่มต้น: 6 USDT)</p>
                    <p class="formula-example">ระบบจะใช้ราคานี้หากราคาที่คำนวณได้ต่ำกว่าค่าที่กำหนด</p>
                </td>
            </tr>
            <tr>
                <th scope="row"><label for="use_reservation_timer">ใช้งานระบบนับเวลาถอยหลัง</label></th>
                <td>
                    <label class="switch">
                        <input type="checkbox" name="use_reservation_timer" id="use_reservation_timer" value="1" <?php checked($use_reservation_timer, 1); ?>>
                        <span class="slider round"></span>
                    </label>
                    <p class="description">เปิด/ปิดการใช้งานระบบนับเวลาถอยหลังสำหรับการจอง</p>
                    <p class="description"><strong>หมายเหตุ:</strong> หากปิดการใช้งาน ระบบจะไม่แสดงเวลานับถอยหลังและจะยกเลิกการจองเมื่อปิดหน้าชำระเงิน</p>
                </td>
            </tr>
            <tr id="reservation_timeout_row" style="<?php echo $use_reservation_timer ? '' : 'display: none;'; ?>">
                <th scope="row"><label for="reservation_timeout">ระยะเวลาการจอง (นาที)</label></th>
                <td>
                    <input type="number" name="reservation_timeout" id="reservation_timeout" value="<?php echo esc_attr($reservation_timeout); ?>" min="1" max="60" class="small-text">
                    <p class="description">ระยะเวลาในการจองตำแหน่งโฆษณาก่อนที่จะหมดอายุ (1-60 นาที)</p>
                    <p class="description"><strong>หมายเหตุ:</strong> ค่านี้จะถูกใช้เป็นเวลาในการนับถอยหลังสำหรับการชำระเงิน</p>
                </td>
            </tr>
        </table>
            <p class="submit">
                <button type="submit" name="submit_global_settings" id="save-global-settings" class="ad-btn ad-btn-primary">บันทึกการตั้งค่าทั่วไป</button>
                <span id="global-settings-status"></span>
            </p>
        </div>
    </form>
    <form method="post" action="" id="price-calculation-form">
        <input type="hidden" name="price_calculation_nonce" value="<?php echo esc_attr($position_nonce); ?>">
        <div class="plisio-card">
            <h3>ตัวคูณสำหรับแต่ละตำแหน่ง</h3>
            <div class="price-actions">
                <button type="button" id="update-all-prices" class="ad-btn ad-btn-secondary">อัพเดทราคาทั้งหมด</button>
                <button type="button" id="reset-price-calculation" class="ad-btn ad-btn-warning">รีเซ็ตการคำนวณ</button>
            </div>
            <table class="wp-list-table widefat fixed striped price-table">
            <thead>
                <tr>
                    <th width="30%">ตำแหน่งป้ายโฆษณา</th>
                    <th width="20%">ตัวคูณ</th>
                    <th width="50%">ราคาประมาณการ (<?php echo number_format($monthly_visitors); ?> ผู้เข้าชม)</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($settings)) : ?>
                    <tr>
                        <td colspan="3">ไม่พบข้อมูลตำแหน่งป้ายโฆษณา</td>
                    </tr>
                <?php else : ?>
                    <?php foreach ($settings as $setting): ?>
                        <tr>
                            <td>
                                <?php echo esc_html($setting->ad_position); ?>
                                <input type="hidden" name="position[]" value="<?php echo esc_attr($setting->ad_position); ?>">
                            </td>
                            <td>
                                <input type="number" name="multiplier[]" class="multiplier-input" value="<?php echo esc_attr($setting->multiplier ?? 1.00); ?>" min="0.01" step="0.01" required>
                            </td>
                            <td>
                                <?php
                                $use_ga = get_option('use_ga_for_pricing', 'no');
                                $price_usdt = 0;

                                if ($use_ga === 'yes') {
                                    $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
                                    if (file_exists($ga_file)) {
                                        require_once($ga_file);
                                        if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                                            $ga_monthly_visitors = amp_get_monthly_users(31);
                                            if ($ga_monthly_visitors > 0) {
                                                $multiplier = $setting->multiplier ?? 1.00;
                                                $price_usdt = ($ga_monthly_visitors / $divisor / $thb_rate) * $multiplier + 2;
                                                $price_usdt = intval($price_usdt);
                                            }
                                        }
                                    }
                                }

                                if ($price_usdt <= 0) {
                                    $price_usdt = $setting->usdt_price ?? 217;
                                }

                                $price_thb = $price_usdt * $thb_rate;
                                ?>
                                <div class="currency-display-horizontal">
                                    <span class="currency-label">USDT:</span>
                                    <span class="currency-value usdt-value"><?php echo number_format($price_usdt, 0); ?></span>
                                    <span class="currency-separator">|</span>
                                    <span class="currency-label">THB:</span>
                                    <span class="currency-value thb-value"><?php echo number_format($price_thb, 0); ?></span>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
            </table>
            <p class="submit">
                <button type="submit" name="submit_price_calculation_settings" id="save-price-calculation-settings" class="ad-btn ad-btn-primary">บันทึกตัวคูณ</button>
                <span id="price-calculation-status"></span>
            </p>
        </div>
    </form>
    <script>
    jQuery(document).ready(function($) {
        function showStatusMessage(type, message) {
            var statusEl = $('<div class="amp-status-message amp-status-' + type + '">' + message + '</div>');
            $('body').append(statusEl);
            statusEl.fadeIn(300);
            setTimeout(function() {
                statusEl.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        }

        function highlightElement($element) {
            $element.addClass('highlight-update');
            setTimeout(function() {
                $element.removeClass('highlight-update');
            }, 1500);
        }

        function autoUpdatePrices() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'update_prices',
                    nonce: '<?php echo wp_create_nonce('update_prices_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success && response.data.prices) {
                        updatePriceDisplay(response.data.prices);
                    }
                }
            });
        }
        
        function autoUpdateExchangeRate() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'refresh_thb_usdt_rate',
                    nonce: '<?php echo wp_create_nonce('refresh_exchange_rate'); ?>',
                    silent: true
                },
                success: function(response) {
                    if (response.success) {
                        $('#thb_rate').val(response.data.rate);
                        $('.rate-value').text(response.data.rate + ' THB/USDT');
                        $('.exchange-rate-last-update').html('อัพเดทล่าสุด: ' + response.data.timestamp + ' น. (อัตโนมัติ)');
                        $('.last-updated').html('อัพเดทล่าสุด: ' + response.data.timestamp + ' (อัตโนมัติ)');
                        highlightElement($('.rate-value').closest('.rate-info-container'));
                        showStatusMessage('success', 'อัพเดทอัตราแลกเปลี่ยนเป็น ' + response.data.rate + ' THB/USDT เรียบร้อยแล้ว');
                    }
                    autoUpdatePrices();
                },
                error: function(xhr, textStatus, errorThrown) {
                    autoUpdatePrices();
                }
            });
        }
        
        function updatePriceDisplay(prices) {
            $('.price-table tbody tr').each(function() {
                var position = $(this).find('input[name="position[]"]').val();
                if (prices[position]) {
                    $(this).find('.usdt-value').text(prices[position].usdt);
                    $(this).find('.thb-value').text(prices[position].thb);
                }
            });
        }
        
        function refreshCurrentPrices() {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'get_current_prices'
                },
                success: function(response) {
                    if (response.success) {
                        updatePriceDisplay(response.data.prices);
                        $('.price-table thead th:last-child').html('ราคาประมาณการ (' + response.data.monthly_visitors + ' ผู้เข้าชม)');
                    }
                }
            });
        }
        
        autoUpdateExchangeRate();

        $('#test-apis-btn').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();
            var $results = $('#api-test-results');
            var $content = $('#api-results-content');

            $btn.prop('disabled', true).html('🔄 กำลังทดสอบ...');
            $results.show();
            $content.html('<div class="testing-message">กำลังทดสอบ API ทั้งหมด กรุณารอสักครู่...</div>');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'test_exchange_apis'
                },
                success: function(response) {
                    $btn.prop('disabled', false).html(originalText);

                    if (response.success) {
                        var html = '<div class="api-test-timestamp">ทดสอบเมื่อ: ' + response.data.timestamp + '</div>';
                        html += '<table class="api-results-table">';
                        html += '<thead><tr><th>API</th><th>สถานะ</th><th>อัตราแลกเปลี่ยน</th><th>เวลาตอบสนอง</th></tr></thead>';
                        html += '<tbody>';

                        response.data.results.forEach(function(result) {
                            var statusClass = result.status === 'success' ? 'success' : 'failed';
                            var statusIcon = result.status === 'success' ? '✅' : '❌';
                            var rateDisplay = result.rate ? result.rate.toFixed(2) + ' THB' : 'N/A';

                            html += '<tr class="' + statusClass + '">';
                            html += '<td>' + result.name + '</td>';
                            html += '<td>' + statusIcon + ' ' + result.status + '</td>';
                            html += '<td>' + rateDisplay + '</td>';
                            html += '<td>' + result.response_time + '</td>';
                            html += '</tr>';
                        });

                        html += '</tbody></table>';
                        $content.html(html);
                    } else {
                        $content.html('<div class="error-message">เกิดข้อผิดพลาดในการทดสอบ API</div>');
                    }
                },
                error: function() {
                    $btn.prop('disabled', false).html(originalText);
                    $content.html('<div class="error-message">เกิดข้อผิดพลาดในการเชื่อมต่อ</div>');
                }
            });
        });
        
        $(document).on('input', '.multiplier-input', function() {
            var $row = $(this).closest('tr');
            var multiplier = parseFloat($(this).val()) || 0;
            var position = $row.find('input[name="position[]"]').val();

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'calculate_single_price',
                    position: position,
                    multiplier: multiplier
                },
                success: function(calcResponse) {
                    if (calcResponse.success) {
                        $row.find('.usdt-value').text(calcResponse.data.usdt);
                        $row.find('.thb-value').text(calcResponse.data.thb);
                    }
                }
            });
        });
        
        $('#use_reservation_timer').on('change', function() {
            if ($(this).is(':checked')) {
                $('#reservation_timeout_row').show();
            } else {
                $('#reservation_timeout_row').hide();
            }
        });
        $('#use_ga_data').on('change', function() {
            var useGA = $(this).is(':checked');
            var monthlyVisitorsField = $('#monthly_visitors');
            if (useGA) {
                monthlyVisitorsField.addClass('readonly-ga-field').prop('readonly', true);
                $(this).parent().after('<span class="ga-synced-badge">GA Synced</span>');
                $('.ga-notice').show();
            } else {
                monthlyVisitorsField.removeClass('readonly-ga-field').prop('readonly', false);
                $('.ga-synced-badge').remove();
                $('.ga-notice').hide();
            }
        });
        $('#global-settings-form').on('submit', function(e) {
            e.preventDefault();
            const button = $('#save-global-settings');
            const statusEl = $('#global-settings-status');
            button.prop('disabled', true).html('กำลังบันทึก...');
            statusEl.removeClass('success error').hide();
            const formData = {
                action: 'save_price_global_settings',
                nonce: '<?php echo $global_nonce; ?>',
                visitors_divisor: $('#visitors_divisor').val(),
                monthly_visitors: $('#monthly_visitors').val(),
                thb_rate: $('#thb_rate').val(),
                trial_multiplier: $('#trial_multiplier').val(),
                minimum_price: $('#minimum_price').val(),
                use_reservation_timer: $('#use_reservation_timer').is(':checked') ? 1 : 0,
                reservation_timeout: $('#reservation_timeout').val()
            };
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('บันทึกการตั้งค่าทั่วไป');
                    if (response.success) {
                        var successMessage = response.data.message || 'บันทึกสำเร็จ';
                        statusEl.addClass('success').html(successMessage).show();
                        refreshCurrentPrices();
                        setTimeout(function() { statusEl.fadeOut(); }, 3000);
                    } else {
                        var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                        statusEl.addClass('error').html(errorMessage).show();
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('บันทึกการตั้งค่าทั่วไป');
                    statusEl.addClass('error').html('เกิดข้อผิดพลาดในการบันทึก').show();
                }
            });
        });
        $('#price-calculation-form').on('submit', function(e) {
            e.preventDefault();
            const button = $('#save-price-calculation-settings');
            const statusEl = $('#price-calculation-status');
            button.prop('disabled', true).html('กำลังบันทึก...');
            statusEl.removeClass('success error').hide();
            const formData = {
                action: 'save_price_calculation_settings',
                nonce: '<?php echo $position_nonce; ?>',
                position: [],
                multiplier: []
            };
            $('input[name="position[]"]').each(function() {
                formData.position.push($(this).val());
            });
            $('input[name="multiplier[]"]').each(function() {
                formData.multiplier.push($(this).val());
            });
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    button.prop('disabled', false).html('บันทึกตัวคูณ');
                    if (response.success) {
                        var successMessage = response.data.message || 'บันทึกสำเร็จ';
                        statusEl.addClass('success').html(successMessage).show();
                        refreshCurrentPrices();
                        setTimeout(function() { statusEl.fadeOut(); }, 3000);
                    } else {
                        var errorMessage = response.data.message || 'เกิดข้อผิดพลาด';
                        statusEl.addClass('error').html(errorMessage).show();
                    }
                },
                error: function() {
                    button.prop('disabled', false).html('บันทึกตัวคูณ');
                    statusEl.addClass('error').html('เกิดข้อผิดพลาดในการบันทึก').show();
                }
            });
        });
        $('#update-all-prices').on('click', function() {
            var $btn = $(this);
            var originalText = $btn.html();
            $btn.prop('disabled', true).html('กำลังอัพเดท...');
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'update_prices',
                    nonce: '<?php echo wp_create_nonce('update_prices_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        if (response.data.prices) {
                            updatePriceDisplay(response.data.prices);
                        }
                        refreshCurrentPrices();
                        showStatusMessage('success', 'อัพเดทราคาสำเร็จ');
                    } else {
                        showStatusMessage('error', 'เกิดข้อผิดพลาด: ' + (response.data.message || 'ไม่สามารถอัพเดทได้'));
                    }
                },
                error: function() {
                    showStatusMessage('error', 'เกิดข้อผิดพลาดในการเชื่อมต่อ');
                },
                complete: function() {
                    $btn.prop('disabled', false).html(originalText);
                }
            });
        });
        $('#reset-price-calculation').on('click', function() {
            if (confirm('คุณต้องการรีเซ็ตการคำนวณราคาใช่หรือไม่?')) {
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.prop('disabled', true).html('กำลังรีเซ็ต...');
                
                refreshCurrentPrices();
                showStatusMessage('success', 'รีเซ็ตการคำนวณราคาเรียบร้อยแล้ว');
                
                setTimeout(function() {
                    $btn.prop('disabled', false).html(originalText);
                }, 1000);
            }
        });
        

    });
    </script>
    <style>
    .exchange-rate-field {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    .visitors-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
        flex-wrap: wrap;
    }
    .ga-synced-badge {
        background: #00a0d2;
        color: white;
        padding: 2px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    .readonly-ga-field {
        background-color: #f0f0f1 !important;
    }
    .use-ga-data {
        margin: 0;
    }
    .ga-notice {
        font-style: italic;
        color: #666;
    }
    .exchange-rate-last-update {
        font-size: 12px;
        color: #666;
        font-style: italic;
    }
    .api-test-section {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #ddd;
    }
    .api-results-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    .api-results-table th,
    .api-results-table td {
        padding: 8px 12px;
        text-align: left;
        border: 1px solid #ddd;
    }
    .api-results-table th {
        background-color: #f8f9fa;
        font-weight: 600;
    }
    .api-results-table tr.success {
        background-color: #f0f9ff;
    }
    .api-results-table tr.failed {
        background-color: #fef2f2;
    }
    .api-test-timestamp {
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
    }
    .testing-message {
        padding: 10px;
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        color: #856404;
    }
    .error-message {
        padding: 10px;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 4px;
        color: #721c24;
    }
    .formula-example {
        font-family: monospace;
        background: #f8f9fa;
        padding: 4px 8px;
        border-radius: 3px;
        border-left: 3px solid #007cba;
        margin-top: 5px;
    }
    .formula-note {
        font-size: 13px;
        color: #666;
        background: #fff3cd;
        padding: 8px 12px;
        border-radius: 4px;
        border-left: 3px solid #ffc107;
        margin-top: 5px;
    }
    .price-actions {
        margin-bottom: 15px;
        display: flex;
        gap: 10px;
    }
    .price-table .currency-display-horizontal {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
    }
    .currency-separator {
        color: #999;
    }
    .currency-value {
        font-weight: bold;
    }
    .usdt-value {
        color: #2e8b57;
    }
    .thb-value {
        color: #dc3545;
    }
    .multiplier-input {
        width: 80px;
    }
    .switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
        margin-right: 10px;
    }
    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }
    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    input:checked + .slider {
        background-color: #2196F3;
    }
    input:checked + .slider:before {
        transform: translateX(26px);
    }
    .ad-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        font-size: 14px;
        transition: background-color 0.3s;
    }
    .ad-btn-primary {
        background-color: #007cba;
        color: white;
    }
    .ad-btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    .ad-btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    .ad-btn:hover {
        opacity: 0.9;
    }
    .amp-status-message {
        position: fixed;
        top: 50px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: bold;
        z-index: 99999;
        display: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        animation: slideInRight 0.3s ease;
    }
    .amp-status-success {
        background: linear-gradient(135deg, #27ae60, #229954);
    }
    .amp-status-error {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
    }
    .auto-refresh-info {
        background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
        border-left: 4px solid #28a745;
    }
    .auto-refresh-content {
        padding: 10px 0;
    }
    .refresh-status {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 16px;
    }
    .status-icon {
        margin-right: 8px;
        font-size: 18px;
    }
    .refresh-details ul {
        margin: 0;
        padding-left: 20px;
    }
    .refresh-details li {
        margin-bottom: 5px;
        color: #495057;
    }
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    #global-settings-status,
    #price-calculation-status {
        margin-left: 10px;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        display: none;
    }
    #global-settings-status.success,
    #price-calculation-status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    #global-settings-status.error,
    #price-calculation-status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .highlight-update {
        transition: background-color 0.5s ease-in-out;
        background-color: #d4edda !important;
    }
    </style>
    <?php
}

function handle_save_price_global_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_global_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    $database = \AdManagementPro\Core\Database::instance();
    
    $settings_to_update = [
        'visitors_divisor' => intval($_POST['visitors_divisor'] ?? 30),
        'monthly_visitors' => intval($_POST['monthly_visitors'] ?? 300000),
        'thb_rate' => floatval($_POST['thb_rate'] ?? 35.5),
        'reservation_timeout' => intval($_POST['reservation_timeout'] ?? 3),
        'use_reservation_timer' => isset($_POST['use_reservation_timer']) ? 1 : 0,
        'trial_multiplier' => floatval($_POST['trial_multiplier'] ?? 1.5),
        'minimum_price' => intval($_POST['minimum_price'] ?? 6)
    ];

    foreach ($settings_to_update as $name => $value) {
        $database->update('ad_price_global_settings', ['setting_value' => $value], ['setting_name' => $name]);
    }

    update_all_ad_positions_usdt_prices();
    wp_send_json_success(['message' => 'บันทึกการตั้งค่าทั่วไปเรียบร้อยแล้ว']);
}

function handle_save_price_calculation_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'save_price_calculation_settings')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    $database = \AdManagementPro\Core\Database::instance();
    $positions = isset($_POST['position']) ? array_map('sanitize_text_field', $_POST['position']) : array();
    $multipliers = isset($_POST['multiplier']) ? $_POST['multiplier'] : array();

    for ($i = 0; $i < count($positions); $i++) {
        $position = $positions[$i];
        $multiplier = floatval($multipliers[$i]);
        if ($multiplier <= 0) $multiplier = 0.01;
        $database->update('ad_price_calculation', ['multiplier' => $multiplier], ['ad_position' => $position]);
    }
    update_all_ad_positions_usdt_prices();
    wp_send_json_success(['message' => 'บันทึกตัวคูณเรียบร้อยแล้ว']);
}

function update_all_ad_positions_usdt_prices() {
    $database = \AdManagementPro\Core\Database::instance();
    $global_settings_rows = $database->get_results("SELECT setting_name, setting_value FROM {$database->get_table('ad_price_global_settings')}");
    $global_settings = wp_list_pluck($global_settings_rows, 'setting_value', 'setting_name');

    $divisor = $global_settings['visitors_divisor'] ?? 30;
    $thb_rate = $global_settings['thb_rate'] ?? 35.5;
    $minimum_price = $global_settings['minimum_price'] ?? 6;

    $use_ga = get_option('use_ga_for_pricing', 'no');
    $ga_monthly_visitors = 0;

    if ($use_ga === 'yes') {
        $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
        if (file_exists($ga_file)) {
            require_once($ga_file);
            if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                $ga_monthly_visitors = amp_get_monthly_users(31);
            }
        }
    }

    $positions = $database->get_results("SELECT * FROM {$database->get_table('ad_price_calculation')}");
    foreach ($positions as $position) {
        $multiplier = $position->multiplier ?? 1.00;

        if ($ga_monthly_visitors > 0) {
            $new_usdt_price = ($ga_monthly_visitors / $divisor / $thb_rate) * $multiplier + 2;
            $new_usdt_price = max(intval($new_usdt_price), $minimum_price);
            $database->update('ad_price_calculation', ['usdt_price' => $new_usdt_price], ['ad_position' => $position->ad_position]);
        }
    }

    AMP_Price_Calculator::clear_cache();
}

function calculate_single_price_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    $position = isset($_POST['position']) ? sanitize_text_field($_POST['position']) : '';
    $multiplier = isset($_POST['multiplier']) ? floatval($_POST['multiplier']) : 0;
    
    if (empty($position) || $multiplier <= 0) {
        wp_send_json_error(['message' => 'Invalid parameters']);
        return;
    }
    
    $database = \AdManagementPro\Core\Database::instance();
    $divisor_row = $database->get_row("SELECT setting_value FROM {$database->get_table('ad_price_global_settings')} WHERE setting_name = %s", ['visitors_divisor']);
    $thb_rate_row = $database->get_row("SELECT setting_value FROM {$database->get_table('ad_price_global_settings')} WHERE setting_name = %s", ['thb_rate']);
    $minimum_price_row = $database->get_row("SELECT setting_value FROM {$database->get_table('ad_price_global_settings')} WHERE setting_name = %s", ['minimum_price']);
    $divisor = $divisor_row ? intval($divisor_row->setting_value) : 30;
    $thb_rate = $thb_rate_row ? floatval($thb_rate_row->setting_value) : 35.5;
    $minimum_price = $minimum_price_row ? intval($minimum_price_row->setting_value) : 6;

    $use_ga = get_option('use_ga_for_pricing', 'no');
    $price_usdt = 0;

    if ($use_ga === 'yes') {
        $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
        if (file_exists($ga_file)) {
            require_once($ga_file);
            if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                $monthly_visitors = amp_get_monthly_users(31);
                if ($monthly_visitors > 0) {
                    $price_usdt = ($monthly_visitors / $divisor / $thb_rate) * $multiplier + 2;
                    $price_usdt = intval($price_usdt);
                }
            }
        }
    }

    if ($price_usdt <= 0) {
        $price_row = $database->get_row("SELECT usdt_price FROM {$database->get_table('ad_price_calculation')} WHERE ad_position = %s", [$position]);
        $base_price = $price_row ? (int)$price_row->usdt_price : 217;
        $price_usdt = $base_price;
    }

    $price_usdt = max($price_usdt, $minimum_price);

    $price_thb = $price_usdt * $thb_rate;
    
    wp_send_json_success([
        'usdt' => number_format($price_usdt, 0),
        'thb' => number_format($price_thb, 0)
    ]);
}

function update_prices_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'update_prices_nonce')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    update_all_ad_positions_usdt_prices();
    $database = \AdManagementPro\Core\Database::instance();
    $global_settings_rows = $database->get_results("SELECT setting_name, setting_value FROM {$database->get_table('ad_price_global_settings')}");
    $global_settings = wp_list_pluck($global_settings_rows, 'setting_value', 'setting_name');
    $thb_rate = $global_settings['thb_rate'] ?? 35.5;
    $monthly_visitors = $global_settings['monthly_visitors'] ?? 300000;

    $use_ga = get_option('use_ga_for_pricing', 'no');
    if ($use_ga === 'yes') {
        $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
        if (file_exists($ga_file)) {
            require_once($ga_file);
            if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                $ga_monthly_visitors = amp_get_monthly_users(31);
                if ($ga_monthly_visitors > 0) {
                    $monthly_visitors = $ga_monthly_visitors;
                }
            }
        }
    }
    $positions = $database->get_results("SELECT ad_position, usdt_price FROM {$database->get_table('ad_price_calculation')}");
    $prices = array();
    foreach ($positions as $position) {
        $prices[$position->ad_position] = array(
            'usdt' => number_format($position->usdt_price, 0),
            'thb' => number_format($position->usdt_price * $thb_rate, 0)
        );
    }
    wp_send_json_success([
        'prices' => $prices,
        'message' => 'อัพเดทราคาสำเร็จ'
    ]);
}

function get_current_prices_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    $database = \AdManagementPro\Core\Database::instance();
    $global_settings_rows = $database->get_results("SELECT setting_name, setting_value FROM `{$database->get_table('ad_price_global_settings')}`");
    $global_settings = wp_list_pluck($global_settings_rows, 'setting_value', 'setting_name');
    $thb_rate = $global_settings['thb_rate'] ?? 35.5;

    $use_ga = get_option('use_ga_for_pricing', 'no');
    $monthly_visitors = 0;

    if ($use_ga === 'yes') {
        $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
        if (file_exists($ga_file)) {
            require_once($ga_file);
            if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                $monthly_visitors = amp_get_monthly_users(31);
            }
        }
    }

    if ($monthly_visitors <= 0) {
        $monthly_visitors = $global_settings['monthly_visitors'] ?? 300000;
    }

    $positions = $database->get_results("SELECT ad_position, usdt_price FROM `{$database->get_table('ad_price_calculation')}`");
    $prices = array();
    foreach ($positions as $position) {
        $prices[$position->ad_position] = array(
            'usdt' => number_format($position->usdt_price, 0),
            'thb' => number_format($position->usdt_price * $thb_rate, 0)
        );
    }
    wp_send_json_success([
        'prices' => $prices,
        'monthly_visitors' => number_format($monthly_visitors)
    ]);
}

function refresh_thb_usdt_rate() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'refresh_exchange_rate')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    $utilities = AMP_Utilities::instance();
    $rate = $utilities->fetch_thb_usdt_rate();
    
    if ($rate && $rate > 0) {
        $database = \AdManagementPro\Core\Database::instance();
        $global_table_name = $database->get_table('ad_price_global_settings');
        
        $thb_rate_exists = $database->get_var("SELECT COUNT(*) FROM {$global_table_name} WHERE setting_name = %s", ['thb_rate']);
        if ($thb_rate_exists) {
            $database->update($global_table_name, ['setting_value' => $rate], ['setting_name' => 'thb_rate']);
        } else {
            $database->insert('ad_price_global_settings', ['setting_name' => 'thb_rate', 'setting_value' => $rate]);
        }
        
        $is_silent = isset($_POST['silent']) && $_POST['silent'];
        $method = $is_silent ? 'auto' : 'manual';
        
        $exchange_rate_log = get_option('exchange_rate_log', array());
        array_unshift($exchange_rate_log, array(
            'rate' => $rate,
            'date' => current_time('mysql'),
            'method' => $method
        ));
        $exchange_rate_log = array_slice($exchange_rate_log, 0, 10);
        update_option('exchange_rate_log', $exchange_rate_log);
        
        update_all_ad_positions_usdt_prices();

        // ดึงแหล่งที่มาของเรทแลกเปลี่ยน
        global $wpdb;
        $exchange_rate_source = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM {$wpdb->prefix}ad_price_global_settings WHERE setting_name = %s",
            'exchange_rate_source'
        ));
        if (!$exchange_rate_source) {
            $exchange_rate_source = 'CoinGecko';
        }

        wp_send_json_success([
            'rate' => number_format($rate, 2),
            'timestamp' => date('d/m/Y H:i'),
            'message' => 'อัพเดทอัตราแลกเปลี่ยนสำเร็จ',
            'source' => $exchange_rate_source
        ]);
    } else {
        wp_send_json_error(['message' => 'ไม่สามารถดึงข้อมูลอัตราแลกเปลี่ยนได้']);
    }
}



function test_exchange_apis() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    $utilities = AMP_Utilities::instance();
    $results = $utilities->test_all_exchange_apis();

    wp_send_json_success([
        'results' => $results,
        'timestamp' => current_time('Y-m-d H:i:s')
    ]);
}

add_action('wp_ajax_save_price_global_settings', 'handle_save_price_global_settings');
add_action('wp_ajax_save_price_calculation_settings', 'handle_save_price_calculation_settings');
add_action('wp_ajax_update_prices', 'update_prices_ajax');
add_action('wp_ajax_get_current_prices', 'get_current_prices_ajax');
add_action('wp_ajax_calculate_single_price', 'calculate_single_price_ajax');
add_action('wp_ajax_refresh_thb_usdt_rate', 'refresh_thb_usdt_rate');
add_action('wp_ajax_test_exchange_apis', 'test_exchange_apis');
