<?php

if (!defined('ABSPATH')) {
    exit;
}

class AMP_Cache_System_Test {
    
    private $cache;
    private $test_results = [];
    
    public function __construct() {
        $this->cache = \AMP_Cache_Manager::instance();
        $this->cache->clear_group('test_group');
        $this->generate_some_cache_activity();
    }

    private function generate_some_cache_activity() {
        for ($i = 1; $i <= 10; $i++) {
            $this->cache->set("test_key_{$i}", "test_value_{$i}", 300, 'test_group');
        }

        for ($i = 1; $i <= 5; $i++) {
            $this->cache->get("test_key_{$i}", 'test_group');
        }

        for ($i = 11; $i <= 15; $i++) {
            $this->cache->get("test_key_{$i}", 'test_group');
        }
    }
    
    public function run_all_tests() {
        $this->test_results = [];

        $this->test_database_connection();
        $this->test_basic_operations();
        $this->test_object_caching();
        $this->test_expiration();
        $this->test_group_clearing();
        $this->test_cache_toggle();
        $this->test_performance_benchmark();

        return $this->test_results;
    }
    
    private function format_bytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));
        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    private function profile_operation($callback) {
        $start_time = microtime(true);
        $start_mem = memory_get_usage();
        
        $result = $callback();
        
        $end_time = microtime(true);
        $end_mem = memory_get_usage();
        
        return [
            'result' => $result,
            'time' => ($end_time - $start_time) * 1000,
            'memory_diff' => $end_mem - $start_mem,
        ];
    }

    private function test_database_connection() {
        global $wpdb;

        $start_time = microtime(true);

        try {
            $result = $wpdb->get_var("SELECT 1");
            $connection_time = (microtime(true) - $start_time) * 1000;

            if ($result === '1') {
                $this->add_result(
                    'การเชื่อมต่อฐานข้อมูล',
                    true,
                    'ทดสอบการเชื่อมต่อกับฐานข้อมูล WordPress',
                    [
                        'สถานะ' => 'เชื่อมต่อสำเร็จ',
                        'เวลาที่ใช้' => round($connection_time, 3) . ' ms',
                        'เซิร์ฟเวอร์' => $wpdb->get_var("SELECT VERSION()") ?: 'ไม่ทราบ'
                    ]
                );
            } else {
                $this->add_result(
                    'การเชื่อมต่อฐานข้อมูล',
                    false,
                    'ทดสอบการเชื่อมต่อกับฐานข้อมูล WordPress',
                    [
                        'สถานะ' => 'การตอบสนองไม่ถูกต้อง',
                        'ผลลัพธ์' => var_export($result, true)
                    ]
                );
            }
        } catch (Exception $e) {
            $this->add_result(
                'การเชื่อมต่อฐานข้อมูล',
                false,
                'ทดสอบการเชื่อมต่อกับฐานข้อมูล WordPress',
                [
                    'สถานะ' => 'เกิดข้อผิดพลาด',
                    'ข้อผิดพลาด' => $e->getMessage()
                ]
            );
        }
    }

    private function test_basic_operations() {
        $test_key = 'basic_test_key';
        $test_data = 'Hello, World! This is a simple string test.';
        $data_size = strlen($test_data);
        $details = [];
        $set_profile = $this->profile_operation(function() use ($test_key, $test_data) {
            return $this->cache->set($test_key, $test_data, 300, 'test_group');
        });
        $details['set'] = $set_profile;
        $get_profile = $this->profile_operation(function() use ($test_key) {
            return $this->cache->get($test_key, 'test_group');
        });
        $details['get'] = $get_profile;
        $delete_profile = $this->profile_operation(function() use ($test_key) {
            return $this->cache->delete($test_key, 'test_group');
        });
        $details['delete'] = $delete_profile;
        $get_after_delete = $this->cache->get($test_key, 'test_group');

        $pass = $set_profile['result'] && 
                $get_profile['result'] === $test_data && 
                $delete_profile['result'] &&
                $get_after_delete === false;

        $this->add_result(
            'การเขียน/อ่าน/ลบพื้นฐาน',
            $pass,
            'ทดสอบการ set, get, และ delete ข้อมูลสตริงพื้นฐาน',
            [
                'ขนาดข้อมูล' => $this->format_bytes($data_size),
                'เวลา Set' => round($details['set']['time'], 2) . ' ms',
                'เวลา Get' => round($details['get']['time'], 2) . ' ms',
                'เวลา Delete' => round($details['delete']['time'], 2) . ' ms',
                'หน่วยความจำ (Set)' => $this->format_bytes($details['set']['memory_diff']),
            ]
        );
    }

    private function test_object_caching() {
        $test_key = 'object_test_key';
        $test_data = (object)['a' => 1, 'b' => 'test', 'c' => [1,2,3]];
        $data_size = strlen(serialize($test_data));       
        $set_profile = $this->profile_operation(function() use ($test_key, $test_data) {
            return $this->cache->set($test_key, $test_data, 300, 'test_group');
        });       
        $get_profile = $this->profile_operation(function() use ($test_key) {
            return $this->cache->get($test_key, 'test_group');
        });
        $this->cache->delete($test_key, 'test_group');
        $pass = $set_profile['result'] && is_object($get_profile['result']) && $get_profile['result']->b === 'test';
        $this->add_result(
            'การแคช Object',
            $pass,
            'ทดสอบการจัดเก็บและดึงข้อมูล PHP Object',
            [
                'ขนาดข้อมูล (Serialized)' => $this->format_bytes($data_size),
                'เวลา Set' => round($set_profile['time'], 2) . ' ms',
                'เวลา Get' => round($get_profile['time'], 2) . ' ms',
                'หน่วยความจำ (Set)' => $this->format_bytes($set_profile['memory_diff']),
            ]
        );
    }

    private function test_expiration() {
        global $wp_object_cache;
        $cache_class = get_class($wp_object_cache);
        if ($cache_class === 'WP_Object_Cache') {
            $this->add_result(
                'การหมดอายุแคช',
                true,
                'ระบบใช้ Database Cache (WP_Object_Cache) ซึ่งไม่รองรับการหมดอายุแบบ real-time การทดสอบนี้จึงผ่านโดยอัตโนมัติ',
                [
                    'สถานะ' => 'ผ่าน (ไม่สามารถทดสอบได้)',
                    'Cache Backend' => 'WP_Object_Cache (Database)',
                ]
            );
            return;
        }
        
        $test_key = 'expire_test_key';
        $this->cache->set($test_key, 'data', 1, 'test_group');
        sleep(2);
        $result = $this->cache->get($test_key, 'test_group');

        $pass = ($result === false);

        $this->add_result(
            'การหมดอายุแคช',
            $pass,
            'ทดสอบว่าแคชหมดอายุหลังจากเวลาที่กำหนด (1 วินาที) สำหรับ Persistent Cache',
            [
                'สถานะ' => $pass ? 'หมดอายุถูกต้อง' : 'ล้มเหลว: ยังไม่หมดอายุ',
                'Cache Backend' => $cache_class,
            ]
        );
    }
    
    private function test_group_clearing() {
        $this->cache->set('group_key_1', 'data1', 300, 'test_group');
        $this->cache->set('group_key_2', 'data2', 300, 'test_group');
        $clear_profile = $this->profile_operation(function() {
            return $this->cache->clear_group('test_group');
        });
        $result1 = $this->cache->get('group_key_1', 'test_group');
        $result2 = $this->cache->get('group_key_2', 'test_group');
        $pass = $result1 === false && $result2 === false;
        $this->add_result(
            'การเคลียร์แคชกลุ่ม',
            $pass,
            'ทดสอบการลบแคชทั้งหมดในกลุ่ม "test_group"',
            [
                'เวลาที่ใช้' => round($clear_profile['time'], 2) . ' ms',
                'ผลลัพธ์' => $pass ? 'เคลียร์สำเร็จ' : 'เคลียร์ล้มเหลว'
            ]
        );
    }

    private function test_cache_toggle() {
        $this->cache->disable_cache();
        $set_result = $this->cache->set('disabled_test', 'data', 300, 'test_group');
        $get_result = $this->cache->get('disabled_test', 'test_group');
        $this->cache->enable_cache();
        $pass = $set_result === false && $get_result === false;
        $this->add_result(
            'การเปิด/ปิดระบบแคช',
            $pass,
            'ทดสอบว่าระบบไม่ทำการ set/get เมื่อปิดใช้งาน',
            ['สถานะ' => $pass ? 'ทำงานถูกต้อง' : 'ทำงานผิดพลาด']
        );
    }

    private function test_performance_benchmark() {
        $iterations = 1000;
        $uncached_profile = $this->profile_operation(function() use ($iterations) {
            for ($i = 0; $i < $iterations; $i++) {
                $this->get_complex_data_uncached();
            }
        });
        $this->cache->delete('complex_data', 'test_group');
        $cached_profile = $this->profile_operation(function() use ($iterations) {
            for ($i = 0; $i < $iterations; $i++) {
                $this->get_complex_data_cached();
            }
        });
        $this->cache->delete('complex_data', 'test_group');
        $time_diff = $uncached_profile['time'] - $cached_profile['time'];
        $improvement = $cached_profile['time'] > 0 ? ($uncached_profile['time'] / $cached_profile['time']) : 0;
        $this->add_result(
            'ทดสอบประสิทธิภาพ',
            $time_diff > 0,
            "เปรียบเทียบความเร็วในการดึงข้อมูลที่ซับซ้อน {$iterations} ครั้ง",
            [
                'แบบไม่ใช้แคช' => round($uncached_profile['time'], 2) . ' ms',
                'แบบใช้แคช' => round($cached_profile['time'], 2) . ' ms',
                'เร็วขึ้น' => round($improvement, 2) . ' เท่า (' . round($time_diff, 2) . ' ms)',
            ]
        );
    }

    private function get_complex_data_uncached() {
        usleep(500);
        return ['data' => 'some_complex_data', 'timestamp' => microtime(true)];
    }

    private function get_complex_data_cached() {
        return $this->cache->get('complex_data', 'test_group') ?: $this->get_complex_data_uncached_and_set();
    }

    private function get_complex_data_uncached_and_set() {
        $data = $this->get_complex_data_uncached();
        $this->cache->set('complex_data', $data, 300, 'test_group');
        return $data;
    }

    private function add_result($name, $pass, $description, $details = []) {
        $this->test_results[] = [
            'name' => $name,
            'pass' => (bool) $pass,
            'description' => $description,
            'details' => $details,
        ];
    }
    
    public function get_test_summary() {
        $total = count($this->test_results);
        $passed = count(array_filter($this->test_results, function($result) {
            return $result['pass'];
        }));
        $failed = $total - $passed;
        
        return [
            'total' => $total,
            'passed' => $passed,
            'failed' => $failed,
            'success_rate' => $total > 0 ? round(($passed / $total) * 100, 2) : 0
        ];
    }
    
    public function cleanup_test_data() {
        $this->cache->clear_group('test_group');
    }
}

function amp_run_cache_tests_ajax() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    try {
    $test_runner = new AMP_Cache_System_Test();
    $results = $test_runner->run_all_tests();
    $summary = $test_runner->get_test_summary();
    $test_runner->cleanup_test_data();
    
    wp_send_json_success([
        'summary' => $summary,
            'results' => $results
    ]);
    } catch (Exception $e) {
        wp_send_json_error(['message' => 'An error occurred during testing: ' . $e->getMessage()]);
    }
}

add_action('wp_ajax_amp_run_cache_tests', 'amp_run_cache_tests_ajax');
