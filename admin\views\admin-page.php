<?php
if (!defined('WPINC')) {
    die;
}

function display_ad_management_page() {
    global $wpdb;

    if (!current_user_can('manage_options')) {
        wp_die('คุณไม่มีสิทธิ์เข้าถึงหน้านี้');
    }

    $current_user = wp_get_current_user();
    $user_roles = $current_user->roles ?? [];
    $is_administrator = current_user_can('manage_options');
    $is_advertiser = current_user_can('amp_advertiser_access');

    // For backward compatibility
    $user_role = '';
    if (!empty($user_roles)) {
        if (in_array('administrator', $user_roles)) {
            $user_role = 'administrator';
        } elseif (in_array('advertiser', $user_roles)) {
            $user_role = 'advertiser';
        } else {
            $user_role = $user_roles[0];
        }
    }

    $can_manage_ownership = true;
    $can_manage_expiration = true;
    $can_delete_positions = true;
    $can_reset_positions = true;
    $can_create_positions = true;
    $can_modify_content = true;
    $table_name_expiration = $wpdb->prefix . 'ad_expiration';
    $notice = '';
    if (isset($_POST['expiration_date']) && isset($_POST['ad_position'])) {
        $ad_position = sanitize_text_field($_POST['ad_position']);
        $expiration_date = sanitize_text_field($_POST['expiration_date']);
        $existing_entry = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$wpdb->prefix}ad_expiration WHERE ad_position = %s", $ad_position));
        if ($existing_entry) {
            $result = $wpdb->update(
                $wpdb->prefix . 'ad_expiration',
                array('expiration_date' => $expiration_date),
                array('ad_position' => $ad_position),
                array('%s'),
                array('%s')
            );
        } else {
            $result = $wpdb->insert(
                $wpdb->prefix . 'ad_expiration',
                array('ad_position' => $ad_position, 'expiration_date' => $expiration_date),
                array('%s', '%s')
            );
        }
        if ($result !== false) {
            $notice = '<div class="updated notice"><p>Expiration date (' . esc_html($expiration_date) . ') set successfully for Ad Position: ' . esc_html($ad_position) . '.</p></div>';
        } else {
            $error = $wpdb->last_error;
            $notice = '<div class="error notice"><p>Failed to set expiration date. Error: ' . esc_html($error) . '</p></div>';
        }
    }

    $all_users = get_users(array(
        'role__in' => array('administrator', 'advertiser')
    ));

    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    $ad_positions_objects = $position_manager->get_positions(['limit' => 9999, 'status' => 'any']);

    usort($ad_positions_objects, function($a, $b) {
        return strnatcasecmp($a->name, $b->name);
    });

    $ad_positions = wp_list_pluck($ad_positions_objects, 'name');

    ?>
    <script>

        window.handleResetAllPositions = function() {
            
            if (typeof Swal === 'undefined') {
                alert('SweetAlert2 not loaded!');
                return;
            }
            
            Swal.fire({
                title: 'Delete All Positions?',
                html: '<p style="margin-bottom: 15px;">This will <strong>PERMANENTLY DELETE ALL</strong> positions and related data:</p>' +
                      '<ul style="text-align: left; margin: 0; padding-left: 20px; color: #666;">' +
                      '<li>🗑️ Delete all position data completely</li>' +
                      '<li>👥 Remove all user ownership records</li>' +
                      '<li>⏰ Cancel all active timers</li>' +
                      '<li>🛒 Empty all shopping carts</li>' +
                      '<li>💳 Remove all payment records</li>' +
                      '<li>📊 Delete all click statistics</li>' +
                      '<li>🖼️ Remove all placeholder images</li>' +
                      '<li>⚙️ Clear all position settings</li>' +
                      '</ul>' +
                      '<p style="margin-top: 15px; color: #e74c3c; font-weight: bold;">⚠️ This action CANNOT be undone! All data will be lost forever!</p>',
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: '🗑️ Yes, Delete Everything',
                cancelButtonText: '❌ Cancel',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    return jQuery.post(ajaxurl, {
                        action: 'reset_all_positions',
                        security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.admin_ajax_nonce : ''
                    }).then(response => {
                        if (!response.success) {
                            throw new Error(response.data || 'Reset failed');
                        }
                        return response;
                    }).catch(error => {
                        console.error('AJAX Error:', error);
                        Swal.showValidationMessage(
                            `Request failed: ${error.message || error}`
                        );
                    });
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                if (result.isConfirmed) {
                    const response = result.value;
                    
                    Swal.fire({
                        title: '✅ Deletion Complete!',
                        html: `<p style="color: #dc3545; font-weight: bold;">Successfully deleted <strong>${response.data.positions_deleted}</strong> positions</p>` +
                              '<p style="color: #666;">All position data has been permanently removed from the system</p>',
                        icon: 'success',
                        timer: 3000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                }
            });
        }
    </script>

    <script>
        var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    </script>
    
    <div class="wrap ads-management-wrap">
        <div class="ads-management-header">
            <h1><i class="fas fa-ad"></i> Ads Management</h1>
            <div class="ads-header-actions">
                <?php if ($can_delete_positions): ?>
                <button id="reset-all-positions" class="ad-btn ad-btn-danger" onclick="if(typeof window.handleResetAllPositions === 'function') window.handleResetAllPositions(); else alert('Delete function not loaded!');">
                    <i class="fas fa-trash-alt"></i> Delete All Positions
                </button>
                <?php endif; ?>
                <?php if ($can_create_positions): ?>
                <button id="add-ad-position" class="ad-btn ad-btn-primary">
                    <i class="fas fa-plus"></i> Add New Position
                </button>
                <?php endif; ?>
                <?php if ($user_role === 'advertiser'): ?>
                <div class="user-permission-note">
                    <i class="fas fa-info-circle"></i>
                    <span>You can modify position content (images, links, SEO keywords) only</span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <?php echo $notice; ?>

        <div class="ads-management-content">
            <div class="ads-table-container">
                <div class="ads-table-header">
                    <h2><i class="fas fa-list"></i> Existing Ad Positions</h2>
                    <div class="ads-table-stats">
                        <span class="stat-item">
                            <i class="fas fa-chart-bar"></i>
                            Total: <strong><?php echo $position_manager->count_positions(['status' => 'any']); ?></strong>
                        </span>
                        <div class="bulk-actions" style="display: none;">
                            <button id="bulk-delete-btn" class="modern-button button-danger" style="margin-left: 10px;">
                                <i class="fas fa-trash"></i> Delete Selected
                            </button>
                        </div>
                    </div>
                </div>

                <div class="ads-table-wrapper">
                    <table class="ads-table">
                        <thead>
                            <tr>
                                <th class="col-checkbox" style="width: 40px;">
                                    <input type="checkbox" id="select-all-positions" title="Select All">
                                </th>
                                <th class="col-position">Position</th>
                                <th class="col-user">User</th>
                                <th class="col-image">Image</th>
                                <th class="col-link">Link</th>
                                <th class="col-seo">SEO Keyword</th>
                                <th class="col-dimensions">Dimensions</th>
                                <th class="col-expiration">Expiration</th>
                                <th class="col-clicks">Clicks (30d)</th>
                                <th class="col-status">Status</th>
                                <th class="col-actions">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="ad-positions-table">
                            <?php echo get_ad_positions_table_html(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div id="ad-position-modal" class="ad-modal">
        <div class="ad-modal-content">
            <div class="ad-modal-header">
                <h2 id="modal-title">
                    <i class="fas fa-plus-circle"></i>
                    <span id="modal-title-text">Add New Position</span>
                </h2>
                <button type="button" class="ad-modal-close" id="close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="ad-modal-body">
                <form id="ad-position-form" enctype="multipart/form-data">
                    <input type="hidden" name="action" id="ad_form_action" value="add_ad_position">
                    <input type="hidden" name="edit_ad_position_name" id="edit_ad_position_name">
                    <input type="hidden" id="ad_management_nonce" name="ad_management_nonce" value="<?php echo $legacy_nonce; ?>">
                    <input type="hidden" id="security" name="security" value="<?php echo $admin_position_nonce; ?>">

                    <div class="ultra-modern-form">
                        <div class="form-section">
                            <div class="ultra-field">
                                <label for="ad_position_name" class="ultra-label">
                                    <i class="fas fa-tag"></i>
                                    Position Name
                                    <?php if (!$can_create_positions): ?>
                                    <span class="field-readonly-note">(Read-only)</span>
                                    <?php endif; ?>
                                </label>
                                <input type="text" name="ad_position_name" id="ad_position_name" required
                                       placeholder="Enter position name (e.g., A1 or A1-A20 for bulk)" class="ultra-input"
                                       <?php echo !$can_create_positions ? 'readonly' : ''; ?>>
                                <div class="field-hint">
                                    <i class="fas fa-info-circle"></i>
                                    <span>Tip: Use pattern like A1-A20 to create multiple positions at once</span>
                                </div>
                            </div>

                            <?php if ($can_create_positions || $can_manage_ownership): ?>
                            <div class="ultra-field">
                                <label for="ad_width" class="ultra-label">
                                    <i class="fas fa-arrows-alt-h"></i>
                                    Width (px)
                                </label>
                                <input type="number" name="ad_width" id="ad_width" required min="1"
                                       placeholder="300" class="ultra-input">
                            </div>
                            <?php endif; ?>

                            <div class="ultra-field">
                                <label for="ad_link" class="ultra-label">
                                    <i class="fas fa-link"></i>
                                    Target Link
                                </label>
                                <input type="url" name="ad_link" id="ad_link"
                                       placeholder="https://example.com" class="ultra-input">
                            </div>

                            <?php if ($can_manage_expiration): ?>
                            <div class="ultra-field">
                                <label for="expiration_date" class="ultra-label">
                                    <i class="fas fa-calendar-alt"></i>
                                    Expiration Date
                                </label>
                                <input type="date" name="expiration_date" id="expiration_date" class="ultra-input">
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-section">
                            <?php if ($can_manage_ownership): ?>
                            <div class="ultra-field">
                                <label for="ad_user_id" class="ultra-label">
                                    <i class="fas fa-user"></i>
                                    Assign User
                                </label>
                                <select name="ad_user_id" id="ad_user_id" class="ultra-select">
                                    <option value="0">Create New User</option>
                                    <?php foreach ($all_users as $user): ?>
                                        <option value="<?php echo $user->ID; ?>"><?php echo $user->user_login; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php endif; ?>

                            <?php if ($can_create_positions || $can_manage_ownership): ?>
                            <div class="ultra-field">
                                <label for="ad_height" class="ultra-label">
                                    <i class="fas fa-arrows-alt-v"></i>
                                    Height (px)
                                </label>
                                <input type="number" name="ad_height" id="ad_height" required min="1"
                                       placeholder="250" class="ultra-input">
                            </div>
                            <?php endif; ?>

                            <div class="ultra-field">
                                <label for="ad_website_name" class="ultra-label">
                                    <i class="fas fa-search"></i>
                                    SEO Keyword
                                </label>
                                <input type="text" name="ad_website_name" id="ad_website_name"
                                       placeholder="Enter SEO keyword" class="ultra-input">
                            </div>

                            <?php if ($can_create_positions): ?>
                            <div class="ultra-field">
                                <label class="ultra-label">
                                    <i class="fas fa-toggle-on"></i>
                                    Status
                                </label>
                                <div class="simple-toggle-container">
                                    <label class="simple-toggle">
                                        <input type="checkbox" name="ad_status" id="ad_status" value="1" checked>
                                        <span class="simple-toggle-slider"></span>
                                    </label>
                                    <span class="simple-toggle-label">Active Position</span>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-section full-width">
                            <div class="ultra-field">
                                <label class="ultra-label">
                                    <i class="fas fa-image"></i>
                                    Image
                                </label>
                                <div class="ultra-image-section">
                                    <div class="ultra-image-controls">
                                        <button type="button" class="ultra-btn primary" id="select-image-button">
                                            <i class="fas fa-photo-video"></i>
                                            Select from Media Library
                                        </button>
                                        <button type="button" class="ultra-btn secondary" id="preview-image-btn" style="display: none;">
                                            <i class="fas fa-eye"></i>
                                            Preview Image
                                        </button>
                                        <button type="button" class="ultra-btn danger" id="clear-image-btn" style="display: none;">
                                            <i class="fas fa-times"></i>
                                            Clear
                                        </button>
                                    </div>
                                    <input type="hidden" name="ad_image_url" id="ad_image_url">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="ultra-modal-footer">
                <button type="button" class="ultra-footer-btn cancel" id="cancel-btn">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button type="submit" form="ad-position-form" class="ultra-footer-btn save" id="save-btn">
                    <i class="fas fa-save"></i>
                    Save Position
                </button>
            </div>
        </div>
    </div>
        <script>
        function waitForDependencies(callback, maxAttempts = 50) {
            let attempts = 0;
            function checkDependencies() {
                attempts++;
                const hasJQuery = typeof jQuery !== 'undefined';
                const hasSwal = typeof Swal !== 'undefined';
                const hasAjaxUrl = typeof ajaxurl !== 'undefined';
                if (hasJQuery && hasSwal && hasAjaxUrl) {
                    callback();
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(checkDependencies, 100);
                } else {
                    console.error('Dependencies not loaded after maximum attempts');
                    callback(); 
                }
            }

            checkDependencies();
        }

        waitForDependencies(function() {
            jQuery(document).ready(function($) {
            function detectBulkPattern(input) {
                input = input.toUpperCase();
                var dashPattern = /^([A-Z]+)(\d+)-([A-Z]+)(\d+)$/;
                var match = input.match(dashPattern);

                if (match) {
                    var prefix1 = match[1];
                    var start = parseInt(match[2]);
                    var prefix2 = match[3];
                    var end = parseInt(match[4]);

                    if (prefix1 === prefix2 && start <= end && end - start <= 100) {
                        return {
                            type: 'range',
                            prefix: prefix1,
                            start: start,
                            end: end,
                            count: end - start + 1
                        };
                    }
                }

                return null;
            }
            function handleBulkCreation(pattern, width, height) {
                var positions = [];
                for (var i = pattern.start; i <= pattern.end; i++) {
                    positions.push(pattern.prefix + i);
                }

                Swal.fire({
                    title: 'Bulk Position Creation',
                    html: `
                        <div style="text-align: left; margin: 20px 0;">
                            <p><strong>Pattern detected:</strong> ${pattern.prefix}${pattern.start}-${pattern.prefix}${pattern.end}</p>
                            <p><strong>Positions to create:</strong> ${pattern.count} positions</p>
                            <p><strong>Size:</strong> ${width} × ${height} px</p>
                            <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;">
                                ${positions.join(', ')}
                            </div>
                        </div>
                    `,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Create All Positions',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        executeBulkCreation(positions, width, height);
                    }
                });
            }
            function executeBulkCreation(positions, width, height) {
                Swal.fire({
                    title: 'Creating Positions...',
                    html: `
                        <div class="bulk-progress">
                            <div class="progress-bar" style="width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden;">
                                <div class="progress-fill" style="width: 0%; height: 100%; background: #3085d6; transition: width 0.3s;"></div>
                            </div>
                            <p class="progress-text" style="margin-top: 10px;">Creating position 0 of ${positions.length}</p>
                        </div>
                    `,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'bulk_create_positions',
                        positions: positions,
                        width: width,
                        height: height,
                        security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.position_nonce : ''
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('#ad-positions-table').html(response.data.html);
                            $('#ad-position-modal').removeClass('show').fadeOut(300);
                            document.getElementById('ad-position-form').reset();

                            var totalPositions = $('#ad-positions-table tr').length - 1;
                            $('#total-positions-count').text(totalPositions);

                            Swal.fire({
                                title: 'Success!',
                                html: `
                                    <p>Successfully created <strong>${response.data.created_count}</strong> positions</p>
                                    ${response.data.skipped_count > 0 ? `<p style="color: #f39c12;">Skipped <strong>${response.data.skipped_count}</strong> existing positions</p>` : ''}
                                `,
                                icon: 'success',
                                timer: 3000,
                                showConfirmButton: false
                            });
                        } else {
                            Swal.fire({
                                title: 'Error',
                                text: response.data || 'Failed to create positions',
                                icon: 'error'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error',
                            text: 'Failed to create positions. Please try again.',
                            icon: 'error'
                        });
                    }
                });
            }

            function resetForm() {
                $('#ad_form_action').val('add_ad_position');
                $('#edit_ad_position_name').val('');
                $('#ad_position_name').val('');
                $('#ad_width').val('');
                $('#ad_height').val('');
                $('#ad_user_id').val('0');
                $('#ad_image_url').val('');
                $('#ad_link').val('');
                $('#ad_website_name').val('');
                $('#expiration_date').val('');
                $('#ad_status').prop('checked', true);
                updateImageStatus('');
                $('#security').val((typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.position_nonce : '');
            }
            function updateImageStatus(imageUrl) {
                const previewBtn = $('#preview-image-btn');
                const clearBtn = $('#clear-image-btn');

                if (imageUrl && imageUrl.trim() !== '') {
                    previewBtn.show();
                    clearBtn.show();
                } else {
                    previewBtn.hide();
                    clearBtn.hide();
                }
            }
            $('#add-ad-position').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                resetForm();
                $('#modal-title-text').text('Add New Position');
                $('#modal-title i').removeClass('fas fa-edit').addClass('fas fa-plus-circle');
                $('#ad-position-modal').addClass('show').fadeIn(300);
            });

            $(document).on('click', '.edit-ad', function(e) {
                e.preventDefault();
                resetForm();
                var position = $(this).data('position');
                var user = $(this).data('user');

                if (typeof ampAdmin === 'undefined' || !ampAdmin.nonces) {
                    Swal.fire({
                        title: 'Error',
                        text: 'Security nonces not loaded. Please refresh the page.',
                        icon: 'error'
                    });
                    return;
                }

                var ajaxData = {
                    action: 'get_ad_data',
                    position: position,
                    user: user,
                    security: ampAdmin.nonces.get_ad_data_nonce
                };

                $('#edit_ad_position_name').val(position);
                $('#ad_position_name').val(position);

                $.post(ajaxurl, ajaxData, function(response) {
                    if (response.success) {
                        var d = response.data;
                        $('#ad_width').val(d.width);
                        $('#ad_height').val(d.height);
                        $('#ad_image_url').val(d.image);
                        updateImageStatus(d.image);
                        $('#ad_user_id').val(d.user_id);
                        $('#ad_link').val(d.link);
                        $('#ad_website_name').val(d.seo_keyword);
                        $('#expiration_date').val(d.expiration_date);
                        $('#ad_status').prop('checked', d.active == 1);
                        $('#modal-title-text').text('Edit Position');
                        $('#modal-title i').removeClass('fas fa-plus-circle').addClass('fas fa-edit');

                        $('#ad-position-modal').addClass('show').fadeIn(300);
                    } else {
                        Swal.fire({
                            title: 'Error',
                            text: response.data || 'Failed to load position data',
                            icon: 'error'
                        });
                    }
                }, 'json').fail(function(xhr, status, error) {
                    console.error('AJAX Error:', xhr.responseText);
                    Swal.fire({
                        title: 'Error',
                        text: 'Failed to load position data. Server response: ' + (xhr.responseText || error),
                        icon: 'error'
                    });
                });
            });

            $('#ad-position-form').on('submit', function(e) {
                e.preventDefault();

                var positionName = $('#ad_position_name').val().trim();
                var width = parseInt($('#ad_width').val());
                var height = parseInt($('#ad_height').val());

                if (!positionName) {
                    Swal.fire({
                        title: 'Validation Error',
                        text: 'Position name is required',
                        icon: 'error'
                    });
                    return;
                }

                if (width <= 0 || height <= 0) {
                    Swal.fire({
                        title: 'Validation Error',
                        text: 'Width and height must be greater than 0',
                        icon: 'error'
                    });
                    return;
                }

                var bulkPattern = detectBulkPattern(positionName);
                if (bulkPattern && !$('#edit_ad_position_name').val()) {
                    handleBulkCreation(bulkPattern, width, height);
                    return;
                }
                
                var f = new FormData(this);
                var isEdit = $('#edit_ad_position_name').val() !== '';

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: f,
                    contentType: false,
                    processData: false,
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('#ad-positions-table').html(response.data.html);
                            $('#ad-position-modal').removeClass('show').fadeOut(300);
                            
                            var totalPositions = $('#ad-positions-table tr').length - 1;
                            $('#total-positions-count').text(totalPositions);
                            
                            document.getElementById('ad-position-form').reset();
                            $('#edit_ad_position_name').val('');

                            Swal.fire({
                                title: 'Success',
                                text: isEdit ? 'Ad position updated successfully' : 'Ad position added successfully',
                                icon: 'success',
                                timer: 1500,
                                showConfirmButton: false
                            });
                        } else {
                            Swal.fire({
                                title: 'Error',
                                text: response.data || 'Unknown error occurred',
                                icon: 'error'
                            });
                        }
                    },
                    error: function(xhr, status, err) {
                        var isEdit = $('#edit_ad_position_name').val() !== '';
                        Swal.fire({
                            title: 'Error',
                            text: 'Failed to ' + (isEdit ? 'update' : 'add') + ' ad position. Please try again.',
                            icon: 'error'
                        });
                    }
                });
            });
            $('#select-image-button').on('click', function(e) {
                e.preventDefault();
                var frame;
                if (frame) { frame.open(); return; }
                frame = wp.media({ title: 'Select Image', multiple: false, library: { type: 'image' } });
                frame.on('select', function() {
                    var a = frame.state().get('selection').first().toJSON();
                    $('#ad_image_url').val(a.url);
                    updateImageStatus(a.url);
                });
                frame.open();
            });

            $('#clear-image-btn').on('click', function(e) {
                e.preventDefault();
                $('#ad_image_url').val('');
                updateImageStatus('');
            });

            $('#preview-image-btn').on('click', function(e) {
                e.preventDefault();
                const imageUrl = $('#ad_image_url').val();
                if (imageUrl) {
                    Swal.fire({
                        title: 'Image Preview',
                        imageUrl: imageUrl,
                        imageAlt: 'Ad Image Preview',
                        showCloseButton: true,
                        showConfirmButton: false,
                        width: 'auto',
                        customClass: {
                            popup: 'image-preview-popup'
                        }
                    });
                }
            });

            $(document).on('change', '.status-toggle input', function() {
                var position = $(this).data('position');
                var status = $(this).is(':checked') ? 1 : 0;
                var statusText = $(this).closest('tr').find('.status-text');
                var $toggleInput = $(this);

                $.post(ajaxurl, {
                    action: 'toggle_ad_status',
                    position: position,
                    status: status,
                    security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.toggle_status_nonce : ''
                }, function(response) {
                    if (response.success) {
                        if (status === 1) {
                            statusText.removeClass('status-inactive').addClass('status-active').text('Active');
                        } else {
                            statusText.removeClass('status-active').addClass('status-inactive').text('Inactive');
                        }
                        Swal.fire({
                            title: 'Status Updated',
                            text: position + ' is now ' + (status === 1 ? 'active' : 'inactive'),
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire('Error!', response.data || 'Status update failed.', 'error');
                        $toggleInput.prop('checked', !status);
                    }
                }).fail(function(xhr, textStatus, errorThrown) {
                    console.error('AJAX error:', textStatus, errorThrown);
                    Swal.fire('Error!', 'Connection failed: ' + textStatus, 'error');
                    $toggleInput.prop('checked', !status);
                });
            });

            $(document).on('click', '.reset-ad', function(e) {
                e.preventDefault();
                var position = $(this).data('position');

                Swal.fire({
                    title: 'Are you sure?',
                    text: `This will reset all ownership and data for "${position}", including images, links, alt text, and expiration date.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#ffc107',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, reset it!',
                    showLoaderOnConfirm: true,
                    preConfirm: () => {
                        return $.post(ajaxurl, {
                            action: 'reset_ad_position',
                            position: position,
                            security: (typeof ampAdmin !== 'undefined' && ampAdmin.nonces) ? ampAdmin.nonces.reset_position_nonce : ''
                        }).then(response => {
                            if (!response.success) {
                                throw new Error(response.data?.message || 'Reset failed');
                            }
                            return response;
                        }).catch(error => {
                            Swal.showValidationMessage(`Reset failed: ${error.message || error}`);
                        });
                    }
                }).then((result) => {
                    if (result.isConfirmed && result.value) {
                        if (result.value.data && result.value.data.html) {
                            $('#ad-positions-table').html(result.value.data.html);
                            var totalPositions = $('#ad-positions-table tr').length;
                            $('#total-positions-count').text(totalPositions);
                        }
                        Swal.fire({
                            title: 'Reset Complete!',
                            text: `Position "${position}" has been completely reset.`,
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    }
                });
            });

            $('#close-modal, #cancel-btn').on('click', function() {
                $('#ad-position-modal').removeClass('show').fadeOut(300);
            });

            $('#ad-position-modal').on('click', function(e) {
                if (e.target === this) {
                    $('#ad-position-modal').removeClass('show').fadeOut(300);
                }
            });

            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#ad-position-modal').is(':visible')) {
                    $('#ad-position-modal').removeClass('show').fadeOut(300);
                }
            });

            $('#select-all-positions').on('change', function() {
                var isChecked = $(this).is(':checked');
                $('.position-checkbox').prop('checked', isChecked);
                toggleBulkActions();
            });

            $(document).on('change', '.position-checkbox', function() {
                var totalCheckboxes = $('.position-checkbox').length;
                var checkedCheckboxes = $('.position-checkbox:checked').length;

                $('#select-all-positions').prop('checked', checkedCheckboxes === totalCheckboxes);
                $('#select-all-positions').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);

                toggleBulkActions();
            });

            function toggleBulkActions() {
                var checkedCount = $('.position-checkbox:checked').length;
                if (checkedCount > 0) {
                    $('.bulk-actions').show();
                } else {
                    $('.bulk-actions').hide();
                }
            }

            $('#bulk-delete-btn').on('click', function() {
                var selectedPositions = [];
                $('.position-checkbox:checked').each(function() {
                    selectedPositions.push($(this).val());
                });

                if (selectedPositions.length === 0) {
                    Swal.fire('Error!', 'Please select at least one position to delete.', 'error');
                    return;
                }

                if (typeof ampAdmin === 'undefined' || !ampAdmin.nonces || !ampAdmin.nonces.delete_position_nonce) {
                    Swal.fire('Error!', 'Security nonces not loaded. Please refresh the page.', 'error');
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    html: `This will permanently delete <strong>${selectedPositions.length}</strong> selected position(s) and all their data.<br><br><strong>This action cannot be undone!</strong>`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#e74c3c',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: `Yes, delete ${selectedPositions.length} position(s)!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: 'Deleting Positions...',
                            html: `Deleting ${selectedPositions.length} position(s), please wait...`,
                            allowOutsideClick: false,
                            allowEscapeKey: false,
                            showConfirmButton: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        $.post(ajaxurl, {
                            action: 'bulk_delete_positions',
                            positions: selectedPositions,
                            security: ampAdmin.nonces.delete_position_nonce
                        }, function(response) {
                            if (response.success) {
                                $('#ad-positions-table').html(response.data.html);
                                $('#select-all-positions').prop('checked', false);
                                $('.bulk-actions').hide();

                                var message = response.data.message;
                                var icon = response.data.failed_count > 0 ? 'warning' : 'success';

                                Swal.fire({
                                    title: 'Bulk Delete Complete!',
                                    html: message,
                                    icon: icon,
                                    timer: 4000,
                                    showConfirmButton: true
                                });
                            } else {
                                Swal.fire('Error!', response.data || 'Failed to delete positions.', 'error');
                            }
                        }).fail(function() {
                            Swal.fire('Error!', 'Failed to delete positions. Please try again.', 'error');
                        });
                    }
                });
            });

            $(document).on('click', '.delete-ad', function(e) {
                e.preventDefault();
                var position = $(this).data('position');

                if (!position) {
                    Swal.fire('Error!', 'Position name not found', 'error');
                    return;
                }

                console.log('Delete button clicked for position:', position);
                console.log('ajaxurl:', ajaxurl);
                console.log('ampAdmin object:', ampAdmin);

                if (typeof ampAdmin === 'undefined' || !ampAdmin.nonces || !ampAdmin.nonces.delete_position_nonce) {
                    Swal.fire('Error!', 'Security nonces not loaded. Please refresh the page.', 'error');
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    text: `This will permanently delete position "${position}" and all its data. This action cannot be undone!`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#e74c3c',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Yes, delete it!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.post(ajaxurl, {
                            action: 'delete_ad',
                            position: position,
                            security: ampAdmin.nonces.delete_position_nonce
                        }, function(response) {
                            if (response.success) {
                                if (response.data && response.data.html) {
                                    $('#ad-positions-table').html(response.data.html);
                                }
                                Swal.fire('Deleted!', 'Position has been deleted.', 'success');
                            } else {
                                Swal.fire('Error!', response.data || 'Failed to delete position.', 'error');
                            }
                        }, 'json').fail(function(xhr, status, error) {
                            console.error('Delete AJAX Error:', xhr.responseText);
                            console.error('Status:', status, 'Error:', error);
                            Swal.fire('Error!', 'Could not connect to the server. Check console for details.', 'error');
                        });
                    }
                });
            });

            $('#reset-all-positions').on('click', function(e) {
                e.preventDefault();

                if (typeof window.handleResetAllPositions === 'function') {
                    window.handleResetAllPositions();
                } else {
                    alert('Reset function not loaded!');
                }
            });

            }); 
        }); 
        </script>
    </div>
    <?php
}


