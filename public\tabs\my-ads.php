<?php
if (!defined('ABSPATH')) {
    exit;
}

require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
require_once AMP_PLUGIN_DIR . 'includes/utils/click-statistics.php';

$current_user_id = get_current_user_id();
if (!$current_user_id) {
    return;
}



$position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('public');
$has_bypass = amp_user_has_bypass_permission($current_user_id);

$user_positions = $position_manager->get_user_positions_with_details($current_user_id);

$filtered_positions = [];
foreach ($user_positions as $position) {
    $ownership_state = $position_manager->get_position_ownership_state($position->name, $current_user_id);
    $is_owner = $ownership_state['is_owned_by_current_user'];
    $is_expired = $ownership_state['is_expired'];

    if ($is_owner) {
        if ($has_bypass) {
            $filtered_positions[] = $position;
        } elseif (!$is_expired) {
            $filtered_positions[] = $position;
        }
    }
}

$user_positions = $filtered_positions;

$click_stats = get_user_position_clicks($current_user_id, false);
$formatted_clicks = [];
foreach ($click_stats as $stat) {
    $formatted_clicks[$stat['ad_position']] = $stat['total_clicks'];
}

require_once AMP_PLUGIN_DIR . 'includes/core/class-database.php';
$database = \AdManagementPro\Core\Database::instance();
$cpc_cpm_data = calculate_user_cpc_cpm($current_user_id, $user_positions, $database);
?>

<script>
window.positionDimensions = <?php
    $dimensions = [];
    foreach ($user_positions as $p) {
        $dimensions[$p->name] = ['width' => $p->width, 'height' => $p->height];
    }
    echo json_encode($dimensions);
?>;
</script>

<style>
.my-ads-container {
    background: var(--body-bg);
    min-height: 100vh;
    padding: 20px;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.my-ads-hero-section {
    background: var(--theme-gradient);
    border-radius: 24px;
    padding: 40px;
    margin-bottom: 30px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.my-ads-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.1;
    pointer-events: none;
}

.my-ads-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.my-ads-hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.my-ads-hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.my-ads-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.my-ads-stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease;
}

.my-ads-stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.my-ads-stat-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.my-ads-stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: white;
}

.my-ads-stat-label {
    font-size: 0.95rem;
    opacity: 0.9;
    color: white;
}

.my-ads-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 25px;
    margin-bottom: 30px;
    max-width: 1600px;
    margin-left: auto;
    margin-right: auto;
}



.my-ad-card {
    background: var(--card-bg);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
    animation: fadeInUp 0.6s ease-out;
}

.my-ad-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: 0 20px 60px rgba(67, 97, 238, 0.15);
    border-color: var(--primary-color);
}

.my-ad-card.expired {
    opacity: 0.8;
    border-color: #dc3545;
}

.my-ad-card.expiring {
    border-color: #ffc107;
    animation: pulse-warning 2s infinite;
}

.my-ad-header {
    padding: 20px 20px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--divider-color);
}

.my-ad-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

.my-ad-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.my-ad-status.active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.my-ad-status.expired {
    background: linear-gradient(135deg, #f8d7da, #f1b5bb);
    color: #721c24;
}

.my-ad-status.expiring {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    animation: pulse-warning 2s infinite;
}

.my-ad-image {
    height: 220px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.my-ad-image:hover {
    transform: scale(1.05);
}

.my-ad-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
    background-size: 20px 20px;
    opacity: 0.3;
    pointer-events: none;
}

.my-ad-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.placeholder-image {
    text-align: center;
    color: var(--muted-text);
    padding: 20px;
}

.placeholder-image i {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.7;
}

.placeholder-text {
    font-size: 1rem;
    font-weight: 500;
}

.my-ad-details {
    padding: 20px;
}

.my-ad-detail {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    font-size: 0.95rem;
}

.my-ad-detail i {
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.my-ad-detail span {
    color: var(--text-color);
    flex: 1;
}

.my-ad-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.my-ad-link:hover {
    text-decoration: underline;
}

.my-ad-no-link {
    color: var(--muted-text);
    font-style: italic;
}

.my-ad-performance {
    background: var(--input-bg);
    border-radius: 8px;
    padding: 8px 12px;
    margin: 8px 0;
    border-left: 3px solid var(--primary-color);
}

.my-ad-performance span {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--primary-color);
}

.my-ad-actions {
    padding: 15px 20px 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.my-ad-btn {
    flex: 1;
    min-width: 130px;
    padding: 12px 18px;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.my-ad-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.my-ad-btn:hover::before {
    left: 100%;
}

.upload-new-ad-btn, .edit-ad-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.upload-new-ad-btn:hover, .edit-ad-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.renew-ad-btn {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3) !important;
}

.renew-ad-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4) !important;
    background: linear-gradient(135deg, #047857, #065f46) !important;
}

.cancel-ownership-btn {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    color: white !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3) !important;
}

.cancel-ownership-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4) !important;
    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
}

.my-ads-empty {
    text-align: center;
    padding: 80px 20px;
    background: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.my-ads-empty-icon {
    font-size: 5rem;
    color: var(--muted-text);
    margin-bottom: 30px;
}

.my-ads-empty h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 15px;
}

.my-ads-empty p {
    color: var(--muted-text);
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.my-ads-cta-btn {
    background: var(--theme-gradient);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.my-ads-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
}

@keyframes pulse-warning {
    0%, 100% {
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.2);
    }
    50% {
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.4);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.price-tooltip {
    animation: tooltipFadeIn 0.2s ease-out;
}




.my-ad-card:nth-child(6) { animation-delay: 0.6s; }



.horizontal-upload-popup {
    border-radius: 20px !important;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4) !important;
    border: none !important;
    background: var(--card-bg) !important;
    color: var(--text-color) !important;
}

.horizontal-upload-container {
    display: flex;
    gap: 30px;
    padding: 20px;
    min-height: 400px;
}

.upload-left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.upload-right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.position-info-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 16px;
    padding: 20px;
    color: white;
    box-shadow: 0 10px 30px rgba(67, 97, 238, 0.3);
}

.position-info-card h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    opacity: 0.9;
}

.info-value {
    font-weight: 600;
    opacity: 1;
}

.current-image-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.section-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

.current-image-display {
    flex: 1;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    background: var(--input-bg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.current-image-preview {
    max-width: 100%;
    max-height: 200px;
    object-fit: contain;
}

.upload-placeholder {
    flex: 1;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    background: var(--input-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 200px;
}

.placeholder-content {
    text-align: center;
    color: var(--light-text);
}

.placeholder-content i {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.upload-area {
    position: relative;
}

.modern-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    border: 2px dashed var(--primary-color);
    border-radius: 12px;
    background: var(--input-bg);
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 10px;
}

.modern-upload-label:hover {
    border-color: var(--primary-color);
    background: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
}

.modern-upload-label i {
    font-size: 28px;
    color: var(--primary-color);
}

.modern-upload-label span {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.upload-preview {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: var(--input-bg);
    border: 2px solid var(--primary-color);
}



.remove-preview {
    position: absolute;
    top: 8px;
    right: 8px;
    background: var(--error-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.remove-preview:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.upload-hint {
    text-align: center;
    margin-top: 8px;
}

.upload-hint small {
    color: var(--warning-color);
    font-size: 11px;
}

.form-fields {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.field-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.field-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.field-group label i {
    color: var(--primary-color);
}

.modern-input {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 13px;
    background: var(--input-bg);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.modern-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.modern-preview-btn {
    padding: 10px 16px;
    background: var(--info-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.modern-preview-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.modern-btn {
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin: 0 5px !important;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
}

.modern-btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.modern-btn-secondary {
    background: var(--border-color) !important;
    color: var(--text-color) !important;
}

.modern-btn-secondary:hover {
    background: var(--light-text) !important;
    transform: translateY(-2px) !important;
}

.modern-btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    color: white !important;
}

.modern-btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4) !important;
}

.modern-btn-light {
    background: #f8f9fa !important;
    color: #6c757d !important;
}

@media (max-width: 1400px) {
    .my-ads-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
    }

    .my-ads-grid.single-item {
        grid-template-columns: 1fr;
        max-width: 400px;
    }

    .my-ads-grid.two-items {
        grid-template-columns: 1fr 1fr;
        max-width: 820px;
    }

    .my-ads-grid.three-items {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
    }
}

@media (max-width: 1024px) {
    .my-ads-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 18px;
    }

    .my-ads-grid.single-item {
        grid-template-columns: 1fr;
        max-width: 350px;
    }

    .my-ads-grid.two-items {
        grid-template-columns: 1fr 1fr;
        max-width: 718px;
    }

    .my-ads-grid.three-items {
        grid-template-columns: repeat(2, 1fr);
        max-width: 718px;
    }
}

@media (max-width: 768px) {
    .my-ads-container {
        padding: 15px;
    }

    .my-ads-hero-section {
        padding: 25px;
    }

    .my-ads-hero-title {
        font-size: 2rem;
    }

    .my-ads-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .my-ads-grid,
    .my-ads-grid.single-item,
    .my-ads-grid.two-items,
    .my-ads-grid.three-items {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 100%;
    }
    
    .my-ad-actions {
        flex-direction: column;
    }
    
    .my-ad-btn {
        min-width: auto;
    }

    .horizontal-upload-container {
        flex-direction: column;
        gap: 20px;
        padding: 15px;
        min-height: auto;
    }

    .upload-left-section,
    .upload-right-section {
        flex: none;
    }

    .position-info-card {
        padding: 15px;
    }

    .current-image-display {
        height: 120px;
    }

    .current-image-preview {
        max-height: 120px;
    }

    .modern-upload-label {
        padding: 20px 15px;
    }

    .modern-upload-label i {
        font-size: 24px;
    }

    .modern-upload-label span {
        font-size: 13px;
    }



    .field-group {
        gap: 4px;
    }

    .modern-input {
        padding: 8px 10px;
        font-size: 12px;
    }

    .modern-preview-btn {
        padding: 8px 12px;
        font-size: 12px;
    }

    .in-cart-btn {
        padding: 10px 14px !important;
        font-size: 13px !important;
        min-width: 120px !important;
    }

    .in-cart-btn i {
        font-size: 14px !important;
    }
}

@media (max-width: 480px) {
    .my-ads-hero-title {
        font-size: 1.7rem;
    }

    .my-ads-stats-grid {
        grid-template-columns: 1fr;
    }

    .my-ads-grid,
    .my-ads-grid.single-item,
    .my-ads-grid.two-items,
    .my-ads-grid.three-items {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: 100%;
    }

    .in-cart-btn {
        padding: 8px 12px !important;
        font-size: 12px !important;
        min-width: 100px !important;
        border-radius: 8px !important;
    }

    .in-cart-btn i {
        font-size: 12px !important;
        margin-right: 2px !important;
    }
}

.modern-preview-popup-overlay {
    z-index: 10000 !important;
}

.modern-preview-popup-overlay .swal2-backdrop {
    background: rgba(0, 0, 0, 0.4) !important;
}

.modern-success-popup {
    border-radius: 20px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
}

.success-popup-header {
    text-align: center;
    margin-bottom: 20px;
}

.success-icon-wrapper {
    font-size: 4rem;
    margin-bottom: 15px;
    animation: bounce 1s ease-in-out;
}

.success-popup-content {
    text-align: center;
}

.success-features {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 8px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 14px;
    color: #495057;
}

.feature-item i {
    color: #28a745;
    font-size: 16px;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.modern-btn-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    color: white !important;
}

.modern-btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
}

.in-cart-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    border: 2px solid #10b981 !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-radius: 12px !important;
    padding: 12px 18px !important;
    font-size: 14px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
    min-width: 130px !important;
    cursor: pointer !important;
}

.in-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.in-cart-btn:hover {
    background: linear-gradient(135deg, #059669, #047857) !important;
    border-color: #059669 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.35) !important;
    color: white !important;
}

.in-cart-btn:hover::before {
    left: 100%;
}

.in-cart-btn:active {
    transform: translateY(0) !important;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.25) !important;
}

.in-cart-btn i {
    font-size: 16px !important;
    margin-right: 4px !important;
}

.in-cart-btn:disabled {
    background: linear-gradient(135deg, #6b7280, #4b5563) !important;
    border-color: #6b7280 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.2) !important;
}

.in-cart-btn:disabled:hover {
    background: linear-gradient(135deg, #6b7280, #4b5563) !important;
    transform: none !important;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.2) !important;
}

.in-cart-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.in-cart-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.amp-ad-preview {
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(67, 97, 238, 0.2);
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
}

.amp-ad-preview:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.3);
}

.amp-ad-preview.clickable {
    cursor: pointer;
}

.amp-ad-preview.clickable::after {
    content: '🔗';
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.amp-ad-preview.clickable:hover::after {
    opacity: 1;
}

.preview-info-section {
    background: var(--input-bg);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid var(--primary-color);
}

.preview-info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
}

.preview-info-row:last-child {
    margin-bottom: 0;
}

.preview-info-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-right: 10px;
    min-width: 120px;
}

.preview-info-value {
    flex: 1;
    font-family: monospace;
    background: rgba(67, 97, 238, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    word-break: break-all;
    font-size: 13px;
}

.preview-status-card {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    border: 1px solid;
}

.preview-status-card.ready {
    background: linear-gradient(135deg, #d4f5d4, #c8e6c9);
    border-color: #4caf50;
    color: #2e7d32;
}

.preview-status-card.incomplete {
    background: linear-gradient(135deg, #ffecb3, #fff3e0);
    border-color: #ff9800;
    color: #f57c00;
}

.preview-status-card.not-ready {
    background: linear-gradient(135deg, #ffebee, #fce4ec);
    border-color: #f44336;
    color: #d32f2f;
}

.price-hover-tooltip {
    border-bottom: 1px dashed var(--primary-color);
    transition: all 0.2s ease;
}

.price-hover-tooltip:hover {
    background: rgba(67, 97, 238, 0.05);
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}


</style>

<div class="my-ads-container">
    <div class="my-ads-hero-section">
        <div class="my-ads-hero-content">
            <h1 class="my-ads-hero-title">📺 ป้ายโฆษณาของฉัน</h1>
            <p class="my-ads-hero-subtitle">จัดการและติดตามประสิทธิภาพป้ายโฆษณาของคุณ</p>
            
            <?php
            $total_ads = count($user_positions);
            $active_ads = 0;
            $expired_ads = 0;
            $total_clicks = 0;
            $expiring_soon = 0;
            
            foreach ($user_positions as $position) {
                $expiration = $position->ownership['expiration_date'];
                $days_remaining = $expiration ? floor((strtotime($expiration) - time()) / (60 * 60 * 24)) : -1;
                
                if ($days_remaining >= 0) {
                    $active_ads++;
                    if ($days_remaining <= 7) {
                        $expiring_soon++;
                    }
                } else {
                    $expired_ads++;
                }
                
                $position_name = $position->name;
                $total_clicks += $formatted_clicks[$position_name] ?? 0;
            }
            ?>
            
            <div class="my-ads-stats-grid">
                <div class="my-ads-stat-card">
                    <span class="my-ads-stat-icon">📊</span>
                    <div class="my-ads-stat-number"><?php echo $total_ads; ?></div>
                    <div class="my-ads-stat-label">ทั้งหมด</div>
                </div>
                <div class="my-ads-stat-card">
                    <span class="my-ads-stat-icon">✅</span>
                    <div class="my-ads-stat-number"><?php echo $active_ads; ?></div>
                    <div class="my-ads-stat-label">ใช้งานอยู่</div>
                </div>
                <div class="my-ads-stat-card">
                    <span class="my-ads-stat-icon">👆</span>
                    <div class="my-ads-stat-number"><?php echo number_format($total_clicks); ?></div>
                    <div class="my-ads-stat-label">คลิกรวม</div>
                </div>
                <div class="my-ads-stat-card">
                    <span class="my-ads-stat-icon">⚠️</span>
                    <div class="my-ads-stat-number"><?php echo $expiring_soon; ?></div>
                    <div class="my-ads-stat-label">ใกล้หมดอายุ</div>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($user_positions)) : ?>
        <div class="my-ads-grid">
            <?php foreach ($user_positions as $position) :
                $position_name = $position->name;
                $ownership_state = $position_manager->get_position_ownership_state($position_name, $current_user_id);
                $expiration = $ownership_state['expiration_date'];
                $is_expired = $ownership_state['is_expired'];
                $days_remaining = $expiration ? floor((strtotime($expiration) - time()) / (60 * 60 * 24)) : -1;
                $clicks = $formatted_clicks[$position_name] ?? 0;

                if ($is_expired) {
                    $status_class = 'expired';
                    $status_text = 'หมดอายุแล้ว';
                } elseif ($days_remaining <= 7 && $days_remaining >= 0) {
                    $status_class = 'expiring';
                    $status_text = 'ใกล้หมดอายุ';
                } else {
                    $status_class = 'active';
                    $status_text = 'ใช้งานอยู่';
                }

            ?>

                <div class="my-ad-card <?php echo esc_attr($status_class); ?>" data-position="<?php echo esc_attr($position_name); ?>">
                    <div class="my-ad-header">
                        <h3><?php echo esc_html($position_name); ?></h3>
                        <span class="my-ad-status <?php echo esc_attr($status_class); ?>">
                            <?php if ($has_bypass && $is_expired) : ?>
                                <i class="fas fa-crown"></i> <?php echo esc_html($status_text); ?> (Bypass)
                            <?php else : ?>
                                <?php echo esc_html($status_text); ?>
                            <?php endif; ?>
                        </span>
                    </div>

                    <div class="my-ad-image clickable-image" data-position="<?php echo esc_attr($position_name); ?>" data-image="<?php echo esc_attr($position->image_url ?? ''); ?>" data-link="<?php echo esc_attr($position->target_url ?? ''); ?>" data-alt="<?php echo esc_attr($position->alt_text ?? ''); ?>">
                        <?php if (!empty($position->image_url)) : ?>
                            <img src="<?php echo esc_url($position->image_url); ?>" alt="<?php echo esc_attr($position->alt_text ?: $position_name); ?>" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                        <?php else : ?>
                            <div class="placeholder-image">
                                <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 10px; opacity: 0.7; color: #4361ee;"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="my-ad-details">
                        <div class="my-ad-detail">
                            <i class="fas fa-link"></i>
                            <span>
                                <?php if (!empty($position->target_url)) : ?>
                                    <a href="<?php echo esc_url($position->target_url); ?>" target="_blank" class="my-ad-link">
                                        <?php echo esc_html(substr($position->target_url, 0, 30) . (strlen($position->target_url) > 30 ? '...' : '')); ?>
                                    </a>
                                <?php else : ?>
                                    <span class="my-ad-no-link">ไม่มีลิงก์</span>
                                <?php endif; ?>
                            </span>
                        </div>

                        <div class="my-ad-detail">
                            <i class="fas fa-mouse-pointer"></i>
                            <span><?php echo esc_html($clicks); ?> คลิก</span>
                        </div>

                        <?php
                        $ad_cpc = isset($cpc_cpm_data['positions'][$position_name]) ? $cpc_cpm_data['positions'][$position_name]['cpc'] : 0;
                        $ad_cpm = isset($cpc_cpm_data['positions'][$position_name]) ? $cpc_cpm_data['positions'][$position_name]['cpm'] : 0;
                        ?>

                        <div class="my-ad-performance">
                            <i class="fas fa-chart-line"></i>
                            <span class="price-hover-tooltip" 
                                  data-cpc="<?php echo number_format($ad_cpc, 2, '.', ''); ?>" 
                                  data-cpm="<?php echo number_format($ad_cpm, 4, '.', ''); ?>"
                                  style="cursor: help; position: relative;">
                                CPC: <?php echo number_format($ad_cpc, 2); ?> USDT | CPM: <?php echo number_format($ad_cpm, 4); ?> USDT
                            </span>
                        </div>

                        <div class="my-ad-detail">
                            <i class="fas fa-calendar-alt"></i>
                            <span>
                                <?php
                                if ($days_remaining >= 0) {
                                    echo 'หมดอายุใน ' . esc_html($days_remaining) . ' วัน';
                                } else {
                                    echo 'หมดอายุแล้ว';
                                }
                                ?>
                            </span>
                        </div>

                        <?php if (!empty($position->alt_text)) : ?>
                        <div class="my-ad-detail">
                            <i class="fas fa-tag"></i>
                            <span>Alt: <?php echo esc_html($position->alt_text); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="my-ad-actions">
                        <?php if (empty($position->image_url)) : ?>
                        <button class="my-ad-btn upload-new-ad-btn" data-position="<?php echo esc_attr($position_name); ?>">
                            <i class="fas fa-upload"></i> อัพโหลด
                        </button>
                        <?php else : ?>
                        <button class="my-ad-btn edit-ad-btn" data-position="<?php echo esc_attr($position_name); ?>" data-image="<?php echo esc_attr($position->image_url); ?>" data-link="<?php echo esc_attr($position->target_url); ?>" data-alt="<?php echo esc_attr($position->alt_text); ?>">
                            <i class="fas fa-edit"></i> แก้ไข
                        </button>
                        <?php endif; ?>

                        <?php if ($has_bypass) : ?>
                            <button class="my-ad-btn cancel-ownership-btn" data-position="<?php echo esc_attr($position_name); ?>">
                                <i class="fas fa-times"></i> ยกเลิก
                            </button>
                        <?php else : ?>
                            <?php if ($days_remaining <= 7 && $days_remaining >= 0) : ?>
                                <button class="my-ad-btn renew-ad-btn" data-position="<?php echo esc_attr($position_name); ?>" data-price="217">
                                    <i class="fas fa-sync-alt"></i> ต่ออายุ
                                </button>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else : ?>
        <div class="my-ads-empty">
            <div class="my-ads-empty-icon">
                <i class="fas fa-ad"></i>
            </div>
            <h3>คุณยังไม่มีป้ายโฆษณา</h3>
            <p>เริ่มต้นซื้อป้ายโฆษณาเพื่อเพิ่มการมองเห็นของคุณ</p>
            <button class="my-ads-cta-btn ajax-nav-btn" data-tab="buy">ซื้อป้ายโฆษณา</button>
        </div>
    <?php endif; ?>
</div>

<script>
(function() {
    if (window.myAdsTabLoaded) {
        return;
    }
    window.myAdsTabLoaded = true;
    
    window.cleanupMyAdsTab = function() {
        if (window.MyAdsTooltip) {
            window.MyAdsTooltip.cleanup();
        }
        window.cachedThbRate = null;
        window.cachedThbRateTime = null;
        window.myAdsTabLoaded = false;
        window.myAdsTabInitialized = false;
        window.cleanupCurrentTab = null;
    };

    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function waitForJQuery() {
        if (typeof jQuery !== 'undefined') {
            initMyAdsTabWithJQuery(jQuery);
        } else {
            setTimeout(waitForJQuery, 100);
        }
    }

    function initMyAdsTabWithJQuery($) {
        'use strict';
        function initMyAdsTab() {
            if (window.myAdsTabInitialized) {
                return;
            }
            window.myAdsTabInitialized = true;
    
            if (typeof window.adDashboardData === 'undefined' && typeof adDashboardData === 'undefined') {
                console.warn('adDashboardData not found, using fallback values');
            }

            if (typeof window.Swal === 'undefined') {
                console.warn('SweetAlert2 not found, using fallback alerts');
                window.Swal = {
                    fire: function(options) {
                        if (typeof options === 'string') {
                            alert(options);
                        } else if (options && options.title) {
                            alert(options.title + (options.text ? '\n' + options.text : ''));
                        }
                        return Promise.resolve({ isConfirmed: confirm('Continue?') });
                    }
                };
            }

        $(document).off('click', '.edit-ad-btn').on('click', '.edit-ad-btn', function(e) {
            e.preventDefault();
            e.stopImmediatePropagation();
            const position = $(this).data('position');
            const currentImage = $(this).data('image') || '';
            const currentLink = $(this).data('link') || '';
            const currentAlt = $(this).data('alt') || '';
            showEditPopup(position, currentImage, currentLink, currentAlt);
        });

        function showEditPopup(position, currentImage, currentLink, currentAlt) {
            window.currentUploadedImage = null;
            const positionData = getPositionDetails(position);

            window.Swal.fire({
                title: `<i class="fas fa-edit"></i> แก้ไขโฆษณา`,
                html: `
                    <div class="horizontal-upload-container">
                        <div class="upload-left-section">
                            <div class="position-info-card">
                                <h4><i class="fas fa-info-circle"></i> ข้อมูลตำแหน่ง: ${position}</h4>
                                <div class="info-details">
                                    <div class="info-row">
                                        <span class="info-label">ขนาด:</span>
                                        <span class="info-value">${positionData.width} × ${positionData.height} px</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">ขนาดไฟล์สูงสุด:</span>
                                        <span class="info-value">200 KB</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">รูปแบบที่รองรับ:</span>
                                        <span class="info-value">JPG, PNG, GIF, WebP, APNG</span>
                                    </div>
                                </div>
                            </div>
                            <div class="current-image-section">
                                <label class="section-label">รูปภาพปัจจุบัน:</label>
                                <div class="current-image-display" style="width: 100%; max-height: 200px; border: 2px solid #ffc107; border-radius: 8px; overflow: hidden; background: #f8f9fa; display: flex; align-items: center; justify-content: center; cursor: ${currentImage ? 'pointer' : 'default'}; position: relative;" ${currentImage ? `onclick="showImagePreview('${currentImage}', '${currentLink}', '${currentAlt}', '${position}')"` : ''}>
                                    ${currentImage ?
                                        `<img src="${currentImage}" alt="Current" style="width: 100%; height: 100%; object-fit: cover;">
                                         <div class="preview-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.7); display: flex; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.3s ease;">
                                            <div style="text-align: center; color: white;">
                                                <i class="fas fa-eye" style="font-size: 24px; margin-bottom: 8px;"></i>
                                                <div style="font-size: 14px; font-weight: 600;">คลิกเพื่อดูตัวอย่าง</div>
                                            </div>
                                         </div>` :
                                        `<div style="text-align: center; color: #6c757d;">
                                            <i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5; color: #ffc107;"></i>
                                            <div style="font-size: 14px; font-weight: 500;">ไม่มีรูปภาพ</div>
                                            <small style="font-size: 12px; color: #6c757d; margin-top: 5px; display: block;">อัพโหลดรูปภาพใหม่ด้านขวา</small>
                                        </div>`
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="upload-right-section">
                            <div class="upload-area">
                                <input type="file" id="ad-image-upload" accept=".jpg,.jpeg,.png,.gif,.webp,.apng" style="display: none;">
                                <label class="modern-upload-label" for="ad-image-upload">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #4361ee; margin-bottom: 10px;"></i>
                                    <span style="font-size: 16px; font-weight: 500; color: #6c757d;">เลือกรูปภาพใหม่ (ถ้าต้องการเปลี่ยน)</span>
                                    <small style="font-size: 12px; color: #6c757d; margin-top: 5px; display: block;">รองรับ JPG, PNG, GIF, WebP, APNG (สูงสุด 200KB)</small>
                                </label>
                                <div class="upload-preview" id="upload-preview" style="display: none; width: 100%; height: 200px; border: 2px solid #28a745; border-radius: 8px; overflow: hidden; position: relative; margin-top: 15px; background: #f8f9fa;">
                                    <img id="preview-image" src="#" alt="Preview" style="width: 100%; height: 100%; object-fit: contain; display: block;">
                                    <button type="button" class="remove-preview" onclick="removePreview()" style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #dc3545;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="preview-btn" onclick="previewAd()" style="position: absolute; bottom: 10px; right: 10px; background: #4361ee; color: white; border: none; border-radius: 6px; padding: 8px 12px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                        <i class="fas fa-eye"></i> พรีวิว
                                    </button>
                                </div>
                                <div class="upload-hint">
                                    <small><i class="fas fa-exclamation-triangle"></i> ขนาดไฟล์ไม่เกิน 200 KB</small>
                                </div>
                            </div>
                            <div class="form-fields">
                                <div class="field-group">
                                    <label for="ad-link-input">
                                        <i class="fas fa-link"></i> ลิงก์เป้าหมาย
                                    </label>
                                    <input type="url" id="ad-link-input" value="${currentLink}" placeholder="https://example.com" class="modern-input">
                                </div>
                                <div class="field-group">
                                    <label for="ad-alt-input">
                                        <i class="fas fa-tag"></i> ข้อความ Alt (SEO)
                                    </label>
                                    <input type="text" id="ad-alt-input" value="${currentAlt}" placeholder="คำอธิบายรูปภาพ" class="modern-input">
                                </div>
                                <div class="field-group">
                                    <button type="button" class="modern-preview-btn" onclick="window.previewAd('${position}', '${currentImage}')">
                                        <i class="fas fa-eye"></i> ดูตัวอย่าง
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                width: '900px',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save"></i> บันทึก',
                cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
                customClass: {
                    popup: 'horizontal-upload-popup',
                    confirmButton: 'modern-btn modern-btn-primary',
                    cancelButton: 'modern-btn modern-btn-secondary'
                },
                buttonsStyling: false,
                didOpen: () => {
                    initUploadHandlers();

                    const currentImageDisplay = document.querySelector('.current-image-display');
                    if (currentImageDisplay) {
                        const overlay = currentImageDisplay.querySelector('.preview-overlay');
                        if (overlay) {
                            currentImageDisplay.addEventListener('mouseenter', function() {
                                overlay.style.opacity = '1';
                            });
                            currentImageDisplay.addEventListener('mouseleave', function() {
                                overlay.style.opacity = '0';
                            });
                        }
                    }

                    const uploadPlaceholder = document.querySelector('.upload-placeholder');
                    if (uploadPlaceholder) {
                        uploadPlaceholder.innerHTML = `
                            <div class="placeholder-content" style="text-align: center; color: #6c757d;">
                                <i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5; color: #4361ee;"></i>
                                <div style="font-size: 14px; font-weight: 500;">ตัวอย่างจะแสดงที่นี่</div>
                                <small style="font-size: 12px; color: #6c757d; margin-top: 5px; display: block;">เลือกรูปภาพเพื่อดูตัวอย่าง</small>
                            </div>
                        `;
                        uploadPlaceholder.style.border = '2px dashed #dee2e6';
                    }
                },
                preConfirm: () => {
                    return validateAndGetEditData(currentImage);
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    uploadAdData(position, result.value);
                }
            });
        }

        $(document).off('click', '.renew-ad-btn').on('click', '.renew-ad-btn', function(e) {
            e.preventDefault();
            const position = $(this).data('position');
            const basePrice = parseFloat($(this).data('price')) || 217;
            if (typeof window.showDurationPopup === 'function') {
                window.showDurationPopup(position, basePrice, $(this), true);
            }
        });

        $(document).off('click', '.cancel-ownership-btn').on('click', '.cancel-ownership-btn', function(e) {
            e.preventDefault();
            const position = $(this).data('position');
            showModernCancelPopup(position);
        });

        function showModernCancelPopup(position) {
            window.Swal.fire({
                title: `<div class="cancel-popup-header">
                    <div class="cancel-icon-wrapper">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>ยืนยันการยกเลิก</h3>
                </div>`,
                html: `
                    <div class="cancel-popup-content">
                        <div class="cancel-info-card">
                            <div class="cancel-position-info">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>ตำแหน่ง: <strong>${position}</strong></span>
                            </div>
                            <div class="cancel-warning-text">
                                <p>การยกเลิกจะทำให้:</p>
                                <ul>
                                    <li><i class="fas fa-times-circle"></i> โฆษณาหยุดแสดงทันที</li>
                                    <li><i class="fas fa-ban"></i> ไม่สามารถกู้คืนได้</li>
                                    <li><i class="fas fa-money-bill-wave"></i> ไม่มีการคืนเงิน</li>
                                </ul>
                            </div>
                        </div>
                        <div class="cancel-confirmation">
                            <p class="cancel-question">คุณแน่ใจหรือไม่ที่จะยกเลิกโฆษณานี้?</p>
                        </div>
                    </div>
                `,
                width: '500px',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-trash-alt"></i> ยืนยันยกเลิก',
                cancelButtonText: '<i class="fas fa-arrow-left"></i> กลับ',
                customClass: {
                    popup: 'modern-cancel-popup',
                    confirmButton: 'modern-btn modern-btn-danger',
                    cancelButton: 'modern-btn modern-btn-light'
                },
                buttonsStyling: false,
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    window.Swal.fire({
                        title: 'กำลังยกเลิก...',
                        html: '<div class="loading-progress"><div class="progress-bar"><div class="progress-fill"></div></div><p>กรุณารอสักครู่</p></div>',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        customClass: {
                            popup: 'modern-loading-popup'
                        }
                    });

                    $.ajax({
                        url: window.adDashboardData ? window.adDashboardData.ajaxurl : '/wp-admin/admin-ajax.php',
                        type: 'POST',
                        data: {
                            action: 'cancel_ad_ownership',
                            position: position,
                            security: window.adDashboardData ? window.adDashboardData.nonce : ''
                        },
                        success: function(response) {
                            if (response.success) {
                                window.Swal.fire({
                                    title: `<div class="success-popup-header">
                                        <div class="success-icon-wrapper">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <h3>ยกเลิกสำเร็จ!</h3>
                                    </div>`,
                                    html: `
                                        <div class="success-popup-content">
                                            <p>ยกเลิกความเป็นเจ้าของโฆษณาตำแหน่ง <strong>${position}</strong> เรียบร้อยแล้ว</p>
                                            <div class="success-note">
                                                <i class="fas fa-info-circle"></i>
                                                <span>หน้าจะรีเฟรชอัตโนมัติ</span>
                                            </div>
                                        </div>
                                    `,
                                    timer: 2000,
                                    timerProgressBar: true,
                                    showConfirmButton: false,
                                    customClass: {
                                        popup: 'modern-success-popup'
                                    }
                                }).then(() => {
                                    if (typeof window.loadTab === 'function') {
                                        window.loadTab('my-ads');
                                    } else {
                                        location.reload();
                                    }
                                });
                            } else {
                                window.Swal.fire({
                                    title: `<div class="error-popup-header">
                                        <div class="error-icon-wrapper">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </div>
                                        <h3>เกิดข้อผิดพลาด</h3>
                                    </div>`,
                                    html: `
                                        <div class="error-popup-content">
                                            <div class="error-message">
                                                <p>${(response.data && response.data.message) || response.message || 'ไม่สามารถยกเลิกโฆษณาได้'}</p>
                                                <div class="error-suggestion">
                                                    <p>กรุณาลองใหม่อีกครั้ง หรือติดต่อผู้ดูแลระบบ</p>
                                                </div>
                                            </div>
                                        </div>
                                    `,
                                    confirmButtonText: '<i class="fas fa-check"></i> ตกลง',
                                    customClass: {
                                        popup: 'modern-error-popup',
                                        confirmButton: 'modern-btn modern-btn-danger'
                                    },
                                    buttonsStyling: false,
                                    width: '450px'
                                });
                            }
                        },
                        error: function() {
                            window.Swal.fire({
                                title: `<div class="error-popup-header">
                                    <div class="error-icon-wrapper">
                                        <i class="fas fa-wifi"></i>
                                    </div>
                                    <h3>ปัญหาการเชื่อมต่อ</h3>
                                </div>`,
                                html: `
                                    <div class="error-popup-content">
                                        <div class="error-message">
                                            <p>ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้</p>
                                            <div class="error-suggestion">
                                                <p>กรุณาตรวจสอบการเชื่อมต่ออินเทอร์เน็ต และลองใหม่อีกครั้ง</p>
                                            </div>
                                        </div>
                                    </div>
                                `,
                                confirmButtonText: '<i class="fas fa-check"></i> ตกลง',
                                customClass: {
                                    popup: 'modern-error-popup',
                                    confirmButton: 'modern-btn modern-btn-danger'
                                },
                                buttonsStyling: false,
                                width: '450px'
                            });
                        }
                    });
                }
            });
        }

        $(document).off('click', '.clickable-image').on('click', '.clickable-image', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const position = $(this).data('position');
            const hasImage = $(this).data('image');
            if (hasImage) {
                showEditPopup(position, $(this).data('image'), $(this).data('link'), $(this).data('alt'));
            } else {
                showUploadPopup(position);
            }
        });

        $(document).off('click', '.upload-new-ad-btn').on('click', '.upload-new-ad-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const position = $(this).data('position');
            showUploadPopup(position);
        });

        function showUploadPopup(position) {
            window.currentUploadedImage = null;
            const positionData = getPositionDetails(position);
            window.Swal.fire({
                title: `<i class="fas fa-upload"></i> อัพโหลดโฆษณา`,
                html: `
                    <div class="horizontal-upload-container">
                        <div class="upload-left-section">
                            <div class="position-info-card">
                                <h4><i class="fas fa-info-circle"></i> ข้อมูลตำแหน่ง: ${position}</h4>
                                <div class="info-details">
                                    <div class="info-row">
                                        <span class="info-label">ขนาด:</span>
                                        <span class="info-value">${positionData.width} × ${positionData.height} px</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">ขนาดไฟล์สูงสุด:</span>
                                        <span class="info-value">200 KB</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">รูปแบบที่รองรับ:</span>
                                        <span class="info-value">JPG, PNG, GIF, WebP, APNG</span>
                                    </div>
                                </div>
                            </div>
                            <div class="upload-placeholder" style="width: 100%; height: 200px; border: 2px dashed var(--border-color); border-radius: 8px; background: var(--input-bg); display: flex; align-items: center; justify-content: center;">
                                <div class="placeholder-content" style="text-align: center; color: var(--muted-text);">
                                    <i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5; color: var(--primary-color);"></i>
                                    <div style="font-size: 14px; font-weight: 500;">ตัวอย่างจะแสดงที่นี่</div>
                                    <small style="font-size: 12px; color: var(--muted-text); margin-top: 5px; display: block;">เลือกรูปภาพเพื่อดูตัวอย่าง</small>
                                </div>
                            </div>
                        </div>
                        <div class="upload-right-section">
                            <div class="upload-area">
                                <input type="file" id="ad-image-upload" accept=".jpg,.jpeg,.png,.gif,.webp,.apng" style="display: none;">
                                <label class="modern-upload-label" for="ad-image-upload">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: var(--primary-color); margin-bottom: 10px;"></i>
                                    <span style="font-size: 16px; font-weight: 500; color: var(--text-color);">เลือกรูปภาพ</span>
                                    <small style="font-size: 12px; color: var(--muted-text); margin-top: 5px; display: block;">รองรับ JPG, PNG, GIF, WebP, APNG (สูงสุด 200KB)</small>
                                </label>
                                <div class="upload-preview" id="upload-preview" style="display: none; width: 100%; height: 200px; border: 2px solid var(--success-color); border-radius: 8px; overflow: hidden; position: relative; margin-top: 15px; background: var(--input-bg);">
                                    <img id="preview-image" src="#" alt="Preview" style="width: 100%; height: 100%; object-fit: contain; display: block;">
                                    <button type="button" class="remove-preview" onclick="removePreview()" style="position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); border: none; border-radius: 50%; width: 30px; height: 30px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: var(--error-color);">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="preview-btn" onclick="previewAd()" style="position: absolute; bottom: 10px; right: 10px; background: var(--primary-color); color: white; border: none; border-radius: 6px; padding: 8px 12px; font-size: 12px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                        <i class="fas fa-eye"></i> พรีวิว
                                    </button>
                                </div>
                                <div class="upload-hint">
                                    <small><i class="fas fa-exclamation-triangle"></i> ขนาดไฟล์ไม่เกิน 200 KB</small>
                                </div>
                            </div>
                            <div class="form-fields">
                                <div class="field-group">
                                    <label for="ad-link-input">
                                        <i class="fas fa-link"></i> ลิงก์เป้าหมาย
                                    </label>
                                    <input type="url" id="ad-link-input" placeholder="https://example.com" class="modern-input">
                                </div>
                                <div class="field-group">
                                    <label for="ad-alt-input">
                                        <i class="fas fa-tag"></i> ข้อความ Alt (SEO)
                                    </label>
                                    <input type="text" id="ad-alt-input" placeholder="คำอธิบายรูปภาพ" class="modern-input">
                                </div>
                                <div class="field-group">
                                    <button type="button" class="modern-preview-btn" onclick="window.previewAd('${position}')">
                                        <i class="fas fa-eye"></i> ดูตัวอย่าง
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `,
                width: '900px',
                showCancelButton: true,
                confirmButtonText: '<i class="fas fa-save"></i> บันทึก',
                cancelButtonText: '<i class="fas fa-times"></i> ยกเลิก',
                customClass: {
                    popup: 'horizontal-upload-popup',
                    confirmButton: 'modern-btn modern-btn-primary',
                    cancelButton: 'modern-btn modern-btn-secondary'
                },
                buttonsStyling: false,
                didOpen: () => {
                    initUploadHandlers();

                    const uploadPlaceholder = document.querySelector('.upload-placeholder');
                    if (uploadPlaceholder) {
                                            uploadPlaceholder.innerHTML = `
                        <div class="placeholder-content" style="text-align: center; color: var(--muted-text);">
                            <i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5; color: var(--primary-color);"></i>
                            <div style="font-size: 14px; font-weight: 500;">ตัวอย่างจะแสดงที่นี่</div>
                            <small style="font-size: 12px; color: var(--muted-text); margin-top: 5px; display: block;">เลือกรูปภาพเพื่อดูตัวอย่าง</small>
                        </div>
                    `;
                    uploadPlaceholder.style.border = '2px dashed var(--border-color)';
                    }

                    const uploadPreview = document.getElementById('upload-preview');
                    const uploadLabel = document.querySelector('.modern-upload-label');
                    if (uploadPreview) uploadPreview.style.display = 'none';
                    if (uploadLabel) uploadLabel.style.display = 'flex';
                },
                preConfirm: () => {
                    return validateAndGetUploadData();
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    uploadAdData(position, result.value);
                }
            });
        }

        window.getPositionDetails = function(position) {
            if (window.positionDimensions && window.positionDimensions[position]) {
                return window.positionDimensions[position];
            }
            return { width: 300, height: 250 };
        };

        window.getCurrentUserId = function() {
            return window.adDashboardData?.userId || 0;
        };



        window.getExchangeRate = function() {
            return new Promise((resolve) => {
                if (window.cachedThbRate && window.cachedThbRateTime && (Date.now() - window.cachedThbRateTime) < 300000) {
                    resolve(window.cachedThbRate);
                    return;
                }

                $.ajax({
                    url: window.adDashboardData ? window.adDashboardData.ajaxurl : '/wp-admin/admin-ajax.php',
                    type: 'POST',
                    data: {
                        action: 'get_exchange_rate',
                        security: window.adDashboardData ? window.adDashboardData.nonce : ''
                    },
                    success: function(response) {
                        const rate = (response.success && response.data.rate) ? parseFloat(response.data.rate) : 35.5;
                        window.cachedThbRate = rate;
                        window.cachedThbRateTime = Date.now();
                        resolve(rate);
                    },
                    error: function() {
                        const fallbackRate = 35.5;
                        window.cachedThbRate = fallbackRate;
                        window.cachedThbRateTime = Date.now();
                        resolve(fallbackRate);
                    }
                });
            });
        };

        function initUploadHandlers() {
            const fileInput = document.getElementById('ad-image-upload');
            const uploadLabel = document.querySelector('.upload-label');
            const preview = document.getElementById('upload-preview');
            const previewImage = document.getElementById('preview-image');
            if (!fileInput) return;
            fileInput.removeAttribute('onchange');
            const newFileInput = fileInput.cloneNode(true);
            fileInput.parentNode.replaceChild(newFileInput, fileInput);
            newFileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    if (file.size > 204800) {
                        window.Swal.showValidationMessage('ขนาดไฟล์เกิน 200 KB กรุณาเลือกไฟล์ที่เล็กกว่า');
                        e.target.value = '';
                        return;
                    }

                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/apng'];
                    if (!allowedTypes.includes(file.type)) {
                        window.Swal.showValidationMessage('รูปแบบไฟล์ไม่ถูกต้อง รองรับเฉพาะ JPG, PNG, GIF, WebP, APNG');
                        e.target.value = '';
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(readerEvent) {
                        window.currentUploadedImage = readerEvent.target.result;

                        const currentPreviewImage = document.getElementById('preview-image');
                        const currentPreview = document.getElementById('upload-preview');
                        const currentUploadLabel = document.querySelector('.modern-upload-label');
                        const uploadPlaceholder = document.querySelector('.upload-placeholder');

                        if (currentPreviewImage) {
                            currentPreviewImage.src = readerEvent.target.result;
                        }
                        if (currentPreview) {
                            currentPreview.style.display = 'block';
                        }
                        if (currentUploadLabel) {
                            currentUploadLabel.style.display = 'none';
                        }

                        if (uploadPlaceholder) {
                            uploadPlaceholder.innerHTML = `
                                <img src="${readerEvent.target.result}" alt="Preview" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">
                            `;
                            uploadPlaceholder.style.border = '2px solid #28a745';
                        }
                    };
                    reader.readAsDataURL(file);
                }
            });
        }

        window.removePreview = function() {
            const fileInput = document.getElementById('ad-image-upload');
            const uploadLabel = document.querySelector('.modern-upload-label');
            const preview = document.getElementById('upload-preview');
            const uploadPlaceholder = document.querySelector('.upload-placeholder');

            fileInput.value = '';
            window.currentUploadedImage = null;
            if (preview) preview.style.display = 'none';
            if (uploadLabel) uploadLabel.style.display = 'flex';

            if (uploadPlaceholder) {
                uploadPlaceholder.innerHTML = `
                    <div class="placeholder-content" style="text-align: center; color: var(--muted-text);">
                        <i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.5; color: var(--primary-color);"></i>
                        <div style="font-size: 14px; font-weight: 500;">ตัวอย่างจะแสดงที่นี่</div>
                        <small style="font-size: 12px; color: var(--muted-text); margin-top: 5px; display: block;">เลือกรูปภาพเพื่อดูตัวอย่าง</small>
                    </div>
                `;
                uploadPlaceholder.style.border = '2px dashed var(--border-color)';
            }
        };

        function validateAndGetUploadData() {
            const fileInput = document.getElementById('ad-image-upload');
            const linkInput = document.getElementById('ad-link-input');
            const altInput = document.getElementById('ad-alt-input');

            if (!fileInput.files[0]) {
                window.Swal.showValidationMessage('กรุณาเลือกรูปภาพ');
                return false;
            }

            if (!linkInput.value.trim()) {
                window.Swal.showValidationMessage('กรุณาใส่ลิงก์เป้าหมาย');
                return false;
            }

            if (!altInput.value.trim()) {
                window.Swal.showValidationMessage('กรุณาใส่ข้อความ Alt');
                return false;
            }

            return {
                file: fileInput.files[0],
                link: linkInput.value.trim(),
                alt: altInput.value.trim()
            };
        }

        function validateAndGetEditData(currentImage) {
            const fileInput = document.getElementById('ad-image-upload');
            const linkInput = document.getElementById('ad-link-input');
            const altInput = document.getElementById('ad-alt-input');

            if (!linkInput.value.trim()) {
                window.Swal.showValidationMessage('กรุณาใส่ลิงก์เป้าหมาย');
                return false;
            }

            if (!altInput.value.trim()) {
                window.Swal.showValidationMessage('กรุณาใส่ข้อความ Alt');
                return false;
            }

            const result = {
                link: linkInput.value.trim(),
                alt: altInput.value.trim(),
                hasNewImage: false
            };

            if (fileInput.files[0]) {
                result.file = fileInput.files[0];
                result.hasNewImage = true;
            } else {
                result.currentImage = currentImage;
            }

            return result;
        }

        window.previewUploadAd = function(position) {
            const linkInput = document.getElementById('ad-link-input');
            const altInput = document.getElementById('ad-alt-input');
            const fileInput = document.getElementById('ad-image-upload');

            let imageSrc = null;
            let link = '';
            let alt = '';

            if (linkInput) link = linkInput.value;
            if (altInput) alt = altInput.value;

            if (window.currentUploadedImage) {
                showPreviewPopup(position, window.currentUploadedImage, link, alt);
                return;
            }

            if (fileInput && fileInput.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    showPreviewPopup(position, e.target.result, link, alt);
                };
                reader.readAsDataURL(fileInput.files[0]);
                return;
            }

            const adCard = document.querySelector(`[data-position="${position}"]`);
            if (adCard) {
                const existingImg = adCard.querySelector('.my-ad-image img');
                if (existingImg) {
                    imageSrc = existingImg.src;
                    if (!alt) alt = existingImg.alt;
                }

                const existingLinkEl = adCard.querySelector('.my-ad-link');
                if (existingLinkEl && !link) {
                    link = existingLinkEl.href;
                }
            }

            if (imageSrc || link) {
                showPreviewPopup(position, imageSrc, link, alt);
            } else {
                if (typeof showMiniPopup === 'function') {
                    showMiniPopup('กรุณาเลือกรูปภาพหรือใส่ลิงก์ก่อนดูตัวอย่าง', 'warning');
                } else {
                    alert('กรุณาเลือกรูปภาพหรือใส่ลิงก์ก่อนดูตัวอย่าง');
                }
            }
        };

        window.showImagePreview = function(imageSrc, link, alt, position) {
            if (!position) {
                const clickedElement = event?.target;
                const adCard = clickedElement?.closest('.my-ad-card');
                position = adCard?.dataset.position || 'unknown';
            }
            showPreviewPopup(position, imageSrc, link, alt);
        };

        window.previewAd = function(position, currentImage = '') {
            const linkInput = document.getElementById('ad-link-input');
            const altInput = document.getElementById('ad-alt-input');
            const link = linkInput ? linkInput.value.trim() : '';
            const alt = altInput ? altInput.value.trim() : '';

            let imageToPreview = '';

            if (window.currentUploadedImage) {
                imageToPreview = window.currentUploadedImage;
            } else if (currentImage) {
                imageToPreview = currentImage;
            }
            
            showPreviewPopup(position, imageToPreview, link, alt);
        };

        function showPreviewPopup(position, imageSrc, link, alt) {
            const positionData = window.getPositionDetails(position);
            const displayAlt = alt || position;
            const hasValidImage = imageSrc && imageSrc !== '#' && imageSrc !== '' && imageSrc !== 'undefined';
            const trackingUrl = window.location.origin + '/dynamic-link.php';
            const params = new URLSearchParams({
                position: position,
                url: encodeURIComponent(link || window.location.origin)
            });
            const finalTrackingUrl = escapeHtml(`${trackingUrl}?${params.toString()}`);
            const escapedLink = escapeHtml(link);
            const escapedImageSrc = escapeHtml(imageSrc);
            const escapedDisplayAlt = escapeHtml(displayAlt);
            const escapedPosition = escapeHtml(position);

            const previewHtml = `
                <div style="text-align: center; padding: 20px; background: var(--card-bg); color: var(--text-color);">
                    <div style="margin-bottom: 20px;">
                        <h4 style="color: var(--text-color); margin-bottom: 10px;">ขนาดจริง: ${positionData.width} × ${positionData.height} px</h4>
                        <p style="color: var(--muted-text); font-size: 14px; margin-bottom: 20px;">คลิกที่โฆษณาเพื่อทดสอบลิงก์ (จะนับสถิติจริง)</p>
                    </div>
                    <div style="text-align: center; margin: 20px 0; padding: 20px; background: var(--input-bg); border-radius: 12px; overflow-x: auto;">
                        ${link ? `<a href="${finalTrackingUrl}" target="_blank" rel="nofollow sponsored" class="amp-ad-link" data-position="${escapedPosition}" style="display: inline-block; text-decoration: none;">` : '<div style="display: inline-block;">'}
                            <div class="amp-ad-preview ${link ? 'clickable' : ''}" style="width: ${positionData.width}px; height: ${positionData.height}px; margin: 0 auto;">
                                ${hasValidImage ? `<img src="${escapedImageSrc}" alt="${escapedDisplayAlt}" style="width: ${positionData.width}px; height: ${positionData.height}px; object-fit: cover; display: block;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div style="width: 100%; height: 100%; display: none; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; flex-direction: column; font-family: Arial, sans-serif;"><i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.7;"></i><span style="font-size: 14px; font-weight: 500;">${escapedPosition}</span><small style="font-size: 12px; opacity: 0.8; margin-top: 5px;">${positionData.width} × ${positionData.height}</small></div>` : `<div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; flex-direction: column; font-family: Arial, sans-serif;"><i class="fas fa-image" style="font-size: 48px; margin-bottom: 10px; opacity: 0.7;"></i><span style="font-size: 14px; font-weight: 500;">${escapedPosition}</span><small style="font-size: 12px; opacity: 0.8; margin-top: 5px;">${positionData.width} × ${positionData.height}</small></div>`}
                            </div>
                        ${link ? '</a>' : '</div>'}
                    </div>
                    <div class="preview-info-section">
                        <div class="preview-info-row">
                            <span class="preview-info-label">📍 ตำแหน่ง:</span>
                            <span class="preview-info-value">${position}</span>
                        </div>
                        <div class="preview-info-row">
                            <span class="preview-info-label">📐 ขนาด:</span>
                            <span class="preview-info-value">${positionData.width} × ${positionData.height} px</span>
                        </div>
                        ${link ? `
                            <div class="preview-info-row">
                                <span class="preview-info-label">🔗 ลิงก์เป้าหมาย:</span>
                                <span class="preview-info-value">${escapedLink}</span>
                            </div>
                            <div class="preview-info-row">
                                <span class="preview-info-label">📊 ลิงก์ติดตาม:</span>
                                <span class="preview-info-value">${finalTrackingUrl}</span>
                            </div>
                        ` : `
                            <div class="preview-info-row">
                                <span class="preview-info-label">⚠️ ลิงก์:</span>
                                <span style="color: var(--warning-color); font-weight: 500;">ไม่มีลิงก์เป้าหมาย</span>
                            </div>
                        `}
                        ${alt ? `
                            <div class="preview-info-row">
                                <span class="preview-info-label">📝 ข้อความ Alt:</span>
                                <span class="preview-info-value">${escapeHtml(alt)}</span>
                            </div>
                        ` : ''}
                    </div>
                    <div class="preview-status-card ${(hasValidImage && link) ? 'ready' : 'incomplete'}">
                        ${(hasValidImage && link) ? 
                            '✅ <strong>โฆษณาพร้อมใช้งาน</strong><br><small>มีรูปภาพและลิงก์เป้าหมาย จะนับสถิติการคลิกจริง</small>' : 
                            hasValidImage ? 
                                '⚠️ <strong>โฆษณายังไม่สมบูรณ์</strong><br><small>มีรูปภาพแล้ว แต่ควรเพิ่มลิงก์เป้าหมายเพื่อให้ผู้ใช้สามารถคลิกได้</small>' :
                                link ?
                                    '⚠️ <strong>โฆษณายังไม่สมบูรณ์</strong><br><small>มีลิงก์แล้ว แต่ยังไม่มีรูปภาพโฆษณา</small>' :
                                    '❌ <strong>โฆษณายังไม่พร้อม</strong><br><small>ยังไม่มีทั้งรูปภาพและลิงก์เป้าหมาย</small>'
                        }
                    </div>
                </div>
            `;

            showPreviewWindow(imageSrc, link, displayAlt, `ตัวอย่างโฆษณา - ${position}`, previewHtml);
        }

        function showPreviewWindow(imageSrc, link, alt, title, customHtml) {
            const escapedImageSrc = escapeHtml(imageSrc);
            const escapedLink = escapeHtml(link);
            const escapedAlt = escapeHtml(alt);
            const htmlContent = customHtml || `
                <div style="text-align: center; padding: 20px; background: var(--card-bg); color: var(--text-color);">
                    <div style="margin-bottom: 20px; background: var(--input-bg); padding: 20px; border-radius: 12px;">
                        <img src="${escapedImageSrc}" alt="${escapedAlt}"
                             style="max-width: 100%; max-height: 400px; object-fit: contain; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    </div>
                    ${link ? `
                        <div style="margin-bottom: 15px; text-align: left;">
                            <strong>🔗 ลิงก์:</strong>
                            <a href="${escapedLink}" target="_blank" style="color: var(--primary-color); text-decoration: none; word-break: break-all;">${escapedLink}</a>
                        </div>
                    ` : ''}
                    ${alt ? `
                        <div style="margin-bottom: 15px; text-align: left;">
                            <strong>📝 ข้อความ Alt:</strong> ${escapedAlt}
                        </div>
                    ` : ''}
                    <div style="background: var(--info-bg); padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <small style="color: var(--info-color);">
                            <i class="fas fa-info-circle"></i>
                            นี่คือตัวอย่างการแสดงผลโฆษณาของคุณ
                        </small>
                    </div>
                </div>
            `;

            const previewOverlay = document.createElement('div');
            previewOverlay.className = 'custom-preview-overlay';
            previewOverlay.innerHTML = `
                <div class="custom-preview-popup">
                    <div class="custom-preview-header">
                        <h2><i class="fas fa-eye"></i> ${title}</h2>
                        <button class="custom-preview-close" onclick="this.closest('.custom-preview-overlay').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="custom-preview-content">
                        ${htmlContent}
                    </div>
                </div>
            `;

            const style = document.createElement('style');
            style.textContent = `
                .custom-preview-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 999999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    animation: fadeIn 0.3s ease;
                }

                .custom-preview-popup {
                    background: var(--card-bg);
                    border-radius: 20px;
                    max-width: 90vw;
                    max-height: 90vh;
                    overflow-y: auto;
                    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
                    position: relative;
                    animation: slideIn 0.3s ease;
                }

                .custom-preview-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 20px 30px;
                    border-radius: 20px 20px 0 0;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .custom-preview-header h2 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: 700;
                }

                .custom-preview-close {
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    color: white;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    cursor: pointer;
                    font-size: 18px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: all 0.3s ease;
                }

                .custom-preview-close:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: scale(1.1);
                }

                .custom-preview-content {
                    padding: 30px;
                    color: #333;
                }

                .custom-preview-content img {
                    border-radius: 12px;
                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                }

                .custom-preview-content a {
                    color: #667eea;
                    text-decoration: none;
                    font-weight: 500;
                }

                .custom-preview-content a:hover {
                    text-decoration: underline;
                }

                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }

                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px) scale(0.9);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0) scale(1);
                    }
                }

                @media (max-width: 768px) {
                    .custom-preview-popup {
                        max-width: 95vw;
                        max-height: 95vh;
                    }

                    .custom-preview-header {
                        padding: 15px 20px;
                    }

                    .custom-preview-header h2 {
                        font-size: 20px;
                    }

                    .custom-preview-content {
                        padding: 20px;
                    }
                }
            `;

            document.head.appendChild(style);
            document.body.appendChild(previewOverlay);

            previewOverlay.addEventListener('click', function(e) {
                if (e.target === previewOverlay) {
                    previewOverlay.remove();
                    style.remove();
                }
            });

            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    previewOverlay.remove();
                    style.remove();
                }
            });
        }

        function uploadAdData(position, data) {
            const formData = new FormData();
            formData.append('action', 'upload_ad');
            formData.append('position', position);
            if (data.file) {
                formData.append('image', data.file);
            } else if (data.currentImage) {
                formData.append('image', data.currentImage);
            }
            formData.append('link', data.link);
            formData.append('alt', data.alt);
            formData.append('security', window.adDashboardData ? window.adDashboardData.nonce : '');
            const isUpdate = !data.file && data.currentImage;
            const actionText = isUpdate ? 'บันทึก' : 'อัพโหลด';
            window.Swal.fire({
                title: `กำลัง${actionText}...`,
                html: '<div class="upload-progress"><i class="fas fa-spinner fa-spin"></i> กรุณารอสักครู่</div>',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                customClass: {
                    popup: 'modern-popup-container loading-popup'
                }
            });

            $.ajax({
                url: window.adDashboardData ? window.adDashboardData.ajaxurl : '/wp-admin/admin-ajax.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        if (response.data && response.data.image_url) {
                            updateCardAfterUpload(
                                position,
                                response.data.image_url,
                                data.link,
                                data.alt
                            );
                        }

                        window.Swal.fire({
                            title: `<div class="success-popup-header">
                                <div class="success-icon-wrapper">
                                    🎉
                                </div>
                                <h3>${actionText}สำเร็จ!</h3>
                            </div>`,
                            html: `
                                <div class="success-popup-content">
                                    <p>${(response.data && response.data.message) || response.message || `${actionText}โฆษณาเรียบร้อยแล้ว`}</p>
                                    <div class="success-features">
                                        <div class="feature-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>โฆษณาพร้อมแสดงผล</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-edit"></i>
                                            <span>สามารถแก้ไขได้ตลอดเวลา</span>
                                        </div>
                                        <div class="feature-item">
                                            <i class="fas fa-chart-line"></i>
                                            <span>ติดตามสถิติการคลิกได้</span>
                                        </div>
                                    </div>
                                </div>
                            `,
                            confirmButtonText: '<i class="fas fa-check"></i> เยี่ยม!',
                            customClass: {
                                popup: 'modern-success-popup',
                                confirmButton: 'modern-btn modern-btn-success'
                            },
                            buttonsStyling: false,
                            width: '500px'
                        });
                    } else {
                        window.Swal.fire({
                            title: 'เกิดข้อผิดพลาด',
                            text: (response.data && response.data.message) || response.message || `ไม่สามารถ${actionText}ได้`,
                            icon: 'error',
                            customClass: {
                                popup: 'modern-popup-container error-popup',
                                confirmButton: 'modern-btn modern-btn-error'
                            },
                            buttonsStyling: false
                        });
                    }
                },
                error: function() {
                    window.Swal.fire({
                        title: 'เกิดข้อผิดพลาด',
                        text: 'ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้',
                        icon: 'error',
                        customClass: {
                            popup: 'modern-popup-container error-popup',
                            confirmButton: 'modern-btn modern-btn-error'
                        },
                        buttonsStyling: false
                    });
                }
            });
        }

        function updateCardAfterUpload(position, imageUrl, targetUrl, altText) {
            const card = $(`.my-ad-card[data-position="${position}"]`);
            if (card.length === 0) {
                return;
            }

            const imageContainer = card.find('.my-ad-image');
            if (imageUrl) {
                const escapedImageUrl = escapeHtml(imageUrl);
                const escapedAltText = escapeHtml(altText || position);
                const escapedTargetUrl = escapeHtml(targetUrl || '');
                imageContainer.html(`<img src="${escapedImageUrl}" alt="${escapedAltText}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`);
                imageContainer.attr('data-image', escapedImageUrl)
                             .attr('data-link', escapedTargetUrl)
                             .attr('data-alt', escapedAltText);
            }

            const linkDetail = card.find('.my-ad-detail').first();
            if (targetUrl) {
                const escapedTargetUrl = escapeHtml(targetUrl);
                const displayUrl = targetUrl.length > 30 ? escapeHtml(targetUrl.substring(0, 30)) + '...' : escapedTargetUrl;
                const linkHtml = `<a href="${escapedTargetUrl}" target="_blank" class="my-ad-link">
                    ${displayUrl}
                </a>`;
                linkDetail.find('span').html(linkHtml);
            } else {
                linkDetail.find('span').html('<span class="my-ad-no-link">ไม่มีลิงก์</span>');
            }

            const actionsContainer = card.find('.my-ad-actions');
            const uploadBtn = actionsContainer.find('.upload-new-ad-btn');

            if (uploadBtn.length > 0 && imageUrl) {
                uploadBtn.removeClass('upload-new-ad-btn')
                        .addClass('edit-ad-btn')
                        .attr('data-image', imageUrl)
                        .attr('data-link', targetUrl || '')
                        .attr('data-alt', altText || '')
                        .html('<i class="fas fa-edit"></i> แก้ไข');

                uploadBtn.off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopImmediatePropagation();
                    const position = $(this).data('position');
                    const currentImage = $(this).data('image') || '';
                    const currentLink = $(this).data('link') || '';
                    const currentAlt = $(this).data('alt') || '';
                    showEditPopup(position, currentImage, currentLink, currentAlt);
                });
            }
        }


    }

        window.MyAdsTooltip = {
            timeout: null,
            visible: false,
            currentElement: null,
            
            init: function() {
                this.cleanup();
                this.bindEvents();
            },
            
            cleanup: function() {
                this.hide(true);
                $(document).off('.myads-tooltip');
                if (this.timeout) clearTimeout(this.timeout);
            },
            
            bindEvents: function() {
                const self = this;
                
                $(document).on('mouseenter.myads-tooltip', '.price-hover-tooltip', function(e) {
                    const $el = $(this);
                    const cpc = parseFloat($el.data('cpc'));
                    const cpm = parseFloat($el.data('cpm'));
                    const card = $el.closest('.my-ad-card');
                    const position = card.data('position');
                    const dimensions = window.getPositionDetails(position);
                    const orientation = dimensions.height > dimensions.width ? 'vertical' : 'horizontal';
                    
                    self.show(e.originalEvent, cpc, cpm, orientation);
                });
                
                $(document).on('mouseleave.myads-tooltip', '.price-hover-tooltip', function() {
                    self.delayedHide();
                });
                
                $(document).on('mousemove.myads-tooltip', '.price-hover-tooltip', function(e) {
                    const orientation = $(this).data('orientation') || 'horizontal';
                    self.updatePosition(e.originalEvent, orientation);
                });
            },
            
            show: function(event, cpc, cpm, orientation) {
                clearTimeout(this.timeout);
                this.visible = true;
                
                window.getExchangeRate().then(rate => {
                    const tooltip = this.getTooltip();
                    tooltip.innerHTML = `
                        <div style="color: #3498db; font-weight: 600; margin-bottom: 4px;">💰 ราคาเป็นเงินบาท</div>
                        <div style="display: flex; ${orientation === 'vertical' ? 'flex-direction: column;' : ''} gap: ${orientation === 'vertical' ? '4px' : '15px'};">
                            <span>CPC: <strong>${(cpc * rate).toFixed(2)} ฿</strong></span>
                            ${orientation === 'horizontal' ? '<span style="color: #7f8c8d;">|</span>' : ''}
                            <span>CPM: <strong>${(cpm * rate).toFixed(4)} ฿</strong></span>
                        </div>
                        <div style="font-size: 11px; color: #95a5a6; margin-top: 4px;">
                            อัตราแลกเปลี่ยน: ${rate.toFixed(2)} ฿/USDT
                        </div>
                    `;
                    
                    this.updatePosition(event, orientation);
                    tooltip.style.opacity = '1';
                    tooltip.style.visibility = 'visible';
                });
            },
            
            hide: function(force = false) {
                if (!force && document.querySelector('.price-hover-tooltip:hover')) return;
                
                const tooltip = document.getElementById('price-tooltip');
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    tooltip.style.visibility = 'hidden';
                    setTimeout(() => tooltip.remove(), 200);
                }
                this.visible = false;
            },
            
            delayedHide: function() {
                this.visible = false;
                this.timeout = setTimeout(() => {
                    if (!this.visible) this.hide();
                }, 150);
            },
            
            getTooltip: function() {
                let tooltip = document.getElementById('price-tooltip');
                if (!tooltip) {
                    tooltip = document.createElement('div');
                    tooltip.id = 'price-tooltip';
                    tooltip.style.cssText = `
                        position: fixed; background: linear-gradient(135deg, #2c3e50, #34495e);
                        color: white; padding: 8px 12px; border-radius: 8px; font-size: 13px;
                        box-shadow: 0 4px 15px rgba(0,0,0,0.3); z-index: 10000;
                        opacity: 0; visibility: hidden; transition: all 0.2s ease;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        pointer-events: none; white-space: nowrap;
                    `;
                    document.body.appendChild(tooltip);
                }
                return tooltip;
            },
            
            updatePosition: function(event, orientation) {
                const tooltip = document.getElementById('price-tooltip');
                if (!tooltip) return;
                
                const rect = tooltip.getBoundingClientRect();
                let left = event.clientX + 10;
                let top = event.clientY - rect.height - 10;
                
                if (left + rect.width > window.innerWidth - 10) {
                    left = event.clientX - rect.width - 10;
                }
                if (top < 10) {
                    top = event.clientY + 20;
                }
                
                tooltip.style.left = left + 'px';
                tooltip.style.top = top + 'px';
            }
        };

        window.addEventListener('beforeunload', () => window.MyAdsTooltip?.cleanup());
        
        $(document).ready(function() {
            initMyAdsTab();
            window.MyAdsTooltip.init();
            window.cleanupCurrentTab = window.cleanupMyAdsTab;
        });
        window.initMyAdsTab = initMyAdsTab;
    }
    waitForJQuery();
})();
</script>