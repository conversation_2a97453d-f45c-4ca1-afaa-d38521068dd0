<?php

if (!defined('WPINC')) {
    die;
}

class AMP_Encryption_Manager {

    private static $instance = null;
    private $encryption_key;
    private $cipher_method = 'AES-256-CBC';

    public static function instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->encryption_key = $this->get_or_create_encryption_key();
    }

    private function get_or_create_encryption_key() {
        $key = get_option('amp_encryption_key_hash', false);
        
        if (!$key) {
            $key = $this->generate_encryption_key();
            update_option('amp_encryption_key_hash', $key, false);
        }
        
        return $key;
    }

    private function generate_encryption_key() {
        $auth_key = defined('AUTH_KEY') ? AUTH_KEY : 'default_auth_key';
        $secure_auth_key = defined('SECURE_AUTH_KEY') ? SECURE_AUTH_KEY : 'default_secure_auth_key';
        $logged_in_key = defined('LOGGED_IN_KEY') ? LOGGED_IN_KEY : 'default_logged_in_key';
        $nonce_key = defined('NONCE_KEY') ? NONCE_KEY : 'default_nonce_key';
        
        $combined = $auth_key . $secure_auth_key . $logged_in_key . $nonce_key . 'amp_secret_salt';
        
        return hash('sha256', $combined);
    }

    public function encrypt($data) {
        if (empty($data)) {
            return '';
        }

        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($this->cipher_method));
        $encrypted = openssl_encrypt($data, $this->cipher_method, $this->encryption_key, 0, $iv);
        
        if ($encrypted === false) {
            error_log('AMP Encryption: Failed to encrypt data');
            return false;
        }

        return base64_encode($iv . $encrypted);
    }

    public function decrypt($encrypted_data) {
        if (empty($encrypted_data)) {
            return '';
        }

        $data = base64_decode($encrypted_data);
        if ($data === false) {
            return false;
        }

        $iv_length = openssl_cipher_iv_length($this->cipher_method);
        $iv = substr($data, 0, $iv_length);
        $encrypted = substr($data, $iv_length);

        $decrypted = openssl_decrypt($encrypted, $this->cipher_method, $this->encryption_key, 0, $iv);
        
        if ($decrypted === false) {
            error_log('AMP Encryption: Failed to decrypt data');
            return false;
        }

        return $decrypted;
    }

    public function encrypt_and_store($option_name, $value) {
        $encrypted_value = $this->encrypt($value);
        if ($encrypted_value === false) {
            return false;
        }

        return update_option($option_name . '_encrypted', $encrypted_value, false);
    }

    public function get_and_decrypt($option_name) {
        $encrypted_value = get_option($option_name . '_encrypted', '');
        if (empty($encrypted_value)) {
            return '';
        }

        return $this->decrypt($encrypted_value);
    }

    public function migrate_existing_secrets() {
        $secrets_to_migrate = [
            'amp_google_client_id',
            'amp_google_client_secret',
            'amp_turnstile_site_key',
            'amp_turnstile_secret_key',
            'plisio_api_key'
        ];

        foreach ($secrets_to_migrate as $secret_name) {
            $current_value = get_option($secret_name, '');
            
            if (!empty($current_value) && !$this->is_already_encrypted($secret_name)) {
                $encrypted = $this->encrypt($current_value);
                if ($encrypted !== false) {
                    update_option($secret_name . '_encrypted', $encrypted, false);
                    update_option($secret_name, '[ENCRYPTED]', false);
                    
                    error_log("AMP Encryption: Migrated {$secret_name} to encrypted storage");
                }
            }
        }
    }

    private function is_already_encrypted($option_name) {
        $encrypted_option = get_option($option_name . '_encrypted', '');
        return !empty($encrypted_option);
    }

    public function get_secret($option_name) {
        $encrypted_value = get_option($option_name . '_encrypted', '');

        if (!empty($encrypted_value)) {
            return $this->decrypt($encrypted_value);
        }

        $plain_value = get_option($option_name, '');
        if (!empty($plain_value) && $plain_value !== '[ENCRYPTED]') {
            $this->encrypt_and_store($option_name, $plain_value);
            update_option($option_name, '[ENCRYPTED]', false);
            return $plain_value;
        }

        return '';
    }

    public function set_secret($option_name, $value) {
        if (empty($value)) {
            $this->delete_secret($option_name);
            return true;
        }

        $encrypted = $this->encrypt($value);
        if ($encrypted === false) {
            error_log("AMP Encryption: Failed to encrypt secret for {$option_name}");
            return false;
        }

        $result1 = update_option($option_name . '_encrypted', $encrypted, false);
        $result2 = update_option($option_name, '[ENCRYPTED]', false);

        if ($result1 && $result2) {
            error_log("AMP Encryption: Successfully encrypted and stored {$option_name}");
            return true;
        } else {
            error_log("AMP Encryption: Failed to store encrypted secret for {$option_name}");
            return false;
        }
    }

    public function delete_secret($option_name) {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $deleted1 = delete_option($option_name . '_encrypted');
        $deleted2 = delete_option($option_name);

        if ($deleted1 || $deleted2) {
            error_log("AMP Encryption: Successfully deleted secret {$option_name}");
            return true;
        }

        return false;
    }

    public function is_encrypted($option_name) {
        $value = get_option($option_name, '');
        return $value === '[ENCRYPTED]';
    }

    public function get_masked_value($option_name) {
        $value = $this->get_secret($option_name);
        
        if (empty($value)) {
            return '';
        }

        $length = strlen($value);
        if ($length <= 8) {
            return str_repeat('*', $length);
        }

        $visible_start = substr($value, 0, 4);
        $visible_end = substr($value, -4);
        $masked_middle = str_repeat('*', $length - 8);
        
        return $visible_start . $masked_middle . $visible_end;
    }

    public function validate_encryption() {
        $test_data = 'test_encryption_' . time();
        $encrypted = $this->encrypt($test_data);
        
        if ($encrypted === false) {
            return false;
        }

        $decrypted = $this->decrypt($encrypted);
        return $decrypted === $test_data;
    }

    public function get_encryption_status() {
        $secrets = [
            'amp_google_client_id',
            'amp_google_client_secret',
            'amp_turnstile_site_key',
            'amp_turnstile_secret_key',
            'plisio_api_key'
        ];

        $status = [];
        foreach ($secrets as $secret) {
            $status[$secret] = [
                'encrypted' => $this->is_encrypted($secret),
                'has_value' => !empty($this->get_secret($secret)),
                'masked_value' => $this->get_masked_value($secret)
            ];
        }

        return $status;
    }

    public function emergency_decrypt_all() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $secrets = [
            'amp_google_client_id',
            'amp_google_client_secret',
            'amp_turnstile_site_key',
            'amp_turnstile_secret_key',
            'plisio_api_key'
        ];

        $decrypted_data = [];
        foreach ($secrets as $secret) {
            $value = $this->get_secret($secret);
            if (!empty($value)) {
                $decrypted_data[$secret] = $value;
            }
        }

        return $decrypted_data;
    }

    public function rotate_encryption_key() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $old_data = $this->emergency_decrypt_all();

        delete_option('amp_encryption_key_hash');
        $this->encryption_key = $this->get_or_create_encryption_key();

        foreach ($old_data as $option_name => $value) {
            $this->set_secret($option_name, $value);
        }

        error_log('AMP Encryption: Encryption key rotated successfully');
        return true;
    }

    public function test_encryption_system() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $test_data = 'test_secret_' . time();
        $test_option = 'amp_encryption_test_' . time();

        $encrypt_result = $this->set_secret($test_option, $test_data);
        if (!$encrypt_result) {
            return [
                'success' => false,
                'message' => 'Failed to encrypt test data'
            ];
        }

        $decrypted_data = $this->get_secret($test_option);

        delete_option($test_option);
        delete_option($test_option . '_encrypted');

        if ($decrypted_data === $test_data) {
            return [
                'success' => true,
                'message' => 'Encryption/Decryption test passed successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Encryption/Decryption test failed - data mismatch'
            ];
        }
    }

    public function verify_all_secrets() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $secrets = [
            'amp_google_client_id',
            'amp_google_client_secret',
            'amp_turnstile_site_key',
            'amp_turnstile_secret_key',
            'plisio_api_key'
        ];

        $results = [];
        foreach ($secrets as $secret) {
            $encrypted_value = get_option($secret . '_encrypted', '');
            $plain_value = get_option($secret, '');
            $decrypted_value = $this->get_secret($secret);

            $results[$secret] = [
                'has_encrypted' => !empty($encrypted_value),
                'plain_is_encrypted_marker' => $plain_value === '[ENCRYPTED]',
                'can_decrypt' => !empty($decrypted_value),
                'status' => (!empty($encrypted_value) && $plain_value === '[ENCRYPTED]' && !empty($decrypted_value)) ? 'OK' : 'ERROR'
            ];
        }

        return $results;
    }

    public function backup_all_secrets() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $secrets = $this->emergency_decrypt_all();

        if (empty($secrets)) {
            return [
                'success' => false,
                'message' => 'No secrets found to backup'
            ];
        }

        $backup_data = [
            'timestamp' => time(),
            'version' => '1.0',
            'secrets' => $secrets
        ];

        $backup_json = json_encode($backup_data, JSON_PRETTY_PRINT);

        return [
            'success' => true,
            'data' => $backup_json,
            'count' => count($secrets),
            'message' => 'Backup created successfully'
        ];
    }

    public function restore_secrets_from_backup($backup_json) {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $backup_data = json_decode($backup_json, true);

        if (!$backup_data || !isset($backup_data['secrets'])) {
            return [
                'success' => false,
                'message' => 'Invalid backup format'
            ];
        }

        $restored_count = 0;
        $errors = [];

        foreach ($backup_data['secrets'] as $secret_name => $value) {
            if (in_array($secret_name, [
                'amp_google_client_id',
                'amp_google_client_secret',
                'amp_turnstile_site_key',
                'amp_turnstile_secret_key',
                'plisio_api_key'
            ])) {
                if ($this->set_secret($secret_name, $value)) {
                    $restored_count++;
                } else {
                    $errors[] = "Failed to restore {$secret_name}";
                }
            }
        }

        return [
            'success' => $restored_count > 0,
            'restored_count' => $restored_count,
            'total_count' => count($backup_data['secrets']),
            'errors' => $errors,
            'message' => "Restored {$restored_count} secrets successfully"
        ];
    }

    public function get_encryption_stats() {
        if (!current_user_can('manage_options')) {
            return false;
        }

        $secrets = [
            'amp_google_client_id',
            'amp_google_client_secret',
            'amp_turnstile_site_key',
            'amp_turnstile_secret_key',
            'plisio_api_key'
        ];

        $stats = [
            'total_secrets' => count($secrets),
            'encrypted_count' => 0,
            'unencrypted_count' => 0,
            'empty_count' => 0,
            'encryption_key_exists' => !empty($this->encryption_key),
            'cipher_method' => $this->cipher_method
        ];

        foreach ($secrets as $secret) {
            $encrypted_value = get_option($secret . '_encrypted', '');
            $plain_value = get_option($secret, '');
            $decrypted_value = $this->get_secret($secret);

            if (!empty($encrypted_value) && $plain_value === '[ENCRYPTED]') {
                $stats['encrypted_count']++;
            } elseif (!empty($plain_value) && $plain_value !== '[ENCRYPTED]') {
                $stats['unencrypted_count']++;
            } else {
                $stats['empty_count']++;
            }
        }

        return $stats;
    }
}
