<?php
/**
 * Main Plugin Class
 *
 * @package AdManagementPro\Core
 */

namespace AdManagementPro\Core;

if (!defined('WPINC')) {
    die;
}

class Plugin {
    
    private static $instance = null;
    private $version = '2.0.0';
    private $plugin_name = 'ad-management-pro';
    private $db;
    private $cache;
    private $security;
    private $modules = [];
    
    private function __construct() {
        $this->define_constants();
        $this->init_core();
        $this->init_hooks();
    }
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function define_constants() {
        if (!defined('AMP_VERSION')) {
            define('AMP_VERSION', $this->version);
        }
        
        if (!defined('AMP_PLUGIN_NAME')) {
            define('AMP_PLUGIN_NAME', $this->plugin_name);
        }
        
        if (!defined('AMP_PLUGIN_DIR')) {
            define('AMP_PLUGIN_DIR', \plugin_dir_path(dirname(__FILE__)));
        }
        
        if (!defined('AMP_PLUGIN_URL')) {
            define('AMP_PLUGIN_URL', \plugin_dir_url(dirname(__FILE__)));
        }
        
        if (!defined('AMP_PLUGIN_FILE')) {
            define('AMP_PLUGIN_FILE', dirname(dirname(__FILE__)) . '/ad-management-pro.php');
        }
    }
    
    private function init_core() {
        try {
            $this->load_required_classes();

            if (!class_exists('\\AdManagementPro\\Core\\Database')) {
                throw new \Exception('Database class not found after loading');
            }
            if (!class_exists('\\AMP_Cache_Manager')) {
                throw new \Exception('Cache class not found after loading');
            }
            if (!class_exists('\\AdManagementPro\\Core\\UnifiedSecurityManager')) {
                throw new \Exception('Security class not found after loading');
            }

            $this->db = \AdManagementPro\Core\Database::instance();
            $this->cache = \AMP_Cache_Manager::instance();
            $this->security = \AdManagementPro\Core\UnifiedSecurityManager::instance();
        } catch (\Exception $e) {
            \error_log('AMP Core Initialization Error: ' . $e->getMessage());
            $this->db = null;
            $this->cache = null;
            $this->security = null;
        }
    }

    private function load_required_classes() {
        $required_classes = [
            'class-database.php' => '\\AdManagementPro\\Core\\Database',
            'class-unified-security-manager.php' => '\\AdManagementPro\\Core\\UnifiedSecurityManager'
        ];

        foreach ($required_classes as $file => $class_name) {
            if (!class_exists($class_name)) {
                $file_path = dirname(__FILE__) . '/' . $file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                }
            }
        }

        if (!class_exists('\\AMP_Cache_Manager')) {
            $cache_file = AMP_PLUGIN_DIR . 'includes/cache/class-cache-manager.php';
            if (file_exists($cache_file)) {
                require_once $cache_file;
            }
        }
    }
    
    private function init_hooks() {
        if (!has_action('plugins_loaded', [$this, 'load_modules'])) {
            \add_action('plugins_loaded', [$this, 'load_modules']);
        }

        if (\current_user_can('manage_options')) {
            if (!has_action('admin_enqueue_scripts', [$this, 'admin_scripts'])) {
                \add_action('admin_enqueue_scripts', [$this, 'admin_scripts']);
            }
        }
    }
    
    public function activate() {
        try {
            if ($this->db) {
                \AdManagementPro\Core\Database::create_tables();
            }

            $this->create_roles();

            if ($this->cache) {
                $this->cache->clear_all();
            }
        } catch (\Exception $e) {
            \error_log('AMP Plugin Activation Error: ' . $e->getMessage());
        }
    }
    
    public function deactivate() {
        try {
            if ($this->cache) {
                $this->cache->clear_all();
            }
        } catch (\Exception $e) {
            \error_log('AMP Plugin Deactivation Error: ' . $e->getMessage());
        }
    }
    
    public function load_modules() {
        $this->load_core_modules();
        $this->load_optional_modules();
    }

    private function load_core_modules() {
        $core_modules = [
            'includes/core/roles.php',
            'includes/core/class-utilities.php'
        ];

        foreach ($core_modules as $module) {
            $file_path = AMP_PLUGIN_DIR . $module;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }

    private function load_optional_modules() {
        if (\current_user_can('manage_options') || \current_user_can('amp_advertiser_access') || wp_doing_ajax()) {
            require_once AMP_PLUGIN_DIR . 'includes/core/class-ajax-handlers.php';
            require_once AMP_PLUGIN_DIR . 'includes/core/class-user-manager.php';
        }

        if (!\current_user_can('manage_options') && !\current_user_can('amp_advertiser_access')) {
            require_once AMP_PLUGIN_DIR . 'includes/core/page-templates.php';
        }

        $module_dirs = [
            'ads' => '\AdManagementPro\Modules\Ads\AdsManager',
            'users' => '\AdManagementPro\Modules\Users\UsersManager'
        ];

        foreach ($module_dirs as $key => $class) {
            $file_path = AMP_PLUGIN_DIR . "includes/modules/{$key}/class-{$key}-manager.php";

            if (file_exists($file_path)) {
                require_once $file_path;
                if (class_exists($class)) {
                    $this->modules[$key] = new $class();
                }
            }
        }
    }
    
    private function create_roles() {
        remove_role('subscriber');
        remove_role('viewer_admin');

        if (!wp_roles()->is_role('advertiser')) {
            add_role('advertiser', __('Advertiser', 'ad-management-pro'), [
                'read' => true,
                'amp_advertiser_access' => true,
            ]);
        }
    }
    
    private function register_shortcodes() {
        \add_shortcode('ad_position', [$this, 'shortcode_ad_position']);
    }
    
    public function admin_scripts($hook) {
        if (strpos($hook, 'ad-management-pro') === false) {
            return;
        }
        
        \wp_enqueue_script('jquery');
        \wp_enqueue_media();
    }
    
    public function shortcode_ad_position($atts) {
        $atts = \shortcode_atts(['position' => ''], $atts);
        return '<!-- Ad position: ' . \esc_attr($atts['position']) . ' -->';
    }
} 