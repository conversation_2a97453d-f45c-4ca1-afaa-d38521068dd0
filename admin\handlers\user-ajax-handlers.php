<?php
if (!defined('WPINC')) {
    die;
}
function amp_init_user_ajax_handlers() {
    if (!function_exists('add_action')) {
        return;
    }

    add_action('wp_ajax_add_new_user', 'amp_handle_add_new_user');
    add_action('wp_ajax_delete_user', 'amp_handle_delete_user');
    add_action('wp_ajax_get_user_data', 'amp_handle_get_user_data');
}

if (!has_action('init', 'amp_init_user_ajax_handlers')) {
    add_action('init', 'amp_init_user_ajax_handlers');
}
function amp_handle_add_new_user() {
    if (!isset($_POST['add_user_nonce']) || !wp_verify_nonce($_POST['add_user_nonce'], 'add_new_user')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    $username = isset($_POST['username']) ? sanitize_user($_POST['username']) : '';
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $password = isset($_POST['password']) ? sanitize_text_field($_POST['password']) : '';
    $display_name = isset($_POST['display_name']) ? sanitize_text_field($_POST['display_name']) : '';
    $telegram_contact = isset($_POST['telegram_contact']) ? sanitize_text_field($_POST['telegram_contact']) : '';
    $show_stats = isset($_POST['show_stats']) ? 1 : 0;
    $bypass_checkout = isset($_POST['bypass_checkout']) ? 1 : 0;
    $ad_positions = isset($_POST['ad_positions']) ? array_map('sanitize_text_field', $_POST['ad_positions']) : [];
    if (empty($username) || empty($email) || empty($password)) {
        wp_send_json_error(['message' => 'Please fill in all required fields']);
        return;
    }
    if (username_exists($username)) {
        wp_send_json_error(['message' => 'Username already exists']);
        return;
    }
    if (email_exists($email)) {
        wp_send_json_error(['message' => 'Email already exists']);
        return;
    }
    $user_id = wp_create_user($username, $password, $email);
    if (is_wp_error($user_id)) {
        wp_send_json_error(['message' => $user_id->get_error_message()]);
        return;
    }
    $user = new WP_User($user_id);
    $user->set_role('advertiser');
    update_user_meta($user_id, 'amp_email_verified', 1);
    if (!empty($display_name)) {
        wp_update_user([
            'ID' => $user_id,
            'display_name' => $display_name
        ]);
    }
    update_user_meta($user_id, 'telegram_contact', $telegram_contact);
    update_user_meta($user_id, 'show_stats', $show_stats);
    update_user_meta($user_id, 'bypass_checkout', $bypass_checkout);
    if (!empty($ad_positions)) {
        require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
        $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
        
        $valid_positions = array();

        foreach ($ad_positions as $position) {
            $position_data = $position_manager->get_position($position);
            if ($position_data) {
                $valid_positions[] = $position;
            } else {
                error_log("AMP: Warning - Attempted to assign non-existent position '{$position}' to new user {$user_id}. Position ignored.");
            }
        }

        if (!empty($valid_positions)) {
            $position_manager->assign_positions_to_user($user_id, $valid_positions);
        }
    }
    wp_send_json_success([
        'message' => 'User created successfully',
        'user_id' => $user_id
    ]);
}
function amp_handle_delete_user() {
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'delete_user')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    $user_id = isset($_POST['customer_id']) ? intval($_POST['customer_id']) : 0;
    if ($user_id <= 0) {
        wp_send_json_error(['message' => 'Invalid user ID']);
        return;
    }
    $user = get_userdata($user_id);
    if (!$user) {
        wp_send_json_error(['message' => 'User not found']);
        return;
    }
    if (!user_can($user_id, 'amp_advertiser_access') || user_can($user_id, 'manage_options')) {
        wp_send_json_error(['message' => 'Cannot delete users with roles other than advertiser']);
        return;
    }
    require_once AMP_PLUGIN_DIR . 'includes/modules/shared/class-position-manager.php';
    $position_manager = \AdManagementPro\Modules\Shared\PositionManager::instance('admin');
    
    $user_positions = get_user_meta($user_id, 'ad_positions', true);
    if (is_array($user_positions) && !empty($user_positions)) {
        foreach ($user_positions as $position_name) {
            $position_manager->reset_position_ownership($position_name);
        }
    }
    if (wp_delete_user($user_id)) {
        wp_send_json_success(['message' => 'User deleted successfully']);
    } else {
        wp_send_json_error(['message' => 'Failed to delete user']);
    }
}
function amp_handle_get_user_data() {
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'get_user_data')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    if ($user_id <= 0) {
        wp_send_json_error(['message' => 'Invalid user ID']);
        return;
    }
    $user = get_userdata($user_id);
    if (!$user) {
        wp_send_json_error(['message' => 'User not found']);
        return;
    }
    $telegram_contact = get_user_meta($user_id, 'telegram_contact', true);
    $show_stats = get_user_meta($user_id, 'show_stats', true);
    $bypass_checkout = get_user_meta($user_id, 'bypass_checkout', true);
    $ad_positions = get_user_meta($user_id, 'ad_positions', true);
    if (!is_array($ad_positions)) {
        $ad_positions = [];
    }
    wp_send_json_success([
        'user_id' => $user_id,
        'username' => sanitize_text_field($user->user_login),
        'email' => sanitize_email($user->user_email),
        'display_name' => sanitize_text_field($user->display_name),
        'telegram_contact' => sanitize_text_field($telegram_contact),
        'show_stats' => (bool) $show_stats,
        'bypass_checkout' => (bool) $bypass_checkout,
        'ad_positions' => is_array($ad_positions) ? array_map('sanitize_text_field', $ad_positions) : []
    ]);
}
