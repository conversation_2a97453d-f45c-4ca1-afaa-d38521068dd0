<?php

if (!defined('ABSPATH')) {
    exit;
}

if (!class_exists('Redis')) {
    return false;
}

class AMP_Redis_Object_Cache {
    
    private $redis;
    private $cache_prefix;
    private $default_expiration = 86400;
    private $connected = false;
    
    public function __construct() {
        $this->cache_prefix = 'amp_redis_';
        $this->connect();
    }
    
    private function connect() {
        try {
            $this->redis = new Redis();
            
            $host = defined('AMP_REDIS_HOST') ? AMP_REDIS_HOST : '127.0.0.1';
            $port = defined('AMP_REDIS_PORT') ? AMP_REDIS_PORT : 6379;
            $timeout = defined('AMP_REDIS_TIMEOUT') ? AMP_REDIS_TIMEOUT : 1;
            
            $this->connected = $this->redis->connect($host, $port, $timeout);
            
            if ($this->connected && defined('AMP_REDIS_PASSWORD') && AMP_REDIS_PASSWORD) {
                $this->redis->auth(AMP_REDIS_PASSWORD);
            }
            
            if ($this->connected && defined('AMP_REDIS_DATABASE') && AMP_REDIS_DATABASE) {
                $this->redis->select(AMP_REDIS_DATABASE);
            }
            
        } catch (Exception $e) {
            $this->connected = false;
            error_log('AMP Redis Cache Connection Error: ' . $e->getMessage());
        }
    }
    
    public function get($key, $group = 'default') {
        if (!$this->connected) {
            return false;
        }
        
        $cache_key = $this->get_cache_key($key, $group);
        
        try {
            $value = $this->redis->get($cache_key);
            return $value !== false ? maybe_unserialize($value) : false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function set($key, $data, $group = 'default', $expiration = 0) {
        if (!$this->connected) {
            return false;
        }
        
        $cache_key = $this->get_cache_key($key, $group);
        $expiration = $expiration > 0 ? $expiration : $this->default_expiration;
        
        try {
            return $this->redis->setex($cache_key, $expiration, maybe_serialize($data));
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function delete($key, $group = 'default') {
        if (!$this->connected) {
            return false;
        }
        
        $cache_key = $this->get_cache_key($key, $group);
        
        try {
            return $this->redis->del($cache_key) > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    public function flush() {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->flushDB();
        } catch (Exception $e) {
            return false;
        }
    }
    
    private function get_cache_key($key, $group) {
        return $this->cache_prefix . $group . ':' . $key;
    }
    
    public function is_connected() {
        return $this->connected;
    }
}

$amp_redis_cache = new AMP_Redis_Object_Cache();

if (!function_exists('wp_cache_get')) {
    function wp_cache_get($key, $group = '') {
        global $amp_redis_cache;
        return $amp_redis_cache->get($key, $group);
    }
}

if (!function_exists('wp_cache_set')) {
    function wp_cache_set($key, $data, $group = '', $expire = 0) {
        global $amp_redis_cache;
        return $amp_redis_cache->set($key, $data, $group, $expire);
    }
}

if (!function_exists('wp_cache_delete')) {
    function wp_cache_delete($key, $group = '') {
        global $amp_redis_cache;
        return $amp_redis_cache->delete($key, $group);
    }
}

if (!function_exists('wp_cache_flush')) {
    function wp_cache_flush() {
        global $amp_redis_cache;
        return $amp_redis_cache->flush();
    }
}

if (!function_exists('wp_cache_add')) {
    function wp_cache_add($key, $data, $group = '', $expire = 0) {
        global $amp_redis_cache;
        if ($amp_redis_cache->get($key, $group) === false) {
            return $amp_redis_cache->set($key, $data, $group, $expire);
        }
        return false;
    }
}

if (!function_exists('wp_cache_replace')) {
    function wp_cache_replace($key, $data, $group = '', $expire = 0) {
        global $amp_redis_cache;
        if ($amp_redis_cache->get($key, $group) !== false) {
            return $amp_redis_cache->set($key, $data, $group, $expire);
        }
        return false;
    }
}

if (!function_exists('wp_cache_close')) {
    function wp_cache_close() {
        return true;
    }
}

if (!function_exists('wp_cache_init')) {
    function wp_cache_init() {
        return true;
    }
}
