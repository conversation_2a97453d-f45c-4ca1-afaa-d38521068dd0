<?php
if (!defined('WPINC')) {
    die;
}

class Plisio_API {
    private $api_key;
    private $base_url = 'https://api.plisio.net/api/v1/';
    private $test_mode;

    public function __construct($api_key = null) {
        if ($api_key) {
            $this->api_key = sanitize_text_field($api_key);
        } else {
            $this->api_key = $this->get_stored_api_key();
        }
        $this->test_mode = $this->get_stored_test_mode();
    }

    private function get_stored_api_key() {
        $encryption_manager = \AMP_Encryption_Manager::instance();
        return $encryption_manager->get_secret('plisio_api_key');
    }

    private function get_stored_test_mode() {
        if (class_exists('AMP_Payment_Handler')) {
            $payment_handler = AMP_Payment_Handler::instance();
            return $payment_handler->get_plisio_setting('test_mode', '0');
        }
        return '0';
    }

    public function get_api_key() {
        return $this->api_key;
    }

    public function is_test_mode() {
        return $this->test_mode === '1';
    }

    public function validate_api_key() {
        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', 'API Key is required');
        }
        if (strlen($this->api_key) < 20) {
            return new WP_Error('invalid_api_key', 'API Key appears to be invalid (too short)');
        }
        return true;
    }

    public function test_connection() {
        $validation = $this->validate_api_key();
        if (is_wp_error($validation)) {
            return $validation;
        }
        $url = $this->base_url . 'currencies?api_key=' . urlencode($this->api_key);
        $response = wp_remote_get($url, array(
            'timeout' => 15,
            'headers' => array(
                'User-Agent' => 'WordPress/Plisio-Plugin',
                'Accept' => 'application/json'
            ),
            'sslverify' => true
        ));
        if (is_wp_error($response)) {
            error_log('Plisio API Connection Error: ' . $response->get_error_message());
            return $response;
        }
        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        if ($http_code !== 200) {
            error_log('Plisio API Test Connection HTTP Error: ' . $http_code);
            return new WP_Error('http_error', 'HTTP Error: ' . $http_code);
        }
        $data = json_decode($body, true);
        if (!$data) {
            return new WP_Error('invalid_response', 'Invalid JSON response');
        }
        if (isset($data['status']) && $data['status'] === 'error') {
            $error_message = isset($data['data']['message']) ? $data['data']['message'] : 'Unknown API error';
            error_log('Plisio API Test Connection Error: ' . $error_message);
            return new WP_Error('api_error', $error_message);
        }
        if (isset($data['status']) && $data['status'] === 'success') {
            return $data;
        }
        return new WP_Error('unexpected_response', 'Unexpected API response format');
    }

    public function create_invoice($params) {
        $validation = $this->validate_api_key();
        if (is_wp_error($validation)) {
            return $validation;
        }

        $required_params = ['order_number', 'order_name', 'source_amount', 'source_currency'];
        foreach ($required_params as $param) {
            if (!isset($params[$param]) || empty($params[$param])) {
                return new WP_Error('missing_param', "Required parameter '{$param}' is missing");
            }
        }

        $default_params = array(
            'api_key' => $this->api_key,
            'currency' => 'USDT_TRX',
            'callback_url' => home_url('/wp-json/ad-management-pro/v1/plisio-webhook'),
            'success_callback_url' => home_url('/wp-json/ad-management-pro/v1/plisio-webhook-success'),
            'fail_callback_url' => home_url('/wp-json/ad-management-pro/v1/plisio-webhook-fail')
        );

        $invoice_params = array_merge($default_params, $params);

        $query_string = http_build_query($invoice_params);
        $url = $this->base_url . 'invoices/new?' . $query_string;



        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'WordPress/Plisio-Plugin',
                'Accept' => 'application/json'
            ),
            'sslverify' => true
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        error_log('Plisio API Create Invoice Response Code: ' . $http_code);
        error_log('Plisio API Create Invoice Response Body: ' . substr($body, 0, 300));

        if ($http_code !== 200) {
            error_log('Plisio API Create Invoice HTTP Error: ' . $http_code);
            $preview = substr($body, 0, 200);
            if (strpos($preview, '<!DOCTYPE html>') !== false || strpos($preview, '<html') !== false) {
                error_log('Plisio API returned HTML page instead of JSON - possible API endpoint error');
                return new WP_Error('api_endpoint_error', 'API returned HTML instead of JSON - check API endpoint and key');
            }
            error_log('Plisio API Response Preview: ' . $preview);
            return new WP_Error('http_error', 'HTTP Error: ' . $http_code);
        }

        $data = json_decode($body, true);
        if (!$data) {
            return new WP_Error('invalid_response', 'Invalid JSON response from API');
        }

        if (isset($data['status']) && $data['status'] === 'error') {
            $error_message = isset($data['data']['message']) ? $data['data']['message'] : 'Failed to create invoice';
            return new WP_Error('invoice_error', $error_message);
        }

        if (isset($data['status']) && $data['status'] === 'success') {
            error_log('Plisio API Success: Invoice created with ID ' . ($data['data']['txn_id'] ?? 'unknown'));
            error_log('Plisio API Full Response: ' . json_encode($data));
            return $data;
        }

        error_log('Plisio API Unexpected Response: ' . json_encode($data));
        return new WP_Error('unexpected_response', 'Unexpected API response format');
    }

    public function get_transaction_details($txn_id) {
        return $this->request('operations/' . $txn_id, 'GET');
        }

    public function get_supported_currencies() {
        return $this->request('currencies', 'GET');
    }

    private function request($endpoint, $method = 'POST', $params = []) {
        $url = $this->base_url . $endpoint;
        
        $params['api_key'] = $this->api_key;
        
        $args = [
            'method'    => $method,
            'timeout'   => 15,
            'headers'   => [
                'User-Agent' => 'WordPress/AdManagementPro-Plugin',
                'Accept'     => 'application/json',
            ],
            'sslverify' => true,
        ];

        if ($method === 'GET') {
            $url .= '?' . http_build_query($params, '', '&');
        } else {
            $args['body'] = json_encode($params);
            $args['headers']['Content-Type'] = 'application/json';
        }

        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            error_log('Plisio API Request Error: ' . $response->get_error_message());
            return $response;
        }
        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        if ($http_code !== 200) {
            error_log('Plisio API Request HTTP Error: ' . $http_code);
            return new WP_Error('http_error', 'HTTP Error: ' . $http_code);
        }
        $data = json_decode($body, true);
        if (!$data) {
            return new WP_Error('invalid_response', 'Invalid JSON response');
        }
        if (isset($data['status']) && $data['status'] === 'error') {
            $error_message = isset($data['data']['message']) ? $data['data']['message'] : 'Unknown API error';
            error_log('Plisio API Request Error: ' . $error_message);
            return new WP_Error('api_error', $error_message);
        }
        if (isset($data['status']) && $data['status'] === 'success') {
            return $data;
        }
        return new WP_Error('unexpected_response', 'Unexpected API response format');
    }
}