<?php
if (!defined('ABSPATH')) {
    exit;
}

add_action('wp_ajax_amp_toggle_cache', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    if (!isset($_POST['enable'])) {
        wp_send_json_error(['message' => 'Missing required parameter']);
        return;
    }
    
    $enable = $_POST['enable'] === '1';

    try {
        if (!class_exists('\AMP_Cache_Manager')) {
            wp_send_json_error(['message' => 'Cache class not found']);
            return;
        }

        $cache_manager = \AMP_Cache_Manager::instance();

        if ($enable) {
            $result = $cache_manager->enable_cache();
        } else {
            $result = $cache_manager->disable_cache();
        }

        if ($result === false) {
            wp_send_json_error(['message' => 'ไม่สามารถเปลี่ยนสถานะแคชได้ - การอัปเดต WordPress option ล้มเหลว']);
            return;
        }


        $cache_info = $cache_manager->get_cache_info();
        if (!isset($cache_info['enabled'])) {
            wp_send_json_error(['message' => 'ไม่สามารถตรวจสอบสถานะแคชได้ - cache_info missing enabled key']);
            return;
        }

        if ($cache_info['enabled'] !== $enable) {
            wp_send_json_error([
                'message' => 'การเปลี่ยนสถานะไม่สำเร็จ - expected: ' . ($enable ? 'true' : 'false') . ', actual: ' . ($cache_info['enabled'] ? 'true' : 'false')
            ]);
            return;
        }

        wp_send_json_success([
            'message' => $enable ? 'เปิดใช้งานแคชแล้ว' : 'ปิดใช้งานแคชแล้ว',
            'enabled' => $enable
        ]);
        
    } catch (Exception $e) {
        wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_clear_cache_group', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    $group = sanitize_text_field($_POST['group']);
    $cache_manager = \AMP_Cache_Manager::instance();
    $cache_manager->clear_group($group);
    
    wp_send_json_success(['message' => "เคลียร์แคชกลุ่ม {$group} เรียบร้อยแล้ว"]);
});

add_action('wp_ajax_amp_clear_all_cache', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    $cache_manager = \AMP_Cache_Manager::instance();
    $cache_manager->clear_all();
    
    wp_send_json_success(['message' => 'เคลียร์แคชทั้งหมดเรียบร้อยแล้ว']);
});

add_action('wp_ajax_amp_toggle_cache_group', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }
    
    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }
    
    if (!isset($_POST['group']) || !isset($_POST['enable'])) {
        wp_send_json_error(['message' => 'Missing required parameters']);
        return;
    }
    
    $group = sanitize_text_field($_POST['group']);
    $enable = $_POST['enable'] === '1';
    $cache_manager = \AMP_Cache_Manager::instance();
    
    try {
        if ($enable) {
            $result = $cache_manager->enable_group($group);
        } else {
            $result = $cache_manager->disable_group($group);
        }
        
        if ($result === false) {
            wp_send_json_error(['message' => 'ไม่สามารถเปลี่ยนสถานะกลุ่มแคชได้']);
            return;
        }
        
        $group_name = ucwords(str_replace('_', ' ', $group));
        wp_send_json_success([
            'message' => $enable ? "เปิดใช้งานแคช {$group_name} แล้ว" : "ปิดใช้งานแคช {$group_name} แล้ว",
            'group' => $group,
            'enabled' => $enable
        ]);
        
    } catch (Exception $e) {
        wp_send_json_error(['message' => 'เกิดข้อผิดพลาด: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_get_cache_stats', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    try {
        $cache_manager = \AMP_Cache_Manager::instance();
        $cache_info = $cache_manager->get_cache_info();
        $cache_stats = $cache_manager->get_cache_stats();

        $total_operations = $cache_stats['hits'] + $cache_stats['misses'];
        $hit_rate = $total_operations > 0 ? round(($cache_stats['hits'] / $total_operations) * 100, 1) : 0;

        wp_send_json_success([
            'hits' => $cache_stats['hits'],
            'misses' => $cache_stats['misses'],
            'sets' => $cache_stats['sets'],
            'hit_rate' => $hit_rate,
            'timestamp' => current_time('mysql')
        ]);

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'เกิดข้อผิดพลาดในการโหลดสถิติ: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_switch_cache_backend', function() {
    if (!current_user_can('manage_options') || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed.']);
        return;
    }

    $backend = sanitize_text_field($_POST['backend']);
    $dropin_path = WP_CONTENT_DIR . '/object-cache.php';

    try {

        if (file_exists($dropin_path)) {
            if (!unlink($dropin_path)) {
                throw new Exception('Could not remove the existing object-cache.php file. Please check file permissions.');
            }
        }


        if ($backend === 'memcached' || $backend === 'redis') {
            $source_path = AMP_PLUGIN_DIR . "includes/cache/{$backend}-object-cache.php";
            if (!file_exists($source_path)) {
                wp_send_json_error(['message' => "Cache backend file not found at: {$source_path}"]);
                return;
            }
            if (!copy($source_path, $dropin_path)) {
                throw new Exception("Could not copy the {$backend} cache backend. Please check file permissions for wp-content.");
            }
        } else {
            wp_send_json_error(['message' => 'Invalid backend specified.']);
        }

        wp_send_json_success([
            'message' => 'Cache backend switched successfully. Please reload the page for changes to take effect.',
        ]);

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'Error: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_update_default_expiration', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    if (!isset($_POST['expiration_hours'])) {
        wp_send_json_error(['message' => 'Missing expiration hours parameter.']);
        return;
    }

    $expiration_hours = floatval($_POST['expiration_hours']);

    if ($expiration_hours <= 0) {
        wp_send_json_error(['message' => 'Expiration time must be a positive number.']);
        return;
    }

    $expiration_seconds = $expiration_hours * 3600;

    try {
        $cache_manager = \AMP_Cache_Manager::instance();
        $result = $cache_manager->set_default_expiration($expiration_seconds);

        if ($result === false) {
            wp_send_json_error(['message' => 'Failed to update default expiration time.']);
            return;
        }

        wp_send_json_success([
            'message' => 'Default cache expiration updated successfully to ' . $expiration_hours . ' hours.',
            'new_expiration_hours' => $expiration_hours
        ]);

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_toggle_object_cache_backend', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    if (!isset($_POST['backend_action'])) {
        wp_send_json_error(['message' => 'Missing required parameter']);
        return;
    }

    $backend_action = sanitize_text_field($_POST['backend_action']);
    $dropin_path = WP_CONTENT_DIR . '/object-cache.php';

    try {
        if ($backend_action === 'enable') {
            if (file_exists($dropin_path)) {
                wp_send_json_error(['message' => '🔴 Persistent Cache ถูกเปิดใช้งานอยู่แล้ว']);
                return;
            }

            $source_path = AMP_PLUGIN_DIR . 'includes/cache/redis-object-cache.php';
            if (!file_exists($source_path)) {
                wp_send_json_error(['message' => '❌ ไม่พบไฟล์ Redis Object Cache ในระบบ']);
                return;
            }

            if (!copy($source_path, $dropin_path)) {
                throw new Exception('ไม่สามารถคัดลอกไฟล์ Object Cache ได้ กรุณาตรวจสอบสิทธิ์การเขียนไฟล์');
            }

            wp_send_json_success(['message' => '🚀 เปิดใช้งาน Persistent Cache (Redis) เรียบร้อยแล้ว']);

        } elseif ($backend_action === 'disable') {
            if (!file_exists($dropin_path)) {
                wp_send_json_error(['message' => '⚠️ Persistent Cache ถูกปิดใช้งานอยู่แล้ว']);
                return;
            }

            if (!unlink($dropin_path)) {
                throw new Exception('ไม่สามารถลบไฟล์ Object Cache ได้ กรุณาตรวจสอบสิทธิ์การเขียนไฟล์');
            }

            wp_send_json_success(['message' => '🔴 ปิดใช้งาน Persistent Cache เรียบร้อยแล้ว ระบบกลับไปใช้ Database Cache']);

        } else {
            wp_send_json_error(['message' => '❌ คำสั่งไม่ถูกต้อง']);
        }

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'Error: ' . $e->getMessage()]);
    }
});

add_action('wp_ajax_amp_run_cache_tests', function() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['message' => 'Permission denied']);
        return;
    }

    if (!isset($_POST['security']) || !wp_verify_nonce($_POST['security'], 'amp_cache_management')) {
        wp_send_json_error(['message' => 'Security check failed']);
        return;
    }

    try {
        if (!class_exists('AMP_Cache_System_Test')) {
            wp_send_json_error(['message' => 'Cache test class not found']);
            return;
        }

        $test_runner = new AMP_Cache_System_Test();
        $results = $test_runner->run_all_tests();
        $summary = $test_runner->get_test_summary();
        $test_runner->cleanup_test_data();

        wp_send_json_success([
            'summary' => $summary,
            'results' => $results
        ]);

    } catch (Exception $e) {
        wp_send_json_error(['message' => 'An error occurred during testing: ' . $e->getMessage()]);
    }
});
