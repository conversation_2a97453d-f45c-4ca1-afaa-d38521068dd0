<?php
if (!defined('ABSPATH')) {
    exit;
}

$ga_file = plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/utils/google-analytics.php';
if (file_exists($ga_file)) {
    require_once($ga_file);
}

$realtime_users = 0;
$daily_users = 0;
$monthly_users = 0;
$monthly_pageviews = 0;
$using_ga4_data = false;
$daily_users_data = null;

if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
    try {
        $realtime_users = amp_get_realtime_users();
        $monthly_users = amp_get_monthly_users(31);

        if (function_exists('amp_get_last_24_hours_users_data')) {
            $ga_24h_data = amp_get_last_24_hours_users_data();
            if ($ga_24h_data && !is_wp_error($ga_24h_data) && !empty($ga_24h_data['values'])) {
                $daily_users_data = $ga_24h_data;
                $daily_users = array_sum($ga_24h_data['values']);
            }
        }

        if (function_exists('amp_get_daily_users_data')) {
            $ga_daily_data_31 = amp_get_daily_users_data(31);
            $ga_daily_data_91 = amp_get_daily_users_data(91);
        }

        $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no');
        if ($exclude_homepage === 'yes') {
            $monthly_pageviews = amp_get_monthly_pageviews_exclude_homepage(31);
        } else {
            $monthly_pageviews = amp_get_monthly_pageviews(31);
        }
        $using_ga4_data = true;
    } catch (Exception $e) {
        $using_ga4_data = false;
    }
}

if (!$using_ga4_data) {
    global $wpdb;
    $price_settings_table = $wpdb->prefix . 'ad_price_global_settings';
    
    $monthly_visitors_setting = $wpdb->get_var($wpdb->prepare(
        "SELECT setting_value FROM {$price_settings_table} WHERE setting_name = %s", 'monthly_visitors'
    ));
    
    $monthly_pageviews_setting = $wpdb->get_var($wpdb->prepare(
        "SELECT setting_value FROM {$price_settings_table} WHERE setting_name = %s", 'monthly_pageviews'
    ));
    
    $realtime_users = rand(15, 45);
    $monthly_users = $monthly_visitors_setting ? intval($monthly_visitors_setting) : 600000;
    if ($daily_users === 0) {
        $daily_users = round($monthly_users / 30);
    }
    $monthly_pageviews = $monthly_pageviews_setting ? intval($monthly_pageviews_setting) : ($monthly_users * 4);
}

if (empty($daily_users_data)) {
    $daily_users_data = array(
        'success' => true,
        'dates' => array(),
        'values' => array()
    );
}

if (!$using_ga4_data || empty($daily_users_data['dates'])) {
    $daily_users_data = array(
        'success' => true,
        'dates' => array(),
        'values' => array()
    );

    for ($i = 91; $i >= 0; $i--) {
        $daily_users_data['dates'][] = date('d M', strtotime("-$i days"));
        $base_value = $daily_users;
        $variation = rand(-($base_value * 0.2), ($base_value * 0.3));
        $weekend_factor = (date('N', strtotime("-$i days")) >= 6) ? 0.7 : 1;
        $daily_users_data['values'][] = max(50, round(($base_value + $variation) * $weekend_factor));
    }

    for ($h = 23; $h >= 0; $h--) {
        $daily_users_data['dates'][] = date('d M H:i', strtotime("-$h hours"));
        $hour_base = round($daily_users / 24);
        $hour_variation = rand(-($hour_base * 0.3), ($hour_base * 0.4));
        $daily_users_data['values'][] = max(5, round($hour_base + $hour_variation));
    }
} else {
    $original_hourly_data = $daily_users_data;

    $extended_data = array(
        'success' => true,
        'dates' => array(),
        'values' => array()
    );

    if (isset($ga_daily_data_91) && !is_wp_error($ga_daily_data_91) && !empty($ga_daily_data_91['dates'])) {
        $extended_data['dates'] = $ga_daily_data_91['dates'];
        $extended_data['values'] = $ga_daily_data_91['values'];
    } else {
        for ($i = 91; $i >= 0; $i--) {
            $extended_data['dates'][] = date('d M', strtotime("-$i days"));
            $base_value = $daily_users;
            $variation = rand(-($base_value * 0.2), ($base_value * 0.3));
            $weekend_factor = (date('N', strtotime("-$i days")) >= 6) ? 0.7 : 1;
            $extended_data['values'][] = max(50, round(($base_value + $variation) * $weekend_factor));
        }
    }

    foreach ($original_hourly_data['dates'] as $index => $date) {
        $extended_data['dates'][] = $date;
        $extended_data['values'][] = $original_hourly_data['values'][$index];
    }

    $daily_users_data = $extended_data;
}


$top_pages = array();
$using_real_top_pages = false;

if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
    try {
        if (function_exists('amp_get_top_pages')) {
            $ga_top_pages_1 = amp_get_top_pages(1, 10);
            $ga_top_pages_31 = amp_get_top_pages(31, 10);
            $ga_top_pages_91 = amp_get_top_pages(91, 10);
            $top_pages_data = array();
            if ($ga_top_pages_1 && !is_wp_error($ga_top_pages_1) && !empty($ga_top_pages_1['pages'])) {
                $top_pages_data[1] = $ga_top_pages_1['pages'];
            }
            if ($ga_top_pages_31 && !is_wp_error($ga_top_pages_31) && !empty($ga_top_pages_31['pages'])) {
                $top_pages_data[31] = $ga_top_pages_31['pages'];
            }
            if ($ga_top_pages_91 && !is_wp_error($ga_top_pages_91) && !empty($ga_top_pages_91['pages'])) {
                $top_pages_data[91] = $ga_top_pages_91['pages'];
            }

            if (!empty($top_pages_data)) {
                $top_pages = $top_pages_data[1] ?? array();
                $using_real_top_pages = true;
            }
        }
    } catch (Exception $e) {
        $using_real_top_pages = false;
    }
}


if (empty($top_pages)) {
    $daily_base = round($monthly_users / 30);

    $mock_pages_data = array(
        1 => array(
            array('page' => 'หน้าแรก', 'visitors' => round($daily_base * 0.4)),
            array('page' => 'บริการ', 'visitors' => round($daily_base * 0.2)),
            array('page' => 'เกี่ยวกับเรา', 'visitors' => round($daily_base * 0.15)),
            array('page' => 'ติดต่อเรา', 'visitors' => round($daily_base * 0.1)),
            array('page' => 'บล็อก', 'visitors' => round($daily_base * 0.08))
        ),
        31 => array(
            array('page' => 'หน้าแรก', 'visitors' => round($monthly_users * 0.35)),
            array('page' => 'เกี่ยวกับเรา', 'visitors' => round($monthly_users * 0.15)),
            array('page' => 'บริการ', 'visitors' => round($monthly_users * 0.12)),
            array('page' => 'ติดต่อเรา', 'visitors' => round($monthly_users * 0.08)),
            array('page' => 'บล็อก', 'visitors' => round($monthly_users * 0.06))
        ),
        91 => array(
            array('page' => 'หน้าแรก', 'visitors' => round($monthly_users * 3 * 0.35)),
            array('page' => 'เกี่ยวกับเรา', 'visitors' => round($monthly_users * 3 * 0.15)),
            array('page' => 'บริการ', 'visitors' => round($monthly_users * 3 * 0.12)),
            array('page' => 'ติดต่อเรา', 'visitors' => round($monthly_users * 3 * 0.08)),
            array('page' => 'บล็อก', 'visitors' => round($monthly_users * 3 * 0.06))
        )
    );

    $top_pages_data = $mock_pages_data;
    $top_pages = $mock_pages_data[1];
}
?>

<div class="analytics-dashboard">
    <div class="analytics-header">
        <h2>📊 สถิติเว็บไซต์</h2>
        <p>ข้อมูลผู้เข้าชมเว็บไซต์และการวิเคราะห์ประสิทธิภาพ</p>
        <?php if (!$using_ga4_data): ?>
        <div class="analytics-notice">
            <i class="fas fa-info-circle"></i>
            <span>ข้อมูลจำลองสำหรับการทดสอบ - เชื่อมต่อ Google Analytics เพื่อดูข้อมูลจริง</span>
        </div>
        <?php endif; ?>
    </div>

    <div class="analytics-stats-grid">
        <div class="analytics-stat-card realtime-users">
            <div class="stat-icon">
                <i class="fas fa-user-clock"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo number_format($realtime_users); ?></div>
                <div class="stat-label">ผู้ใช้งานขณะนี้</div>
                <div class="stat-description">จำนวนผู้ใช้งานที่กำลังเข้าชมเว็บไซต์ในขณะนี้</div>
                <?php if ($using_ga4_data): ?>
                <div class="stat-source">📈 ข้อมูลจาก Google Analytics</div>
                <?php else: ?>
                <div class="stat-source">🔄 ข้อมูลจำลอง</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="analytics-stat-card daily-users">
            <div class="stat-icon">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo number_format($daily_users); ?></div>
                <div class="stat-label">ผู้ใช้งาน 24 ชม.ล่าสุด</div>
                <div class="stat-description">ผลรวมผู้ใช้งานในช่วง 24 ชั่วโมงที่ผ่านมา</div>
                <?php if ($using_ga4_data): ?>
                <div class="stat-source">📈 ข้อมูลจาก Google Analytics</div>
                <?php else: ?>
                <div class="stat-source">🔄 ข้อมูลจำลอง</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="analytics-stat-card monthly-users">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo number_format($monthly_users); ?></div>
                <div class="stat-label">ผู้ใช้งาน 31 วัน</div>
                <div class="stat-description">จำนวนผู้ใช้งานที่เข้าชมเว็บไซต์ในช่วง 31 วันที่ผ่านมา (ไม่รวมวันปัจจุบัน)</div>
                <?php if ($using_ga4_data): ?>
                <div class="stat-source">📈 ข้อมูลจาก Google Analytics</div>
                <?php else: ?>
                <div class="stat-source">🔄 ข้อมูลจำลอง</div>
                <?php endif; ?>
            </div>
        </div>

        <div class="analytics-stat-card monthly-pageviews">
            <div class="stat-icon">
                <i class="fas fa-eye"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value"><?php echo number_format($monthly_pageviews); ?></div>
                <div class="stat-label">เพจวิว 31 วัน</div>
                <div class="stat-description">
                    จำนวนเพจวิวทั้งหมดในช่วง 31 วันที่ผ่านมา (ไม่รวมวันปัจจุบัน)
                    <?php
                    $exclude_homepage = get_option('exclude_homepage_from_pageviews', 'no');
                    if ($exclude_homepage === 'yes'):
                        $reduction_percentage = get_option('pageviews_reduction_percentage', '9');
                    ?>
                        <br><small style="color: #f39c12;">🚫 ไม่รวมหน้าแรกและหน้าอื่นๆ (-<?php echo esc_html($reduction_percentage); ?>%)</small>
                    <?php endif; ?>
                </div>
                <?php if ($using_ga4_data): ?>
                <div class="stat-source">📈 ข้อมูลจาก Google Analytics</div>
                <?php else: ?>
                <div class="stat-source">🔄 ข้อมูลจำลอง</div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="analytics-section">
        <div class="analytics-section-header">
            <div class="section-title">
                <h3 id="chart-title">📈 กราฟผู้ใช้งานรายชั่วโมง</h3>
                <div class="chart-info">
                    <?php if ($using_ga4_data && !empty($daily_users_data['dates'])): ?>
                        <span id="chart-data-source" class="data-source real-data">📊 ข้อมูลจริงจาก Google Analytics (1 วัน)</span>
                    <?php else: ?>
                        <span id="chart-data-source" class="data-source mock-data">🔄 ข้อมูลจำลอง (1 วัน)</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="chart-controls">
                <div class="period-selector">
                    <button class="period-btn active" data-period="1">1 วัน</button>
                    <button class="period-btn" data-period="31">31 วัน</button>
                    <button class="period-btn" data-period="91">91 วัน</button>
                </div>
            </div>
        </div>
        <div class="analytics-chart-container">
            <canvas id="dailyUsersChart" width="800" height="300" style="max-width: 100%; height: auto; min-height: 250px;"></canvas>
            <div id="ga4-chart-dates" data-dates='<?php echo json_encode($daily_users_data['dates']); ?>' style="display: none;"></div>
            <div id="ga4-chart-values" data-values='<?php echo json_encode($daily_users_data['values']); ?>' style="display: none;"></div>
            <div id="top-pages-data" data-pages='<?php echo json_encode($top_pages_data); ?>' data-using-real='<?php echo $using_real_top_pages ? 'true' : 'false'; ?>' style="display: none;"></div>
        </div>
    </div>

    <div class="analytics-section">
        <div class="analytics-section-header">
            <div class="section-title">
                <h3>🏆 หน้าเว็บยอดนิยม</h3>
                <div class="chart-info">
                    <?php if ($using_real_top_pages): ?>
                        <span class="data-source real-data" id="top-pages-period-label">📊 ข้อมูลจริงจาก Google Analytics (31 วัน)</span>
                    <?php else: ?>
                        <span class="data-source mock-data">🔄 ข้อมูลจำลอง</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="analytics-table-container">
            <table class="analytics-table">
                <thead>
                    <tr>
                        <th>อันดับ</th>
                        <th>หน้าเว็บ</th>
                        <th><?php echo $using_real_top_pages ? 'เพจวิว' : 'ผู้เข้าชม'; ?></th>
                    </tr>
                </thead>
                <tbody id="top-pages-tbody">
                    <?php foreach ($top_pages as $index => $page): ?>
                    <tr>
                        <td><span class="rank-badge"><?php echo $index + 1; ?></span></td>
                        <td>
                            <div class="page-title"><?php echo esc_html($page['page']); ?></div>
                        </td>
                        <td><strong><?php echo number_format($page['visitors']); ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="analytics-info">
        <div class="info-icon">
            <i class="fas fa-lightbulb"></i>
        </div>
        <div class="info-content">
            <h4>💡 เกี่ยวกับข้อมูลสถิติ</h4>
            <?php if ($using_ga4_data): ?>
            <p>✅ ข้อมูลสถิติเว็บไซต์นี้มาจาก Google Analytics 4 ซึ่งเป็นเครื่องมือวิเคราะห์เว็บไซต์ที่มีความแม่นยำสูง</p>
            <p>🔄 ข้อมูลจะถูกอัพเดทโดยอัตโนมัติเมื่อรีเฟรชหน้า</p>
            <?php if ($using_real_top_pages): ?>
            <p>🏆 หน้าเว็บยอดนิยมแสดงข้อมูลจริงจาก Google Analytics ตามจำนวนเพจวิวในช่วง 31 วันที่ผ่านมา</p>
            <?php endif; ?>
            <?php else: ?>
            <p>🔄 ข้อมูลนี้เป็นข้อมูลจำลองสำหรับการทดสอบระบบ</p>
            <p>⚙️ เชื่อมต่อ Google Analytics 4 ในหน้าการตั้งค่าเพื่อดูข้อมูลจริงของเว็บไซต์</p>
            <p>📊 ข้อมูลจริงจะช่วยให้คุณวิเคราะห์ประสิทธิภาพการโฆษณาได้แม่นยำยิ่งขึ้น</p>
            <p>🏆 เมื่อเชื่อมต่อ GA4 แล้ว คุณจะเห็นข้อมูลหน้าเว็บยอดนิยมจริงจากเว็บไซต์ของคุณ</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function initializeAnalytics() {
    const chartCanvas = document.getElementById('dailyUsersChart');
    if (chartCanvas) {
        const ctx = chartCanvas.getContext('2d');
        const datesElement = document.getElementById('ga4-chart-dates');
        const valuesElement = document.getElementById('ga4-chart-values');

        if (datesElement && valuesElement) {
            try {
                const dates = JSON.parse(datesElement.getAttribute('data-dates'));
                const values = JSON.parse(valuesElement.getAttribute('data-values'));

                window.dailyUsersChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: dates.slice(-24),
                        datasets: [{
                            label: 'ผู้ใช้งานรายชั่วโมง (24 ชั่วโมงล่าสุด)',
                            data: values.slice(-24),
                            borderColor: 'rgba(67, 97, 238, 1)',
                            backgroundColor: function(context) {
                                const chart = context.chart;
                                const {ctx, chartArea} = chart;
                                if (!chartArea) return null;

                                const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
                                gradient.addColorStop(0, 'rgba(67, 97, 238, 0.3)');
                                gradient.addColorStop(0.5, 'rgba(67, 97, 238, 0.15)');
                                gradient.addColorStop(1, 'rgba(67, 97, 238, 0.05)');
                                return gradient;
                            },
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: 'rgba(67, 97, 238, 1)',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 8,
                            pointHoverBackgroundColor: 'rgba(67, 97, 238, 1)',
                            pointHoverBorderColor: '#ffffff',
                            pointHoverBorderWidth: 3,
                            shadowOffsetX: 3,
                            shadowOffsetY: 3,
                            shadowBlur: 10,
                            shadowColor: 'rgba(67, 97, 238, 0.3)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    color: '#4a5568',
                                    font: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    padding: 20
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(45, 55, 72, 0.95)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: 'rgba(67, 97, 238, 1)',
                                borderWidth: 2,
                                cornerRadius: 10,
                                displayColors: false,
                                callbacks: {
                                    label: function(context) {
                                        return 'ผู้ใช้งาน: ' + context.parsed.y.toLocaleString() + ' คน';
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    borderColor: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    color: '#4a5568',
                                    font: {
                                        size: 12
                                    }
                                }
                            },
                            y: {
                                beginAtZero: true,
                                min: 0,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)',
                                    borderColor: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    color: '#4a5568',
                                    font: {
                                        size: 12
                                    },
                                    callback: function(value) {
                                        return value.toLocaleString();
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        animation: {
                            duration: 2500,
                            easing: 'easeInOutQuart',
                            delay: (context) => {
                                let delay = 0;
                                if (context.type === 'data' && context.mode === 'default') {
                                    delay = context.dataIndex * 50;
                                }
                                return delay;
                            }
                        },
                        animations: {
                            tension: {
                                duration: 1000,
                                easing: 'linear',
                                from: 1,
                                to: 0.4,
                                loop: false
                            },
                            y: {
                                duration: 2000,
                                easing: 'easeInOutQuart',
                                from: (ctx) => {
                                    if (ctx.type === 'data') {
                                        const chart = ctx.chart;
                                        const meta = chart.getDatasetMeta(ctx.datasetIndex);
                                        return chart.scales.y.getPixelForValue(0);
                                    }
                                }
                            }
                        }
                    }
                });

            } catch (e) {
                chartCanvas.style.display = 'none';
                const errorDiv = document.createElement('div');
                errorDiv.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">ไม่สามารถโหลดกราฟได้</p>';
                chartCanvas.parentNode.appendChild(errorDiv);
            }
        }
    }

    const periodButtons = document.querySelectorAll('.period-btn');

    if (periodButtons.length > 0) {
        periodButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                periodButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                const selectedDays = parseInt(this.dataset.period);

                if (selectedDays) {
                    updateChartData(selectedDays);
                }
            });
        });
    }

    updateChartData(1);

}

function updateChartData(days) {
    updateChart(days);
    updateTopPages(days);
}

function updateChart(days) {
    if (!window.dailyUsersChart) return;

    const datesElement = document.getElementById('ga4-chart-dates');
    const valuesElement = document.getElementById('ga4-chart-values');

    if (!datesElement || !valuesElement) return;

    try {
        const allDates = JSON.parse(datesElement.getAttribute('data-dates'));
        const allValues = JSON.parse(valuesElement.getAttribute('data-values'));

        const dataMap = {
            1: {
                label: 'ผู้ใช้งานรายชั่วโมง',
                title: '📈 กราฟผู้ใช้งานรายชั่วโมง'
            },
            31: {
                label: 'ผู้ใช้งานรายวัน',
                title: '📈 กราฟผู้ใช้งานรายวัน'
            },
            91: {
                label: 'ผู้ใช้งานรายวัน',
                title: '📈 กราฟผู้ใช้งานรายวัน'
            }
        };

        const config = dataMap[days];
        if (!config) return;

        let filteredDates, filteredValues;

        if (days === 1) {
            filteredDates = allDates.slice(-24);
            filteredValues = allValues.slice(-24);
        } else if (days === 31) {
            filteredDates = allDates.slice(-55, -24);
            filteredValues = allValues.slice(-55, -24);
        } else {
            filteredDates = allDates.slice(0, -24);
            filteredValues = allValues.slice(0, -24);
        }

        window.dailyUsersChart.data.labels = filteredDates;
        window.dailyUsersChart.data.datasets[0].data = filteredValues;
        window.dailyUsersChart.data.datasets[0].label = `${config.label} (${filteredDates.length} ${days === 1 ? 'ชั่วโมงล่าสุด' : 'วันล่าสุด'})`;

        const chartTitle = document.getElementById('chart-title');
        if (chartTitle) {
            chartTitle.textContent = config.title;
        }

        const dataSource = document.getElementById('chart-data-source');
        if (dataSource) {
            const isRealData = dataSource.classList.contains('real-data');
            const prefix = isRealData ? '📊 ข้อมูลจริงจาก Google Analytics' : '🔄 ข้อมูลจำลอง';
            dataSource.textContent = `${prefix} (${days} วัน)`;
        }

        window.dailyUsersChart.update('resize');
    } catch (e) {

    }
}

function updateTopPages(days) {
    const topPagesElement = document.getElementById('top-pages-data');
    const tbody = document.getElementById('top-pages-tbody');
    const periodLabel = document.getElementById('top-pages-period-label');

    if (!topPagesElement || !tbody) return;

    try {
        const allPagesData = JSON.parse(topPagesElement.getAttribute('data-pages'));
        const pagesForPeriod = allPagesData[days] || [];

        if (pagesForPeriod.length > 0) {
            tbody.innerHTML = '';
            pagesForPeriod.forEach((page, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td><span class="rank-badge">${index + 1}</span></td>
                    <td><div class="page-title">${escapeHtml(page.page)}</div></td>
                    <td><strong>${formatNumber(page.visitors)}</strong></td>
                `;
                tbody.appendChild(row);
            });
        }

        if (periodLabel) {
            periodLabel.textContent = `📊 ข้อมูลจริงจาก Google Analytics (${days} วัน)`;
        }
    } catch (e) {

    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAnalytics);
} else {
    initializeAnalytics();
}
</script>

<style>
.analytics-dashboard {
    max-width: 1600px;
    margin: 0 auto;
    padding: 20px;
}

.analytics-header {
    text-align: center;
    margin-bottom: 40px;
    background: var(--theme-gradient);
    border-radius: 20px;
    padding: 20px;
    position: relative;
    overflow: hidden;
}

.analytics-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--diagonal-stripe);
    background-size: var(--diagonal-stripe-size);
    opacity: 0.3;
}

.analytics-header h2 {
    font-size: 28px;
    margin-bottom: 15px;
    color: white;
    font-weight: 700;
    position: relative;
    z-index: 1;
}

.analytics-header p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    position: relative;
    z-index: 1;
    margin-bottom: 0;
}

.analytics-notice {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 15px 20px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: white;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.analytics-stat-card {
    background: var(--card-bg);
    border-radius: 16px;
    padding: 25px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--divider-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.analytics-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
}

.analytics-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.analytics-stat-card.realtime-users::before {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.analytics-stat-card.daily-users::before {
    background: linear-gradient(135deg, #4834d4, #686de0);
}

.analytics-stat-card.monthly-users::before {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.analytics-stat-card.monthly-pageviews::before {
    background: linear-gradient(135deg, #ff9ff3, #f368e0);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: white;
    background: var(--primary-color);
}

.realtime-users .stat-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.daily-users .stat-icon {
    background: linear-gradient(135deg, #4834d4, #686de0);
}

.monthly-users .stat-icon {
    background: linear-gradient(135deg, #00d2d3, #54a0ff);
}

.monthly-pageviews .stat-icon {
    background: linear-gradient(135deg, #ff9ff3, #f368e0);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 8px;
    line-height: 1;
}

.stat-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.stat-description {
    font-size: 14px;
    color: var(--light-text);
    line-height: 1.4;
    margin-bottom: 12px;
}

.stat-source {
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 500;
    background: rgba(67, 97, 238, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    display: inline-block;
}

.analytics-section {
    background: var(--card-bg);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: var(--shadow);
    border: 1px solid var(--divider-color);
}

.analytics-section h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--divider-color);
}

.analytics-chart-container {
    position: relative;
    height: 400px;
    background: #fff;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    overflow: hidden;
}

.analytics-chart-container:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.analytics-chart-container canvas {
    transition: all 0.3s ease;
}

.analytics-table-container {
    overflow-x: auto;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--input-bg);
    border-radius: 12px;
    overflow: hidden;
}

.analytics-table th {
    background: var(--primary-color);
    color: white;
    padding: 12px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
}

.analytics-table td {
    padding: 12px;
    border-bottom: 1px solid var(--divider-color);
    color: var(--text-color);
    font-size: 14px;
}

.analytics-table tr:last-child td {
    border-bottom: none;
}

.analytics-table tr:hover {
    background: rgba(67, 97, 238, 0.05);
}

.analytics-info {
    background: linear-gradient(135deg, var(--card-bg), rgba(67, 97, 238, 0.05));
    border-radius: 16px;
    padding: 30px;
    display: flex;
    align-items: flex-start;
    border: 1px solid var(--divider-color);
    box-shadow: var(--shadow);
}

.info-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffa726, #ff7043);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
    flex-shrink: 0;
}

.info-icon i {
    color: white;
    font-size: 24px;
}

.info-content h4 {
    color: var(--text-color);
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
}

.info-content p {
    color: var(--light-text);
    margin: 0 0 12px 0;
    line-height: 1.6;
    font-size: 14px;
}

.info-content p:last-child {
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .analytics-stats-grid {
        grid-template-columns: 1fr;
    }

    .analytics-info {
        flex-direction: column;
        text-align: center;
    }

    .info-icon {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .analytics-chart-container {
        height: 300px;
        padding: 20px;
    }

    .period-selector {
        display: flex;
        flex-direction: row;
        gap: 0;
        width: 100%;
    }

    .period-btn {
        padding: 12px 8px;
        font-size: 12px;
        min-width: auto;
        text-align: center;
        border-radius: 0;
        width: 33.333%;
        flex: 1;
        border-right: 1px solid rgba(255,255,255,0.2);
    }

    .period-btn:first-child {
        border-radius: 8px 0 0 8px;
    }

    .period-btn:last-child {
        border-radius: 0 8px 8px 0;
        border-right: none;
    }
}

.analytics-section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    gap: 20px;
}

@media (max-width: 768px) {
    .analytics-section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .chart-controls {
        margin-left: 0;
        justify-content: center;
    }

    .analytics-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .analytics-table {
        min-width: 100%;
        font-size: 12px;
    }

    .analytics-table th,
    .analytics-table td {
        padding: 8px 6px;
    }

    .analytics-table th:first-child,
    .analytics-table td:first-child {
        width: 45px;
        min-width: 45px;
        padding: 8px 4px;
    }

    .analytics-table th:last-child,
    .analytics-table td:last-child {
        width: 65px;
        min-width: 65px;
        font-size: 11px;
        padding: 8px 4px;
    }

    .page-title {
        font-size: 11px;
        line-height: 1.2;
        word-break: break-word;
    }

    .rank-badge {
        padding: 2px 5px;
        font-size: 10px;
        min-width: 18px;
        height: 18px;
    }
}

.section-title h3 {
    margin: 0 0 5px 0;
    font-size: 1.4em;
}

.chart-info {
    margin-top: 5px;
}

.data-source {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
}

.data-source.real-data {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.5);
    color: #4CAF50;
}

.data-source.mock-data {
    background: rgba(255, 152, 0, 0.2);
    border: 1px solid rgba(255, 152, 0, 0.5);
    color: #FF9800;
}

.chart-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.period-selector {
    display: flex;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 4px;
    border: 1px solid #e9ecef;
    position: relative;
    z-index: 1;
}

.period-btn {
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
    user-select: none;
    min-width: 60px;
}

.period-btn:hover {
    background: rgba(67, 97, 238, 0.1);
    color: #4361ee;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(67, 97, 238, 0.2);
}

.period-btn.active {
    background: #4361ee;
    color: white;
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.3);
    font-weight: 600;
}

.period-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.3);
}



.rank-badge {
    background: var(--primary-color);
    color: white;
    padding: 3px 6px;
    border-radius: 50%;
    font-size: 11px;
    font-weight: 600;
    min-width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.page-title {
    font-weight: 600;
    color: var(--text-color);
    font-size: 13px;
    line-height: 1.3;
}

.analytics-table th:first-child,
.analytics-table td:first-child {
    text-align: center;
    width: 60px;
}

.analytics-table th:last-child,
.analytics-table td:last-child {
    text-align: right;
    width: 90px;
}
</style>