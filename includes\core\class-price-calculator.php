<?php
if (!defined('ABSPATH')) {
    exit;
}
final class AMP_Price_Calculator {
    private static $instance = null;
    private array $discounts = [];
    private float $trial_multiplier = 1.8;
    private $cache;

    private function __construct() {
        global $wpdb;
        $this->cache = \AMP_Cache_Manager::instance();

        $cached_discounts = $this->cache->get('discount_rates', 'pricing_data');
        if (is_array($cached_discounts)) {
            $this->discounts = $cached_discounts;
        } else {
            $this->discounts = [];
            $discount_table = $wpdb->prefix . 'ad_discount_rates';
            $results = $wpdb->get_results("SELECT duration, discount_percentage FROM {$discount_table}", ARRAY_A);
            if (is_array($results)) {
                foreach ($results as $result) {
                    $this->discounts[(int)$result['duration']] = (float)$result['discount_percentage'];
                }
            }
            $this->cache->set('discount_rates', $this->discounts, 0, 'pricing_data');
        }

        $cached_multiplier = $this->cache->get('trial_multiplier', 'pricing_data');
        if ($cached_multiplier !== false) {
            $this->trial_multiplier = (float)$cached_multiplier;
        } else {
            $global_settings_table = $wpdb->prefix . 'ad_price_global_settings';
            $trial_multiplier_row = $wpdb->get_row($wpdb->prepare(
                "SELECT setting_value FROM {$global_settings_table} WHERE setting_name = %s",
                'trial_multiplier'
            ));
            $this->trial_multiplier = $trial_multiplier_row ? (float)$trial_multiplier_row->setting_value : 1.8;
            $this->cache->set('trial_multiplier', $this->trial_multiplier, 0, 'pricing_data');
        }
    }
    public static function instance(): AMP_Price_Calculator {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public static function clear_cache(): void {
        $cache = \AMP_Cache_Manager::instance();
        $cache->clear_group('pricing_data');
        self::$instance = null;
    }
    
    public function get_base_price(string $position_name): int {
        global $wpdb;

        $cache_key = 'base_price_' . $position_name;
        $cached_price = $this->cache->get($cache_key, 'pricing_data');
        if (false !== $cached_price) {
            return (int) $cached_price;
        }

        $use_ga = get_option('use_ga_for_pricing', 'no');

        if ($use_ga === 'yes') {
            $ga_file = AMP_PLUGIN_DIR . 'includes/utils/google-analytics.php';
            if (file_exists($ga_file)) {
                require_once($ga_file);
                if (function_exists('amp_is_ga_configured') && amp_is_ga_configured()) {
                    $monthly_visitors = amp_get_monthly_users(31);
                    if ($monthly_visitors > 0) {
                        $divisor_row = $wpdb->get_row($wpdb->prepare(
                            "SELECT setting_value FROM {$wpdb->prefix}ad_price_global_settings WHERE setting_name = %s",
                            'divisor'
                        ));
                        $thb_rate_row = $wpdb->get_row($wpdb->prepare(
                            "SELECT setting_value FROM {$wpdb->prefix}ad_price_global_settings WHERE setting_name = %s",
                            'thb_rate'
                        ));
                        $multiplier_row = $wpdb->get_row($wpdb->prepare(
                            "SELECT multiplier FROM {$wpdb->prefix}ad_price_calculation WHERE ad_position = %s",
                            $position_name
                        ));

                        $divisor = $divisor_row ? floatval($divisor_row->setting_value) : 30;
                        $thb_rate = $thb_rate_row ? floatval($thb_rate_row->setting_value) : 35.5;
                        $multiplier = $multiplier_row ? floatval($multiplier_row->multiplier) : 1.0;

                        $base_price = ($monthly_visitors / $divisor / $thb_rate) * $multiplier + 2;

                        $minimum_price_row = $wpdb->get_row($wpdb->prepare(
                            "SELECT setting_value FROM {$wpdb->prefix}ad_price_global_settings WHERE setting_name = %s",
                            'minimum_price'
                        ));
                        $minimum_price = $minimum_price_row ? intval($minimum_price_row->setting_value) : 6;
                        $base_price = max(intval($base_price), $minimum_price);

                        $this->cache->set($cache_key, $base_price, 0, 'pricing_data');
                        return $base_price;
                    }
                }
            }
        }

        $price_table = $wpdb->prefix . 'ad_price_calculation';
        $price_row = $wpdb->get_row($wpdb->prepare(
            "SELECT usdt_price FROM {$price_table} WHERE ad_position = %s",
            $position_name
        ));

        $base_price = $price_row ? (int)$price_row->usdt_price : 217;

        $minimum_price_row = $wpdb->get_row($wpdb->prepare(
            "SELECT setting_value FROM {$wpdb->prefix}ad_price_global_settings WHERE setting_name = %s",
            'minimum_price'
        ));
        $minimum_price = $minimum_price_row ? intval($minimum_price_row->setting_value) : 6;
        $base_price = max($base_price, $minimum_price);

        $this->cache->set($cache_key, $base_price, 0, 'pricing_data');
        return $base_price;
    }
    public function get_discount_rate(int $duration_months): float {
        return $this->discounts[$duration_months] ?? 0.0;
    }
    public function get_trial_multiplier(): float {
        return $this->trial_multiplier;
    }
    public function calculate_price_details(string $position_name, int $duration_days): array {
        $base_price = $this->get_base_price($position_name);
        $final_price = 0;
        $discount_rate = 0;
        $original_price = 0;
        $is_trial = ($duration_days === 7);
        if ($is_trial) {
            $original_price = ($base_price / 30) * 7;
            $final_price = $original_price * $this->get_trial_multiplier();
        } else {
            $months = (int) round($duration_days / 30);
            $original_price = $base_price * $months;
            $discount_rate = $this->get_discount_rate($months);
            $final_price = $original_price;
            if ($discount_rate > 0) {
                $final_price *= (1 - ($discount_rate / 100));
            }
        }
        return [
            'base_price'     => $base_price,
            'original_price' => ceil($original_price),
            'final_price'    => ceil($final_price),
            'discount_rate'  => $discount_rate,
            'is_trial'       => $is_trial,
        ];
    }
}