<?php
/*
Plugin Name: Ad Management Pro Uninstall Handler
Description: <PERSON>les custom uninstall popup for Ad Management Pro plugin with Security Protection
Version: 1.2
*/

if (!defined('ABSPATH')) {
    exit;
}

if (!function_exists('amp_check_uninstall_request')) {
    add_action('admin_init', 'amp_check_uninstall_request');
    add_action('admin_notices', 'amp_show_uninstall_notice');
    add_action('admin_enqueue_scripts', 'amp_uninstall_handler_assets');
    add_action('wp_ajax_amp_process_uninstall', 'amp_process_uninstall');

    function amp_check_uninstall_request() {
        if (!current_user_can('activate_plugins')) {
            return;
        }

        if (isset($_GET['amp_confirm_uninstall'])) {
            $nonce = isset($_GET['_wpnonce']) ? sanitize_text_field($_GET['_wpnonce']) : '';

            if (!wp_verify_nonce($nonce, 'amp_uninstall_confirm')) {
                wp_die('Security check failed');
            }

            $confirm_action = sanitize_text_field($_GET['amp_confirm_uninstall']);
            if ($confirm_action === 'yes') {
                update_option('amp_confirmed_uninstall', true);
                wp_send_json_success([
                    'action' => 'delete_all',
                    'message' => 'กำลังลบปลั๊กอินและข้อมูลทั้งหมด...'
                ]);
                exit;
            } elseif ($confirm_action === 'no') {
                update_option('amp_keep_data_on_uninstall', true);
                wp_send_json_success([
                    'action' => 'keep_data',
                    'message' => 'กำลังลบปลั๊กอิน (เก็บข้อมูลไว้)...'
                ]);
                exit;
            }
        }
    }

    function amp_show_uninstall_notice() {
        if (!current_user_can('activate_plugins')) {
            return;
        }

        if (isset($_GET['amp_uninstall_confirmed'])) {
            ?>
            <div class="notice notice-success is-dismissible">
                <p><strong>Ad Management Pro:</strong> ข้อมูลทั้งหมดจะถูกลบเมื่อถอนการติดตั้งปลั๊กอิน</p>
            </div>
            <?php
            return;
        }

        if (isset($_GET['amp_data_kept'])) {
            ?>
            <div class="notice notice-info is-dismissible">
                <p><strong>Ad Management Pro:</strong> ข้อมูลจะถูกเก็บไว้แม้หลังจากถอนการติดตั้งปลั๊กอิน</p>
            </div>
            <?php
            return;
        }

        $screen = get_current_screen();
        if ($screen && $screen->id === 'plugins') {
            add_action('admin_footer', 'amp_add_hidden_uninstall_popup');
        }
    }

    function amp_uninstall_handler_assets($hook) {
        if ('plugins.php' !== $hook) {
            return;
        }

        wp_enqueue_script('jquery');
    }

    function amp_add_hidden_uninstall_popup() {
        $nonce = wp_create_nonce('amp_uninstall_confirm');
    ?>
    <div id="amp-uninstall-overlay" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        z-index: 999999;
        display: none;
        align-items: center;
        justify-content: center;
    ">
        <div style="
            background: white;
            padding: 40px;
            border-radius: 16px;
            max-width: 600px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        ">
            <div style="font-size: 48px; margin-bottom: 20px;">⚠️</div>
            <h2 style="color: #e74c3c; margin-bottom: 20px; font-size: 24px;">ยืนยันการถอนการติดตั้ง</h2>

            <div style="text-align: left; margin-bottom: 30px; background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <p style="margin-bottom: 15px; font-size: 16px; line-height: 1.6;">
                    คุณต้องการลบข้อมูลทั้งหมดของ <strong>Ad Management Pro</strong> หรือไม่?
                </p>

                <h4 style="color: #e74c3c; margin-bottom: 10px;">ข้อมูลที่จะถูกลบ:</h4>
                <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                    <li>🎯 ตำแหน่งโฆษณาทั้งหมด (ข้อมูลจากฐานข้อมูล)</li>
                    <li>👥 ข้อมูลผู้ใช้และบทบาทที่สร้างโดยปลั๊กอิน</li>
                    <li>📊 สถิติการคลิกและการชำระเงิน</li>
                    <li>⚙️ การตั้งค่าทั้งหมด (Google Analytics, Payment, Security)</li>
                    <li>📁 ไฟล์ที่อัพโหลดและ API credentials</li>
                    <li>🗄️ ตารางฐานข้อมูลทั้งหมด (8 ตาราง)</li>
                </ul>

                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin-top: 15px;">
                    <strong>⚠️ คำเตือน:</strong> การดำเนินการนี้ไม่สามารถย้อนกลับได้!
                </div>
            </div>

            <div style="display: flex; gap: 20px; justify-content: center;">
                <button id="amp-delete-all-btn" type="button"
                   style="
                       background: linear-gradient(135deg, #e74c3c, #c0392b);
                       color: white;
                       padding: 15px 30px;
                       border: none;
                       border-radius: 8px;
                       font-weight: bold;
                       font-size: 16px;
                       box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
                       transition: all 0.3s ease;
                       cursor: pointer;
                   "
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(231, 76, 60, 0.4)';"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(231, 76, 60, 0.3)';">
                    🗑️ ลบข้อมูลทั้งหมด
                </button>

                <button id="amp-keep-data-btn" type="button"
                   style="
                       background: linear-gradient(135deg, #27ae60, #229954);
                       color: white;
                       padding: 15px 30px;
                       border: none;
                       border-radius: 8px;
                       font-weight: bold;
                       font-size: 16px;
                       box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
                       transition: all 0.3s ease;
                       cursor: pointer;
                   "
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 16px rgba(39, 174, 96, 0.4)';"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(39, 174, 96, 0.3)';">
                    💾 เก็บข้อมูลไว้
                </button>
            </div>

            <p style="margin-top: 20px; font-size: 14px; color: #666;">
                หากเลือก "เก็บข้อมูลไว้" คุณสามารถติดตั้งปลั๊กอินใหม่และใช้ข้อมูลเดิมได้
            </p>
        </div>
    </div>

    <style>
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.9);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    </style>

    <script>
    document.getElementById('amp-uninstall-overlay').addEventListener('click', function(e) {
        if (e.target === this) {
            e.preventDefault();
            return false;
        }
    });

    jQuery(document).ready(function($) {
        $('tr[data-plugin="ad-management-pro/ad-management-pro.php"] .delete a, a[href*="ad-management-pro"][href*="delete"]').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $('#amp-uninstall-overlay').css('display', 'flex');
            return false;
        });

        $('#doaction, #doaction2').on('click', function(e) {
            var action = $(this).siblings('select').val();
            if (action === 'delete-selected') {
                var checkedPlugins = $('input[name="checked[]"]:checked');
                var ourPlugin = false;

                checkedPlugins.each(function() {
                    if ($(this).val() === 'ad-management-pro/ad-management-pro.php') {
                        ourPlugin = true;
                        return false;
                    }
                });

                if (ourPlugin) {
                    e.preventDefault();
                    e.stopPropagation();
                    $('#amp-uninstall-overlay').css('display', 'flex');
                    return false;
                }
            }
        });

        $('#amp-delete-all-btn').on('click', function() {
            processUninstall(false);
        });

        $('#amp-keep-data-btn').on('click', function() {
            processUninstall(true);
        });

        function processUninstall(keepData) {
            var button = keepData ? $('#amp-keep-data-btn') : $('#amp-delete-all-btn');
            var originalText = button.html();

            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> กำลังดำเนินการ...');

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'amp_process_uninstall',
                    nonce: '<?php echo wp_create_nonce('amp_uninstall_process'); ?>',
                    keep_data: keepData
                },
                success: function(response) {
                    if (response.success) {
                        window.location.href = response.data.redirect;
                    } else {
                        button.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    button.prop('disabled', false).html(originalText);
                }
            });
        }

        var originalConfirm = window.confirm;
        window.confirm = function(message) {
            if (message && (message.includes('delete') || message.includes('remove'))) {
                var currentUrl = window.location.href;
                var currentPlugin = '';

                if (currentUrl.includes('plugin=ad-management-pro') ||
                    currentUrl.includes('ad-management-pro/ad-management-pro.php')) {
                    $('#amp-uninstall-overlay').css('display', 'flex');
                    return false;
                }

                var activeElement = document.activeElement;
                if (activeElement) {
                    var pluginRow = $(activeElement).closest('tr[data-plugin]');
                    if (pluginRow.length > 0) {
                        currentPlugin = pluginRow.attr('data-plugin');
                        if (currentPlugin === 'ad-management-pro/ad-management-pro.php') {
                            $('#amp-uninstall-overlay').css('display', 'flex');
                            return false;
                        }
                    }
                }
            }
            return originalConfirm.call(this, message);
        };
    });
    </script>
    <?php
    }

    function amp_process_uninstall() {
        if (!current_user_can('activate_plugins')) {
            wp_send_json_error(['message' => 'ไม่มีสิทธิ์ในการดำเนินการ']);
        }

        $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : '';
        if (!wp_verify_nonce($nonce, 'amp_uninstall_process')) {
            wp_send_json_error(['message' => 'Security check failed']);
        }

        $keep_data = isset($_POST['keep_data']) ? (bool)$_POST['keep_data'] : false;
        $cleanup_log = [];

        if (!$keep_data) {
            global $wpdb;

            $tables_to_delete = [
                $wpdb->prefix . 'ad_positions',  
                $wpdb->prefix . 'ad_payments', 
                $wpdb->prefix . 'ad_discount_rates',
                $wpdb->prefix . 'ad_price_calculation',
                $wpdb->prefix . 'ad_price_global_settings',
                $wpdb->prefix . 'plisio_settings',
                $wpdb->prefix . 'ad_login_attempts'
            ];

            $tables_deleted = 0;
            foreach ($tables_to_delete as $table) {
                if ($wpdb->get_var("SHOW TABLES LIKE '$table'") === $table) {
                    $wpdb->query("DROP TABLE IF EXISTS `$table`");
                    $tables_deleted++;
                }
            }

            $options_to_delete = [
                'ad_positions', 'ad_sizes', 'ads',
                'telegram_default_link', 'site_logo_url',
                'amp_google_client_id', 'amp_google_client_secret',
                'price_calculation_settings', 'discount_settings',
                'security_settings', 'amp_confirmed_uninstall',
                'amp_keep_data_on_uninstall', 'amp_database_version',
                'exchange_rate_log', 'thb_rate', 'exchange_rate',
                'use_ga_for_pricing', 'monthly_visitors', 'monthly_pageviews',
                'amp_wp_admin_protection', 'amp_allowed_ips',
                'amp_whm_cpanel_detection', 'amp_emergency_access_code',
                'amp_wp_admin_setup_completed', 'amp_wp_admin_setup_required'
            ];

            foreach ($options_to_delete as $option) {
                delete_option($option);
            }

            $upload_dir = wp_upload_dir();
            $logs_base_dir = $upload_dir['basedir'] . '/amp-logs';

            $deleted_folders = 0;
            if (is_dir($logs_base_dir)) {
                $deleted_folders = amp_delete_directory_recursive($logs_base_dir);
                $cleanup_log[] = "ลบ Log Folders: {$deleted_folders} โฟลเดอร์";
            }

            $cleanup_log[] = "ลบ Database Tables: {$tables_deleted} ตาราง";
            $cleanup_log[] = "ลบ Options: " . count($options_to_delete) . " รายการ";
        } else {
            $cleanup_log[] = 'เก็บข้อมูลไว้ตามที่ผู้ใช้เลือก';
        }

        if (!function_exists('delete_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugin_file = 'ad-management-pro/ad-management-pro.php';
        $result = delete_plugins([$plugin_file]);

        $mu_plugins_file = WP_CONTENT_DIR . '/mu-plugins/amp-uninstall-handler.php';
        if (file_exists($mu_plugins_file)) {
            unlink($mu_plugins_file);
            $cleanup_log[] = 'ลบ MU Plugin Handler';
        }

        $mu_plugins_dir = WP_CONTENT_DIR . '/mu-plugins';
        if (is_dir($mu_plugins_dir) && count(scandir($mu_plugins_dir)) == 2) {
            rmdir($mu_plugins_dir);
            $cleanup_log[] = 'ลบ MU Plugins Directory';
        }

        $htaccess_file = ABSPATH . '.htaccess';
        if (file_exists($htaccess_file) && is_writable($htaccess_file)) {
            $htaccess_content = file_get_contents($htaccess_file);
            $pattern = '/# AMP Security Protection - START.*?# AMP Security Protection - END\s*/s';
            $new_content = preg_replace($pattern, '', $htaccess_content);
            if (file_put_contents($htaccess_file, $new_content) !== false) {
                $cleanup_log[] = 'ลบ .htaccess Security Rules';
            }
        }

        if (is_wp_error($result)) {
            wp_send_json_error(['message' => 'ไม่สามารถลบปลั๊กอินได้: ' . $result->get_error_message()]);
        } else {
            $final_message = !$keep_data ?
                'ลบปลั๊กอินและข้อมูลทั้งหมดเรียบร้อยแล้ว' :
                'ลบปลั๊กอินเรียบร้อยแล้ว (เก็บข้อมูลไว้)';

            wp_send_json_success([
                'message' => $final_message,
                'redirect' => admin_url('plugins.php'),
                'cleanup_log' => $cleanup_log
            ]);
        }
    }

    function amp_delete_directory_recursive($dir) {
        if (!is_dir($dir)) {
            return 0;
        }

        $deleted_count = 0;
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                if (unlink($file->getPathname())) {
                    $deleted_count++;
                }
            } elseif ($file->isDir()) {
                rmdir($file->getPathname());
            }
        }

        if (rmdir($dir)) {
            $deleted_count++;
        }

        return $deleted_count;
    }
}
