.new-checkout-popup {
    background: rgb(255 255 255 / 0%) !important;
    backdrop-filter: blur(20px) !important;
    border-radius: 20px;
    border: 3px solid transparent !important;
    background-origin: border-box !important;
    background-clip: content-box, border-box !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    color: white;
    width: 1200px !important;
    max-width: 95vw !important;
    max-height: 90vh !important;
    padding: 0 !important;
    overflow: auto !important;
}

.new-checkout-popup .swal2-html-container {
    padding: 0;
    margin: 0;
    overflow: visible;
}

.new-checkout-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.checkout-header-modern {
    background: var(--theme-gradient);
    padding: 25px 30px;
    border-radius: 20px 20px 0 0;
    position: relative;
    overflow: hidden;
}

.checkout-header-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%),
        radial-gradient(circle at 20% 50%, rgba(255,255,255,0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(255,255,255,0.08) 0%, transparent 50%);
    background-size: 20px 20px, 100% 100%, 100% 100%, 100% 100%;
    opacity: 0.6;
    pointer-events: none;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.header-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.header-icon i {
    font-size: 24px;
    color: white;
}

.header-content h2 {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: white;
    flex: 1;
    text-align: center;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.close-btn i {
    color: white;
    font-size: 16px;
}

.warning-banner {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.4);
    border-radius: 10px;
    padding: 12px 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.warning-banner i {
    color: #ffd700;
    font-size: 18px;
}

.warning-banner span {
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.warning-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 13px;
    line-height: 1.4;
    text-align: center;
}

.checkout-body-horizontal {
    display: flex;
    gap: 30px;
    padding: 30px;
    background: #ffffff;
    border-radius: 0 0 20px 20px;
}

.dark-mode .checkout-body-horizontal {
    background: var(--card-bg);
}

.cart-items-horizontal {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 100%;
    overflow-y: auto;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: #667eea #f1f1f1;
}

.cart-items-horizontal::-webkit-scrollbar {
    height: 8px;
}

.cart-items-horizontal::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.cart-items-horizontal::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

.cart-items-horizontal::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}

.left-panel {
    flex: 4;
}

.right-panel {
    flex: 3;
}

.summary-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.dark-mode .summary-card {
    background: var(--card-bg-secondary);
    border: 1px solid var(--border-color);
}

.summary-card h3 {
    font-size: 18px;
    font-weight: 700;
    margin: 0 0 20px;
    color: #2c3e50;
    text-align: center;
}

.dark-mode .summary-card h3 {
    color: var(--text-primary);
}

.position-item-horizontal {
    background: #ffffff;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    gap: 15px;
}

.dark-mode .position-item-horizontal {
    background: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.position-item-horizontal:hover {
    border-color: #667eea;
    box-shadow: 0 2px 12px rgba(102, 126, 234, 0.15);
}

.position-info {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.position-item {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 0;
    margin-right: 15px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 250px;
    flex-shrink: 0;
}

.position-item:last-child {
    margin-right: 0;
}

.position-item-horizontal .position-name {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.dark-mode .position-item-horizontal .position-name {
    color: var(--text-primary);
}

.position-duration {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
}

.dark-mode .position-duration {
    color: var(--text-secondary);
}

.position-price {
    font-weight: 600;
    font-size: 15px;
    color: #667eea;
    white-space: nowrap;
    margin-left: 16px;
}

.position-item .position-name {
    font-size: 20px;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 15px;
    text-align: center;
}

.position-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.duration, .price {
    font-size: 14px;
    color: #6c757d;
}

.price {
    font-weight: 600;
    color: #28a745;
}

.total-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 2px solid #e9ecef;
    font-weight: 700;
}

.dark-mode .total-section {
    border-top-color: var(--border-color);
}

.total-label {
    color: #2c3e50;
    font-size: 16px;
}

.dark-mode .total-label {
    color: var(--text-primary);
}

.total-amount {
    color: #28a745;
    font-size: 18px;
}

.payment-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border: 1px solid #e9ecef;
}

.dark-mode .payment-card {
    background: var(--card-bg-secondary);
    border-color: var(--border-color);
}

.payment-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    padding: 15px;
    background: var(--theme-gradient);
    border-radius: 10px;
    color: white;
}

.payment-header i {
    font-size: 20px;
}

.timer-section-centered {
    text-align: center;
    margin: 25px 0;
    padding: 20px;
    background: linear-gradient(135deg, rgba(106, 76, 147, 0.1), rgba(74, 20, 140, 0.1));
    border-radius: 15px;
    border: 2px solid rgba(106, 76, 147, 0.3);
}

.timer-header-with-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 15px;
}

.timer-header-with-icon .timer-label {
    font-size: 16px;
    font-weight: 600;
    color: #6a4c93;
}

.dark-mode .timer-header-with-icon .timer-label {
    color: var(--text-secondary);
}

.timer-icon {
    font-size: 24px;
    color: #6a4c93;
    animation: pulse 2s infinite;
}

.dark-mode .timer-icon {
    color: var(--accent-color);
}

.timer-display-centered {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 28px;
    font-weight: bold;
    color: #6a4c93;
    margin-top: 10px;
}

.dark-mode .timer-display-centered {
    color: var(--text-primary);
}

.timer-display-centered .time-unit {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    min-width: 80px;
}

.dark-mode .timer-display-centered .time-unit {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
}

.timer-display-centered .timer-minutes,
.timer-display-centered .timer-seconds {
    font-size: 32px;
    font-weight: 700;
    color: #4a148c;
    line-height: 1;
}

.dark-mode .timer-display-centered .timer-minutes,
.dark-mode .timer-display-centered .timer-seconds {
    color: var(--text-primary);
}

.timer-display-centered .unit-label {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    font-weight: 500;
}

.dark-mode .timer-display-centered .unit-label {
    color: var(--text-secondary);
}

.timer-display-centered .separator {
    font-size: 36px;
    color: #6a4c93;
    font-weight: bold;
    margin: 0 5px;
}

.dark-mode .timer-display-centered .separator {
    color: var(--text-primary);
}

.payment-buttons-horizontal {
    display: flex;
    gap: 15px;
    margin-top: 25px;
    justify-content: center;
    align-items: stretch;
}

.btn-primary-wide {
    flex: 2;
    padding: 18px 30px;
    background: linear-gradient(135deg, #4361ee 0%, #7209b7 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
    min-height: 56px;
    white-space: nowrap;
}

.btn-primary-wide:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
    text-decoration: none;
    color: white;
}

.btn-cancel-wide {
    flex: 1;
    padding: 18px 25px;
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-height: 56px;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-cancel-wide:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

@media (max-width: 768px) {
    .new-checkout-popup {
        width: 95vw !important;
        max-width: 95vw !important;
        max-height: 95vh !important;
        margin: 2.5vh auto !important;
    }

    .checkout-header-modern {
        padding: 20px 20px;
    }

    .checkout-body-horizontal {
        flex-direction: column !important;
        gap: 20px !important;
        padding: 20px !important;
    }

    .summary-card,
    .payment-card {
        width: 100% !important;
        margin: 0 !important;
        padding: 20px !important;
    }

    .payment-buttons-horizontal {
        flex-direction: column;
    }

    .btn-primary-wide,
    .btn-cancel-wide {
        flex: none;
        width: 100%;
    }

    .timer-display-centered {
        font-size: 24px;
    }

    .timer-display-centered .timer-minutes,
    .timer-display-centered .timer-seconds {
        font-size: 28px;
    }

    .timer-display-centered .time-unit {
        padding: 12px 15px;
        min-width: 70px;
    }

    .position-item-horizontal {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
        padding: 15px !important;
    }

    .position-info {
        width: 100% !important;
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .position-price {
        margin-left: 0 !important;
        align-self: flex-end !important;
    }
}

@media (max-width: 480px) {
    .new-checkout-popup {
        width: 98vw !important;
        max-width: 98vw !important;
        max-height: 98vh !important;
        margin: 1vh auto !important;
        border-radius: 15px !important;
    }

    .checkout-header-modern {
        padding: 15px 15px;
        border-radius: 15px 15px 0 0;
    }

    .checkout-header-modern h2 {
        font-size: 20px !important;
    }

    .checkout-body-horizontal {
        padding: 15px !important;
        gap: 15px !important;
    }

    .summary-card,
    .payment-card {
        padding: 15px !important;
        border-radius: 12px !important;
    }

    .summary-card h3 {
        font-size: 16px !important;
        margin-bottom: 15px !important;
    }

    .position-item-horizontal {
        padding: 12px !important;
        border-radius: 8px !important;
    }

    .position-item-horizontal .position-name {
        font-size: 14px !important;
    }

    .position-duration {
        font-size: 12px !important;
    }

    .position-price {
        font-size: 14px !important;
    }

    .total-section {
        padding: 12px 0 !important;
    }

    .total-label {
        font-size: 14px !important;
    }

    .total-amount {
        font-size: 16px !important;
    }

    .payment-header {
        padding: 12px !important;
        margin-bottom: 15px !important;
    }

    .payment-header span {
        font-size: 14px !important;
    }

    .info-row {
        padding: 10px 0 !important;
    }

    .label,
    .value {
        font-size: 13px !important;
    }

    .timer-display-centered {
        font-size: 20px !important;
        margin: 15px 0 !important;
    }

    .timer-display-centered .timer-minutes,
    .timer-display-centered .timer-seconds {
        font-size: 24px !important;
    }

    .timer-display-centered .time-unit {
        padding: 10px 12px !important;
        min-width: 60px !important;
    }

    .btn-primary-wide,
    .btn-cancel-wide {
        padding: 12px 20px !important;
        font-size: 14px !important;
        margin: 5px 0 !important;
    }
}

@media (max-width: 360px) {
    .new-checkout-popup {
        width: 100vw !important;
        max-width: 100vw !important;
        max-height: 100vh !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .checkout-header-modern {
        border-radius: 0 !important;
        padding: 12px 12px;
    }

    .checkout-body-horizontal {
        padding: 12px !important;
    }

    .summary-card,
    .payment-card {
        padding: 12px !important;
    }
}

.payment-header span {
    font-size: 16px;
    font-weight: 600;
}

.payment-info {
    margin-bottom: 20px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.dark-mode .info-row {
    border-bottom-color: var(--border-color);
}

.info-row:last-child {
    border-bottom: none;
}

.label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

.dark-mode .label {
    color: var(--text-secondary);
}

.value {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
    word-break: break-all;
}

.dark-mode .value {
    color: var(--text-primary);
}

.status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.pending {
    background: rgba(255, 193, 7, 0.2);
    color: #f39c12;
}

.modern-success-popup {
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
}

.modern-cancel-confirmation-popup {
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
    border: 2px solid rgba(255, 107, 107, 0.3) !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    max-width: 480px !important;
    padding: 0 !important;
}

.modern-cancel-confirmation-popup .swal2-title {
    font-size: 22px !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 10px !important;
    padding: 25px 25px 0 25px !important;
}

.modern-cancel-confirmation-popup .swal2-html-container {
    margin: 0 !important;
    padding: 0 25px 25px 25px !important;
}

.modern-cancel-confirmation-popup .swal2-actions {
    margin: 0 !important;
    padding: 20px 25px 25px 25px !important;
    gap: 15px !important;
    justify-content: center !important;
}

.cancel-confirm-btn {
    padding: 12px 25px !important;
    border-radius: 12px !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    min-width: 140px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.cancel-confirm-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4) !important;
}

.cancel-confirm-btn-danger:hover {
    background: linear-gradient(135deg, #ff5252 0%, #e53935 100%) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6) !important;
    transform: translateY(-2px) !important;
}

.cancel-confirm-btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4) !important;
}

.cancel-confirm-btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.6) !important;
    transform: translateY(-2px) !important;
}

.cancel-confirm-btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4) !important;
}

.cancel-confirm-btn-success:hover {
    background: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
}

.dark-mode .new-checkout-popup {
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8) !important;
}

.dark-mode .checkout-body-horizontal {
    background: var(--card-bg) !important;
}

.dark-mode .summary-card,
.dark-mode .payment-card {
    background: var(--card-bg-secondary) !important;
    border-color: var(--border-color) !important;
}

.dark-mode .position-item-horizontal {
    background: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark-mode .position-item-horizontal .position-name {
    color: var(--text-primary) !important;
}

.dark-mode .position-duration {
    color: var(--text-secondary) !important;
}

.dark-mode .total-label {
    color: var(--text-primary) !important;
}

.dark-mode .total-section {
    border-top-color: var(--border-color) !important;
}

.dark-mode .info-row {
    border-bottom-color: var(--border-color) !important;
}

.dark-mode .value {
    color: var(--text-primary) !important;
}

.dark-mode .label {
    color: var(--text-secondary) !important;
}

.dark-mode .timer-display-centered .time-unit {
    background: var(--card-bg) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.dark-mode .timer-display-centered .unit-label {
    color: var(--text-secondary) !important;
}
