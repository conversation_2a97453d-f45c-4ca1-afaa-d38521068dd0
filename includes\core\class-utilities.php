<?php

if (!defined('WPINC')) {
    die;
}

class AMP_Utilities {
    
    private static $instance = null;
    
    private function __construct() {
        $this->init_hooks();
    }
    
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function init_hooks() {
        add_action('wp_ajax_amp_track_click', array($this, 'track_ad_click'));
        add_action('wp_ajax_nopriv_amp_track_click', array($this, 'track_ad_click'));
        add_action('wp_ajax_amp_track_global_click', array($this, 'track_global_click'));
        add_action('wp_ajax_nopriv_amp_track_global_click', array($this, 'track_global_click'));
    }
    
    public function track_ad_click() {
        if (!check_ajax_referer('amp_track_click', 'nonce', false)) {
            wp_send_json_error('Invalid nonce');
        }

        $ad_id = intval($_POST['ad_id'] ?? 0);
        if ($ad_id <= 0) {
            wp_send_json_error('Invalid ad ID');
        }

        $this->record_click($ad_id);
        wp_send_json_success('Click recorded');
    }
    
    private function record_click($ad_id) {
        if (!function_exists('record_ad_click')) {
            require_once plugin_dir_path(__FILE__) . '../utils/click-statistics.php';
        }

        record_ad_click($ad_id);
    }

    public function track_global_click() {
        if (!function_exists('record_global_click')) {
            require_once plugin_dir_path(__FILE__) . '../utils/click-statistics.php';
        }

        $clicks = intval($_POST['clicks'] ?? 1);
        if ($clicks <= 0) {
            $clicks = 1;
        }

        $result = record_global_click($clicks);

        if ($result) {
            wp_send_json_success('Global click recorded');
        } else {
            wp_send_json_error('Failed to record global click');
        }
    }
    

    
    public function fetch_thb_usdt_rate() {
        $apis = [
            [
                'name' => 'CoinGecko',
                'url' => 'https://api.coingecko.com/api/v3/simple/price?ids=tether&vs_currencies=thb',
                'parser' => function($data) {
                    return isset($data['tether']['thb']) ? floatval($data['tether']['thb']) : false;
                }
            ],
            [
                'name' => 'ExchangeRate-API',
                'url' => 'https://api.exchangerate-api.com/v4/latest/USDT',
                'parser' => function($data) {
                    return isset($data['rates']['THB']) ? floatval($data['rates']['THB']) : false;
                }
            ],
            [
                'name' => 'Coinbase',
                'url' => 'https://api.coinbase.com/v2/exchange-rates?currency=USDT',
                'parser' => function($data) {
                    return isset($data['data']['rates']['THB']) ? floatval($data['data']['rates']['THB']) : false;
                }
            ],
            [
                'name' => 'CoinLore',
                'url' => 'https://api.coinlore.net/api/ticker/?id=518',
                'parser' => function($data) {
                    if (isset($data[0]['price_usd'])) {
                        $usd_price = floatval($data[0]['price_usd']);
                        $thb_rate = $this->get_current_usd_thb_rate();
                        return $usd_price > 0 ? $usd_price * $thb_rate : false;
                    }
                    return false;
                }
            ],
            [
                'name' => 'Kraken',
                'url' => 'https://api.kraken.com/0/public/Ticker?pair=USDTUSD',
                'parser' => function($data) {
                    if (isset($data['result']['USDTZUSD']['c'][0])) {
                        $usd_price = floatval($data['result']['USDTZUSD']['c'][0]);
                        $thb_rate = $this->get_current_usd_thb_rate();
                        return $usd_price > 0 ? $usd_price * $thb_rate : false;
                    }
                    return false;
                }
            ]
        ];

        foreach ($apis as $api) {
            $rate = $this->try_fetch_rate($api);
            if ($rate && is_numeric($rate) && $rate > 30 && $rate < 50) {
                error_log("Successfully fetched USDT/THB rate from {$api['name']}: {$rate}");

                global $wpdb;
                $table_name = $wpdb->prefix . 'ad_price_global_settings';

                $wpdb->replace($table_name, array(
                    'setting_name' => 'thb_rate',
                    'setting_value' => $rate
                ));

                $wpdb->replace($table_name, array(
                    'setting_name' => 'exchange_rate_last_updated',
                    'setting_value' => time()
                ));

                $wpdb->replace($table_name, array(
                    'setting_name' => 'exchange_rate_source',
                    'setting_value' => $api['name']
                ));

                set_transient('amp_last_successful_rate', $rate, 86400);

                return $rate;
            } else {
                error_log("Failed to fetch valid rate from {$api['name']}: " . ($rate ?: 'null'));
            }
        }

        $last_successful_rate = get_transient('amp_last_successful_rate');
        if ($last_successful_rate && is_numeric($last_successful_rate)) {
            error_log('All USDT/THB rate APIs failed, using last successful rate: ' . $last_successful_rate);
            return floatval($last_successful_rate);
        }

        $stored_rate = get_option('amp_thb_rate', 35.5);
        if ($stored_rate && is_numeric($stored_rate) && $stored_rate > 30 && $stored_rate < 50) {
            error_log('All USDT/THB rate APIs failed, using stored rate: ' . $stored_rate);
            return floatval($stored_rate);
        }

        error_log('All USDT/THB rate APIs failed, using default fallback rate: 35.5');
        return 35.5;
    }

    private function get_current_usd_thb_rate() {
        $cached_rate = get_transient('amp_usd_thb_rate');
        if ($cached_rate) {
            return $cached_rate;
        }

        $usd_thb_apis = [
            'https://api.exchangerate-api.com/v4/latest/USD',
            'https://api.fixer.io/latest?base=USD&symbols=THB'
        ];

        foreach ($usd_thb_apis as $api_url) {
            try {
                $response = wp_remote_get($api_url, array(
                    'timeout' => 5,
                    'headers' => array(
                        'User-Agent' => 'AMP-Plugin/' . AMP_VERSION . '; ' . home_url()
                    )
                ));

                if (!is_wp_error($response)) {
                    $body = wp_remote_retrieve_body($response);
                    $data = json_decode($body, true);

                    if (isset($data['rates']['THB'])) {
                        $rate = floatval($data['rates']['THB']);
                        if ($rate > 30 && $rate < 40) {
                            set_transient('amp_usd_thb_rate', $rate, 3600);
                            return $rate;
                        }
                    }
                }
            } catch (Exception $e) {
                continue;
            }
        }

        return 35.5;
    }

    private function try_fetch_rate($api) {
        try {
            $response = wp_remote_get($api['url'], array(
                'timeout' => 8,
                'headers' => array(
                    'User-Agent' => 'AMP-Plugin/' . AMP_VERSION . '; ' . home_url(),
                    'Accept' => 'application/json'
                ),
                'sslverify' => false
            ));

            if (is_wp_error($response)) {
                error_log("{$api['name']} API Error: " . $response->get_error_message());
                return false;
            }

            $http_code = wp_remote_retrieve_response_code($response);
            if ($http_code !== 200) {
                error_log("{$api['name']} API: HTTP {$http_code} response");
                return false;
            }

            $body = wp_remote_retrieve_body($response);
            if (empty($body)) {
                error_log("{$api['name']} API: Empty response body");
                return false;
            }

            $data = json_decode($body, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("{$api['name']} API: JSON decode error - " . json_last_error_msg());
                return false;
            }

            $rate = $api['parser']($data);

            if ($rate && is_numeric($rate) && $rate > 30 && $rate < 50) {
                return floatval($rate);
            }

            error_log("{$api['name']} API: Invalid rate value: " . ($rate ?: 'null') . " (expected 30-50 THB)");
            return false;

        } catch (Exception $e) {
            error_log("{$api['name']} API Exception: " . $e->getMessage());
            return false;
        }
    }

    public function test_all_exchange_apis() {
        $apis = [
            [
                'name' => 'CoinGecko',
                'url' => 'https://api.coingecko.com/api/v3/simple/price?ids=tether&vs_currencies=thb',
                'parser' => function($data) {
                    return isset($data['tether']['thb']) ? floatval($data['tether']['thb']) : false;
                }
            ],
            [
                'name' => 'ExchangeRate-API',
                'url' => 'https://api.exchangerate-api.com/v4/latest/USDT',
                'parser' => function($data) {
                    return isset($data['rates']['THB']) ? floatval($data['rates']['THB']) : false;
                }
            ],
            [
                'name' => 'Coinbase',
                'url' => 'https://api.coinbase.com/v2/exchange-rates?currency=USDT',
                'parser' => function($data) {
                    return isset($data['data']['rates']['THB']) ? floatval($data['data']['rates']['THB']) : false;
                }
            ]
        ];

        $results = [];
        foreach ($apis as $api) {
            $start_time = microtime(true);
            $rate = $this->try_fetch_rate($api);
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000, 2);

            $results[] = [
                'name' => $api['name'],
                'status' => $rate ? 'success' : 'failed',
                'rate' => $rate,
                'response_time' => $response_time . 'ms'
            ];
        }

        return $results;
    }

    public function update_exchange_rate() {
        $last_update = get_option('amp_exchange_rate_last_update', 0);
        $update_interval = 24 * 60 * 60;
        
        if ((time() - $last_update) < $update_interval) {
            return false;
        }
        
        $new_rate = $this->fetch_thb_usdt_rate();
        
        if ($new_rate) {
            update_option('amp_thb_rate', $new_rate);
            update_option('amp_exchange_rate_last_update', time());
            
            if (class_exists('AMP_Cache_Manager')) {
                $cache = AMP_Cache_Manager::instance();
                if ($cache) {
                    $cache->clear_group('exchange_rates');
                }
            }
            
            return $new_rate;
        }
        
        return false;
    }

    public function get_current_exchange_rate() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ad_price_global_settings';
        
        $rate = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM {$table_name} WHERE setting_name = %s",
            'thb_rate'
        ));
        
        if ($rate) {
            return floatval($rate);
        }
        
        $fallback_rate = get_option('amp_thb_rate', 35.5);
        return floatval($fallback_rate);
    }

    public function should_update_exchange_rate() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'ad_price_global_settings';
        
        $last_updated = $wpdb->get_var($wpdb->prepare(
            "SELECT setting_value FROM {$table_name} WHERE setting_name = %s",
            'exchange_rate_last_updated'
        ));
        
        if (!$last_updated) {
            return true;
        }
        
        $update_interval = 24 * 60 * 60;
        return (time() - intval($last_updated)) >= $update_interval;
    }

    public static function get_client_ip() {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = trim(explode(',', $_SERVER[$key])[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
} 